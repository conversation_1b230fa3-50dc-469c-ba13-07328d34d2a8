﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable IdentifierTypo  -->
<!--  <PERSON><PERSON><PERSON>per disable UnusedMember.Local  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<Page x:Class="CollapseLauncher.Pages.OOBE.OOBEStartUpMenu"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:animatedvisuals="using:Microsoft.UI.Xaml.Controls.AnimatedVisuals"
      xmlns:controls="using:Hi3Helper.CommunityToolkit.WinUI.Controls"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:helper="using:Hi3Helper"
      xmlns:innerConfig="using:Hi3Helper.Shared.Region"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:ui="using:CommunityToolkit.WinUI"
      Background="Transparent"
      mc:Ignorable="d">
    <Grid>
        <AnimatedVisualPlayer x:Name="WelcomeLogoIntro"
                              Width="Auto"
                              HorizontalAlignment="Stretch"
                              AutoPlay="False" />
        <Grid x:Name="MainUI"
              Visibility="Collapsed">
            <Grid.ChildrenTransitions>
                <PopupThemeTransition />
            </Grid.ChildrenTransitions>
            <Image x:Name="ContainerBackgroundImage"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   Opacity="0.5"
                   Source="ms-appx:///Assets/Images/PageBackground/StartupBackground2.png"
                   Stretch="UniformToFill" />
            <Grid x:Name="ContainerGrid"
                  Margin="48"
                  HorizontalAlignment="Stretch"
                  VerticalAlignment="Stretch">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition MaxWidth="478" />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>
                <StackPanel x:Name="CollapseLogoContainer"
                            HorizontalAlignment="Left"
                            Orientation="Horizontal">
                    <Image x:Name="GridBG_IconImg"
                           Width="160"
                           Margin="0,0,0,0"
                           Opacity="0.8"
                           PointerEntered="GridBG_Icon_PointerEntered"
                           PointerExited="GridBG_Icon_PointerExited"
                           Source="ms-appx:///Assets/CollapseLauncherLogo.png">
                        <Image.OpacityTransition>
                            <ScalarTransition />
                        </Image.OpacityTransition>
                        <Image.ScaleTransition>
                            <Vector3Transition Duration="0:0:0.2" />
                        </Image.ScaleTransition>
                    </Image>
                </StackPanel>
                <Grid Grid.Row="0"
                      Grid.Column="0"
                      Grid.ColumnSpan="2"
                      HorizontalAlignment="Right"
                      VerticalAlignment="Stretch">
                    <Button x:Name="PrevPageButton"
                            VerticalAlignment="Top"
                            Click="PrevPageButton_Click"
                            Opacity="0"
                            Style="{ThemeResource TransparentDefaultButtonStyle}"
                            Visibility="Collapsed">
                        <Button.OpacityTransition>
                            <ScalarTransition />
                        </Button.OpacityTransition>
                        <Button.Content>
                            <StackPanel>
                                <AnimatedIcon Width="24"
                                              MinHeight="{ThemeResource ComboBoxMinHeight}"
                                              HorizontalAlignment="Center">
                                    <animatedvisuals:AnimatedBackVisualSource />
                                    <AnimatedIcon.FallbackIconSource>
                                        <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                        FontSize="12"
                                                        Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                        Glyph="&#xE70D;" />
                                    </AnimatedIcon.FallbackIconSource>
                                </AnimatedIcon>
                                <TextBlock FontWeight="Bold"
                                           Text="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.SetupBackButton}" />
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </Grid>
                <Grid x:Name="TitleTextGrid"
                      Grid.Row="1"
                      Grid.Column="0">
                    <StackPanel x:Name="TitleTextContainer"
                                Margin="0,16,16,0"
                                VerticalAlignment="Top"
                                Orientation="Vertical">
                        <TextBlock FontSize="48"
                                   FontWeight="Light"
                                   Text="{x:Bind helper:Locale.Lang._StartupPage.Title1}"
                                   TextWrapping="Wrap" />
                        <TextBlock Margin="0,0,0,0"
                                   FontSize="48"
                                   FontWeight="Bold"
                                   Text="{x:Bind helper:Locale.Lang._StartupPage.Title2}"
                                   TextWrapping="Wrap" />
                    </StackPanel>
                </Grid>
                <ScrollViewer x:Name="LauncherFolderContainer"
                              Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Margin="0,0,0,0"
                              VerticalAlignment="Center">
                    <StackPanel HorizontalAlignment="Left"
                                VerticalAlignment="Center">
                        <StackPanel.Transitions>
                            <EntranceThemeTransition />
                        </StackPanel.Transitions>
                        <TextBlock Margin="0,0,0,16"
                                   FontSize="32"
                                   Foreground="{ThemeResource SystemAccentColor}"
                                   Style="{ThemeResource TitleLargeTextBlockStyle}"
                                   Text="{x:Bind helper:Locale.Lang._StartupPage.Subtitle1}" />
                        <TextBlock FontWeight="Normal"
                                   Style="{ThemeResource SubtitleTextBlockStyle}"
                                   Text="{x:Bind helper:Locale.Lang._StartupPage.Subtitle2}" />
                        <TextBlock Margin="0,0,0,32"
                                   FontWeight="Normal"
                                   Style="{ThemeResource SubtitleTextBlockStyle}"
                                   Text="{x:Bind helper:Locale.Lang._StartupPage.Subtitle3}" />
                        <TextBlock Margin="0,0,0,16"
                                   Style="{ThemeResource SubtitleTextBlockStyle}">
                            <Run FontWeight="Normal"
                                 Text="{x:Bind helper:Locale.Lang._StartupPage.Subtitle4_1}" />
                            <Run Foreground="{ThemeResource SystemAccentColor}"
                                 Text="{x:Bind helper:Locale.Lang._StartupPage.Subtitle4_2}" />
                            <Run FontWeight="Normal"
                                 Text="{x:Bind helper:Locale.Lang._StartupPage.Subtitle4_3}" />
                        </TextBlock>
                        <StackPanel Orientation="Vertical">
                            <TextBlock x:Name="ErrMsg"
                                       Margin="0,0,0,16"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Center"
                                       FontWeight="SemiBold"
                                       Foreground="{ThemeResource TextFillColorPrimary}"
                                       Text=""
                                       TextTrimming="Clip"
                                       TextWrapping="Wrap" />
                            <Button Click="ChooseFolder"
                                    CornerRadius="16"
                                    Style="{ThemeResource AccentButtonStyle}">
                                <StackPanel Margin="8,0"
                                            Orientation="Horizontal">
                                    <FontIcon Margin="0,0,8,0"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              FontSize="14"
                                              Glyph="&#xf07b;" />
                                    <TextBlock FontWeight="Medium"
                                               Text="{x:Bind helper:Locale.Lang._StartupPage.ChooseFolderBtn}"
                                               TextWrapping="Wrap" />
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
                <ScrollViewer x:Name="CustomizationContainer"
                              Grid.Row="1"
                              Grid.Column="0"
                              Grid.ColumnSpan="2"
                              Margin="0,-48,0,24"
                              Visibility="Collapsed">
                    <StackPanel x:Name="SettingsCardContainer">
                        <TextBlock Margin="0,0,0,16"
                                   FontSize="32"
                                   Text="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationTitle}" />
                        <controls:SettingsCard x:Name="SettingsCardItemLanguage"
                                               Margin="0,2"
                                               Description="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsLanguageDescription}"
                                               Header="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsLanguageHeader}"
                                               HeaderIcon="{ui:FontIcon Glyph=&#xf57d;}">
                            <ComboBox x:Name="SelectLang"
                                      ItemsSource="{x:Bind LangList}"
                                      MaxDropDownHeight="200"
                                      SelectedIndex="{x:Bind SelectedLangIndex, Mode=TwoWay}"
                                      SelectionChanged="FixComboBoxSize" />
                        </controls:SettingsCard>
                        <controls:SettingsCard x:Name="SettingsCardItemWindowSize"
                                               Margin="0,2"
                                               Description="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsWindowSizeDescription}"
                                               Header="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsWindowSizeHeader}"
                                               HeaderIcon="{ui:FontIcon Glyph=&#xf2d2;}">
                            <ComboBox x:Name="SelectWindowSize"
                                      SelectedIndex="{x:Bind SelectedWindowSizeProfile, Mode=TwoWay}"
                                      SelectionChanged="FixComboBoxSize">
                                <ComboBoxItem Content="{x:Bind helper:Locale.Lang._SettingsPage.AppWindowSize_Normal}" />
                                <ComboBoxItem Content="{x:Bind helper:Locale.Lang._SettingsPage.AppWindowSize_Small}" />
                            </ComboBox>
                        </controls:SettingsCard>
                        <controls:SettingsCard x:Name="SettingsCardItemPreferredCDN"
                                               Margin="0,2"
                                               Description="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsCDNDescription}"
                                               Header="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsCDNHeader}"
                                               HeaderIcon="{ui:FontIcon Glyph=&#xf6ff;}">
                            <StackPanel Orientation="Horizontal">
                                <Button Margin="0,0,0,0"
                                        Click="RefreshCDNCheckButtonClick"
                                        Style="{ThemeResource TransparentDefaultButtonStyle}">
                                    <Button.Content>
                                        <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                  FontSize="12"
                                                  Glyph="&#xf021;" />
                                    </Button.Content>
                                </Button>
                                <Button Margin="0,0,8,0"
                                        Style="{ThemeResource TransparentDefaultButtonStyle}">
                                    <Button.Content>
                                        <FontIcon FontFamily="{ThemeResource FontAwesome}"
                                                  FontSize="12"
                                                  Glyph="&#x3f;" />
                                    </Button.Content>
                                    <Button.Flyout>
                                        <Flyout>
                                            <StackPanel MaxWidth="380">
                                                <TextBlock TextWrapping="WrapWholeWords">
                                                    <Run FontSize="20"
                                                         FontWeight="Bold"
                                                         Text="{x:Bind helper:Locale.Lang._StartupPage.CDNHelpTitle_1}" />
                                                    <Run FontSize="12"
                                                         Text="{x:Bind helper:Locale.Lang._StartupPage.CDNHelpTitle_2}" />
                                                    <Run FontSize="12"
                                                         FontWeight="Bold"
                                                         Text="{x:Bind helper:Locale.Lang._StartupPage.CDNHelpTitle_3}" />
                                                    <Run FontSize="12"
                                                         Text="{x:Bind helper:Locale.Lang._StartupPage.CDNHelpTitle_4}" />
                                                </TextBlock>
                                                <TextBlock MaxWidth="380"
                                                           Margin="0,8,0,0"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._StartupPage.CDNHelpDetail_1}" />
                                                    <Run FontWeight="Bold"
                                                         Text="{x:Bind helper:Locale.Lang._StartupPage.CDNHelpDetail_2}" />
                                                    <Run Text="{x:Bind helper:Locale.Lang._StartupPage.CDNHelpDetail_3}" />
                                                </TextBlock>
                                                <TextBlock Text="{x:Bind helper:Locale.Lang._StartupPage.CDNHelpDetail_4}" />
                                                <TextBlock Margin="0,12,0,0"
                                                           FontSize="20"
                                                           FontWeight="Bold"
                                                           Text="{x:Bind helper:Locale.Lang._StartupPage.CDNsAvailable}" />
                                                <ItemsControl MaxWidth="380"
                                                              Margin="0,0,0,-8"
                                                              ItemsSource="{x:Bind innerConfig:LauncherConfig.CDNList}">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate x:DataType="innerConfig:CDNURLProperty">
                                                            <StackPanel Margin="0,8">
                                                                <TextBlock FontSize="16"
                                                                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                                           Text="{x:Bind Name}" />
                                                                <TextBlock Text="{x:Bind Description}"
                                                                           TextWrapping="Wrap" />
                                                            </StackPanel>
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </StackPanel>
                                        </Flyout>
                                    </Button.Flyout>
                                </Button>
                                <ComboBox x:Name="SelectCDN"
                                          SelectedIndex="{x:Bind SelectedCDN, Mode=TwoWay}"
                                          SelectionChanged="FixComboBoxSize" />
                            </StackPanel>
                        </controls:SettingsCard>
                        <controls:SettingsExpander x:Name="SettingsCardItemTheme"
                                                   Margin="0,2"
                                                   Description="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsStyleDescription}"
                                                   Header="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsStyleHeader}"
                                                   HeaderIcon="{ui:FontIcon Glyph=&#xf53f;}">
                            <controls:SettingsExpander.Items>
                                <controls:SettingsCard Description="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsStyleThemeDescription}"
                                                       Header="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsStyleThemeHeader}">
                                    <ComboBox x:Name="SettingsAppThemeCombobox"
                                              SelectedIndex="{x:Bind SelectedTheme, Mode=TwoWay}"
                                              SelectionChanged="FixComboBoxSize">
                                        <ComboBoxItem Content="{x:Bind helper:Locale.Lang._SettingsPage.AppThemes_Default}" />
                                        <ComboBoxItem Content="{x:Bind helper:Locale.Lang._SettingsPage.AppThemes_Light}" />
                                        <ComboBoxItem Content="{x:Bind helper:Locale.Lang._SettingsPage.AppThemes_Dark}" />
                                    </ComboBox>
                                </controls:SettingsCard>
                                <controls:SettingsCard Description="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsStyleCustomBackgroundDescription}"
                                                       Header="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.CustomizationSettingsStyleCustomBackgroundHeader}">
                                    <CheckBox Checked="CustomBackgroundCheckedOpen"
                                              Content="{x:Bind helper:Locale.Lang._SettingsPage.AppBG_Checkbox}"
                                              Unchecked="CustomBackgroundCheckedClose" />
                                </controls:SettingsCard>
                            </controls:SettingsExpander.Items>
                        </controls:SettingsExpander>
                    </StackPanel>
                </ScrollViewer>
                <Button x:Name="NextPageButton"
                        Grid.Row="2"
                        Grid.Column="0"
                        Grid.ColumnSpan="2"
                        Margin="0,0,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Bottom"
                        Click="NextPageButton_Click"
                        IsEnabled="False"
                        Opacity="0"
                        Style="{ThemeResource TransparentDefaultButtonStyle}">
                    <Button.OpacityTransition>
                        <ScalarTransition />
                    </Button.OpacityTransition>
                    <Button.Content>
                        <StackPanel>
                            <TextBlock FontSize="16"
                                       FontStretch="UltraExpanded"
                                       FontWeight="SemiBold"
                                       Text="{x:Bind helper:Locale.Lang._OOBEStartUpMenu.SetupNextButton}" />
                            <AnimatedIcon x:Name="DropDownGlyph"
                                          Width="24"
                                          MinHeight="{ThemeResource ComboBoxMinHeight}"
                                          Margin="0,-4,0,-8"
                                          HorizontalAlignment="Center">
                                <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                <AnimatedIcon.FallbackIconSource>
                                    <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                    FontSize="12"
                                                    Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                    Glyph="&#xE70D;" />
                                </AnimatedIcon.FallbackIconSource>
                            </AnimatedIcon>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </Grid>
        </Grid>
        <Grid x:Name="IntroSequenceUI"
              Margin="64"
              VerticalAlignment="Center">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <StackPanel x:Name="WelcomeVCarouselGrid"
                        Grid.Row="1"
                        Grid.Column="0"
                        Margin="0,32,0,0"
                        HorizontalAlignment="Left">
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="WelcomeVCarouselText1"
                               FontSize="32"
                               Opacity="0"
                               Text="Welcome" />
                    <TextBlock x:Name="WelcomeVCarouselText2"
                               FontSize="32"
                               Opacity="0"
                               Text=" To" />
                </StackPanel>
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="WelcomeVCarouselText3"
                               FontSize="48"
                               FontWeight="Bold"
                               Opacity="0"
                               Text="Collapse" />
                    <TextBlock x:Name="WelcomeVCarouselText4"
                               FontSize="48"
                               FontWeight="Bold"
                               Opacity="0"
                               Text=" Launcher" />
                </StackPanel>
            </StackPanel>
        </Grid>
        <Frame x:Name="OverlayFrame"
               x:FieldModifier="internal" />
    </Grid>
</Page>
