﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable IdentifierTypo  -->
<!--  <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Local  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<Page x:Class="CollapseLauncher.Pages.UnavailablePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:helper="using:Hi3Helper"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      mc:Ignorable="d">
    <Grid>
        <StackPanel Margin="0,128,0,0"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Orientation="Vertical">
            <ProgressRing x:Name="Ring"
                          Width="48"
                          Height="48"
                          Margin="32"
                          IsActive="True"
                          IsIndeterminate="false"
                          Maximum="100"
                          Value="100" />
            <TextBlock x:Name="Title"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Style="{ThemeResource SubtitleTextBlockStyle}"
                       Text="{x:Bind helper:Locale.Lang._Misc.FeatureUnavailableTitle}" />
            <TextBlock x:Name="Subtitle"
                       Margin="0,8,0,192"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                       Text="{x:Bind helper:Locale.Lang._Misc.FeatureUnavailableSubtitle}" />
        </StackPanel>
    </Grid>
</Page>
