﻿<Page x:Class="CollapseLauncher.Pages.FileCleanupPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:behaviors="using:CommunityToolkit.WinUI.Behaviors"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:datatable="using:CommunityToolkit.WinUI.Controls.Labs.DataTable"
      xmlns:helper="using:Hi3Helper"
      xmlns:interactivity="using:Microsoft.Xaml.Interactivity"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:page="using:CollapseLauncher.Pages"
      xmlns:type="using:CollapseLauncher.InstallManager.Base"
      mc:Ignorable="d">
    <Page.Resources>
        <page:FileSizeToStringLiteralConverter x:Key="FileSizeToStringConverter" />
    </Page.Resources>
    <Grid Padding="32,32,32,32"
          Background="{ThemeResource WebView2GridBackground}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid ColumnSpacing="8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <TextBlock Margin="0,0,0,16"
                       Style="{ThemeResource TitleLargeTextBlockStyle}"
                       Text="{x:Bind helper:Locale.Lang._FileCleanupPage.Title}" />
            <Button x:Name="MenuReScanButton"
                    Grid.Column="1"
                    x:FieldModifier="internal">
                <Button.Content>
                    <Grid Margin="0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Margin="0,0,8,0"
                                   HorizontalAlignment="Left"
                                   VerticalAlignment="Center"
                                   FontWeight="Medium"
                                   HorizontalTextAlignment="Left"
                                   Text="{x:Bind helper:Locale.Lang._FileCleanupPage.TopButtonRescan}" />
                        <FontIcon Grid.Column="1"
                                  HorizontalAlignment="Right"
                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                  FontSize="14"
                                  Glyph="" />
                    </Grid>
                </Button.Content>
            </Button>
            <Button x:Name="MenuExitButton"
                    Grid.Column="2"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    x:FieldModifier="internal"
                    Style="{ThemeResource AccentButtonStyle}">
                <Button.Content>
                    <FontIcon Margin="0,2,0,3"
                              FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="14"
                              Glyph="" />
                </Button.Content>
            </Button>
        </Grid>
        <Grid Grid.Row="1"
              Margin="-16,0,-16,-16"
              Padding="16"
              Background="{ThemeResource AcrylicBackgroundFillColorDefaultBrush}"
              CornerRadius="8">
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Grid x:Name="NoFilesTextGrid">
                <Grid.OpacityTransition>
                    <ScalarTransition />
                </Grid.OpacityTransition>
                <TextBlock Grid.Row="0"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Opacity="0.6"
                           Style="{ThemeResource TitleTextBlockStyle}"
                           Text="{x:Bind helper:Locale.Lang._FileCleanupPage.NoFilesToBeDeletedText}" />
            </Grid>
            <ListView x:Name="ListViewTable"
                      Grid.Row="0"
                      IsMultiSelectCheckBoxEnabled="True"
                      ItemsSource="{x:Bind FileInfoSource}"
                      SelectionChanged="ListView_SelectionChanged"
                      SelectionMode="Multiple">
                <ListView.OpacityTransition>
                    <ScalarTransition />
                </ListView.OpacityTransition>
                <ListView.Header>
                    <Border Padding="0,8,0,0"
                            Background="{ThemeResource AcrylicBackgroundFillColorBaseBrush}"
                            CornerRadius="8">
                        <interactivity:Interaction.Behaviors>
                            <behaviors:StickyHeaderBehavior />
                        </interactivity:Interaction.Behaviors>
                        <datatable:DataTable Margin="12,0,0,8">
                            <datatable:DataColumn HorizontalAlignment="Stretch"
                                                  HorizontalContentAlignment="Stretch"
                                                  CanResize="False"
                                                  Content="{x:Bind helper:Locale.Lang._FileCleanupPage.ListViewFieldFileName}"
                                                  DesiredWidth="*"
                                                  FontSize="13"
                                                  FontWeight="Bold"
                                                  Tag="Filename" />
                            <datatable:DataColumn MinWidth="192"
                                                  HorizontalContentAlignment="Stretch"
                                                  CanResize="False"
                                                  FontSize="13"
                                                  FontWeight="Bold"
                                                  Tag="Filesize">
                                <TextBlock Margin="0,4,20,4"
                                           HorizontalAlignment="Right"
                                           Text="{x:Bind helper:Locale.Lang._FileCleanupPage.ListViewFieldFileSize}" />
                            </datatable:DataColumn>
                        </datatable:DataTable>
                    </Border>
                </ListView.Header>
                <ListView.ItemTemplate>
                    <DataTemplate x:DataType="type:LocalFileInfo">
                        <datatable:DataRow>
                            <TextBlock Margin="0,0,16,0"
                                       VerticalAlignment="Center"
                                       Text="{x:Bind RelativePath}"
                                       TextTrimming="CharacterEllipsis" />
                            <TextBlock Margin="-30,0,56,0"
                                       VerticalAlignment="Center"
                                       Text="{x:Bind FileSize, Converter={StaticResource FileSizeToStringConverter}}"
                                       TextAlignment="Right"
                                       TextTrimming="CharacterEllipsis" />
                        </datatable:DataRow>
                    </DataTemplate>
                </ListView.ItemTemplate>
                <ListView.ItemContainerStyle>
                    <Style BasedOn="{StaticResource DefaultListViewItemStyle}"
                           TargetType="ListViewItem">
                        <Setter Property="HorizontalContentAlignment" Value="Left" />
                    </Style>
                </ListView.ItemContainerStyle>
            </ListView>
            <Grid Grid.Row="1"
                  Margin="14,16,0,0"
                  ColumnSpacing="8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <CheckBox x:Name="ToggleCheckAllCheckBox"
                          Checked="ToggleCheckAll"
                          IsChecked="False"
                          Unchecked="ToggleCheckAll" />
                <Button x:Name="DeleteSelectedFiles"
                        Grid.Column="0"
                        HorizontalAlignment="Right"
                        Click="DeleteSelectedFiles_Click">
                    <Button.Content>
                        <Grid Margin="0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock x:Name="DeleteSelectedFilesText"
                                       Margin="0,0,8,0"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"
                                       HorizontalTextAlignment="Left"
                                       Text="" />
                            <FontIcon Grid.Column="1"
                                      HorizontalAlignment="Right"
                                      FontFamily="{ThemeResource FontAwesome}"
                                      FontSize="14"
                                      Glyph="" />
                        </Grid>
                    </Button.Content>
                </Button>
                <Button x:Name="DeleteAllFiles"
                        Grid.Column="1"
                        HorizontalAlignment="Right"
                        Click="DeleteAllFiles_Click"
                        Style="{ThemeResource AccentButtonStyle}">
                    <Button.Content>
                        <Grid Margin="0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock Margin="0,0,8,0"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"
                                       HorizontalTextAlignment="Left"
                                       Text="{x:Bind helper:Locale.Lang._FileCleanupPage.BottomButtonDeleteAllFiles}" />
                            <FontIcon Grid.Column="1"
                                      HorizontalAlignment="Right"
                                      FontFamily="{ThemeResource FontAwesome}"
                                      FontSize="14"
                                      Glyph="" />
                        </Grid>
                    </Button.Content>
                </Button>
            </Grid>
        </Grid>
    </Grid>
</Page>
