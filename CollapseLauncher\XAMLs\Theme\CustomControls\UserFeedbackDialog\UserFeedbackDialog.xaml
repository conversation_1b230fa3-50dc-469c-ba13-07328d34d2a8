﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable XAML1103  -->
<!--  <PERSON><PERSON><PERSON><PERSON> disable Xaml.RedundantNamespaceAlias  -->
<!--  ReSharper disable Xaml.InvalidResourceType  -->
<!--  ReSharper disable Xaml.StaticResourceNotResolved  -->
<!--  ReS<PERSON>per disable Xaml.PathError  -->
<!--  <PERSON>S<PERSON>per disable UnusedMember.Local  -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:animatedVisuals="using:Microsoft.UI.Xaml.Controls.AnimatedVisuals"
                    xmlns:controls="using:CollapseLauncher.XAMLs.Theme.CustomControls.UserFeedbackDialog"
                    xmlns:helper="using:Hi3Helper">

    <ResourceDictionary.ThemeDictionaries>
        <ResourceDictionary x:Key="Default">
            <AcrylicBrush x:Key="AcrylicTitleGridBackground"
                          TintColor="{ThemeResource SystemAccentColor}"
                          TintLuminosityOpacity="0.8"
                          TintOpacity="0.9"
                          FallbackColor="{ThemeResource SystemAccentColor}"/>
            <AcrylicBrush x:Key="AcrylicInputGridBackground"
                          TintColor="{ThemeResource SolidBackgroundFillColorBase}"
                          TintLuminosityOpacity="0.9"
                          TintOpacity="0.4"
                          FallbackColor="{ThemeResource SolidBackgroundFillColorBase}"/>
            <AcrylicBrush x:Key="AcrylicConfirmationGridBackground"
                          TintColor="{ThemeResource SystemAccentColor}"
                          TintLuminosityOpacity="0.0"
                          TintOpacity="0.8"
                          FallbackColor="{ThemeResource SystemAccentColor}"/>
            <StaticResource x:Key="SmokeGridOverlayBackground"
                            ResourceKey="SmokeFillColorCollapseBrush" />
        </ResourceDictionary>
        <ResourceDictionary x:Key="Light">
            <AcrylicBrush x:Key="AcrylicTitleGridBackground"
                          TintColor="{ThemeResource SystemAccentColor}"
                          TintLuminosityOpacity="0.9"
                          TintOpacity="0.9"
                          FallbackColor="{ThemeResource SystemAccentColor}"/>
            <AcrylicBrush x:Key="AcrylicInputGridBackground"
                          TintColor="{ThemeResource SolidBackgroundFillColorBase}"
                          TintLuminosityOpacity="0.9"
                          TintOpacity="0.8" 
                          FallbackColor="{ThemeResource SolidBackgroundFillColorBase}"/>
            <AcrylicBrush x:Key="AcrylicConfirmationGridBackground"
                          TintColor="{ThemeResource SystemAccentColor}"
                          TintLuminosityOpacity="0.0"
                          TintOpacity="0.9" 
                          FallbackColor="{ThemeResource SolidBackgroundFillColorBase}"/>
            <StaticResource x:Key="SmokeGridOverlayBackground"
                            ResourceKey="SmokeFillColorCollapseBrush" />
        </ResourceDictionary>
    </ResourceDictionary.ThemeDictionaries>

    <StaticResource x:Key="BackgroundBrush"
                    ResourceKey="CardBackgroundFillColorDefaultBrush" />
    <StaticResource x:Key="StrokeColorBrush"
                    ResourceKey="CardStrokeColorDefaultBrush" />
    <StaticResource x:Key="ContentDialogSmokeFill"
                    ResourceKey="SmokeFillColorCollapseBrush" />

    <ThemeShadow x:Key="SharedShadow" />

    <Style TargetType="controls:UserFeedbackDialog">
        <Setter Property="Title" Value="{x:Null}" />
        <Setter Property="Message" Value="{x:Null}" />
        <Setter Property="IsTitleReadOnly" Value="False" />
        <Setter Property="IsMessageReadOnly" Value="False" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}" />
        <Setter Property="Padding" Value="80" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="controls:UserFeedbackDialog">
                    <Border x:Name="Container">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="DialogShowingStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition To="DialogHidden">
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                           Storyboard.TargetProperty="Visibility">
                                                <DiscreteObjectKeyFrame KeyTime="0:0:0"
                                                                        Value="Visible" />
                                            </ObjectAnimationUsingKeyFrames>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                           Storyboard.TargetProperty="IsHitTestVisible">
                                                <DiscreteObjectKeyFrame KeyTime="0:0:0"
                                                                        Value="False" />
                                            </ObjectAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ScaleTransform"
                                                                           Storyboard.TargetProperty="ScaleX">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="1.0" />
                                                <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                      KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                      Value="1.05" />
                                            </DoubleAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ScaleTransform"
                                                                           Storyboard.TargetProperty="ScaleY">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="1.0" />
                                                <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                      KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                      Value="1.05" />
                                            </DoubleAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="TitleGridBackgroundImageTransform"
                                                                           Storyboard.TargetProperty="X">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:.50"
                                                                        Value="0" />
                                                <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                      KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                      Value="32" />
                                            </DoubleAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                           Storyboard.TargetProperty="Opacity">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="1.0" />
                                                <LinearDoubleKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                      Value="0.0" />
                                            </DoubleAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </VisualTransition>
                                    <VisualTransition To="DialogShowing">
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                           Storyboard.TargetProperty="Visibility">
                                                <DiscreteObjectKeyFrame KeyTime="0:0:0"
                                                                        Value="Visible" />
                                            </ObjectAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ScaleTransform"
                                                                           Storyboard.TargetProperty="ScaleX">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="1.05" />
                                                <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                      KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                      Value="1.0" />
                                            </DoubleAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ScaleTransform"
                                                                           Storyboard.TargetProperty="ScaleY">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="1.05" />
                                                <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                      KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                      Value="1.0" />
                                            </DoubleAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="TitleGridBackgroundImageTransform"
                                                                           Storyboard.TargetProperty="X">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="32" />
                                                <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                      KeyTime="0:0:.50"
                                                                      Value="0" />
                                            </DoubleAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                           Storyboard.TargetProperty="Opacity">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="0.0" />
                                                <LinearDoubleKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                      Value="1.0" />
                                            </DoubleAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </VisualTransition>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="DialogHidden" />
                                <VisualState x:Name="DialogShowing">
                                    <VisualState.Setters>
                                        <Setter Target="PrimaryButton.IsTabStop" Value="True" />
                                        <Setter Target="CloseButton.IsTabStop" Value="True" />
                                        <Setter Target="LayoutRoot.Visibility" Value="Visible" />
                                        <Setter Target="BackgroundElement.TabFocusNavigation" Value="Cycle" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="DialogShowingWithoutSmokeLayer">
                                    <VisualState.Setters>
                                        <Setter Target="PrimaryButton.IsTabStop" Value="True" />
                                        <Setter Target="CloseButton.IsTabStop" Value="True" />
                                        <Setter Target="LayoutRoot.Visibility" Value="Visible" />
                                        <Setter Target="LayoutRoot.Background" Value="{x:Null}" />
                                    </VisualState.Setters>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid x:Name="LayoutRoot"
                              Visibility="Visible">
                            <Rectangle x:Name="SmokeLayerBackground"
                                       Fill="{ThemeResource SmokeGridOverlayBackground}" />
                            <Grid x:Name="BackgroundElement"
                                  Margin="32"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  CornerRadius="{TemplateBinding CornerRadius}"
                                  RenderTransformOrigin="0.5,0.5">
                                <Grid.RenderTransform>
                                    <ScaleTransform x:Name="ScaleTransform" />
                                </Grid.RenderTransform>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Grid x:Name="TitleGrid"
                                      Height="48"
                                      MaxWidth="640"
                                      Margin="0,0,0,8"
                                      Padding="16,0"
                                      Background="{ThemeResource AcrylicTitleGridBackground}"
                                      CornerRadius="8"
                                      Shadow="{StaticResource SharedShadow}"
                                      Translation="0,0,32">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <Image x:Name="TitleGridBackgroundImage"
                                           Grid.Column="0"
                                           Grid.ColumnSpan="2"
                                           Height="Auto"
                                           Margin="0, 0, -16, 0"
                                           HorizontalAlignment="Center"
                                           Opacity="1"
                                           Source="/Assets/Images/GamePoster/headerposter_genshin.png"
                                           Stretch="UniformToFill">
                                        <Image.RenderTransform>
                                            <TranslateTransform x:Name="TitleGridBackgroundImageTransform" />
                                        </Image.RenderTransform>
                                    </Image>
                                    <FontIcon Grid.Column="0"
                                              Margin="0,0,8,0"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              Foreground="{ThemeResource TextFillColorInverseBrush}"
                                              Glyph="&#xe594;" />
                                    <TextBlock x:Name="TitleGridText"
                                               Grid.Column="1"
                                               VerticalAlignment="Center"
                                               FontSize="18"
                                               FontWeight="SemiBold"
                                               Foreground="{ThemeResource TextFillColorInverseBrush}" />
                                </Grid>
                                <Grid Grid.Row="1"
                                      HorizontalAlignment="Stretch"
                                      VerticalAlignment="Center"
                                      CornerRadius="8"
                                      Shadow="{StaticResource SharedShadow}"
                                      Translation="0,0,32">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="*" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid x:Name="InputGrid"
                                          MaxWidth="640"
                                          Padding="16,16"
                                          Background="{ThemeResource AcrylicInputGridBackground}"
                                          CornerRadius="8,8,0,0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <TextBox x:Name="FeedbackTitleInput"
                                                 HorizontalAlignment="Stretch"
                                                 HorizontalContentAlignment="Stretch"
                                                 FontSize="24"
                                                 IsReadOnly="{TemplateBinding IsTitleReadOnly}"
                                                 IsSpellCheckEnabled="False"
                                                 IsTextPredictionEnabled="False"
                                                 Margin="0,0,0,16"
                                                 Text="{TemplateBinding Title}">
                                            <TextBox.Header>
                                                <TextBlock FontSize="14"/>
                                            </TextBox.Header>
                                        </TextBox>
                                        <TextBox x:Name="FeedbackMessageInput"
                                                 Grid.Row="1"
                                                 MinHeight="150"
                                                 MaxHeight="270"
                                                 HorizontalAlignment="Stretch"
                                                 HorizontalContentAlignment="Stretch"
                                                 AcceptsReturn="True"
                                                 IsReadOnly="{TemplateBinding IsMessageReadOnly}"
                                                 IsSpellCheckEnabled="False"
                                                 IsTextPredictionEnabled="False"
                                                 ScrollViewer.VerticalScrollBarVisibility="Auto"
                                                 Text="{TemplateBinding Message}"
                                                 TextWrapping="Wrap">
                                            <TextBox.Header>
                                                <TextBlock/>
                                            </TextBox.Header>
                                        </TextBox>
                                    </Grid>
                                    <Grid x:Name="ConfirmationGrid"
                                          Grid.Row="1"
                                          MaxWidth="640"
                                          Padding="16,16"
                                          HorizontalAlignment="Stretch"
                                          Background="{ThemeResource AcrylicConfirmationGridBackground}"
                                          ColumnSpacing="8"
                                          CornerRadius="0, 0, 8, 8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <Grid ColumnSpacing="16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <TextBlock x:Name="FeedbackRatingText"
                                                       Grid.Column="0"
                                                       VerticalAlignment="Center"
                                                       FontSize="12"
                                                       FontWeight="Bold"
                                                       TextWrapping="Wrap" />
                                            <RatingControl x:Name="FeedbackRatingControl"
                                                           Grid.Column="1"
                                                           Margin="0,0,0,-4"
                                                           HorizontalAlignment="Left"
                                                           VerticalAlignment="Center" />
                                        </Grid>
                                        <Button x:Name="PrimaryButton"
                                                Grid.Column="2"
                                                CornerRadius="8"
                                                IsEnabled="False"
                                                Style="{ThemeResource AccentButtonStyle}">
                                            <Grid Margin="0,0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Margin="4,0,0,0"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           FontWeight="SemiBold" />
                                                <ProgressRing Grid.Column="1"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"
                                                              Margin="8,0,0,0"
                                                              Opacity="0"
                                                              Visibility="Collapsed"
                                                              Maximum="100"
                                                              Value="100"
                                                              Width="16"
                                                              Height="16"/>
                                                <Grid Grid.Column="1"
                                                      Margin="4,0,0,0"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      RenderTransformOrigin="0.5, 0.5">
                                                    <Grid.RenderTransform>
                                                        <RotateTransform Angle="-90" />
                                                    </Grid.RenderTransform>
                                                    <AnimatedIcon Width="16"
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center">
                                                        <animatedVisuals:AnimatedChevronDownSmallVisualSource />
                                                        <AnimatedIcon.FallbackIconSource>
                                                            <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                                            Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                                            Glyph="&#xE70D;" />
                                                        </AnimatedIcon.FallbackIconSource>
                                                    </AnimatedIcon>
                                                </Grid>
                                            </Grid>
                                        </Button>
                                        <Button x:Name="CloseButton"
                                                Grid.Column="1"
                                                CornerRadius="8"
                                                Style="{ThemeResource TransparentDefaultButtonStyle}">
                                            <TextBlock HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       FontWeight="SemiBold"
                                                       HorizontalTextAlignment="Left" />
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
