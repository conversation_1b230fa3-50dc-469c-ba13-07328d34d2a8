﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable IdentifierTypo  -->
<!--  <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Local  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<Page x:Class="CollapseLauncher.Pages.RepairPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:behaviors="using:CommunityToolkit.WinUI.Behaviors"
      xmlns:control="using:CommunityToolkit.WinUI.Controls"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:datatable="using:CommunityToolkit.WinUI.Controls.Labs.DataTable"
      xmlns:helper="using:Hi3Helper"
      xmlns:interactivity="using:Microsoft.Xaml.Interactivity"
      xmlns:local="using:CollapseLauncher"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      CacheMode="BitmapCache"
      Loaded="InitializeLoaded"
      NavigationCacheMode="Disabled"
      Unloaded="Page_Unloaded"
      mc:Ignorable="d">
    <Grid>
        <Grid x:Name="PageContent"
              Margin="32,40,32,18">
            <Grid.RowDefinitions>
                <RowDefinition Height="80" />
                <RowDefinition />
                <RowDefinition Height="124" />
            </Grid.RowDefinitions>
            <TextBlock Grid.Row="0"
                       Style="{ThemeResource TitleLargeTextBlockStyle}"
                       Text="{x:Bind helper:Locale.Lang._GameRepairPage.PageTitle}" />
            <Grid x:Name="RepairDataTableGrid"
                  Grid.Row="1"
                  VerticalAlignment="Stretch"
                  Visibility="Collapsed">
                <Grid.ChildrenTransitions>
                    <TransitionCollection>
                        <PopupThemeTransition />
                    </TransitionCollection>
                </Grid.ChildrenTransitions>
                <ListView ItemsSource="{x:Bind CurrentGameProperty.GameRepair.AssetEntry}">
                    <ListView.Header>
                        <Border Padding="0,8,0,0"
                                Background="{ThemeResource AcrylicBackgroundFillColorBaseBrush}"
                                CornerRadius="8">
                            <interactivity:Interaction.Behaviors>
                                <behaviors:StickyHeaderBehavior />
                            </interactivity:Interaction.Behaviors>
                            <datatable:DataTable Margin="12,0,0,8">
                                <datatable:DataColumn MinWidth="128"
                                                      CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._GameRepairPage.ListCol1}"
                                                      DesiredWidth="5*"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="FileName" />
                                <datatable:DataColumn CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._GameRepairPage.ListCol2}"
                                                      DesiredWidth="92"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="DataType" />
                                <datatable:DataColumn MinWidth="128"
                                                      CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._GameRepairPage.ListCol3}"
                                                      DesiredWidth="7*"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="Source" />
                                <datatable:DataColumn CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._GameRepairPage.ListCol4}"
                                                      DesiredWidth="92"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="FileSize" />
                                <datatable:DataColumn CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._GameRepairPage.ListCol5}"
                                                      DesiredWidth="92"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="CurCRC" />
                                <datatable:DataColumn CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._GameRepairPage.ListCol6}"
                                                      DesiredWidth="92"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="ExptCRC" />
                            </datatable:DataTable>
                        </Border>
                    </ListView.Header>
                    <ListView.ItemTemplate>
                        <DataTemplate x:DataType="local:IAssetProperty">
                            <datatable:DataRow HorizontalAlignment="Left">
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind Name}"
                                           TextTrimming="CharacterEllipsis" />
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind AssetTypeString}"
                                           TextTrimming="CharacterEllipsis" />
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind Source}"
                                           TextTrimming="CharacterEllipsis" />
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind SizeStr}"
                                           TextTrimming="CharacterEllipsis" />
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind LocalCRC}"
                                           TextTrimming="CharacterEllipsis" />
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind RemoteCRC}"
                                           TextTrimming="CharacterEllipsis" />
                            </datatable:DataRow>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                    <ListView.ItemContainerStyle>
                        <Style BasedOn="{StaticResource DefaultListViewItemStyle}"
                               TargetType="ListViewItem">
                            <Setter Property="HorizontalContentAlignment" Value="Left" />
                        </Style>
                    </ListView.ItemContainerStyle>
                </ListView>
            </Grid>
            <StackPanel Grid.Row="2"
                        VerticalAlignment="Bottom">
                <TextBlock x:Name="RepairStatus"
                           Margin="0,0,0,16"
                           FontSize="18"
                           Text="{x:Bind helper:Locale.Lang._GameRepairPage.Status1}"
                           TextTrimming="CharacterEllipsis" />
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="230" />
                        <ColumnDefinition />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock Margin="0,0,0,8"
                                   FontSize="16"
                                   FontWeight="Medium"
                                   Text="{x:Bind helper:Locale.Lang._GameRepairPage.PerProgressTitle1}" />
                        <control:DockPanel HorizontalAlignment="Stretch"
                                           LastChildFill="False">
                            <TextBlock x:Name="RepairPerFileStatus"
                                       control:DockPanel.Dock="Left"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._GameRepairPage.StatusNone}" />
                            <TextBlock control:DockPanel.Dock="Right"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="%" />
                            <TextBlock control:DockPanel.Dock="Right"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="{x:Bind RepairPerFileProgressBar.Value, Mode=OneWay}" />
                        </control:DockPanel>
                        <ProgressBar x:Name="RepairPerFileProgressBar"
                                     Height="25"
                                     Maximum="100"
                                     Value="0" />
                    </StackPanel>
                    <StackPanel Grid.Column="1"
                                Margin="32,0">
                        <TextBlock Margin="0,0,0,8"
                                   FontSize="16"
                                   FontWeight="Medium"
                                   Text="{x:Bind helper:Locale.Lang._GameRepairPage.TotalProgressTitle1}" />
                        <control:DockPanel HorizontalAlignment="Stretch"
                                           LastChildFill="False">
                            <TextBlock x:Name="RepairTotalStatus"
                                       control:DockPanel.Dock="Left"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._GameRepairPage.StatusNone}" />
                            <TextBlock control:DockPanel.Dock="Right"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="%" />
                            <TextBlock control:DockPanel.Dock="Right"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="{x:Bind RepairTotalProgressBar.Value, Mode=OneWay}" />
                        </control:DockPanel>
                        <ProgressBar x:Name="RepairTotalProgressBar"
                                     Height="25"
                                     Maximum="100"
                                     Value="0" />
                    </StackPanel>
                    <Grid Grid.Row="0"
                          Grid.Column="2"
                          Margin="0,0,0,10"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Bottom">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Button x:Name="RepairFilesBtn"
                                HorizontalAlignment="Stretch"
                                Click="StartGameRepair"
                                CornerRadius="16"
                                Style="{ThemeResource AccentButtonStyle}"
                                Visibility="Collapsed">
                            <StackPanel Orientation="Horizontal">
                                <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                          FontSize="16"
                                          Glyph="&#xf6e3;" />
                                <TextBlock Margin="8,0"
                                           FontWeight="SemiBold"
                                           Text="{x:Bind helper:Locale.Lang._GameRepairPage.RepairBtn1}" />
                            </StackPanel>
                        </Button>
                        <SplitButton x:Name="CheckFilesBtn"
                                     Grid.Column="0"
                                     HorizontalAlignment="Stretch"
                                     Click="StartGameCheckSplitButton"
                                     CornerRadius="16"
                                     Tag="Fast"
                                     ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._GameRepairPage.RepairBtn2QuickDesc}">
                            <StackPanel Orientation="Horizontal"
                                        Spacing="8">
                                <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                          FontSize="16"
                                          Glyph="&#xf002;" />
                                <TextBlock FontWeight="SemiBold"
                                           Text="{x:Bind helper:Locale.Lang._GameRepairPage.RepairBtn2Quick}" />
                            </StackPanel>
                            <SplitButton.Flyout>
                                <Flyout Placement="Top">
                                    <StackPanel Margin="-8"
                                                Spacing="8">
                                        <Button x:Name="CheckFilesQuickBtn"
                                                HorizontalAlignment="Stretch"
                                                HorizontalContentAlignment="Stretch"
                                                Click="StartGameCheck"
                                                Tag="Fast"
                                                ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._GameRepairPage.RepairBtn2QuickDesc}">
                                            <Grid ColumnSpacing="12">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                <FontIcon Grid.Column="1"
                                                          HorizontalAlignment="Right"
                                                          FontFamily="{ThemeResource FontAwesomeSolid}"
                                                          FontSize="16"
                                                          Glyph="&#xf101;" />
                                                <TextBlock Grid.Column="0"
                                                           HorizontalAlignment="Left"
                                                           FontWeight="SemiBold"
                                                           Text="{x:Bind helper:Locale.Lang._GameRepairPage.RepairBtn2Quick}" />
                                            </Grid>
                                        </Button>
                                        <Button x:Name="CheckFilesFullBtn"
                                                HorizontalAlignment="Stretch"
                                                HorizontalContentAlignment="Stretch"
                                                Click="StartGameCheck"
                                                Style="{ThemeResource AccentButtonStyle}"
                                                Tag="Full"
                                                ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._GameRepairPage.RepairBtn2FullDesc}">
                                            <Grid ColumnSpacing="12">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                <FontIcon Grid.Column="1"
                                                          HorizontalAlignment="Right"
                                                          FontFamily="{ThemeResource FontAwesomeSolid}"
                                                          FontSize="16"
                                                          Glyph="&#xf105;" />
                                                <TextBlock Grid.Column="0"
                                                           HorizontalAlignment="Left"
                                                           FontWeight="SemiBold"
                                                           Text="{x:Bind helper:Locale.Lang._GameRepairPage.RepairBtn2Full}" />
                                            </Grid>
                                        </Button>
                                    </StackPanel>
                                </Flyout>
                            </SplitButton.Flyout>
                        </SplitButton>
                        <Button x:Name="CancelBtn"
                                Grid.Column="1"
                                Margin="16,0,0,0"
                                HorizontalAlignment="Stretch"
                                Click="CancelOperation"
                                CornerRadius="16"
                                IsEnabled="False">
                            <TextBlock Margin="8,0"
                                       FontWeight="Medium"
                                       Text="{x:Bind helper:Locale.Lang._Misc.Cancel}" />
                        </Button>
                    </Grid>
                    <StackPanel Grid.Column="0"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal" />
                </Grid>
            </StackPanel>
        </Grid>
        <Grid x:Name="Overlay"
              Visibility="Collapsed">
            <StackPanel Margin="0,176,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Vertical">
                <ProgressRing x:Name="Ring"
                              Width="48"
                              Height="48"
                              Margin="32"
                              IsActive="True"
                              IsIndeterminate="false"
                              Maximum="100"
                              Value="100" />
                <TextBlock x:Name="OverlayTitle"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Style="{ThemeResource SubtitleTextBlockStyle}"
                           Text="Title" />
                <TextBlock x:Name="OverlaySubtitle"
                           Margin="0,8,0,192"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                           Text="Subtitle" />
            </StackPanel>
        </Grid>
    </Grid>
</Page>
