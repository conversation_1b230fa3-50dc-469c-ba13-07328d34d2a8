﻿<ContentPage
    x:Class="H.NotifyIcon.Apps.Maui.MainPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:tb="https://notifyicon.com/"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:H.NotifyIcon.Apps.Maui"
    x:DataType="pages:MainPage"
    >

    <Grid>
        <tb:TaskbarIcon
            IconSource="red.ico"
            x:Name="TrayIcon"
            LeftClickCommand="{Binding ShowHideWindowCommand}"
            NoLeftClickDelay="True"
            >
            <FlyoutBase.ContextFlyout>
                <MenuFlyout>
                    <MenuFlyoutItem Command="{Binding ShowHideWindowCommand}" Text="Show/Hide Window" />
                    <MenuFlyoutSeparator />
                    <MenuFlyoutItem Command="{Binding ExitApplicationCommand}" Text="Exit" />
                </MenuFlyout>
            </FlyoutBase.ContextFlyout>
        </tb:TaskbarIcon>
    </Grid>
    <!-- <ScrollView> -->
    <!--     <skia:SKCanvasView x:Name="CanvasView" /> -->
    <!-- </ScrollView> -->

</ContentPage>
