﻿<Page x:Class="CollapseLauncher.DisconnectedPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:animatedvisuals="using:Microsoft.UI.Xaml.Controls.AnimatedVisuals"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:helper="using:Hi3Helper"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:uielementex="using:CollapseLauncher.Extension"
      Background="Transparent"
      mc:Ignorable="d">
    <Grid Margin="32">
        <Grid x:Name="FrontGrid"
              Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="0.6*" />
            </Grid.ColumnDefinitions>
            <StackPanel Margin="0,-32,0,0"
                        VerticalAlignment="Center">
                <TextBlock FontSize="56"
                           FontWeight="Normal"
                           Text="{x:Bind helper:Locale.Lang._DisconnectedPage.Header1}" />
                <TextBlock Margin="0,-24,0,0"
                           FontSize="72"
                           FontWeight="Bold"
                           Text="{x:Bind helper:Locale.Lang._DisconnectedPage.Header2}" />
                <!--  ReSharper disable once UnusedMember.Local  -->
                <Button x:Name="ShowErrorBtn"
                        Margin="0,16,0,0"
                        Padding="8,8"
                        HorizontalAlignment="Stretch"
                        HorizontalContentAlignment="Stretch"
                        Click="ShowError"
                        CornerRadius="{x:Bind uielementex:UIElementExtensions.AttachRoundedKindCornerRadius(ShowErrorBtn)}"
                        Style="{ThemeResource AccentButtonStyle}">
                    <Grid Margin="8,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Margin="0,-2,16,0"
                                   HorizontalAlignment="Left"
                                   VerticalAlignment="Center"
                                   FontSize="20"
                                   FontWeight="Medium"
                                   HorizontalTextAlignment="Left"
                                   Text="{x:Bind helper:Locale.Lang._DisconnectedPage.ShowErrorBtn}" />
                        <Grid Grid.Column="1"
                              Margin="4,0,0,0"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              RenderTransformOrigin="0.5, 0.5">
                            <Grid.RenderTransform>
                                <RotateTransform Angle="-90" />
                            </Grid.RenderTransform>
                            <AnimatedIcon Width="24"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center">
                                <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                            </AnimatedIcon>
                        </Grid>
                    </Grid>
                </Button>
                <!--  ReSharper disable once UnusedMember.Local  -->
                <Button x:Name="GoToAppSettingsBtn"
                        Margin="0,8,0,0"
                        Padding="8,8"
                        HorizontalAlignment="Stretch"
                        HorizontalContentAlignment="Stretch"
                        Click="GoToAppSettings"
                        CornerRadius="{x:Bind uielementex:UIElementExtensions.AttachRoundedKindCornerRadius(GoToAppSettingsBtn)}"
                        Style="{ThemeResource DefaultButtonStyle}">
                    <Grid Margin="8,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Margin="0,-2,16,0"
                                   HorizontalAlignment="Left"
                                   VerticalAlignment="Center"
                                   FontSize="20"
                                   FontWeight="Medium"
                                   HorizontalTextAlignment="Left"
                                   Text="{x:Bind helper:Locale.Lang._DisconnectedPage.GoToAppSettingsBtn}" />
                        <Grid Grid.Column="1"
                              Margin="4,0,0,0"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              RenderTransformOrigin="0.5, 0.5">
                            <Grid.RenderTransform>
                                <RotateTransform Angle="-90" />
                            </Grid.RenderTransform>
                            <AnimatedIcon Width="24"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center">
                                <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                            </AnimatedIcon>
                        </Grid>
                    </Grid>
                </Button>
                <TextBlock Margin="0,16,0,0"
                           FontSize="20"
                           Style="{ThemeResource TitleTextBlockStyle}"
                           Text="{x:Bind helper:Locale.Lang._DisconnectedPage.RegionChangerTitle}" />
                <TextBlock Margin="0,0,0,16"
                           Text="{x:Bind helper:Locale.Lang._DisconnectedPage.RegionChangerSubtitle}" />
                <!--  ReSharper disable once UnusedMember.Local  -->
                <Grid x:Name="ComboBoxGameGrid"
                      CornerRadius="{x:Bind uielementex:UIElementExtensions.AttachRoundedKindCornerRadius(ComboBoxGameGrid)}">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <ComboBox x:Name="ComboBoxGameCategory"
                              HorizontalAlignment="Stretch"
                              Canvas.ZIndex="2"
                              CornerRadius="15,0,0,15"
                              PlaceholderText="{x:Bind helper:Locale.Lang._GameClientTitles['Honkai Impact 3rd']}"
                              SelectionChanged="SetGameCategoryChange"
                              Style="{ThemeResource DefaultComboBoxStyle}" />
                    <ComboBox x:Name="ComboBoxGameRegion"
                              Grid.Column="1"
                              HorizontalAlignment="Stretch"
                              Canvas.ZIndex="2"
                              CornerRadius="0,15,15,0"
                              PlaceholderText="{x:Bind helper:Locale.Lang._GameClientRegions['Southeast Asia']}"
                              SelectionChanged="SetGameRegionChange"
                              Style="{ThemeResource DefaultComboBoxStyle}" />
                </Grid>
            </StackPanel>
            <TextBlock Grid.Column="0"
                       VerticalAlignment="Bottom"
                       FontSize="16"
                       Opacity="0.75">
                <Run Text="{x:Bind helper:Locale.Lang._DisconnectedPage.Footer1}" />
                <Run FontWeight="Bold"
                     Foreground="{ThemeResource AccentColor}"
                     Text="{x:Bind helper:Locale.Lang._DisconnectedPage.Footer2}" />
                <Run Text="{x:Bind helper:Locale.Lang._DisconnectedPage.Footer3}" />
            </TextBlock>
            <Image Grid.Column="1"
                   Margin="32,0,0,0"
                   HorizontalAlignment="Right"
                   VerticalAlignment="Bottom"
                   PointerPressed="PaimonClicked"
                   Source="ms-appx:///Assets/Images/GameMascot/PaimonSleep-MonoTransparent.png" />
        </Grid>
        <Grid x:Name="OverlayFrameBg"
              Margin="-32"
              Visibility="Collapsed" />
        <Grid Margin="-32">
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Frame x:Name="OverlayFrame"
                   CornerRadius="16" />
            <Button x:Name="GoBackOverlayFrame"
                    Grid.Row="1"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Bottom"
                    HorizontalContentAlignment="Left"
                    BorderThickness="0"
                    Click="GoBackFromOverlayFrame"
                    CornerRadius="0"
                    Style="{ThemeResource NewAccentButtonStyle}"
                    Visibility="Collapsed">
                <Grid Margin="0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <Grid HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          RenderTransformOrigin="0.5, 0.5">
                        <AnimatedIcon Width="24"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center">
                            <animatedvisuals:AnimatedBackVisualSource />
                        </AnimatedIcon>
                    </Grid>
                    <TextBlock Grid.Column="1"
                               Margin="8,-2,8,0"
                               HorizontalAlignment="Left"
                               VerticalAlignment="Center"
                               FontSize="14"
                               FontWeight="Medium"
                               HorizontalTextAlignment="Left"
                               Text="{x:Bind helper:Locale.Lang._DisconnectedPage.GoBackOverlayFrameBtn}" />
                </Grid>
            </Button>
        </Grid>
    </Grid>
</Page>
