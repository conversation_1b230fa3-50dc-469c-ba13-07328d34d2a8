<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="VcsDirectoryMappings">
    <mapping directory="" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ColorThief" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/H.NotifyIcon" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/Hi3Helper.EncTool" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/Hi3Helper.EncTool/Streams/CacheStream" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/Hi3Helper.EncTool/UABT" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/Hi3Helper.Http" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/Hi3Helper.SharpDiscordRPC" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/Hi3Helper.Sophon" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/Hi3Helper.Win32" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ImageEx" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/InnoSetupHelper/InnoSetupLogParser" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/SevenZipExtractor" vcs="Git" />
  </component>
</project>