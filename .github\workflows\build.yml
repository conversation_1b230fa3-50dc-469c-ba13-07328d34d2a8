# Credits to @<PERSON><PERSON><PERSON> from <PERSON><PERSON> for his contributions!
name: Build-Canary
#run-name: Canary Build for ${{ github.ref }}

on:
  workflow_dispatch:
  push:
    branches:
        - 'main'
        - '1.83.x-staging'
    paths-ignore:
        - '**.md'
        - 'Hi3Helper.Core/Lang/**.json'
        - 'Docs/**'
        - '**/packages.lock.json'
  pull_request:
    branches-ignore:
      - 'stable'
      - 'preview'
      - 'translations_**'
    paths-ignore:
        - '**.md'
        - 'Hi3Helper.Core/Lang/**.json'
        - 'Docs/**'
        - '**/packages.lock.json'

env:
  DOTNET_INSTALL_DIR: '.\.dotnet' 
  DOTNET_VERSION: '9.0.3xx'
  DOTNET_QUALITY: 'ga'
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages

jobs:
  build:
    runs-on: windows-latest
    strategy:
      matrix:
        configuration: [Debug] # No need to distribute Debug builds
        platform: [x64]
        framework: [net9.0-windows10.0.26100.0]

    env:
      Configuration: ${{ matrix.configuration }}
      Platform: ${{ matrix.platform }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Install .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        dotnet-quality: ${{ env.DOTNET_QUALITY }}
        cache: true
        cache-dependency-path: CollapseLauncher/packages.lock.json

    - name: Update .NET workload
      run: dotnet workload update

    - name: Build
      run: |
        dotnet publish CollapseLauncher -c ${{matrix.Configuration}} -p:PublishProfile=Publish-DebugCIRelease -p:PublishDir=".\debug-build\"
          
    - name: Upload Artifact
      uses: actions/upload-artifact@v4
      with:
          name: collapse_${{ matrix.platform }}-${{ matrix.configuration }}_${{ matrix.framework }}_${{ github.sha }}
          path: ./CollapseLauncher/debug-build/
          compression-level: 9

  build-nativeaot:
    runs-on: windows-latest
    strategy:
      matrix:
        configuration: [Debug]
        platform: [x64]
        framework: [net9.0-windows10.0.26100.0]

    env:
      Configuration: ${{ matrix.configuration }}
      Platform: ${{ matrix.platform }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Install .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        dotnet-quality: ${{ env.DOTNET_QUALITY }}
        cache: true
        cache-dependency-path: CollapseLauncher/packages.lock.json

    - name: Update .NET workload
      run: dotnet workload update
        
    - name: Build
      run: |
        dotnet publish CollapseLauncher -c ${{matrix.Configuration}} -p:PublishProfile=Publish-DebugCIReleaseAOT -p:PublishDir=".\debug-aot-build\"
    
    - name: Upload debug symbols
      uses: actions/upload-artifact@v4
      with:
        name: aot-experimental-symbols_collapse_${{ matrix.platform }}-${{ matrix.configuration }}_${{ matrix.framework }}_${{ github.sha }}
        path: ./CollapseLauncher/debug-aot-build/**/*.pdb
        compression-level: 9
      
    - name: Remove debug symbols
      run: |
        Remove-Item -Path "./CollapseLauncher/debug-aot-build/*.pdb" -Recurse -Force

    - name: Upload Artifact
      uses: actions/upload-artifact@v4
      with:
            name: aot-experimental_collapse_${{ matrix.platform }}-${{ matrix.configuration }}_${{ matrix.framework }}_${{ github.sha }}
            path: ./CollapseLauncher/debug-aot-build/
            compression-level: 9
        
  notify-discord:
    runs-on: ubuntu-latest
    if: always()
    needs: [build, build-nativeaot]
    steps:
    - name: Notify Discord
      uses: sarisia/actions-status-discord@v1.15.1
      if: always()
      continue-on-error: true
      with:
        webhook: ${{ secrets.DISCORD_WEBHOOK_NIGHTLY }}
        title: Collapse Launcher CI build is complete!
        status: ${{ job.status }}
        description: |
          Commit `${{ github.sha }}` by ${{ github.actor }}
          Click [here](https://nightly.link/CollapseLauncher/Collapse/actions/runs/${{ github.run_id }}) to download!
