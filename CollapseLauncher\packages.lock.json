{"version": 1, "dependencies": {"net9.0-windows10.0.26100": {"CommunityToolkit.Common": {"type": "Direct", "requested": "[8.4.0, )", "resolved": "8.4.0", "contentHash": "yqU6s4sLjaMuJ0OS6MSpOLkdbLS8HhyD0RSMQ/+/AyAjXizLMdXnlImgVBXPEGmBsNKDjApGF7uFmSh/xhie7A=="}, "CommunityToolkit.Mvvm": {"type": "Direct", "requested": "[8.4.0, )", "resolved": "8.4.0", "contentHash": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw=="}, "CommunityToolkit.WinUI.Behaviors": {"type": "Direct", "requested": "[8.2.250402, )", "resolved": "8.2.250402", "contentHash": "Bb0uLjMJrLyNDaARHnlFGjTw7T/y0NkL2VTJWY5iWVw8NWCoBxFJCs0KQ258I/PRth5DG582Zy2CDn6HTkUliw==", "dependencies": {"CommunityToolkit.WinUI.Animations": "8.2.250402", "CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002", "Microsoft.Xaml.Behaviors.WinUI.Managed": "3.0.0"}}, "CommunityToolkit.WinUI.Controls.Primitives": {"type": "Direct", "requested": "[8.2.250402, )", "resolved": "8.2.250402", "contentHash": "Wx3t1zADrzBWDar45uRl+lmSxDO5Vx7tTMFm/mNgl3fs5xSQ1ySPdGqD10EFov3rkKc5fbpHGW5xj8t62Yisvg==", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}}, "CommunityToolkit.WinUI.Controls.Sizers": {"type": "Direct", "requested": "[8.2.250402, )", "resolved": "8.2.250402", "contentHash": "8kWJX+TxUyYbLFPpzoivtYPnSI9kgBGL1dfdsteQSw6Yq69v7mv/Z3emTLHqBJXOi9WKCiq2HNhKXuCH18/ctA==", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}}, "CommunityToolkit.WinUI.Converters": {"type": "Direct", "requested": "[8.2.250402, )", "resolved": "8.2.250402", "contentHash": "fLWJmy2jBO0ECYmu4eWJzjPp1Drww3XcARdig8HQXSMZkg9k857g1lQQzJr3MVPaLeMPDqeKONtsc0kxBLLMCw==", "dependencies": {"CommunityToolkit.Common": "8.2.1", "Microsoft.WindowsAppSDK": "1.6.250108002"}}, "CommunityToolkit.WinUI.Extensions": {"type": "Direct", "requested": "[8.2.250402, )", "resolved": "8.2.250402", "contentHash": "rAOYzNX6kdUeeE1ejGd6Q8B+xmyZvOrWFUbqCgOtP8OQsOL66en9ZQTtzxAlaaFC4qleLvnKcn8FJFBezujOlw==", "dependencies": {"CommunityToolkit.Common": "8.2.1", "Microsoft.WindowsAppSDK": "1.6.250108002"}}, "CommunityToolkit.WinUI.Media": {"type": "Direct", "requested": "[8.2.250402, )", "resolved": "8.2.250402", "contentHash": "/FB24b8g65fp4p9/IdBROgByqxavIbdJt7u8klfbSv8ABV/SqTjR2yduvCXdyVdYxWyyT+ecMf3Gmt8HOGkU2A==", "dependencies": {"CommunityToolkit.WinUI.Animations": "8.2.250402", "CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.Graphics.Win2D": "1.3.1", "Microsoft.WindowsAppSDK": "1.6.250108002"}}, "GitInfo": {"type": "Direct", "requested": "[3.5.0, )", "resolved": "3.5.0", "contentHash": "w7pFvv7PutCdK126BSq9O+wXfiyXQ68c2BeZnd9AHAbkV+IlTCF47LCZHNCqNchWMEv+GBbWGHCcGzZT411tIw==", "dependencies": {"ThisAssembly.Constants": "2.0.6"}}, "HtmlAgilityPack": {"type": "Direct", "requested": "[1.12.1, )", "resolved": "1.12.1", "contentHash": "SP6/2Y26CXtxjXn0Wwsom9Ek35SNWKHEu/IWhNEFejBSSVWWXPRSlpqpBSYWv1SQhYFnwMO01xVbEdK3iRR4hg=="}, "Libsql.Client": {"type": "Direct", "requested": "[0.5.0, )", "resolved": "0.5.0", "contentHash": "5Tw6O9sBDAN1aV+kpOSVvqvFk6Ahk6bYz0TTx3808Dp40M45gKTtzTzI9SS1VeAkljE6BAOeKaykpPnG36oOgw=="}, "Markdig.Signed": {"type": "Direct", "requested": "[0.41.2, )", "resolved": "0.41.2", "contentHash": "oso8cno80OzCgAQOygtJXT1UhlVr2eSjwzXLqSLHyfKmSJ+3OcwDbrpzNwMj3/1SgncvQxP7OsNQ3bRfE3HvjQ=="}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw=="}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}}, "Microsoft.Graphics.Win2D": {"type": "Direct", "requested": "[1.3.2, )", "resolved": "1.3.2", "contentHash": "xAv4+Xj8wePW1innIUSXebX6UHMGDzOxwKPwYNUNY7mBrsRL/hPFEtuPLpBSxc3iBnuaCogPDCDeN7T4b1xV0Q==", "dependencies": {"Microsoft.WindowsAppSDK": "1.6.241114003"}}, "Microsoft.NET.ILLink.Tasks": {"type": "Direct", "requested": "[9.0.6, )", "resolved": "9.0.6", "contentHash": "TXy3SbJzGXQbxxIxCjdrp8bwEyTDImyYNpTpd6v7P3JL2Y7dno8EYG7dPezfYTa5SoWKdhbH9cbnwHHs3BR5gA=="}, "Microsoft.NETCore.Targets": {"type": "Direct", "requested": "[6.0.0-preview.4.21253.7, )", "resolved": "6.0.0-preview.4.21253.7", "contentHash": "ipvxo/6FRdrmcsAtCGI9QPeaZZSyeCk9LiSVIJ4AN36IZQG7KFtk9ZbbYgvy4wzCzHkbBmTWaf2ESBqrYEtCRg=="}, "Microsoft.Web.WebView2": {"type": "Direct", "requested": "[1.0.3344-prerelease, )", "resolved": "1.0.3344-prerelease", "contentHash": "I1n0XLQuJcVOrTPLl0pYARi57q+AtVswrCHHHaB0ROvmxn3IvYQg+r++IYUpJnzucwEyDNWqn/ZLoxTeD6sjAA=="}, "Microsoft.Windows.CsWinRT": {"type": "Direct", "requested": "[2.2.0, )", "resolved": "2.2.0", "contentHash": "R8MDVawukHrKgYCFgYaoLfbAna/FrImEFRxDK9k8ITbrXv3KLE5uN6tRqrNV83H4MFdqJ/uiA/hg1cSsXkl0vw=="}, "Microsoft.Windows.SDK.BuildTools": {"type": "Direct", "requested": "[10.0.26100.4188, )", "resolved": "10.0.26100.4188", "contentHash": "sjksqXKnxleoY9qvKPS/fsiGKZ8ZYjwDTfSQvfv6gETTtQQIqZRIhCZCZ+rgjFjS6ZVjjn7iRjT/X64v25IEyA=="}, "Microsoft.WindowsAppSDK": {"type": "Direct", "requested": "[1.8.250515001-experimental2, )", "resolved": "1.8.250515001-experimental2", "contentHash": "S2Eu7Gn889hatfHfMdPoWULJ88/3oxuzSOAEZd1zgp2i3YbL1MxBeOd3YQyZHqOMjdJN/MSUziDjmMcEsze8Pg==", "dependencies": {"Microsoft.WindowsAppSDK.AI": "[1.8.135-experimental]", "Microsoft.WindowsAppSDK.Base": "[1.8.250509001-experimental]", "Microsoft.WindowsAppSDK.DWrite": "[1.8.25050910-experimental.2]", "Microsoft.WindowsAppSDK.Foundation": "[1.8.250507001-experimental]", "Microsoft.WindowsAppSDK.InteractiveExperiences": "[1.8.250509002-experimental]", "Microsoft.WindowsAppSDK.Packages": "[1.8.250515001-experimental2]", "Microsoft.WindowsAppSDK.Widgets": "[1.8.250505003-experimental]", "Microsoft.WindowsAppSDK.WinUI": "[1.8.250507002-experimental]"}}, "Microsoft.Xaml.Behaviors.WinUI.Managed": {"type": "Direct", "requested": "[3.0.0, )", "resolved": "3.0.0", "contentHash": "IoeGOYbOvhnNlzYs79z2TB1EFVldlQKJHH1gjUMtrViu+MJZfnkyNeCiK1IBVo8zP86VuFPZ4Yg6Vm4mYJ8tGA==", "dependencies": {"Microsoft.WindowsAppSDK": "1.6.250108002"}}, "PhotoSauce.MagicScaler": {"type": "Direct", "requested": "[0.15.0, )", "resolved": "0.15.0", "contentHash": "ajPOdS5wGIZrHGT0NHvmmrQ7sJb0Vp+Ex3JauD7Ru+U+F+N7tTYRVDcvXW2ER56MLzC+VSqK3KjRn4Sntkf3vA=="}, "PhotoSauce.NativeCodecs.Libwebp": {"type": "Direct", "requested": "[*-*, )", "resolved": "1.4.0-preview1", "contentHash": "0AyuhMpynCdCq84dh0wU/Kjuxzo3GkYny5Z1l2JlWTHD2muUlE3Akqa1CtEPj0lif9o/KHnYwhmIWnP0ZctF4g==", "dependencies": {"PhotoSauce.MagicScaler": "[0.15.0]"}}, "Roman-Numerals": {"type": "Direct", "requested": "[2.0.1, )", "resolved": "2.0.1", "contentHash": "Xvhd0IPCKhdYppa9RhDr4fkwhoMMWfahwCfyVtRgt6TRlbUw0gOmSjJR/+L+/s0QNhLTK93C+2pSlrkDgsoY2w=="}, "SharpCompress": {"type": "Direct", "requested": "[0.40.0, )", "resolved": "0.40.0", "contentHash": "yP/aFX1jqGikVF7u2f05VEaWN4aCaKNLxSas82UgA2GGVECxq/BcqZx3STHCJ78qilo1azEOk1XpBglIuGMb7w==", "dependencies": {"System.Buffers": "4.6.0", "ZstdSharp.Port": "0.8.5"}}, "System.CommandLine": {"type": "Direct", "requested": "[2.0.0-beta4.22272.1, )", "resolved": "2.0.0-beta4.22272.1", "contentHash": "1uqED/q2H0kKoLJ4+hI2iPSBSEdTuhfCYADeJrAqERmiGQ2NNacYKRNEQ+gFbU4glgVyK8rxI+ZOe1onEtr/Pg=="}, "System.CommandLine.NamingConventionBinder": {"type": "Direct", "requested": "[2.0.0-beta4.22272.1, )", "resolved": "2.0.0-beta4.22272.1", "contentHash": "ux2eUA/syF+JtlpMDc/Lsd6PBIBuwjH3AvHnestoh5uD0WKT5b+wkQxDWVCqp9qgVjMBTLNhX19ZYFtenunt9A==", "dependencies": {"System.CommandLine": "2.0.0-beta4.22272.1"}}, "System.Security.AccessControl": {"type": "Direct", "requested": "[6.0.1, )", "resolved": "6.0.1", "contentHash": "IQ4NXP/B3Ayzvw0rDQzVTYsCKyy0Jp9KI6aYcK7UnGVlR9+Awz++TIPCQtPYfLJfOpm8ajowMR09V7quD3sEHw=="}, "System.Security.Cryptography.ProtectedData": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "tgS+q4zR08qX4ZA/HPtpNXU2i1EDRBNhNtbTGFdSgYyWcKf5mUtlihbzKcYqArisG1b3x4dk/FIOiWxnHrSENw=="}, "System.Text.Encoding.CodePages": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "P6gqCJcVjdWA50Eai95eYdG4ryrb1xCd770NhQrvj847C7m30CJwQqzKykWoEMvV4qvjUazL/m7UMQdMZTp5xw=="}, "System.Text.Json": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "rnP61ZfloTgPQPe7ecr36loNiGX3g1PocxlKHdY/FUpDSsExKkTxpMAlB4X35wNEPr1X7mkYZuQvW3Lhxmu7KA=="}, "TurnerSoftware.DinoDNS": {"type": "Direct", "requested": "[0.3.0, )", "resolved": "0.3.0", "contentHash": "4tYkD4ENlwozKrNuNXSCjnqKVX5vQjPvxbdSgN37KeGX+x8AT/u6QK3tToO/uxv42c/cPp8n3a8BRrPZWX7BCw=="}, "Velopack": {"type": "Direct", "requested": "[0.0.1297, )", "resolved": "0.0.1297", "contentHash": "FfZ3B+U97TSZ4Ibcvve6TqJvSYp7jOAPKFUTG69FYb2MjgTeHzfd9j1/jgHRhDDlldpPnXM6iExuihrSKQRiNA==", "dependencies": {"NuGet.Versioning": "6.14.0"}}, "CommunityToolkit.WinUI.Animations": {"type": "Transitive", "resolved": "8.2.250402", "contentHash": "KMKFGu2BD9qUBJO4FAAmUy0fdLB8/DXPcW1ZhO8k6agZtNnsKqc0yAz5OX9Dt1dvK38uHYGd5z9yQkwq+DAqUg==", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}}, "CommunityToolkit.WinUI.Helpers": {"type": "Transitive", "resolved": "8.2.250402", "contentHash": "DThBXB4hT3/aJ7xFKQJw/C0ZEs1QhZL7QG6AFOYcpnGWNlv3tkF761PFtTyhpNQrR1AFfzml5zG+zWfFbKs6Mw==", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}}, "CommunityToolkit.WinUI.Triggers": {"type": "Transitive", "resolved": "8.2.250402", "contentHash": "laHIrBQkwQCurTNSQdGdEXUyoCpqY8QFXSybDM/Q1Ti/23xL+sRX/gHe3pP8uMM59bcwYbYlMRCbIaLnxnrdjw==", "dependencies": {"CommunityToolkit.WinUI.Helpers": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}}, "Google.Protobuf": {"type": "Transitive", "resolved": "3.31.1", "contentHash": "gSnJbUmGiOTdWddPhqzrEscHq9Ls6sqRDPB9WptckyjTUyx70JOOAaDLkFff8gManZNN3hllQ4aQInnQyq/Z/A=="}, "Hi3Helper.ZstdNet": {"type": "Transitive", "resolved": "1.6.4", "contentHash": "/0HO97NquyAeu67BznsPG3Bitb2RweDlRd39Dm0N70pIb6ETjsQI/ysnwGrygxrPwwE25oDNoueAaJfOZTrACg=="}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA=="}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ=="}, "Microsoft.Win32.SystemEvents": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "D4OYNpmvAsF9MkaY2W8Jue2XuNHDhygvwzo019hs+lP85KaVnOlXmqsjDKr1dHb1DPxDnOKpe6mAgJN7S6ttwg=="}, "Microsoft.Windows.SDK.BuildTools.MSIX": {"type": "Transitive", "resolved": "1.7.20250508.1", "contentHash": "omgDNQElO0w0wY20I7XLGHISyn7Z4quh9Pc+Gp9XkRHJgiks7ARDhq4Vu+DK32bDoCBrFe1CLK4ovete4QiuVw=="}, "Microsoft.Windows.SDK.Win32Metadata": {"type": "Transitive", "resolved": "63.0.31-preview", "contentHash": "ImR/LEJIcTiQuwYognFXPED1+zRjlb4WYX5mC3E8d0Q09Zey+MMqfMDxNlX1vBbcP3dy/Fp0Lbav25FsXZwyYQ=="}, "Microsoft.Windows.WDK.Win32Metadata": {"type": "Transitive", "resolved": "0.13.25-experimental", "contentHash": "IM50tb/+UIwBr9FMr6ZKcZjCMW+Axo6NjGqKxgjUfyCY8dRnYUfrJEXxAaXoWtYP4X8EmASmC1Jtwh4XucseZg==", "dependencies": {"Microsoft.Windows.SDK.Win32Metadata": "63.0.31-preview"}}, "Microsoft.WindowsAppSDK.AI": {"type": "Transitive", "resolved": "1.8.135-experimental", "contentHash": "era1Dvr3kfOqdwRuS4hzHp/29WqUCDI5FXNoLbVj8utTWlek0kMld2RvmhFLS/WEe7BKtj+ie/n5Zk/jRm/O6g==", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250501001-experimental", "Microsoft.WindowsAppSDK.Foundation": "1.8.250504002-experimental"}}, "Microsoft.WindowsAppSDK.Base": {"type": "Transitive", "resolved": "1.8.250509001-experimental", "contentHash": "kw60pLocD7M09t5bOxcx6juKdk2e5xdSO5cGCaedcNgD+e1h92vUFT9WU1xCzc0sXIt2x1FL426T606PjoXXUQ==", "dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.22621.3233", "Microsoft.Windows.SDK.BuildTools.MSIX": "1.7.20250508.1"}}, "Microsoft.WindowsAppSDK.DWrite": {"type": "Transitive", "resolved": "1.8.25050910-experimental.2", "contentHash": "Rl2jkdsNhgNmwJa+1SYoO+tG9x/4O/eGHK+Q7HJFxjVo3uICcVVa0rG+/bPdDiS2Hb5yr4zq7l4miVs+IBd6/w==", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250501001-experimental"}}, "Microsoft.WindowsAppSDK.Foundation": {"type": "Transitive", "resolved": "1.8.250507001-experimental", "contentHash": "VfpfQKD3H0/aka91cTNyzjjlSVBGQ2BSv+ZER/TUuHLKmksz13eq8u5KYeVdyjUY4xF0okPtQLm8gGFBBTPz3w==", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250501001-experimental", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250506001-experimental"}}, "Microsoft.WindowsAppSDK.InteractiveExperiences": {"type": "Transitive", "resolved": "1.8.250509002-experimental", "contentHash": "DWUf6sCB/wmqgrpgX49pG43W/XqYHNEr1yE6aKE964hSO8HnB7HRElJUUCBhzEmjZLaESqJf2Il7Tf9OfGHX2A==", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250501001-experimental"}}, "Microsoft.WindowsAppSDK.Packages": {"type": "Transitive", "resolved": "1.8.250515001-experimental2", "contentHash": "cS1BxcFDjpLp1KA7NVVDMte3cErMa19Oi0EWzo1C6XjijL614H+KPbuZ3A8b1zHfZh9awRcyQd/uHSFOH1zVAQ==", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250509001-experimental"}}, "Microsoft.WindowsAppSDK.Widgets": {"type": "Transitive", "resolved": "1.8.250505003-experimental", "contentHash": "2JcNMyu3o/DcjAP6xbRNkzsEPBXju+rhsjp9xuUP1o6/24rl4AU1hPVNN6gjbUbvPeickkDQOPtYgtkmbc9oZQ==", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250501001-experimental"}}, "Microsoft.WindowsAppSDK.WinUI": {"type": "Transitive", "resolved": "1.8.250507002-experimental", "contentHash": "E8s3bj3ECUOR/a6KoTgjf73TE5GboJDY8JHITNUfODa7uZuQPFFM7IJABE4yYGpECSX8cG9TWTLXhuYCKZ9Y0g==", "dependencies": {"Microsoft.Web.WebView2": "1.0.3179.45", "Microsoft.WindowsAppSDK.Base": "1.8.250501001-experimental", "Microsoft.WindowsAppSDK.Foundation": "1.8.250507001-experimental", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250506001-experimental"}}, "NuGet.Versioning": {"type": "Transitive", "resolved": "6.14.0", "contentHash": "4v4blkhCv8mpKtfx+z0G/X0daVCzdIaHSC51GkUspugi5JIMn2Bo8xm5PdZYF0U68gOBfz/+aPWMnpRd85Jbow=="}, "Sentry": {"type": "Transitive", "resolved": "5.10.0", "contentHash": "YArPx8bL+kU2htpoOI1k2W/n3gbuiA7TLchYIs4AcLbbB2MXmX1+Eo1IIiBf484eqVXRpO0Dm3bvQa4fg+ZhCw=="}, "SharpHDiffPatch.Core": {"type": "Transitive", "resolved": "2.3.3", "contentHash": "KQK+381J3Y2MjxwoG63vvPkDFReN03fp6tCVE6OCctZSgxXhQ0pXCpruX5xyhstx/I6yGPVBejmAyodaSK4tGQ==", "dependencies": {"Hi3Helper.ZstdNet": "1.6.4", "ZstdSharp.Port": "0.8.5"}}, "System.Buffers": {"type": "Transitive", "resolved": "4.6.0", "contentHash": "lN6tZi7Q46zFzAbRYXTIvfXcyvQQgxnY7Xm6C6xQ9784dEL1amjM6S6Iw4ZpsvesAKnRVsM4scrDQaDqSClkjA=="}, "System.Drawing.Common": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "T/6nqx0B7/uTe5JjBwrKZilLuwfhHLOVmNKlT/wr4A9Dna94mgTdz3lTfrdJ72QRx7IHCv/LzoJPmFSfK/N6WA==", "dependencies": {"Microsoft.Win32.SystemEvents": "9.0.5"}}, "System.IO.Hashing": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "oMOYyDCLu7sRdupDnpq1yXqUiM+K0j9ZZAVIYDoT06cXTnflNiCLTCb9A6+1yn2a3aBqmOi1xMudbF+WOPFY5Q=="}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg=="}, "ThisAssembly.Constants": {"type": "Transitive", "resolved": "2.0.6", "contentHash": "XF1RWxkEx+xvSRZOpkLF0rrclMsjKYkO+j7UoCk+ebSzKcxoIye7Na4HvkmIJogNoB+vZqSz7df5McxgzZHtbg==", "dependencies": {"Microsoft.CSharp": "4.7.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "ZstdSharp.Port": {"type": "Transitive", "resolved": "0.8.5", "contentHash": "TR4j17WeVSEb3ncgL2NqlXEqcy04I+Kk9CaebNDplUeL8XOgjkZ7fP4Wg4grBdPLIqsV86p2QaXTkZoRMVOsew=="}, "colorthief": {"type": "Project", "dependencies": {"System.Drawing.Common": "[9.0.5, )"}}, "discordrpc": {"type": "Project", "dependencies": {"Microsoft.Extensions.Logging": "[9.0.5, )", "System.Text.Json": "[9.0.5, )"}}, "h.generatedicons.system.drawing": {"type": "Project", "dependencies": {"System.Drawing.Common": "[9.0.5, )"}}, "h.notifyicon": {"type": "Project", "dependencies": {"H.GeneratedIcons.System.Drawing": "[1.0.0, )", "Microsoft.Extensions.Logging.Abstractions": "[9.0.5, )", "Microsoft.Windows.SDK.Win32Metadata": "[63.0.31-preview, )", "Microsoft.Windows.WDK.Win32Metadata": "[0.13.25-experimental, )"}}, "h.notifyicon.winui": {"type": "Project", "dependencies": {"H.NotifyIcon": "[0.0.0, )", "Microsoft.Extensions.Logging.Abstractions": "[9.0.5, )", "Microsoft.Web.WebView2": "[1.0.3344-prerelease, )", "Microsoft.Windows.SDK.BuildTools": "[10.0.26100.4188, )", "Microsoft.WindowsAppSDK": "[1.8.250515001-experimental2, )"}}, "hi3helper.core": {"type": "Project", "dependencies": {"Hi3Helper.EncTool": "[1.0.0, )", "Hi3Helper.Win32": "[1.0.0, )", "Microsoft.Windows.CsWinRT": "[2.2.0, )", "Sentry": "[5.10.0, )"}}, "hi3helper.enctool": {"type": "Project", "dependencies": {"Google.Protobuf": "[3.31.1, )", "Hi3Helper.Http": "[2.0.0, )", "Hi3Helper.Win32": "[1.0.0, )", "System.IO.Hashing": "[9.0.5, )"}}, "hi3helper.http": {"type": "Project"}, "hi3helper.sophon": {"type": "Project", "dependencies": {"Google.Protobuf": "[3.31.1, )", "Hi3Helper.ZstdNet": "[*, )", "SharpHDiffPatch.Core": "[*, )", "System.IO.Hashing": "[*, )"}}, "hi3helper.win32": {"type": "Project", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "[9.0.5, )"}}, "hi3helper.win32.winrt": {"type": "Project", "dependencies": {"Hi3Helper.Win32": "[1.0.0, )", "Microsoft.Extensions.Logging.Abstractions": "[9.0.5, )"}}, "ImageCropper": {"type": "Project", "dependencies": {"CommunityToolkit.Common": "[8.4.0, )", "CommunityToolkit.WinUI.Extensions": "[8.2.250402, )", "CommunityToolkit.WinUI.Media": "[8.2.250402, )", "Microsoft.Graphics.Win2D": "[1.3.2, )", "Microsoft.Web.WebView2": "[1.0.3344-prerelease, )", "Microsoft.Windows.CsWinRT": "[2.2.0, )", "Microsoft.Windows.SDK.BuildTools": "[10.0.26100.4188, )", "Microsoft.WindowsAppSDK": "[1.8.250515001-experimental2, )"}}, "imageex": {"type": "Project", "dependencies": {"CommunityToolkit.Common": "[8.4.0, )", "CommunityToolkit.WinUI.Extensions": "[8.2.250402, )", "Microsoft.Web.WebView2": "[1.0.3344-prerelease, )", "Microsoft.Windows.SDK.BuildTools": "[10.0.26100.4188, )", "Microsoft.WindowsAppSDK": "[1.8.250515001-experimental2, )"}}, "innosetuphelper": {"type": "Project", "dependencies": {"System.IO.Hashing": "[9.0.5, )"}}, "SettingsControls": {"type": "Project", "dependencies": {"CommunityToolkit.Common": "[8.4.0, )", "CommunityToolkit.WinUI.Triggers": "[8.2.250402, )", "Microsoft.Web.WebView2": "[1.0.3344-prerelease, )", "Microsoft.Windows.CsWinRT": "[2.2.0, )", "Microsoft.Windows.SDK.BuildTools": "[10.0.26100.4188, )", "Microsoft.WindowsAppSDK": "[1.8.250515001-experimental2, )"}}, "sevenzipextractor": {"type": "Project"}}, "net9.0-windows10.0.26100/win-x64": {"Libsql.Client": {"type": "Direct", "requested": "[0.5.0, )", "resolved": "0.5.0", "contentHash": "5Tw6O9sBDAN1aV+kpOSVvqvFk6Ahk6bYz0TTx3808Dp40M45gKTtzTzI9SS1VeAkljE6BAOeKaykpPnG36oOgw=="}, "Microsoft.Graphics.Win2D": {"type": "Direct", "requested": "[1.3.2, )", "resolved": "1.3.2", "contentHash": "xAv4+Xj8wePW1innIUSXebX6UHMGDzOxwKPwYNUNY7mBrsRL/hPFEtuPLpBSxc3iBnuaCogPDCDeN7T4b1xV0Q==", "dependencies": {"Microsoft.WindowsAppSDK": "1.6.241114003"}}, "Microsoft.Web.WebView2": {"type": "Direct", "requested": "[1.0.3344-prerelease, )", "resolved": "1.0.3344-prerelease", "contentHash": "I1n0XLQuJcVOrTPLl0pYARi57q+AtVswrCHHHaB0ROvmxn3IvYQg+r++IYUpJnzucwEyDNWqn/ZLoxTeD6sjAA=="}, "PhotoSauce.NativeCodecs.Libwebp": {"type": "Direct", "requested": "[*-*, )", "resolved": "1.4.0-preview1", "contentHash": "0AyuhMpynCdCq84dh0wU/Kjuxzo3GkYny5Z1l2JlWTHD2muUlE3Akqa1CtEPj0lif9o/KHnYwhmIWnP0ZctF4g==", "dependencies": {"PhotoSauce.MagicScaler": "[0.15.0]"}}, "System.Security.AccessControl": {"type": "Direct", "requested": "[6.0.1, )", "resolved": "6.0.1", "contentHash": "IQ4NXP/B3Ayzvw0rDQzVTYsCKyy0Jp9KI6aYcK7UnGVlR9+Awz++TIPCQtPYfLJfOpm8ajowMR09V7quD3sEHw=="}, "System.Text.Encoding.CodePages": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "P6gqCJcVjdWA50Eai95eYdG4ryrb1xCd770NhQrvj847C7m30CJwQqzKykWoEMvV4qvjUazL/m7UMQdMZTp5xw=="}, "Microsoft.Win32.SystemEvents": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "D4OYNpmvAsF9MkaY2W8Jue2XuNHDhygvwzo019hs+lP85KaVnOlXmqsjDKr1dHb1DPxDnOKpe6mAgJN7S6ttwg=="}, "Microsoft.WindowsAppSDK.Foundation": {"type": "Transitive", "resolved": "1.8.250507001-experimental", "contentHash": "VfpfQKD3H0/aka91cTNyzjjlSVBGQ2BSv+ZER/TUuHLKmksz13eq8u5KYeVdyjUY4xF0okPtQLm8gGFBBTPz3w==", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250501001-experimental", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250506001-experimental"}}}}}