﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable IdentifierTypo  -->
<!--  <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Local  -->
<!--  <PERSON>Sharper disable Xaml.ConstructorWarning  -->
<Page x:Class="CollapseLauncher.MarkdownFramePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:helper="using:Hi3Helper"
      xmlns:markdown="using:CommunityToolkit.Labs.WinUI.Labs.MarkdownTextBlock"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      Background="{ThemeResource WebView2GridBackground}"
      Unloaded="MarkdownFramePage_OnUnloaded"
      mc:Ignorable="d">
    <Page.Resources>
        <ThemeShadow x:Name="SharedShadow" />
    </Page.Resources>
    <Grid x:Name="MarkdownPanel"
          Canvas.ZIndex="8"
          Shadow="{ThemeResource SharedShadow}"
          Visibility="Visible">
        <Grid.RowDefinitions>
            <RowDefinition Height="36" />
            <RowDefinition Height="64" />
            <RowDefinition />
        </Grid.RowDefinitions>
        <TextBlock x:Name="MarkdownFrameTitle"
                   Grid.Row="0"
                   Grid.RowSpan="2"
                   Margin="160,16,160,0"
                   HorizontalAlignment="Center"
                   FontSize="20"
                   FontWeight="Bold"
                   TextTrimming="CharacterEllipsis"
                   Visibility="Collapsed" />
        <Grid x:Name="WebViewNavPanel"
              Grid.Row="1"
              Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="40" />
                <ColumnDefinition Width="40" />
                <ColumnDefinition Width="72" />
                <ColumnDefinition />
                <ColumnDefinition Width="64" />
                <ColumnDefinition Width="44" />
            </Grid.ColumnDefinitions>
            <Button x:Name="MarkdownOpenExternalBtn"
                    Grid.Column="4"
                    Width="64"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Stretch"
                    Click="MarkdownOpenExternalBtn_Click"
                    CornerRadius="16"
                    ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._Dialogs.OpenInExternalBrowser}"
                    Visibility="Collapsed">
                <Button.Content>
                    <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="14"
                              Glyph="&#xf08e;" />
                </Button.Content>
            </Button>
            <Button x:Name="MarkdownCloseBtn"
                    Grid.Column="5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Stretch"
                    x:FieldModifier="internal"
                    CornerRadius="16"
                    Style="{ThemeResource AccentButtonStyle}"
                    ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._Dialogs.CloseOverlay}">
                <Button.Content>
                    <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="14"
                              Glyph="&#xf00d;" />
                </Button.Content>
            </Button>
        </Grid>
        <ScrollViewer Grid.Row="2"
                      Margin="32,12,32,24">
            <markdown:MarkdownTextBlock x:Name="MarkdownContainer"
                                        Padding="16"
                                        VerticalAlignment="Stretch"
                                        Background="Transparent"
                                        Config="{x:Bind _markdownConfig}"
                                        Text="# Loading content..." />
        </ScrollViewer>
    </Grid>
</Page>
