﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable IdentifierTypo  -->
<!--  <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Local  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<Page x:Class="CollapseLauncher.WebView2FramePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      Unloaded="WebView2Unload"
      mc:Ignorable="d">
    <Page.Resources>
        <ThemeShadow x:Name="SharedShadow" />
    </Page.Resources>
    <Grid x:Name="WebView2Panel"
          Background="{ThemeResource WebView2GridBackground}"
          Canvas.ZIndex="8"
          Shadow="{ThemeResource SharedShadow}"
          Visibility="Collapsed">
        <Grid.RowDefinitions>
            <RowDefinition Height="36" />
            <RowDefinition Height="64" />
            <RowDefinition />
        </Grid.RowDefinitions>
        <TextBlock x:Name="WebViewWindowTitle"
                   Grid.Row="0"
                   Grid.RowSpan="2"
                   Margin="160,16,160,12"
                   HorizontalAlignment="Center"
                   FontSize="13"
                   FontWeight="Bold"
                   TextTrimming="CharacterEllipsis" />
        <Grid x:Name="WebViewNavPanel"
              Grid.Row="1"
              Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="40" />
                <ColumnDefinition Width="40" />
                <ColumnDefinition Width="72" />
                <ColumnDefinition />
                <ColumnDefinition Width="64" />
                <ColumnDefinition Width="44" />
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0"
                        Grid.ColumnSpan="3"
                        Orientation="Horizontal">
                <Button x:Name="WebView2BackBtn"
                        Width="48"
                        VerticalAlignment="Stretch"
                        Click="WebView2BackBtn_Click"
                        CornerRadius="16,0,0,16"
                        IsEnabled="False"
                        Style="{ThemeResource AccentButtonStyle}">
                    <Button.Content>
                        <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                  FontSize="14"
                                  Glyph="&#xf104;" />
                    </Button.Content>
                </Button>
                <Button x:Name="WebView2ForwardBtn"
                        Width="48"
                        VerticalAlignment="Stretch"
                        Click="WebView2ForwardBtn_Click"
                        CornerRadius="0,16,16,0"
                        IsEnabled="False"
                        Style="{ThemeResource AccentButtonStyle}">
                    <Button.Content>
                        <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                  FontSize="14"
                                  Glyph="&#xf105;" />
                    </Button.Content>
                </Button>
                <Button x:Name="WebView2ReloadBtn"
                        Width="48"
                        Margin="8,0,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Stretch"
                        Click="WebView2ReloadBtn_Click"
                        CornerRadius="16"
                        Style="{ThemeResource AccentButtonStyle}">
                    <Button.Content>
                        <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                  FontSize="14"
                                  Glyph="&#xf01e;" />
                    </Button.Content>
                </Button>
            </StackPanel>
            <Grid Grid.Column="3"
                  Margin="8,0"
                  CornerRadius="5">
                <TextBox x:Name="WebView2URLBox"
                         IsReadOnly="True" />
                <ProgressBar x:Name="WebView2LoadingStatus"
                             HorizontalAlignment="Stretch"
                             VerticalAlignment="Bottom" />
            </Grid>
            <Button x:Name="WebView2OpenExternalBtn"
                    Grid.Column="4"
                    Width="64"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Stretch"
                    Click="WebView2OpenExternalBtn_Click"
                    CornerRadius="16">
                <Button.Content>
                    <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="14"
                              Glyph="&#xf08e;" />
                </Button.Content>
            </Button>
            <Button x:Name="WebView2CloseBtn"
                    Grid.Column="5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Stretch"
                    Click="WebView2CloseBtn_Click"
                    CornerRadius="16"
                    Style="{ThemeResource AccentButtonStyle}">
                <Button.Content>
                    <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="14"
                              Glyph="&#xf00d;" />
                </Button.Content>
            </Button>
        </Grid>
        <Grid x:Name="WebView2WindowContainer"
              Grid.Row="2"
              HorizontalAlignment="Stretch"
              VerticalAlignment="Stretch" />
    </Grid>
</Page>
