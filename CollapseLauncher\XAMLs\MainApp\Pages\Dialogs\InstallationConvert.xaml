﻿<Page x:Class="CollapseLauncher.Dialogs.InstallationConvert"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:helper="using:Hi3Helper"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      x:Name="ConvertPage"
      x:FieldModifier="public"
      Background="{ThemeResource PageBackgroundAcrylicBrush}"
      Loaded="Page_Loaded"
      mc:Ignorable="d">
    <Grid Margin="32"
          VerticalAlignment="Stretch">
        <TextBlock Style="{ThemeResource TitleLargeTextBlockStyle}"
                   Text="{x:Bind helper:Locale.Lang._InstallConvert.PageTitle}" />
        <Grid Margin="0,48,0,32"
              HorizontalAlignment="Stretch"
              VerticalAlignment="Stretch">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="32*" />
                <ColumnDefinition Width="681*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>
            <Slider x:Name="ProgressSlider"
                    Grid.RowSpan="5"
                    Margin="0,40,0,40"
                    HorizontalAlignment="Left"
                    Background="#22000000"
                    CanDrag="False"
                    Maximum="4"
                    Minimum="0"
                    Orientation="Vertical"
                    RenderTransformOrigin="0.5,0.5"
                    TickFrequency="1"
                    TickPlacement="TopLeft"
                    Value="0">
                <Slider.RenderTransform>
                    <CompositeTransform Rotation="180" />
                </Slider.RenderTransform>
            </Slider>
            <Rectangle Grid.Row="0"
                       Grid.RowSpan="5"
                       Grid.Column="0"
                       Grid.ColumnSpan="2"
                       Fill="Transparent" />
            <StackPanel x:Name="Step1"
                        Grid.Row="0"
                        Grid.Column="1"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Opacity="1"
                        Orientation="Horizontal">
                <ProgressRing x:Name="Step1ProgressRing"
                              Width="32"
                              Height="32"
                              Margin="0,0,16,0"
                              IsIndeterminate="True"
                              Value="100" />
                <StackPanel Orientation="Vertical">
                    <TextBlock VerticalAlignment="Center"
                               Style="{ThemeResource SubtitleTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.Step1Title}" />
                    <TextBlock x:Name="Step1ProgressStatus"
                               VerticalAlignment="Center"
                               Style="{ThemeResource BodyTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.StepNotRunning}" />
                </StackPanel>
            </StackPanel>
            <StackPanel x:Name="Step2"
                        Grid.Row="1"
                        Grid.Column="1"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Opacity="0.25"
                        Orientation="Horizontal">
                <ProgressRing x:Name="Step2ProgressRing"
                              Width="32"
                              Height="32"
                              Margin="0,0,16,0"
                              IsIndeterminate="false"
                              Value="100" />
                <StackPanel Orientation="Vertical">
                    <TextBlock VerticalAlignment="Center"
                               Style="{ThemeResource SubtitleTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.Step2Title}" />
                    <TextBlock x:Name="Step2ProgressStatus"
                               VerticalAlignment="Center"
                               Style="{ThemeResource BodyTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.StepNotRunning}" />
                </StackPanel>
            </StackPanel>
            <StackPanel x:Name="Step3"
                        Grid.Row="2"
                        Grid.Column="1"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Opacity="0.25"
                        Orientation="Horizontal">
                <ProgressRing x:Name="Step3ProgressRing"
                              Width="32"
                              Height="32"
                              Margin="0,0,16,0"
                              IsIndeterminate="False"
                              Value="100" />
                <StackPanel Orientation="Vertical">
                    <TextBlock x:Name="Step3ProgressTitle"
                               VerticalAlignment="Center"
                               Style="{ThemeResource SubtitleTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.Step3Title}" />
                    <TextBlock x:Name="Step3ProgressStatus"
                               VerticalAlignment="Center"
                               Style="{ThemeResource BodyTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.StepNotRunning}" />
                </StackPanel>
            </StackPanel>
            <StackPanel x:Name="Step4"
                        Grid.Row="3"
                        Grid.Column="1"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Opacity="0.25"
                        Orientation="Horizontal">
                <ProgressRing x:Name="Step4ProgressRing"
                              Width="32"
                              Height="32"
                              Margin="0,0,16,0"
                              IsIndeterminate="false"
                              Value="100" />
                <StackPanel Orientation="Vertical">
                    <TextBlock VerticalAlignment="Center"
                               Style="{ThemeResource SubtitleTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.Step4Title}" />
                    <TextBlock x:Name="Step4ProgressStatus"
                               VerticalAlignment="Center"
                               Style="{ThemeResource BodyTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.StepNotRunning}" />
                </StackPanel>
            </StackPanel>
            <StackPanel x:Name="Step5"
                        Grid.Row="4"
                        Grid.Column="1"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Opacity="0.25"
                        Orientation="Horizontal">
                <ProgressRing x:Name="Step5ProgressRing"
                              Width="32"
                              Height="32"
                              Margin="0,0,16,0"
                              IsIndeterminate="false"
                              Value="100" />
                <StackPanel Orientation="Vertical">
                    <TextBlock VerticalAlignment="Center"
                               Style="{ThemeResource SubtitleTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.Step5Title}" />
                    <TextBlock x:Name="Step5ProgressStatus"
                               VerticalAlignment="Center"
                               Style="{ThemeResource BodyTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.StepNotRunning}" />
                </StackPanel>
            </StackPanel>
        </Grid>
        <Grid HorizontalAlignment="Stretch"
              VerticalAlignment="Bottom">
            <Button x:Name="CancelBtn"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Bottom"
                    Click="CancelConversion"
                    CornerRadius="16"
                    Style="{ThemeResource AccentButtonStyle}">
                <StackPanel Orientation="Horizontal">
                    <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="16"
                              Glyph="&#xf00d;" />
                    <TextBlock Margin="8,0,0,0"
                               FontWeight="Medium"
                               Text="{x:Bind helper:Locale.Lang._InstallConvert.CancelBtn}" />
                </StackPanel>
            </Button>
            <TextBlock HorizontalAlignment="Right"
                       VerticalAlignment="Bottom"
                       Style="{ThemeResource BodyTextBlockStyle}">
                <Span>
                    <Run Text="{x:Bind helper:Locale.Lang._InstallConvert.PageFooter1}" />
                    <Run FontWeight="Black"
                         Text="{x:Bind helper:Locale.Lang._InstallConvert.PageFooter2}" />
                    <Run Text="{x:Bind helper:Locale.Lang._InstallConvert.PageFooter3}" />
                </Span>
            </TextBlock>
        </Grid>
    </Grid>
</Page>
