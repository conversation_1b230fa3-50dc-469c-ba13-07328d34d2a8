@echo off
REM CollapseLauncher Batch Configuration Processing Tool (Windows)
REM Batch encrypt or decrypt multiple configuration files

setlocal enabledelayedexpansion

echo ==========================================
echo CollapseLauncher Batch Processing Tool
echo ==========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Get operation type
echo Select operation:
echo 1. Batch Decrypt
echo 2. Batch Encrypt
echo.
set /p OPERATION="Enter choice (1 or 2): "

if "%OPERATION%"=="1" (
    set COMMAND=batch-decrypt
    set PREFIX=decrypted_
    echo Selected: Batch Decrypt
) else if "%OPERATION%"=="2" (
    set COMMAND=batch-encrypt
    set PREFIX=encrypted_
    echo Selected: Batch Encrypt
) else (
    echo Invalid choice. Please enter 1 or 2.
    pause
    exit /b 1
)

echo.

REM Get input directory
set /p INPUT_DIR="Enter input directory path: "
if not exist "%INPUT_DIR%" (
    echo Error: Input directory not found: %INPUT_DIR%
    pause
    exit /b 1
)

REM Get output directory
set /p OUTPUT_DIR="Enter output directory path: "
if not exist "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
    echo Created output directory: %OUTPUT_DIR%
)

REM Check if master key file exists
set MASTER_KEY_FILE=..\MasterKeyGenerator\output\config_master.json
if not exist "%MASTER_KEY_FILE%" (
    set /p MASTER_KEY_FILE="Enter master key file path: "
    if not exist "!MASTER_KEY_FILE!" (
        echo Error: Master key file not found: !MASTER_KEY_FILE!
        pause
        exit /b 1
    )
)

echo.
echo Input Directory: %INPUT_DIR%
echo Output Directory: %OUTPUT_DIR%
echo Master Key: %MASTER_KEY_FILE%
echo Operation: %COMMAND%
echo.

REM Check dependencies
echo Checking dependencies...
python -c "import cryptography, json" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Dependencies OK
echo.

REM Count JSON files
set FILE_COUNT=0
for %%F in ("%INPUT_DIR%\*.json") do (
    set /a FILE_COUNT+=1
)

if %FILE_COUNT%==0 (
    echo No JSON files found in input directory.
    pause
    exit /b 1
)

echo Found %FILE_COUNT% JSON file(s) to process.
echo.

REM Confirm operation
set /p CONFIRM="Proceed with %COMMAND%? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 1
)

echo.
echo Starting batch processing...
echo ==========================================

REM Execute batch operation
python crypto_tool.py %COMMAND% --input-dir "%INPUT_DIR%" --output-dir "%OUTPUT_DIR%" --master-key "%MASTER_KEY_FILE%"

if errorlevel 1 (
    echo.
    echo Error: Batch processing failed
    pause
    exit /b 1
)

echo.
echo ==========================================
echo Batch processing completed successfully!
echo ==========================================
echo Input: %INPUT_DIR%
echo Output: %OUTPUT_DIR%
echo Processed: %FILE_COUNT% file(s)
echo.

REM Ask if user wants to open output directory
set /p OPEN_DIR="Open output directory? (y/n): "
if /i "%OPEN_DIR%"=="y" (
    start explorer "%OUTPUT_DIR%"
)

pause
