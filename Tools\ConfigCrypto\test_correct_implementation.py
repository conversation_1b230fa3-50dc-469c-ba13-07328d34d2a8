#!/usr/bin/env python3
"""
Test script to verify the corrected implementation matches C# behavior.
"""

import json
import tempfile
from pathlib import Path

# Import our corrected modules
from crypto_tool import CollapseDecryptor
import sys
import os

# Add MasterKeyGenerator to path
sys.path.append(str(Path(__file__).parent.parent / "MasterKeyGenerator"))
from generate_master_key import Master<PERSON>eyGenerator


def test_master_key_generation_and_usage():
    """Test the complete flow: generate master key -> use for encryption/decryption."""
    
    print("=" * 60)
    print("Testing Corrected Implementation")
    print("=" * 60)
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Step 1: Generate master key using corrected generator
        print("Step 1: Generating master key...")
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        
        # Create master key config
        master_config = generator.create_master_key_config()
        
        # Save master key
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        print(f"✓ Master key generated and saved to: {master_key_path}")
        print(f"  Key format: {master_config['Key'][:50]}...")
        print(f"  BitSize: {master_config['BitSize']}")
        print(f"  Hash: {master_config['Hash']}")
        
        # Step 2: Load master key using corrected decryptor
        print("\nStep 2: Loading master key...")
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            print("✓ Master key loaded successfully")
        except Exception as e:
            print(f"❌ Failed to load master key: {e}")
            return False
        
        # Step 3: Test encryption/decryption cycle
        print("\nStep 3: Testing encryption/decryption...")
        test_data = "https://example.com/api/test"
        
        try:
            # Encrypt
            encrypted = decryptor.encrypt_field(test_data)
            print(f"✓ Encryption successful")
            print(f"  Original: {test_data}")
            print(f"  Encrypted: {encrypted[:50]}...")
            
            # Decrypt
            decrypted = decryptor.decrypt_field(encrypted)
            print(f"✓ Decryption successful")
            print(f"  Decrypted: {decrypted}")
            
            # Verify
            if decrypted == test_data:
                print("✓ Encryption/Decryption cycle successful!")
                return True
            else:
                print(f"❌ Data mismatch: expected '{test_data}', got '{decrypted}'")
                return False
                
        except Exception as e:
            print(f"❌ Encryption/Decryption failed: {e}")
            return False


def test_with_known_data():
    """Test with the known config_master.json data from the user."""
    
    print("\n" + "=" * 60)
    print("Testing with Known Master Key Data")
    print("=" * 60)
    
    # Create the known master key config
    known_config = {
        "Key": "Q29sbGFwc2UBAAAAAAAAAGICAAAAAAAAXgIAAAAAAACPLoEwggJaAgEAAoGBAK16ClpvNiUl+FGIoZLdhUo66d6Wak8begMidGXsR7LErWHrAhblDpgwa5NJ8T02Nq+6AeGQvbQ4R85gOujHuesJA+O2d3whAXyxcZhFXVArzNUSBXUMUAgewjGhVlx6FXwYTXA+tDrVw+9bxbrAS4qGuhwx2ypPC3YFnVddDlmhAgERAoGBAKNFr2QsbzIFnmra8mwbyL5VkNF+gixWGHtruNhl6SC5G6dzxbsx755LsIqf8ht+UZZUtnnxo3xxNIYAN3Gs6zYDOoAQ3BWu4s574iIkE8ksRbs8t5l6Y0kBWhoHb5rlEC4IOamoXHRoE+OeRc6ORkrviMk6Q/nTaCsu/TS0274xAkEA16GiwrgCGvcYbgSZOBJRx4G9kioGgexLSyW62iK4EuT0Xu9xyflBDaC4yooFkxrflqEAIiEfTqNGlYeJks+5qwJBAM30GOHVovw5aN/Musb1+KVBAI9YW3haP0Isux9ND9PPVtCgujHYvhmOFLkRibYF+1GXxkHR0Y/JgY4oxcoVJeMCQHIoZTno8g5GlHZ657RF7w9Er6e75VPXcyfIrjc/jqCXVDJCh4kLfMr6vC79xrdKdl7NtMbGPcA4Uotl32vXYksCQBg61cA3QFnoou0nJQhZLE+tS1wogztV6VMUUj/q8s2f7BiLYTMKcLe2XMp6iKwAtCe3gLxzCZhyDz3mrduKBHUCQD+P74KtVxe+zfCvzsU23I1hQgtXfr91ucVkCW5lNyn1RdsDkrWXiC09l9drOwBw/rlHzfTv8XRASHqnEi1cVo8D",
        "BitSize": 128,
        "Hash": 4849480174666812961
    }
    
    # Create temporary file
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        master_key_path = temp_path / "known_master_key.json"
        
        with open(master_key_path, 'w') as f:
            json.dump(known_config, f, indent=2)
        
        print(f"Using known master key: {known_config['Key'][:50]}...")
        
        # Try to load the known master key
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            print("✓ Known master key loaded successfully")
            
            # Test with some sample encrypted data (if we had any)
            print("✓ Ready to decrypt real configuration data")
            return True
            
        except Exception as e:
            print(f"❌ Failed to load known master key: {e}")
            print("This indicates our implementation still needs adjustment")
            return False


def main():
    """Run all tests."""
    print("Testing Corrected CollapseLauncher Crypto Implementation")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # Test 1: Generate and use new master key
    if test_master_key_generation_and_usage():
        success_count += 1
    
    # Test 2: Use known master key
    if test_with_known_data():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 All tests passed! Implementation appears correct.")
        return True
    else:
        print("❌ Some tests failed. Implementation needs further adjustment.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
