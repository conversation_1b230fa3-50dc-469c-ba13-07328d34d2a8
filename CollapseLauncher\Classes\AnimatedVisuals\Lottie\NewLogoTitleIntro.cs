﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//       LottieGen version:
//           8.0.280225.1+7cd366a738
//       
//       Command:
//           LottieGen -Language CSharp -Namespace CollapseLauncher.AnimatedVisuals.Lottie -Public -WinUIVersion 3.0 -InputFile NewLogoTitleIntro.json
//       
//       Input file:
//           NewLogoTitleIntro.json (453329 bytes created 11:22+07:00 Jun 17 2024)
//       
//       LottieGen source:
//           http://aka.ms/Lottie
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
// ____________________________________
// |       Object stats       | Count |
// |__________________________|_______|
// | All CompositionObjects   |   976 |
// |--------------------------+-------|
// | Expression animators     |     6 |
// | KeyFrame animators       |    75 |
// | Reference parameters     |     6 |
// | Expression operations    |    10 |
// |--------------------------+-------|
// | Animated brushes         |     1 |
// | Animated gradient stops  |     - |
// | ExpressionAnimations     |     6 |
// | PathKeyFrameAnimations   |     6 |
// |--------------------------+-------|
// | ContainerVisuals         |    57 |
// | ShapeVisuals             |    46 |
// |--------------------------+-------|
// | ContainerShapes          |     3 |
// | CompositionSpriteShapes  |    97 |
// |--------------------------+-------|
// | Brushes                  |    76 |
// | Gradient stops           |     6 |
// | CompositionVisualSurface |    42 |
// ------------------------------------
using Hi3Helper.Shared.Region;
using Microsoft.Graphics.Canvas;
using Microsoft.Graphics.Canvas.Effects;
using Microsoft.Graphics.Canvas.Geometry;
using Microsoft.UI.Composition;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Media;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Numerics;
using Windows.Foundation;
using Windows.UI;

namespace CollapseLauncher.AnimatedVisuals.Lottie
{
    // Name:        CollapseLauncherNewLogoTitleIntro
    // Frame rate:  60 fps
    // Frame count: 600
    // Duration:    10000.0 mS
    sealed partial class NewLogoTitleIntro
        : Microsoft.UI.Xaml.Controls.IAnimatedVisualSource
        , Microsoft.UI.Xaml.Controls.IAnimatedVisualSource2
        , Microsoft.UI.Xaml.Controls.IDynamicAnimatedVisualSource
        , INotifyPropertyChanged
    {
        const int c_loadedImageSurfaceCount = 1;
        int _loadCompleteEventCount;
        bool _isImageLoadingAsynchronous;
        bool _isTryCreateAnimatedVisualCalled;
        bool _isImageLoadingStarted;
        HashSet<TypedEventHandler<IDynamicAnimatedVisualSource, object>> _animatedVisualInvalidatedEventTokenTable = new HashSet<TypedEventHandler<IDynamicAnimatedVisualSource, object>>();
        LoadedImageSurface _image_image_0;

        // Animation duration: 10.000 seconds.
        internal const long c_durationTicks = 100000000;

        /// <summary>
        /// This implementation of the INotifyPropertyChanged.PropertyChanged event is specific
        /// to C# and does not work on WinRT.
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        public event TypedEventHandler<IDynamicAnimatedVisualSource, object> AnimatedVisualInvalidated
        {
            add
            {
                _animatedVisualInvalidatedEventTokenTable.Add(value);
            }
            remove
            {
                _animatedVisualInvalidatedEventTokenTable.Remove(value);
            }
        }

        /// <summary>
        /// If this property is set to true, <see cref="TryCreateAnimatedVisual"/> will return
        /// null until all images have loaded. When all images have loaded,
        /// <see cref="TryCreateAnimatedVisual"/> will return the AnimatedVisual. To use, set
        /// it when instantiating the AnimatedVisualSource. Once
        /// <see cref="TryCreateAnimatedVisual"/> is called, changes made to this property will
        /// be ignored. Default value is false.
        /// </summary>
        public bool IsImageLoadingAsynchronous
        {
            get { return _isImageLoadingAsynchronous; }
            set
            {
                if (!_isTryCreateAnimatedVisualCalled && _isImageLoadingAsynchronous != value)
                {
                    _isImageLoadingAsynchronous = value;
                    NotifyPropertyChanged(nameof(IsImageLoadingAsynchronous));
                }
            }
        }

        /// <summary>
        /// Returns true if all images have finished loading.
        /// </summary>
        public bool IsImageLoadingCompleted { get; private set; }

        public Microsoft.UI.Xaml.Controls.IAnimatedVisual TryCreateAnimatedVisual(Compositor compositor)
        {
            object ignored = null;
            return TryCreateAnimatedVisual(compositor, out ignored);
        }

        public Microsoft.UI.Xaml.Controls.IAnimatedVisual TryCreateAnimatedVisual(Compositor compositor, out object diagnostics)
        {
            _isTryCreateAnimatedVisualCalled = true;
            diagnostics = null;

            EnsureImageLoadingStarted();

            if (_isImageLoadingAsynchronous && _loadCompleteEventCount != c_loadedImageSurfaceCount)
            {
                return null;
            }

            var res = 
                new NewLogoTitleIntro_AnimatedVisual(
                    compositor,
                    _image_image_0
                    );
                res.CreateAnimations();
                return res;
        }
        void EnsureImageLoadingStarted()
        {
            if (!_isImageLoadingStarted)
            {
                var eventHandler = new TypedEventHandler<LoadedImageSurface, LoadedImageSourceLoadCompletedEventArgs>(HandleLoadCompleted);
                _image_image_0 = LoadedImageSurface.StartLoadFromStream(File.OpenRead(Path.Combine(LauncherConfig.AppExecutableDir, @"Assets\CollapseLauncherLogo.png")).AsRandomAccessStream());
                _image_image_0.LoadCompleted += eventHandler;
                _isImageLoadingStarted = true;
            }
        }

        void HandleLoadCompleted(LoadedImageSurface sender, LoadedImageSourceLoadCompletedEventArgs e)
        {
            _loadCompleteEventCount++;
            sender.LoadCompleted -= HandleLoadCompleted;

            if (_loadCompleteEventCount == c_loadedImageSurfaceCount)
            {
                IsImageLoadingCompleted = true;
                NotifyPropertyChanged(nameof(IsImageLoadingCompleted));
                if (_isImageLoadingAsynchronous)
                {
                    foreach (var v in _animatedVisualInvalidatedEventTokenTable) v.Invoke(this, null);
                }
            }
        }

        void NotifyPropertyChanged(string name)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        }


        /// <summary>
        /// Gets the number of frames in the animation.
        /// </summary>
        public double FrameCount => 600d;

        /// <summary>
        /// Gets the frame rate of the animation.
        /// </summary>
        public double Framerate => 60d;

        /// <summary>
        /// Gets the duration of the animation.
        /// </summary>
        public TimeSpan Duration => TimeSpan.FromTicks(100000000);

        /// <summary>
        /// Converts a zero-based frame number to the corresponding progress value denoting the
        /// start of the frame.
        /// </summary>
        public double FrameToProgress(double frameNumber)
        {
            return frameNumber / 600d;
        }

        /// <summary>
        /// Returns a map from marker names to corresponding progress values.
        /// </summary>
        public IReadOnlyDictionary<string, double> Markers =>
            new Dictionary<string, double>
            {
            };

        /// <summary>
        /// Sets the color property with the given name, or does nothing if no such property
        /// exists.
        /// </summary>
        public void SetColorProperty(string propertyName, Color value)
        {
        }

        /// <summary>
        /// Sets the scalar property with the given name, or does nothing if no such property
        /// exists.
        /// </summary>
        public void SetScalarProperty(string propertyName, double value)
        {
        }

        sealed partial class NewLogoTitleIntro_AnimatedVisual
            : Microsoft.UI.Xaml.Controls.IAnimatedVisual
            , Microsoft.UI.Xaml.Controls.IAnimatedVisual2
        {
            const long c_durationTicks = 100000000;
            readonly Compositor _c;
            readonly Color _fgColor = InnerLauncherConfig.IsAppThemeLight ? Color.FromArgb(0xFF, 0x22, 0x22, 0x22) : Color.FromArgb(0xFF, 0xFF, 0xFF, 0xFF);
            readonly Color _fgTransparentColor = InnerLauncherConfig.IsAppThemeLight ? Color.FromArgb(0x00, 0x22, 0x22, 0x22) : Color.FromArgb(0x00, 0xFF, 0xFF, 0xFF);
            readonly ExpressionAnimation _reusableExpressionAnimation;
            readonly LoadedImageSurface _image_image_0;
            AnimationController _animationController_0;
            BooleanKeyFrameAnimation _isVisibleBooleanAnimation_0;
            BooleanKeyFrameAnimation _isVisibleBooleanAnimation_1;
            BooleanKeyFrameAnimation _isVisibleBooleanAnimation_2;
            BooleanKeyFrameAnimation _isVisibleBooleanAnimation_5;
            BooleanKeyFrameAnimation _isVisibleBooleanAnimation_6;
            BooleanKeyFrameAnimation _isVisibleBooleanAnimation_7;
            CompositionColorBrush _animatedColorBrush_TransparentWhite_to_TransparentWhite;
            CompositionColorBrush _colorBrush_Black;
            CompositionColorBrush _colorBrush_White;
            CompositionColorGradientStop _gradientStop_0_AlmostAqua_FF04E3FB;
            CompositionColorGradientStop _gradientStop_0_AlmostLawnGreen_FF7CFB04;
            CompositionColorGradientStop _gradientStop_0_AlmostOrchid_FFFB4BDC;
            CompositionColorGradientStop _gradientStop_0p563_AlmostDarkOrange_FFFB841C;
            CompositionColorGradientStop _gradientStop_1_AlmostDodgerBlue_FF046CFB;
            CompositionColorGradientStop _gradientStop_1_AlmostYellow_FFEBFB04;
            CompositionContainerShape _containerShape_0;
            CompositionContainerShape _containerShape_1;
            CompositionContainerShape _containerShape_2;
            CompositionEffectFactory _effectFactory_0;
            CompositionEffectFactory _effectFactory_1;
            CompositionPath _path_0;
            CompositionPath _path_1;
            CompositionPath _path_2;
            CompositionPath _path_3;
            CompositionPath _path_4;
            CompositionPath _path_5;
            CompositionPath _path_6;
            CompositionPath _path_7;
            CompositionPathGeometry _pathGeometry_00;
            CompositionPathGeometry _pathGeometry_01;
            CompositionPathGeometry _pathGeometry_02;
            CompositionPathGeometry _pathGeometry_03;
            CompositionPathGeometry _pathGeometry_04;
            CompositionPathGeometry _pathGeometry_05;
            CompositionPathGeometry _pathGeometry_06;
            CompositionPathGeometry _pathGeometry_07;
            CompositionPathGeometry _pathGeometry_08;
            CompositionPathGeometry _pathGeometry_09;
            CompositionPathGeometry _pathGeometry_10;
            CompositionPathGeometry _pathGeometry_11;
            CompositionPathGeometry _pathGeometry_12;
            CompositionPathGeometry _pathGeometry_13;
            CompositionPathGeometry _pathGeometry_14;
            CompositionPathGeometry _pathGeometry_15;
            CompositionPathGeometry _pathGeometry_16;
            CompositionPathGeometry _pathGeometry_17;
            CompositionPathGeometry _pathGeometry_18;
            CompositionPathGeometry _pathGeometry_19;
            CompositionPathGeometry _pathGeometry_20;
            ContainerVisual _containerVisual_00;
            ContainerVisual _containerVisual_12;
            ContainerVisual _containerVisual_25;
            ContainerVisual _containerVisual_39;
            ContainerVisual _containerVisual_40;
            ContainerVisual _containerVisual_44;
            ContainerVisual _containerVisual_45;
            ContainerVisual _containerVisual_47;
            ContainerVisual _root;
            CubicBezierEasingFunction _cubicBezierEasingFunction_0;
            CubicBezierEasingFunction _cubicBezierEasingFunction_1;
            CubicBezierEasingFunction _cubicBezierEasingFunction_2;
            CubicBezierEasingFunction _cubicBezierEasingFunction_3;
            CubicBezierEasingFunction _cubicBezierEasingFunction_4;
            CubicBezierEasingFunction _cubicBezierEasingFunction_5;
            InsetClip _insetClip_0;
            ScalarKeyFrameAnimation _opacityScalarAnimation_0_to_1;
            ScalarKeyFrameAnimation _positionXScalarAnimation_960_to_227;
            ScalarKeyFrameAnimation _positionXScalarAnimation_1181_to_1016;
            ScalarKeyFrameAnimation _positionYScalarAnimation_540_to_540;
            ScalarKeyFrameAnimation _positionYScalarAnimation_651_to_594;
            ScalarKeyFrameAnimation _rotationAngleInDegreesScalarAnimation_0_to_0;
            ScalarKeyFrameAnimation _rotationAngleInDegreesScalarAnimation_0_to_m360;
            ScalarKeyFrameAnimation _rotationAngleInDegreesScalarAnimation_m4_to_0;
            ScalarKeyFrameAnimation _scalarAnimation_1_to_0p29;
            ScalarKeyFrameAnimation _trimStartScalarAnimation_1_to_0_0;
            ScalarKeyFrameAnimation _trimStartScalarAnimation_1_to_0_1;
            ScalarKeyFrameAnimation _trimStartScalarAnimation_1_to_0_2;
            ShapeVisual _shapeVisual_00;
            ShapeVisual _shapeVisual_07;
            ShapeVisual _shapeVisual_08;
            ShapeVisual _shapeVisual_09;
            ShapeVisual _shapeVisual_10;
            ShapeVisual _shapeVisual_11;
            ShapeVisual _shapeVisual_18;
            ShapeVisual _shapeVisual_19;
            ShapeVisual _shapeVisual_20;
            ShapeVisual _shapeVisual_21;
            ShapeVisual _shapeVisual_22;
            ShapeVisual _shapeVisual_29;
            ShapeVisual _shapeVisual_30;
            ShapeVisual _shapeVisual_31;
            ShapeVisual _shapeVisual_32;
            ShapeVisual _shapeVisual_33;
            ShapeVisual _shapeVisual_34;
            ShapeVisual _shapeVisual_35;
            ShapeVisual _shapeVisual_36;
            ShapeVisual _shapeVisual_37;
            ShapeVisual _shapeVisual_38;
            ShapeVisual _shapeVisual_39;
            ShapeVisual _shapeVisual_40;
            ShapeVisual _shapeVisual_41;
            ShapeVisual _shapeVisual_42;
            ShapeVisual _shapeVisual_43;
            ShapeVisual _shapeVisual_44;
            ShapeVisual _shapeVisual_45;
            StepEasingFunction _holdThenStepEasingFunction;
            StepEasingFunction _stepThenHoldEasingFunction;

            void BindProperty(
                CompositionObject target,
                string animatedPropertyName,
                string expression,
                string referenceParameterName,
                CompositionObject referencedObject)
            {
                _reusableExpressionAnimation.ClearAllParameters();
                _reusableExpressionAnimation.Expression = expression;
                _reusableExpressionAnimation.SetReferenceParameter(referenceParameterName, referencedObject);
                target.StartAnimation(animatedPropertyName, _reusableExpressionAnimation);
            }

            BooleanKeyFrameAnimation CreateBooleanKeyFrameAnimation(float initialProgress, bool initialValue)
            {
                var result = _c.CreateBooleanKeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InsertKeyFrame(initialProgress, initialValue);
                return result;
            }

            ColorKeyFrameAnimation CreateColorKeyFrameAnimation(float initialProgress, Color initialValue, CompositionEasingFunction initialEasingFunction)
            {
                var result = _c.CreateColorKeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InterpolationColorSpace = CompositionColorSpace.Rgb;
                result.InsertKeyFrame(initialProgress, initialValue, initialEasingFunction);
                return result;
            }

            PathKeyFrameAnimation CreatePathKeyFrameAnimation(float initialProgress, CompositionPath initialValue, CompositionEasingFunction initialEasingFunction)
            {
                var result = _c.CreatePathKeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InsertKeyFrame(initialProgress, initialValue, initialEasingFunction);
                return result;
            }

            ScalarKeyFrameAnimation CreateScalarKeyFrameAnimation(float initialProgress, float initialValue, CompositionEasingFunction initialEasingFunction)
            {
                var result = _c.CreateScalarKeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InsertKeyFrame(initialProgress, initialValue, initialEasingFunction);
                return result;
            }

            Vector3KeyFrameAnimation CreateVector3KeyFrameAnimation(float initialProgress, Vector3 initialValue, CompositionEasingFunction initialEasingFunction)
            {
                var result = _c.CreateVector3KeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InsertKeyFrame(initialProgress, initialValue, initialEasingFunction);
                return result;
            }

            CompositionSpriteShape CreateSpriteShape(CompositionGeometry geometry, Matrix3x2 transformMatrix)
            {
                var result = _c.CreateSpriteShape(geometry);
                result.TransformMatrix = transformMatrix;
                return result;
            }

            CompositionSpriteShape CreateSpriteShape(CompositionGeometry geometry, Matrix3x2 transformMatrix, CompositionBrush fillBrush)
            {
                var result = _c.CreateSpriteShape(geometry);
                result.TransformMatrix = transformMatrix;
                result.FillBrush = fillBrush;
                return result;
            }

            AnimationController AnimationController_0()
            {
                if (_animationController_0 != null) { return _animationController_0; }
                var result = _animationController_0 = _c.CreateAnimationController();
                result.Pause();
                BindProperty(_animationController_0, "Progress", "_.Progress", "_", _root);
                return result;
            }

            BooleanKeyFrameAnimation IsVisibleBooleanAnimation_0()
            {
                // Frame 0.
                if (_isVisibleBooleanAnimation_0 != null) { return _isVisibleBooleanAnimation_0; }
                var result = _isVisibleBooleanAnimation_0 = CreateBooleanKeyFrameAnimation(0F, false);
                // Frame 14.
                result.InsertKeyFrame(0.0233333334F, true);
                return result;
            }

            BooleanKeyFrameAnimation IsVisibleBooleanAnimation_1()
            {
                // Frame 0.
                if (_isVisibleBooleanAnimation_1 != null) { return _isVisibleBooleanAnimation_1; }
                var result = _isVisibleBooleanAnimation_1 = CreateBooleanKeyFrameAnimation(0F, false);
                // Frame 8.
                result.InsertKeyFrame(0.0133333337F, true);
                return result;
            }

            BooleanKeyFrameAnimation IsVisibleBooleanAnimation_2()
            {
                // Frame 0.
                if (_isVisibleBooleanAnimation_2 != null) { return _isVisibleBooleanAnimation_2; }
                var result = _isVisibleBooleanAnimation_2 = CreateBooleanKeyFrameAnimation(0F, false);
                // Frame 19.
                result.InsertKeyFrame(0.0316666663F, true);
                return result;
            }

            // PreComp layer: Crescent
            BooleanKeyFrameAnimation IsVisibleBooleanAnimation_3()
            {
                // Frame 0.
                var result = CreateBooleanKeyFrameAnimation(0F, true);
                // Frame 60.
                result.InsertKeyFrame(0.100000001F, false);
                return result;
            }

            // PreComp layer: Crescent
            BooleanKeyFrameAnimation IsVisibleBooleanAnimation_4()
            {
                // Frame 0.
                var result = CreateBooleanKeyFrameAnimation(0F, false);
                // Frame 60.
                result.InsertKeyFrame(0.100000001F, true);
                // Frame 165.
                result.InsertKeyFrame(0.275000006F, false);
                return result;
            }

            BooleanKeyFrameAnimation IsVisibleBooleanAnimation_5()
            {
                // Frame 0.
                if (_isVisibleBooleanAnimation_5 != null) { return _isVisibleBooleanAnimation_5; }
                var result = _isVisibleBooleanAnimation_5 = CreateBooleanKeyFrameAnimation(0F, false);
                // Frame 165.
                result.InsertKeyFrame(0.275000006F, true);
                return result;
            }

            BooleanKeyFrameAnimation IsVisibleBooleanAnimation_6()
            {
                // Frame 0.
                if (_isVisibleBooleanAnimation_6 != null) { return _isVisibleBooleanAnimation_6; }
                var result = _isVisibleBooleanAnimation_6 = CreateBooleanKeyFrameAnimation(0F, false);
                // Frame 60.
                result.InsertKeyFrame(0.100000001F, true);
                return result;
            }

            BooleanKeyFrameAnimation IsVisibleBooleanAnimation_7()
            {
                // Frame 0.
                if (_isVisibleBooleanAnimation_7 != null) { return _isVisibleBooleanAnimation_7; }
                var result = _isVisibleBooleanAnimation_7 = CreateBooleanKeyFrameAnimation(0F, false);
                // Frame 339.
                result.InsertKeyFrame(0.564999998F, true);
                return result;
            }

            // Layer aggregator
            BooleanKeyFrameAnimation IsVisibleBooleanAnimation_8()
            {
                // Frame 0.
                var result = CreateBooleanKeyFrameAnimation(0F, true);
                // Frame 570.
                result.InsertKeyFrame(0.949999988F, false);
                return result;
            }

            CanvasGeometry Geometry_00()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(207F, -364F));
                    builder.AddLine(new Vector2(99F, -348F));
                    builder.AddCubicBezier(new Vector2(99F, -348F), new Vector2(50F, -341F), new Vector2(54F, -272F));
                    builder.AddCubicBezier(new Vector2(58F, -203F), new Vector2(48F, -183F), new Vector2(48F, -183F));
                    builder.AddLine(new Vector2(114F, -114F));
                    builder.AddLine(new Vector2(137F, -142F));
                    builder.AddCubicBezier(new Vector2(137F, -142F), new Vector2(144F, -188F), new Vector2(163F, -213F));
                    builder.AddCubicBezier(new Vector2(182F, -238F), new Vector2(209F, -259F), new Vector2(209F, -259F));
                    builder.AddCubicBezier(new Vector2(209F, -259F), new Vector2(267F, -280F), new Vector2(280F, -283F));
                    builder.AddCubicBezier(new Vector2(293F, -286F), new Vector2(313F, -294F), new Vector2(313F, -294F));
                    builder.AddLine(new Vector2(291F, -340F));
                    builder.AddLine(new Vector2(207F, -364F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_01()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-165F, -322F));
                    builder.AddCubicBezier(new Vector2(-197.438995F, -309.411987F), new Vector2(-195.628998F, -259.214996F), new Vector2(-206F, -226F));
                    builder.AddCubicBezier(new Vector2(-220.386993F, -179.923996F), new Vector2(-235.104004F, -133.113007F), new Vector2(-239F, -85F));
                    builder.AddCubicBezier(new Vector2(-242.856995F, -37.3730011F), new Vector2(-240.511993F, 11.6239996F), new Vector2(-229F, 58F));
                    builder.AddCubicBezier(new Vector2(-216.841995F, 106.978996F), new Vector2(-194.149994F, 153.248001F), new Vector2(-169F, 197F));
                    builder.AddCubicBezier(new Vector2(-137.522003F, 251.759995F), new Vector2(-100.507004F, 303.536011F), new Vector2(-60F, 352F));
                    builder.AddCubicBezier(new Vector2(-44.007F, 371.134003F), new Vector2(-25.9139996F, 396.916992F), new Vector2(-1F, 398F));
                    builder.AddCubicBezier(new Vector2(57.1940002F, 400.529999F), new Vector2(115.171997F, 381.669006F), new Vector2(170F, 362F));
                    builder.AddCubicBezier(new Vector2(221.235001F, 343.621002F), new Vector2(273.760986F, 321.654999F), new Vector2(314F, 285F));
                    builder.AddCubicBezier(new Vector2(358.433014F, 244.524994F), new Vector2(393.769012F, 192.432999F), new Vector2(417F, 137F));
                    builder.AddCubicBezier(new Vector2(428.984985F, 108.403F), new Vector2(431.816986F, 70.0500031F), new Vector2(415F, 44F));
                    builder.AddCubicBezier(new Vector2(405.786987F, 29.7290001F), new Vector2(380.790985F, 49.6469994F), new Vector2(366F, 58F));
                    builder.AddCubicBezier(new Vector2(351.627014F, 66.1159973F), new Vector2(345.764008F, 87.1080017F), new Vector2(330F, 92F));
                    builder.AddCubicBezier(new Vector2(295.122986F, 102.823997F), new Vector2(257.506989F, 102.095001F), new Vector2(221F, 103F));
                    builder.AddCubicBezier(new Vector2(176.649002F, 104.099998F), new Vector2(131.018997F, 108.845001F), new Vector2(88F, 98F));
                    builder.AddCubicBezier(new Vector2(49.6879997F, 88.3410034F), new Vector2(12.4700003F, 69.3180008F), new Vector2(-17F, 43F));
                    builder.AddCubicBezier(new Vector2(-51.5110016F, 12.1800003F), new Vector2(-81.3170013F, -26.2420006F), new Vector2(-99F, -69F));
                    builder.AddCubicBezier(new Vector2(-122.862999F, -126.700996F), new Vector2(-123.238998F, -191.582001F), new Vector2(-139F, -252F));
                    builder.AddCubicBezier(new Vector2(-145.283005F, -276.084991F), new Vector2(-141.794998F, -331.005005F), new Vector2(-165F, -322F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_02()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(544.5F, -77.5F));
                    builder.AddCubicBezier(new Vector2(520.90802F, -211.578995F), new Vector2(263.60199F, 7.80499983F), new Vector2(137.5F, -43.5F));
                    builder.AddCubicBezier(new Vector2(-5.29500008F, -101.596001F), new Vector2(-32.3919983F, -356.510986F), new Vector2(-185.5F, -374.5F));
                    builder.AddCubicBezier(new Vector2(-297.52301F, -387.661987F), new Vector2(-392.670013F, -218.362F), new Vector2(-390.889008F, -105.582001F));
                    builder.AddCubicBezier(new Vector2(-387.753998F, 92.9160004F), new Vector2(-312.131989F, 307.382996F), new Vector2(-172.5F, 448.5F));
                    builder.AddCubicBezier(new Vector2(-74.2409973F, 547.804016F), new Vector2(125.860001F, 613.133972F), new Vector2(238.5F, 530.5F));
                    builder.AddCubicBezier(new Vector2(421.438995F, 396.294006F), new Vector2(583.81897F, 145.953995F), new Vector2(544.5F, -77.5F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - Path
            CanvasGeometry Geometry_03()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(520.651001F, -145.072006F));
                    builder.AddCubicBezier(new Vector2(491.152008F, -194.589996F), new Vector2(395.011993F, -110.815002F), new Vector2(371.022003F, -58.4059982F));
                    builder.AddCubicBezier(new Vector2(356.339996F, -26.3320007F), new Vector2(447.285004F, -20.5300007F), new Vector2(447.493988F, 14.7449999F));
                    builder.AddCubicBezier(new Vector2(448.279999F, 147.369995F), new Vector2(476.283997F, 321.585999F), new Vector2(373.773987F, 405.738007F));
                    builder.AddCubicBezier(new Vector2(225.350998F, 527.580994F), new Vector2(-361.98999F, 455.973999F), new Vector2(-187.406006F, 535.947021F));
                    builder.AddCubicBezier(new Vector2(97.0429993F, 666.247009F), new Vector2(524.763F, 795.085022F), new Vector2(750.260986F, 578.197998F));
                    builder.AddCubicBezier(new Vector2(932.56897F, 402.85199F), new Vector2(650.106995F, 72.237999F), new Vector2(520.651001F, -145.072006F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_04()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-123F, -405F));
                    builder.AddLine(new Vector2(-182F, -393F));
                    builder.AddLine(new Vector2(-307F, -297F));
                    builder.AddCubicBezier(new Vector2(-307F, -297F), new Vector2(-327F, -258F), new Vector2(-340F, -219F));
                    builder.AddCubicBezier(new Vector2(-353F, -180F), new Vector2(-395F, -105F), new Vector2(-395F, -102F));
                    builder.AddCubicBezier(new Vector2(-395F, -99F), new Vector2(-424F, 9F), new Vector2(-397F, 72F));
                    builder.AddCubicBezier(new Vector2(-370F, 135F), new Vector2(-352F, 190F), new Vector2(-352F, 193F));
                    builder.AddCubicBezier(new Vector2(-352F, 196F), new Vector2(-261F, 279F), new Vector2(-261F, 279F));
                    builder.AddCubicBezier(new Vector2(-261F, 279F), new Vector2(-141F, 350F), new Vector2(-138F, 352F));
                    builder.AddCubicBezier(new Vector2(-135F, 354F), new Vector2(-54F, 382F), new Vector2(-54F, 382F));
                    builder.AddLine(new Vector2(-68F, 345F));
                    builder.AddCubicBezier(new Vector2(-68F, 345F), new Vector2(-102F, 304F), new Vector2(-124F, 276F));
                    builder.AddCubicBezier(new Vector2(-146F, 248F), new Vector2(-211F, 116F), new Vector2(-211F, 113F));
                    builder.AddCubicBezier(new Vector2(-211F, 110F), new Vector2(-235F, -15F), new Vector2(-235F, -15F));
                    builder.AddLine(new Vector2(-248F, -102F));
                    builder.AddCubicBezier(new Vector2(-248F, -102F), new Vector2(-227F, -175F), new Vector2(-217F, -211F));
                    builder.AddCubicBezier(new Vector2(-207F, -247F), new Vector2(-181F, -294F), new Vector2(-181F, -294F));
                    builder.AddLine(new Vector2(-136F, -372F));
                    builder.AddLine(new Vector2(-123F, -405F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_05()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(-72F, -509.5F));
                    builder.AddCubicBezier(new Vector2(-72F, -509.5F), new Vector2(-461.703003F, -534.575989F), new Vector2(-461.703003F, -534.575989F));
                    builder.AddCubicBezier(new Vector2(-461.703003F, -534.575989F), new Vector2(-441.571014F, -465.643005F), new Vector2(-441.571014F, -465.643005F));
                    builder.AddCubicBezier(new Vector2(-441.571014F, -465.643005F), new Vector2(-145.356995F, -462.356995F), new Vector2(-102.857002F, -435.643005F));
                    builder.AddCubicBezier(new Vector2(-60.4370003F, -408.835999F), new Vector2(-83F, -352.5F), new Vector2(-83F, -352.5F));
                    builder.AddCubicBezier(new Vector2(-83F, -352.5F), new Vector2(-58.6269989F, -394.282013F), new Vector2(-58.6269989F, -394.282013F));
                    builder.AddCubicBezier(new Vector2(-58.6269989F, -394.282013F), new Vector2(-13F, -472.5F), new Vector2(-13F, -472.5F));
                    builder.AddCubicBezier(new Vector2(-13F, -472.5F), new Vector2(-72F, -509.5F), new Vector2(-72F, -509.5F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - Path
            CanvasGeometry Geometry_06()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(-140F, -449.5F));
                    builder.AddCubicBezier(new Vector2(-140F, -449.5F), new Vector2(-511.623993F, -323.559998F), new Vector2(-511.623993F, -323.559998F));
                    builder.AddCubicBezier(new Vector2(-511.623993F, -323.559998F), new Vector2(-459F, 459.5F), new Vector2(-459F, 459.5F));
                    builder.AddCubicBezier(new Vector2(-459F, 459.5F), new Vector2(-105F, 428F), new Vector2(-69F, 386F));
                    builder.AddCubicBezier(new Vector2(-34.1170006F, 345.303986F), new Vector2(-151F, -292.5F), new Vector2(-151F, -292.5F));
                    builder.AddCubicBezier(new Vector2(-151F, -292.5F), new Vector2(-126.626999F, -334.282013F), new Vector2(-126.626999F, -334.282013F));
                    builder.AddCubicBezier(new Vector2(-126.626999F, -334.282013F), new Vector2(-81F, -412.5F), new Vector2(-81F, -412.5F));
                    builder.AddCubicBezier(new Vector2(-81F, -412.5F), new Vector2(-140F, -449.5F), new Vector2(-140F, -449.5F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_07()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(173.231003F, 18.2870007F));
                    builder.AddCubicBezier(new Vector2(173.231003F, 18.2870007F), new Vector2(170.276993F, 14.5869999F), new Vector2(169.459F, 13.665F));
                    builder.AddCubicBezier(new Vector2(149.216003F, -9.13899994F), new Vector2(119.681F, -23.5090008F), new Vector2(86.7900009F, -23.5090008F));
                    builder.AddCubicBezier(new Vector2(25.7590008F, -23.5090008F), new Vector2(-23.7159996F, 25.9659996F), new Vector2(-23.7159996F, 86.9970016F));
                    builder.AddCubicBezier(new Vector2(-23.7159996F, 120.623001F), new Vector2(-8.69699955F, 150.740997F), new Vector2(14.9980001F, 171.009003F));
                    builder.AddCubicBezier(new Vector2(15.3710003F, 171.326996F), new Vector2(16.1219997F, 171.957001F), new Vector2(16.1219997F, 171.957001F));
                    builder.AddCubicBezier(new Vector2(16.1219997F, 171.957001F), new Vector2(16.8110008F, 172.632004F), new Vector2(17.5119991F, 173.222F));
                    builder.AddCubicBezier(new Vector2(17.5119991F, 173.222F), new Vector2(16.3999996F, 173.328003F), new Vector2(15.8430004F, 173.378006F));
                    builder.AddCubicBezier(new Vector2(10.6870003F, 173.837006F), new Vector2(5.46600008F, 174.072006F), new Vector2(0.189999998F, 174.072006F));
                    builder.AddCubicBezier(new Vector2(-96.0780029F, 174.072006F), new Vector2(-174.117996F, 96.0319977F), new Vector2(-174.117996F, -0.236000001F));
                    builder.AddCubicBezier(new Vector2(-174.117996F, -96.5039978F), new Vector2(-96.0780029F, -174.544006F), new Vector2(0.189999998F, -174.544006F));
                    builder.AddCubicBezier(new Vector2(96.4580002F, -174.544006F), new Vector2(174.498001F, -96.5039978F), new Vector2(174.498001F, -0.236000001F));
                    builder.AddCubicBezier(new Vector2(174.498001F, 5.25299978F), new Vector2(173.231003F, 18.2870007F), new Vector2(173.231003F, 18.2870007F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_08()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(173.731003F, 18.7870007F));
                    builder.AddCubicBezier(new Vector2(173.731003F, 18.7870007F), new Vector2(173.847F, 18.7110004F), new Vector2(173.414993F, 18.1539993F));
                    builder.AddCubicBezier(new Vector2(172.983002F, 17.5970001F), new Vector2(172.367996F, 16.9720001F), new Vector2(172.367996F, 16.9720001F));
                    builder.AddCubicBezier(new Vector2(172.367996F, 16.9720001F), new Vector2(170.776993F, 15.0869999F), new Vector2(169.959F, 14.165F));
                    builder.AddCubicBezier(new Vector2(149.716003F, -8.63899994F), new Vector2(120.181F, -23.0090008F), new Vector2(87.2900009F, -23.0090008F));
                    builder.AddCubicBezier(new Vector2(26.2590008F, -23.0090008F), new Vector2(-23.2159996F, 26.4659996F), new Vector2(-23.2159996F, 87.4970016F));
                    builder.AddCubicBezier(new Vector2(-23.2159996F, 121.123001F), new Vector2(-8.19699955F, 151.240997F), new Vector2(15.4980001F, 171.509003F));
                    builder.AddCubicBezier(new Vector2(15.8710003F, 171.826996F), new Vector2(16.6219997F, 172.457001F), new Vector2(16.6219997F, 172.457001F));
                    builder.AddCubicBezier(new Vector2(16.6219997F, 172.457001F), new Vector2(17.3110008F, 173.132004F), new Vector2(18.0119991F, 173.722F));
                    builder.AddCubicBezier(new Vector2(18.0119991F, 173.722F), new Vector2(16.8999996F, 173.828003F), new Vector2(16.3430004F, 173.878006F));
                    builder.AddCubicBezier(new Vector2(11.1870003F, 174.337006F), new Vector2(5.96600008F, 174.572006F), new Vector2(0.689999998F, 174.572006F));
                    builder.AddCubicBezier(new Vector2(-95.5780029F, 174.572006F), new Vector2(-173.617996F, 96.5319977F), new Vector2(-173.617996F, 0.263999999F));
                    builder.AddCubicBezier(new Vector2(-173.617996F, -96.0039978F), new Vector2(-95.5780029F, -174.044006F), new Vector2(0.689999998F, -174.044006F));
                    builder.AddCubicBezier(new Vector2(96.9580002F, -174.044006F), new Vector2(174.998001F, -96.0039978F), new Vector2(174.998001F, 0.263999999F));
                    builder.AddCubicBezier(new Vector2(174.998001F, 5.75299978F), new Vector2(174.744003F, 11.1820002F), new Vector2(174.248001F, 16.5410004F));
                    builder.AddCubicBezier(new Vector2(174.201004F, 17.0489998F), new Vector2(174.100006F, 18.0610008F), new Vector2(174.100006F, 18.0610008F));
                    builder.AddLine(new Vector2(173.731003F, 18.7870007F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_09()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(443.60199F, -36.7270012F));
                    builder.AddCubicBezier(new Vector2(443.799988F, -34.3030014F), new Vector2(443.97699F, -31.8740005F), new Vector2(444.135986F, -29.4389992F));
                    builder.AddCubicBezier(new Vector2(444.768005F, -19.7369995F), new Vector2(445.088989F, -9.95100021F), new Vector2(445.088989F, -0.0890000015F));
                    builder.AddCubicBezier(new Vector2(445.088989F, 245.695007F), new Vector2(245.841003F, 444.941986F), new Vector2(0.057F, 444.941986F));
                    builder.AddCubicBezier(new Vector2(-245.727005F, 444.941986F), new Vector2(-444.973999F, 245.695007F), new Vector2(-444.973999F, -0.0890000015F));
                    builder.AddCubicBezier(new Vector2(-444.973999F, -232.796997F), new Vector2(-266.364014F, -423.787994F), new Vector2(-38.7540016F, -443.451996F));
                    builder.AddCubicBezier(new Vector2(-33.0839996F, -443.941986F), new Vector2(-20.6819992F, -444.803986F), new Vector2(-20.6819992F, -444.803986F));
                    builder.AddCubicBezier(new Vector2(-20.6819992F, -444.803986F), new Vector2(-135.621002F, -375.77301F), new Vector2(-135.621002F, -202.735992F));
                    builder.AddCubicBezier(new Vector2(-135.621002F, -29.6989994F), new Vector2(4.65299988F, 110.574997F), new Vector2(177.690002F, 110.574997F));
                    builder.AddCubicBezier(new Vector2(278.885986F, 110.574997F), new Vector2(368.876007F, 62.5979996F), new Vector2(426.157013F, -11.8500004F));
                    builder.AddCubicBezier(new Vector2(429.790009F, -16.5709991F), new Vector2(434.002991F, -21.7889996F), new Vector2(437.173004F, -26.8449993F));
                    builder.AddCubicBezier(new Vector2(437.173004F, -26.8449993F), new Vector2(440.427002F, -31.9190006F), new Vector2(443.60199F, -36.7270012F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_10()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(444.10199F, -36.2270012F));
                    builder.AddCubicBezier(new Vector2(444.299988F, -33.8030014F), new Vector2(444.47699F, -31.3740005F), new Vector2(444.635986F, -28.9389992F));
                    builder.AddCubicBezier(new Vector2(445.268005F, -19.2369995F), new Vector2(445.588989F, -9.45100021F), new Vector2(445.588989F, 0.411000013F));
                    builder.AddCubicBezier(new Vector2(445.588989F, 246.195007F), new Vector2(246.341003F, 445.441986F), new Vector2(0.556999981F, 445.441986F));
                    builder.AddCubicBezier(new Vector2(-245.227005F, 445.441986F), new Vector2(-444.473999F, 246.195007F), new Vector2(-444.473999F, 0.411000013F));
                    builder.AddCubicBezier(new Vector2(-444.473999F, -232.296997F), new Vector2(-265.864014F, -423.287994F), new Vector2(-38.2540016F, -442.951996F));
                    builder.AddCubicBezier(new Vector2(-32.5839996F, -443.441986F), new Vector2(-20.1819992F, -444.303986F), new Vector2(-20.1819992F, -444.303986F));
                    builder.AddCubicBezier(new Vector2(-20.1819992F, -444.303986F), new Vector2(-135.121002F, -375.27301F), new Vector2(-135.121002F, -202.235992F));
                    builder.AddCubicBezier(new Vector2(-135.121002F, -29.1989994F), new Vector2(5.15299988F, 111.074997F), new Vector2(178.190002F, 111.074997F));
                    builder.AddCubicBezier(new Vector2(279.385986F, 111.074997F), new Vector2(369.376007F, 63.0979996F), new Vector2(426.657013F, -11.3500004F));
                    builder.AddCubicBezier(new Vector2(430.290009F, -16.0709991F), new Vector2(434.502991F, -21.2889996F), new Vector2(437.673004F, -26.3449993F));
                    builder.AddCubicBezier(new Vector2(437.673004F, -26.3449993F), new Vector2(440.927002F, -31.4190006F), new Vector2(444.10199F, -36.2270012F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_11()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(180.559998F, 401.828003F));
                    builder.AddCubicBezier(new Vector2(180.559998F, 401.828003F), new Vector2(168.653F, 401.692993F), new Vector2(153.007004F, 393.994995F));
                    builder.AddCubicBezier(new Vector2(143.610992F, 389.371002F), new Vector2(134.205002F, 384.316986F), new Vector2(124.843002F, 378.826996F));
                    builder.AddCubicBezier(new Vector2(17.2609997F, 315.746002F), new Vector2(-97.6100006F, 194.794006F), new Vector2(-114.246002F, 12.4989996F));
                    builder.AddCubicBezier(new Vector2(-131.690002F, -178.643005F), new Vector2(-63.8619995F, -294.923004F), new Vector2(-15.6359997F, -374.856995F));
                    builder.AddCubicBezier(new Vector2(6.5079999F, -411.559998F), new Vector2(14.625F, -433.006989F), new Vector2(14.625F, -433.006989F));
                    builder.EndFigure(CanvasFigureLoop.Open);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_12()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(15.125F, -432.506989F));
                    builder.AddLine(new Vector2(-66F, -392.5F));
                    builder.AddLine(new Vector2(-168F, -336.5F));
                    builder.AddLine(new Vector2(-235F, -259F));
                    builder.AddLine(new Vector2(-293F, -99F));
                    builder.AddCubicBezier(new Vector2(-293F, -99F), new Vector2(-306.5F, -29F), new Vector2(-311.5F, -9F));
                    builder.AddCubicBezier(new Vector2(-316.5F, 11F), new Vector2(-286F, 135F), new Vector2(-286F, 135F));
                    builder.AddLine(new Vector2(-182.5F, 278.5F));
                    builder.AddLine(new Vector2(-71.75F, 362.75F));
                    builder.AddLine(new Vector2(148.311996F, 407.562012F));
                    builder.AddLine(new Vector2(181.059998F, 402.328003F));
                    builder.AddCubicBezier(new Vector2(181.059998F, 402.328003F), new Vector2(169.153F, 402.192993F), new Vector2(153.507004F, 394.494995F));
                    builder.AddCubicBezier(new Vector2(144.110992F, 389.871002F), new Vector2(134.705002F, 384.816986F), new Vector2(125.343002F, 379.326996F));
                    builder.AddCubicBezier(new Vector2(17.7609997F, 316.246002F), new Vector2(-97.1100006F, 195.294006F), new Vector2(-113.746002F, 12.9989996F));
                    builder.AddCubicBezier(new Vector2(-131.190002F, -178.143005F), new Vector2(-63.3619995F, -294.423004F), new Vector2(-15.1359997F, -374.356995F));
                    builder.AddCubicBezier(new Vector2(7.0079999F, -411.059998F), new Vector2(15.125F, -432.506989F), new Vector2(15.125F, -432.506989F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_13()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(1089.46497F, 78.6149979F));
                    builder.AddCubicBezier(new Vector2(1017.22498F, 109.956001F), new Vector2(953.440002F, 178.020004F), new Vector2(930.471985F, 253.341995F));
                    builder.AddCubicBezier(new Vector2(910.903992F, 317.510986F), new Vector2(933.356995F, 397.852997F), new Vector2(976.434998F, 449.281006F));
                    builder.AddCubicBezier(new Vector2(1025.60803F, 507.984985F), new Vector2(1105.10205F, 546.937988F), new Vector2(1181.44299F, 552.95697F));
                    builder.AddCubicBezier(new Vector2(1226.24597F, 556.489014F), new Vector2(1258.05103F, 504.865997F), new Vector2(1290.77698F, 474.063995F));
                    builder.AddCubicBezier(new Vector2(1331.26697F, 435.954987F), new Vector2(1381.69299F, 400.277008F), new Vector2(1399.59497F, 347.634003F));
                    builder.AddCubicBezier(new Vector2(1414.22302F, 304.617004F), new Vector2(1406.099F, 249.720001F), new Vector2(1379.62195F, 212.796005F));
                    builder.AddCubicBezier(new Vector2(1346.57397F, 166.709F), new Vector2(1288.18396F, 144.285004F), new Vector2(1236.70996F, 120.481003F));
                    builder.AddCubicBezier(new Vector2(1190.39502F, 99.0630035F), new Vector2(1136.27698F, 58.3059998F), new Vector2(1089.46497F, 78.6149979F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Masks
            // - - - - Layer: Crescent
            // - Path
            CanvasGeometry Geometry_14()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(1348.52502F, 335.460999F));
                    builder.AddCubicBezier(new Vector2(1297.71106F, 399.363007F), new Vector2(1242.88098F, 460.045013F), new Vector2(1187.20398F, 519.757996F));
                    builder.AddCubicBezier(new Vector2(1172.58496F, 535.437012F), new Vector2(1141.69897F, 540.088013F), new Vector2(1138.03406F, 561.208984F));
                    builder.AddCubicBezier(new Vector2(1135.51599F, 575.721008F), new Vector2(1168.65002F, 560.255981F), new Vector2(1181.44299F, 552.95697F));
                    builder.AddCubicBezier(new Vector2(1220.479F, 530.685974F), new Vector2(1258.05005F, 504.865997F), new Vector2(1290.77698F, 474.063995F));
                    builder.AddCubicBezier(new Vector2(1331.26697F, 435.954987F), new Vector2(1381.69299F, 400.277008F), new Vector2(1399.59497F, 347.634003F));
                    builder.AddCubicBezier(new Vector2(1414.22302F, 304.617004F), new Vector2(1396.56799F, 254.955002F), new Vector2(1379.62195F, 212.796997F));
                    builder.AddCubicBezier(new Vector2(1370.677F, 190.542999F), new Vector2(1380.13196F, 261.299011F), new Vector2(1374.23804F, 284.548004F));
                    builder.AddCubicBezier(new Vector2(1369.56604F, 302.977997F), new Vector2(1360.35803F, 320.579987F), new Vector2(1348.52502F, 335.460999F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_15()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(875.450012F, 49.9220009F));
                    builder.AddCubicBezier(new Vector2(872.450012F, 49.9189987F), new Vector2(665.411987F, 90.7300034F), new Vector2(660.408997F, 93.7249985F));
                    builder.AddCubicBezier(new Vector2(655.406006F, 96.7200012F), new Vector2(491.312988F, 198.570007F), new Vector2(491.312988F, 198.570007F));
                    builder.AddCubicBezier(new Vector2(491.312988F, 198.570007F), new Vector2(455.082001F, 450.536987F), new Vector2(455.082001F, 450.536987F));
                    builder.AddCubicBezier(new Vector2(455.082001F, 450.536987F), new Vector2(499.929993F, 616.578003F), new Vector2(528.857971F, 694.604004F));
                    builder.AddCubicBezier(new Vector2(557.786011F, 772.630981F), new Vector2(616.656982F, 913.684998F), new Vector2(616.656982F, 913.684998F));
                    builder.AddCubicBezier(new Vector2(616.656982F, 913.684998F), new Vector2(846.572021F, 1006.896F), new Vector2(846.572021F, 1006.896F));
                    builder.AddCubicBezier(new Vector2(846.572021F, 1006.896F), new Vector2(1170.57996F, 998.192993F), new Vector2(1170.57996F, 998.192993F));
                    builder.AddCubicBezier(new Vector2(1170.57996F, 998.192993F), new Vector2(1305.64905F, 923.317017F), new Vector2(1395.79102F, 768.400024F));
                    builder.AddCubicBezier(new Vector2(1485.93298F, 613.482971F), new Vector2(1504.99402F, 546.5F), new Vector2(1504.99805F, 542.5F));
                    builder.AddCubicBezier(new Vector2(1505.00195F, 538.5F), new Vector2(1421.09204F, 440.423004F), new Vector2(1418.08997F, 442.420013F));
                    builder.AddCubicBezier(new Vector2(1415.08801F, 444.416992F), new Vector2(1301.98804F, 553.314026F), new Vector2(1301.98804F, 553.314026F));
                    builder.AddCubicBezier(new Vector2(1301.98804F, 553.314026F), new Vector2(1124.95105F, 593.151001F), new Vector2(1124.95105F, 593.151001F));
                    builder.AddCubicBezier(new Vector2(1124.95105F, 593.151001F), new Vector2(1022.974F, 569.057007F), new Vector2(945.049988F, 485.985992F));
                    builder.AddCubicBezier(new Vector2(867.125977F, 402.914001F), new Vector2(911.104004F, 425.954987F), new Vector2(876.20697F, 313.923004F));
                    builder.AddCubicBezier(new Vector2(841.309998F, 201.891006F), new Vector2(929.364014F, 142.972F), new Vector2(929.364014F, 142.972F));
                    builder.AddCubicBezier(new Vector2(929.364014F, 142.972F), new Vector2(985.450012F, 50.0229988F), new Vector2(985.450012F, 50.0229988F));
                    builder.AddCubicBezier(new Vector2(985.450012F, 50.0229988F), new Vector2(878.450012F, 49.9249992F), new Vector2(875.450012F, 49.9220009F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Masks
            // - - - - Layer: Crescent
            // - Path
            CanvasGeometry Geometry_16()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(899.703003F, 336.518005F));
                    builder.AddCubicBezier(new Vector2(880.221985F, 308.040009F), new Vector2(841.718994F, 265.925995F), new Vector2(787.952026F, 269.972992F));
                    builder.AddCubicBezier(new Vector2(698.690002F, 276.691986F), new Vector2(571.866028F, 361.194F), new Vector2(571.866028F, 361.194F));
                    builder.AddCubicBezier(new Vector2(571.866028F, 361.194F), new Vector2(441.135986F, 584.179016F), new Vector2(441.135986F, 584.179016F));
                    builder.AddCubicBezier(new Vector2(441.135986F, 584.179016F), new Vector2(507.656006F, 727.205017F), new Vector2(551.286011F, 796.84198F));
                    builder.AddCubicBezier(new Vector2(582.629028F, 846.869019F), new Vector2(665.575989F, 942.406982F), new Vector2(665.575989F, 942.406982F));
                    builder.AddCubicBezier(new Vector2(665.575989F, 942.406982F), new Vector2(922.164978F, 1022.44F), new Vector2(922.164978F, 1022.44F));
                    builder.AddCubicBezier(new Vector2(922.164978F, 1022.44F), new Vector2(1295.52698F, 931.799988F), new Vector2(1295.52698F, 931.799988F));
                    builder.AddCubicBezier(new Vector2(1295.52698F, 931.799988F), new Vector2(1412.47205F, 792.499023F), new Vector2(1455.42395F, 719.559021F));
                    builder.AddCubicBezier(new Vector2(1498.26904F, 646.801025F), new Vector2(1551.99902F, 572.788025F), new Vector2(1552.07202F, 496.143005F));
                    builder.AddCubicBezier(new Vector2(1552.104F, 461.924011F), new Vector2(1516.15295F, 412.864014F), new Vector2(1455.71594F, 408.959015F));
                    builder.AddCubicBezier(new Vector2(1396.08899F, 405.105988F), new Vector2(1330.67798F, 481.837006F), new Vector2(1330.67798F, 481.837006F));
                    builder.AddCubicBezier(new Vector2(1330.67798F, 481.837006F), new Vector2(1185.79895F, 551.000977F), new Vector2(1185.79895F, 551.000977F));
                    builder.AddCubicBezier(new Vector2(1185.79895F, 551.000977F), new Vector2(1123.21802F, 509.955994F), new Vector2(1079.44202F, 506.300995F));
                    builder.AddCubicBezier(new Vector2(1052.03101F, 504.011993F), new Vector2(1044.55005F, 543.208008F), new Vector2(1018.258F, 538.28302F));
                    builder.AddCubicBezier(new Vector2(987.940002F, 532.604004F), new Vector2(978.440002F, 487.382996F), new Vector2(978.440002F, 487.382996F));
                    builder.AddCubicBezier(new Vector2(978.440002F, 487.382996F), new Vector2(899.752014F, 433.402008F), new Vector2(899.752014F, 433.402008F));
                    builder.AddCubicBezier(new Vector2(899.752014F, 433.402008F), new Vector2(920.320984F, 366.65799F), new Vector2(899.703003F, 336.518005F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Masks
            // - - - - Layer: Crescent
            // - Path
            CanvasGeometry Geometry_17()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(1076.88696F, 585.47998F));
                    builder.AddCubicBezier(new Vector2(1033.08606F, 581.176025F), new Vector2(1023.54901F, 532.078979F), new Vector2(979.267029F, 534.343994F));
                    builder.AddCubicBezier(new Vector2(875.367004F, 539.65802F), new Vector2(692.695007F, 605.130981F), new Vector2(692.695007F, 605.130981F));
                    builder.AddCubicBezier(new Vector2(692.695007F, 605.130981F), new Vector2(420.216003F, 784.642029F), new Vector2(420.216003F, 784.642029F));
                    builder.AddCubicBezier(new Vector2(420.216003F, 784.642029F), new Vector2(510.05899F, 903.021973F), new Vector2(584.927979F, 950.198975F));
                    builder.AddCubicBezier(new Vector2(621.846008F, 973.461975F), new Vector2(738.953979F, 985.489014F), new Vector2(738.953979F, 985.489014F));
                    builder.AddCubicBezier(new Vector2(738.953979F, 985.489014F), new Vector2(1035.55603F, 1045.755F), new Vector2(1035.55603F, 1045.755F));
                    builder.AddCubicBezier(new Vector2(1035.55603F, 1045.755F), new Vector2(1482.94897F, 832.210022F), new Vector2(1482.94897F, 832.210022F));
                    builder.AddCubicBezier(new Vector2(1482.94897F, 832.210022F), new Vector2(1523.54895F, 708.195984F), new Vector2(1544.87305F, 646.297974F));
                    builder.AddCubicBezier(new Vector2(1570.12805F, 572.992004F), new Vector2(1631.16394F, 501.125F), new Vector2(1622.68506F, 426.608002F));
                    builder.AddCubicBezier(new Vector2(1619.20203F, 395.997009F), new Vector2(1563.27795F, 369.44101F), new Vector2(1512.15601F, 358.766998F));
                    builder.AddCubicBezier(new Vector2(1468.00598F, 349.548004F), new Vector2(1373.71497F, 374.622986F), new Vector2(1373.71497F, 374.622986F));
                    builder.AddCubicBezier(new Vector2(1373.71497F, 374.622986F), new Vector2(1277.07104F, 487.776001F), new Vector2(1277.07104F, 487.776001F));
                    builder.AddCubicBezier(new Vector2(1277.07104F, 487.776001F), new Vector2(1294.22095F, 522.205017F), new Vector2(1281.03003F, 536.77301F));
                    builder.AddCubicBezier(new Vector2(1274.47095F, 544.017029F), new Vector2(1252.21301F, 523.646973F), new Vector2(1240.37097F, 528.30603F));
                    builder.AddCubicBezier(new Vector2(1219.25195F, 536.61499F), new Vector2(1204.57202F, 566.856018F), new Vector2(1204.57202F, 566.856018F));
                    builder.AddCubicBezier(new Vector2(1204.57202F, 566.856018F), new Vector2(1193.85803F, 555.427979F), new Vector2(1193.85803F, 555.427979F));
                    builder.AddCubicBezier(new Vector2(1193.85803F, 555.427979F), new Vector2(1119.11206F, 589.629028F), new Vector2(1076.88696F, 585.47998F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Masks
            // - - - - Layer: Crescent
            // - Path
            CanvasGeometry Geometry_18()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(1167.099F, 569.278015F));
                    builder.AddCubicBezier(new Vector2(1121.29199F, 610.299011F), new Vector2(1170.95203F, 678.267029F), new Vector2(1106.81006F, 710.591003F));
                    builder.AddCubicBezier(new Vector2(1020.23798F, 754.218018F), new Vector2(773.247986F, 767.755005F), new Vector2(773.247986F, 767.755005F));
                    builder.AddCubicBezier(new Vector2(773.247986F, 767.755005F), new Vector2(406.269989F, 918.284973F), new Vector2(406.269989F, 918.284973F));
                    builder.AddCubicBezier(new Vector2(406.269989F, 918.284973F), new Vector2(512.421021F, 1028.57202F), new Vector2(607.356018F, 1052.43701F));
                    builder.AddCubicBezier(new Vector2(666.05603F, 1067.19299F), new Vector2(787.872986F, 1014.211F), new Vector2(787.872986F, 1014.211F));
                    builder.AddCubicBezier(new Vector2(787.872986F, 1014.211F), new Vector2(1111.14905F, 1061.29797F), new Vector2(1111.14905F, 1061.29797F));
                    builder.AddCubicBezier(new Vector2(1111.14905F, 1061.29797F), new Vector2(1607.89697F, 765.817017F), new Vector2(1607.89697F, 765.817017F));
                    builder.AddCubicBezier(new Vector2(1607.89697F, 765.817017F), new Vector2(1595.53894F, 653.353027F), new Vector2(1604.50696F, 597.45697F));
                    builder.AddCubicBezier(new Vector2(1616.23999F, 524.325989F), new Vector2(1684.43103F, 453.214996F), new Vector2(1669.76001F, 380.251007F));
                    builder.AddCubicBezier(new Vector2(1663.96399F, 351.425995F), new Vector2(1595.67505F, 338.540009F), new Vector2(1549.78406F, 325.304993F));
                    builder.AddCubicBezier(new Vector2(1504.53406F, 312.255005F), new Vector2(1402.40503F, 303.145996F), new Vector2(1402.40503F, 303.145996F));
                    builder.AddCubicBezier(new Vector2(1402.40503F, 303.145996F), new Vector2(1337.91797F, 445.626007F), new Vector2(1337.91797F, 445.626007F));
                    builder.AddCubicBezier(new Vector2(1337.91797F, 445.626007F), new Vector2(1346.18201F, 471.087006F), new Vector2(1345.48206F, 483.927002F));
                    builder.AddCubicBezier(new Vector2(1344.83496F, 495.791992F), new Vector2(1338.552F, 507.358002F), new Vector2(1333.922F, 518.939026F));
                    builder.AddCubicBezier(new Vector2(1333.745F, 519.380005F), new Vector2(1331.69202F, 518.419006F), new Vector2(1331.69202F, 518.419006F));
                    builder.AddCubicBezier(new Vector2(1331.69202F, 518.419006F), new Vector2(1287.97498F, 548.356995F), new Vector2(1287.97498F, 548.356995F));
                    builder.AddCubicBezier(new Vector2(1287.97498F, 548.356995F), new Vector2(1189.52197F, 549.197998F), new Vector2(1167.099F, 569.278015F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Masks
            // - - - - Layer: Crescent
            // - Path
            CanvasGeometry Geometry_19()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(1342.32397F, 953.036987F));
                    builder.AddCubicBezier(new Vector2(1339.32397F, 953.033997F), new Vector2(1303.12805F, 971.968018F), new Vector2(1298.125F, 974.963013F));
                    builder.AddCubicBezier(new Vector2(1293.12195F, 977.958984F), new Vector2(894.077026F, 1011.69098F), new Vector2(894.077026F, 1011.69098F));
                    builder.AddCubicBezier(new Vector2(894.077026F, 1011.69098F), new Vector2(385.351013F, 1118.74805F), new Vector2(385.351013F, 1118.74805F));
                    builder.AddCubicBezier(new Vector2(385.351013F, 1118.74805F), new Vector2(612.070007F, 1127.76794F), new Vector2(640.997986F, 1205.79395F));
                    builder.AddCubicBezier(new Vector2(669.927002F, 1283.82104F), new Vector2(861.252014F, 1057.29395F), new Vector2(861.252014F, 1057.29395F));
                    builder.AddCubicBezier(new Vector2(861.252014F, 1057.29395F), new Vector2(1224.54004F, 1084.61401F), new Vector2(1224.54004F, 1084.61401F));
                    builder.AddCubicBezier(new Vector2(1224.54004F, 1084.61401F), new Vector2(1795.31897F, 666.22699F), new Vector2(1795.31897F, 666.22699F));
                    builder.AddCubicBezier(new Vector2(1795.31897F, 666.22699F), new Vector2(1603.81396F, 679.112976F), new Vector2(1693.95605F, 524.195984F));
                    builder.AddCubicBezier(new Vector2(1784.09802F, 369.278992F), new Vector2(1740.36804F, 314.714996F), new Vector2(1740.37195F, 310.714996F));
                    builder.AddCubicBezier(new Vector2(1740.37598F, 306.714996F), new Vector2(1609.22498F, 273.115997F), new Vector2(1606.22302F, 275.113007F));
                    builder.AddCubicBezier(new Vector2(1603.22095F, 277.109985F), new Vector2(1445.44104F, 195.932007F), new Vector2(1445.44104F, 195.932007F));
                    builder.AddCubicBezier(new Vector2(1445.44104F, 195.932007F), new Vector2(1429.18994F, 382.399994F), new Vector2(1429.18994F, 382.399994F));
                    builder.AddCubicBezier(new Vector2(1429.18994F, 382.399994F), new Vector2(1617.41895F, 473.661011F), new Vector2(1617.01001F, 587.559021F));
                    builder.AddCubicBezier(new Vector2(1616.65698F, 686.021973F), new Vector2(1633.87F, 629.075012F), new Vector2(1604.53503F, 742.690002F));
                    builder.AddCubicBezier(new Vector2(1581.29395F, 832.700989F), new Vector2(1521.78003F, 802.73999F), new Vector2(1521.78003F, 802.73999F));
                    builder.AddCubicBezier(new Vector2(1521.78003F, 802.73999F), new Vector2(1368.26599F, 870.83197F), new Vector2(1368.26599F, 870.83197F));
                    builder.AddCubicBezier(new Vector2(1368.26599F, 870.83197F), new Vector2(1345.32397F, 953.039978F), new Vector2(1342.32397F, 953.036987F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - Layer: CrescentColor
            // - Path
            CanvasGeometry Geometry_20()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(1348.52502F, 335.460999F));
                    builder.AddCubicBezier(new Vector2(1322.54797F, 383.390015F), new Vector2(1306.32104F, 438.579987F), new Vector2(1269.14294F, 478.453003F));
                    builder.AddCubicBezier(new Vector2(1233.89905F, 516.252014F), new Vector2(1177.42004F, 527.749023F), new Vector2(1138.03406F, 561.208984F));
                    builder.AddCubicBezier(new Vector2(1126.80896F, 570.744995F), new Vector2(1168.65002F, 560.255981F), new Vector2(1181.44299F, 552.95697F));
                    builder.AddCubicBezier(new Vector2(1220.479F, 530.685974F), new Vector2(1258.05005F, 504.86499F), new Vector2(1290.77698F, 474.062988F));
                    builder.AddCubicBezier(new Vector2(1331.26697F, 435.95401F), new Vector2(1381.69397F, 400.277008F), new Vector2(1399.59497F, 347.634003F));
                    builder.AddCubicBezier(new Vector2(1414.22302F, 304.617004F), new Vector2(1396.56799F, 254.955002F), new Vector2(1379.62195F, 212.796997F));
                    builder.AddCubicBezier(new Vector2(1370.677F, 190.542999F), new Vector2(1380.13196F, 261.299011F), new Vector2(1374.23804F, 284.548004F));
                    builder.AddCubicBezier(new Vector2(1369.56604F, 302.977997F), new Vector2(1357.58496F, 318.746002F), new Vector2(1348.52502F, 335.460999F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_21()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(875.450012F, 49.9220009F));
                    builder.AddCubicBezier(new Vector2(802.950012F, 59.6699982F), new Vector2(728.630981F, 67.3249969F), new Vector2(660.408997F, 93.7249985F));
                    builder.AddCubicBezier(new Vector2(598.557983F, 117.658997F), new Vector2(524.390991F, 141.087006F), new Vector2(491.312988F, 198.570007F));
                    builder.AddCubicBezier(new Vector2(448.990997F, 272.11499F), new Vector2(448.678009F, 365.925995F), new Vector2(455.082001F, 450.536987F));
                    builder.AddCubicBezier(new Vector2(461.497009F, 535.286011F), new Vector2(500.862F, 614.356018F), new Vector2(528.857971F, 694.604004F));
                    builder.AddCubicBezier(new Vector2(554.771973F, 768.885986F), new Vector2(560.549988F, 858.536011F), new Vector2(616.656982F, 913.684998F));
                    builder.AddCubicBezier(new Vector2(675.633972F, 971.655029F), new Vector2(764.820984F, 994.424011F), new Vector2(846.572021F, 1006.896F));
                    builder.AddCubicBezier(new Vector2(953.377991F, 1023.19098F), new Vector2(1071.479F, 1041.22705F), new Vector2(1170.57996F, 998.192993F));
                    builder.AddCubicBezier(new Vector2(1268.95605F, 955.473999F), new Vector2(1332.33704F, 854.866028F), new Vector2(1395.79102F, 768.400024F));
                    builder.AddCubicBezier(new Vector2(1445.27502F, 700.971008F), new Vector2(1499.29004F, 625.942017F), new Vector2(1504.99805F, 542.5F));
                    builder.AddCubicBezier(new Vector2(1508.01294F, 498.420013F), new Vector2(1462.20996F, 440.070007F), new Vector2(1418.08997F, 442.420013F));
                    builder.AddCubicBezier(new Vector2(1364.64795F, 445.266998F), new Vector2(1349.58203F, 528.841003F), new Vector2(1301.98804F, 553.314026F));
                    builder.AddCubicBezier(new Vector2(1248.19495F, 580.973999F), new Vector2(1184.39099F, 604.362976F), new Vector2(1124.95105F, 593.151001F));
                    builder.AddCubicBezier(new Vector2(1056.35999F, 580.213013F), new Vector2(991.479004F, 538.10498F), new Vector2(945.049988F, 485.985992F));
                    builder.AddCubicBezier(new Vector2(903.958984F, 439.859009F), new Vector2(879.028992F, 375.632996F), new Vector2(876.20697F, 313.923004F));
                    builder.AddCubicBezier(new Vector2(873.481018F, 254.309998F), new Vector2(906.539978F, 198.110001F), new Vector2(929.364014F, 142.972F));
                    builder.AddCubicBezier(new Vector2(943.203979F, 109.537003F), new Vector2(1003.59198F, 81.3330002F), new Vector2(985.450012F, 50.0229988F));
                    builder.AddCubicBezier(new Vector2(967.067017F, 18.2970009F), new Vector2(911.789978F, 45.0359993F), new Vector2(875.450012F, 49.9220009F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - Layer: CrescentColor
            // - Path
            CanvasGeometry Geometry_22()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(899.703003F, 336.518005F));
                    builder.AddCubicBezier(new Vector2(875.223999F, 300.734985F), new Vector2(831.184021F, 266.718994F), new Vector2(787.952026F, 269.972992F));
                    builder.AddCubicBezier(new Vector2(709.987976F, 275.841003F), new Vector2(571.866028F, 361.194F), new Vector2(571.866028F, 361.194F));
                    builder.AddCubicBezier(new Vector2(571.866028F, 361.194F), new Vector2(441.135986F, 584.179016F), new Vector2(441.135986F, 584.179016F));
                    builder.AddCubicBezier(new Vector2(441.135986F, 584.179016F), new Vector2(508.901001F, 729.190979F), new Vector2(551.286011F, 796.84198F));
                    builder.AddCubicBezier(new Vector2(584.039001F, 849.119019F), new Vector2(665.575989F, 942.406982F), new Vector2(665.575989F, 942.406982F));
                    builder.AddCubicBezier(new Vector2(665.575989F, 942.406982F), new Vector2(922.164978F, 1022.44F), new Vector2(922.164978F, 1022.44F));
                    builder.AddCubicBezier(new Vector2(922.164978F, 1022.44F), new Vector2(1295.52698F, 931.799988F), new Vector2(1295.52698F, 931.799988F));
                    builder.AddCubicBezier(new Vector2(1295.52698F, 931.799988F), new Vector2(1410.47803F, 795.88501F), new Vector2(1455.42395F, 719.559021F));
                    builder.AddCubicBezier(new Vector2(1496.59705F, 649.640015F), new Vector2(1551.99597F, 577.284973F), new Vector2(1552.07202F, 496.143005F));
                    builder.AddCubicBezier(new Vector2(1552.11304F, 452.828003F), new Vector2(1498.94104F, 411.752014F), new Vector2(1455.71594F, 408.959015F));
                    builder.AddCubicBezier(new Vector2(1407.57397F, 405.847992F), new Vector2(1330.67798F, 481.837006F), new Vector2(1330.67798F, 481.837006F));
                    builder.AddCubicBezier(new Vector2(1330.67798F, 481.837006F), new Vector2(1185.79895F, 551.000977F), new Vector2(1185.79895F, 551.000977F));
                    builder.AddCubicBezier(new Vector2(1185.79895F, 551.000977F), new Vector2(1117.78796F, 509.212006F), new Vector2(1079.44202F, 506.300995F));
                    builder.AddCubicBezier(new Vector2(1056.495F, 504.55899F), new Vector2(1040.87695F, 542.52002F), new Vector2(1018.258F, 538.28302F));
                    builder.AddCubicBezier(new Vector2(997.085022F, 534.317017F), new Vector2(978.440002F, 487.382996F), new Vector2(978.440002F, 487.382996F));
                    builder.AddCubicBezier(new Vector2(978.440002F, 487.382996F), new Vector2(899.752014F, 433.402008F), new Vector2(899.752014F, 433.402008F));
                    builder.AddCubicBezier(new Vector2(899.752014F, 433.402008F), new Vector2(917.937012F, 363.171997F), new Vector2(899.703003F, 336.518005F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - Layer: CrescentColor
            // - Path
            CanvasGeometry Geometry_23()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(1076.88696F, 585.47998F));
                    builder.AddCubicBezier(new Vector2(1040.32898F, 581.888F), new Vector2(1015.953F, 532.468018F), new Vector2(979.267029F, 534.343994F));
                    builder.AddCubicBezier(new Vector2(881F, 539.369995F), new Vector2(692.695007F, 605.130981F), new Vector2(692.695007F, 605.130981F));
                    builder.AddCubicBezier(new Vector2(692.695007F, 605.130981F), new Vector2(420.216003F, 784.642029F), new Vector2(420.216003F, 784.642029F));
                    builder.AddCubicBezier(new Vector2(420.216003F, 784.642029F), new Vector2(519.067993F, 908.697998F), new Vector2(584.927979F, 950.198975F));
                    builder.AddCubicBezier(new Vector2(629.491028F, 978.280029F), new Vector2(738.953979F, 985.489014F), new Vector2(738.953979F, 985.489014F));
                    builder.AddCubicBezier(new Vector2(738.953979F, 985.489014F), new Vector2(1035.55603F, 1045.755F), new Vector2(1035.55603F, 1045.755F));
                    builder.AddCubicBezier(new Vector2(1035.55603F, 1045.755F), new Vector2(1482.94897F, 832.210022F), new Vector2(1482.94897F, 832.210022F));
                    builder.AddCubicBezier(new Vector2(1482.94897F, 832.210022F), new Vector2(1523.59802F, 708.054016F), new Vector2(1544.87402F, 646.297974F));
                    builder.AddCubicBezier(new Vector2(1570.17896F, 572.846985F), new Vector2(1631.46802F, 503.798004F), new Vector2(1622.68506F, 426.608002F));
                    builder.AddCubicBezier(new Vector2(1617.79797F, 383.656006F), new Vector2(1554.47302F, 367.602997F), new Vector2(1512.15601F, 358.766998F));
                    builder.AddCubicBezier(new Vector2(1466.68799F, 349.27301F), new Vector2(1373.71497F, 374.622986F), new Vector2(1373.71497F, 374.622986F));
                    builder.AddCubicBezier(new Vector2(1373.71497F, 374.622986F), new Vector2(1277.07104F, 487.776001F), new Vector2(1277.07104F, 487.776001F));
                    builder.AddCubicBezier(new Vector2(1277.07104F, 487.776001F), new Vector2(1292.02795F, 524.627014F), new Vector2(1281.03003F, 536.77301F));
                    builder.AddCubicBezier(new Vector2(1271.73804F, 547.034973F), new Vector2(1253.25305F, 523.237F), new Vector2(1240.37097F, 528.30603F));
                    builder.AddCubicBezier(new Vector2(1224.05298F, 534.72699F), new Vector2(1204.57202F, 566.856018F), new Vector2(1204.57202F, 566.856018F));
                    builder.AddCubicBezier(new Vector2(1204.57202F, 566.856018F), new Vector2(1193.85803F, 555.427979F), new Vector2(1193.85803F, 555.427979F));
                    builder.AddCubicBezier(new Vector2(1193.85803F, 555.427979F), new Vector2(1116.94995F, 589.416016F), new Vector2(1076.88696F, 585.47998F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - Layer: CrescentColor
            // - Path
            CanvasGeometry Geometry_24()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(1167.099F, 569.278015F));
                    builder.AddCubicBezier(new Vector2(1128.948F, 603.442017F), new Vector2(1152.54297F, 687.544006F), new Vector2(1106.81006F, 710.591003F));
                    builder.AddCubicBezier(new Vector2(1006.07001F, 761.357971F), new Vector2(773.247986F, 767.755005F), new Vector2(773.247986F, 767.755005F));
                    builder.AddCubicBezier(new Vector2(773.247986F, 767.755005F), new Vector2(406.269989F, 918.284973F), new Vector2(406.269989F, 918.284973F));
                    builder.AddCubicBezier(new Vector2(406.269989F, 918.284973F), new Vector2(529.210999F, 1032.79297F), new Vector2(607.356018F, 1052.43701F));
                    builder.AddCubicBezier(new Vector2(667.007019F, 1067.43201F), new Vector2(787.872986F, 1014.211F), new Vector2(787.872986F, 1014.211F));
                    builder.AddCubicBezier(new Vector2(787.872986F, 1014.211F), new Vector2(1111.14905F, 1061.29797F), new Vector2(1111.14905F, 1061.29797F));
                    builder.AddCubicBezier(new Vector2(1111.14905F, 1061.29797F), new Vector2(1607.89697F, 765.817017F), new Vector2(1607.89697F, 765.817017F));
                    builder.AddCubicBezier(new Vector2(1607.89697F, 765.817017F), new Vector2(1595.61499F, 652.879028F), new Vector2(1604.50696F, 597.45697F));
                    builder.AddCubicBezier(new Vector2(1616.48303F, 522.812988F), new Vector2(1684.66296F, 454.365997F), new Vector2(1669.76001F, 380.251007F));
                    builder.AddCubicBezier(new Vector2(1661.08899F, 337.127991F), new Vector2(1592.04797F, 337.493988F), new Vector2(1549.78406F, 325.304993F));
                    builder.AddCubicBezier(new Vector2(1502.05103F, 311.539001F), new Vector2(1402.40503F, 303.145996F), new Vector2(1402.40503F, 303.145996F));
                    builder.AddCubicBezier(new Vector2(1402.40503F, 303.145996F), new Vector2(1337.91797F, 445.626007F), new Vector2(1337.91797F, 445.626007F));
                    builder.AddCubicBezier(new Vector2(1337.91797F, 445.626007F), new Vector2(1346.18994F, 470.933014F), new Vector2(1345.48206F, 483.927002F));
                    builder.AddCubicBezier(new Vector2(1344.81299F, 496.199005F), new Vector2(1338.48499F, 507.527008F), new Vector2(1333.922F, 518.939026F));
                    builder.AddCubicBezier(new Vector2(1333.63904F, 519.64801F), new Vector2(1331.69202F, 518.419006F), new Vector2(1331.69202F, 518.419006F));
                    builder.AddCubicBezier(new Vector2(1331.69202F, 518.419006F), new Vector2(1287.97498F, 548.356995F), new Vector2(1287.97498F, 548.356995F));
                    builder.AddCubicBezier(new Vector2(1287.97498F, 548.356995F), new Vector2(1197.56104F, 541.999023F), new Vector2(1167.099F, 569.278015F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - Layer: CrescentColor
            // - Path
            CanvasGeometry Geometry_25()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(1342.32397F, 953.036987F));
                    builder.AddCubicBezier(new Vector2(1333.13599F, 966.677002F), new Vector2(1314.43201F, 972.828979F), new Vector2(1298.125F, 974.963013F));
                    builder.AddCubicBezier(new Vector2(1164.03003F, 992.51001F), new Vector2(1027.66797F, 990.646973F), new Vector2(894.077026F, 1011.69098F));
                    builder.AddCubicBezier(new Vector2(722.89801F, 1038.65601F), new Vector2(522.85498F, 1013.28699F), new Vector2(385.351013F, 1118.74805F));
                    builder.AddCubicBezier(new Vector2(313.92099F, 1173.53198F), new Vector2(551.718994F, 1217.323F), new Vector2(640.997986F, 1205.79395F));
                    builder.AddCubicBezier(new Vector2(728.815002F, 1194.45398F), new Vector2(774.554993F, 1075.29797F), new Vector2(861.252014F, 1057.29395F));
                    builder.AddCubicBezier(new Vector2(980.153015F, 1032.60303F), new Vector2(1112.52295F, 1131.51196F), new Vector2(1224.54004F, 1084.61401F));
                    builder.AddCubicBezier(new Vector2(1442.13794F, 993.512024F), new Vector2(1643.84302F, 847.067993F), new Vector2(1795.31897F, 666.22699F));
                    builder.AddCubicBezier(new Vector2(1832.66699F, 621.638F), new Vector2(1702.83997F, 581.677002F), new Vector2(1693.95605F, 524.195984F));
                    builder.AddCubicBezier(new Vector2(1682.83301F, 452.227997F), new Vector2(1764.56494F, 379.402008F), new Vector2(1740.37195F, 310.714996F));
                    builder.AddCubicBezier(new Vector2(1725.00195F, 267.07901F), new Vector2(1649.33704F, 291.891998F), new Vector2(1606.22302F, 275.113007F));
                    builder.AddCubicBezier(new Vector2(1550.55005F, 253.445999F), new Vector2(1496.53198F, 164.968994F), new Vector2(1445.44104F, 195.932007F));
                    builder.AddCubicBezier(new Vector2(1392.08301F, 228.268997F), new Vector2(1404.15405F, 325.252991F), new Vector2(1429.18994F, 382.401001F));
                    builder.AddCubicBezier(new Vector2(1466.39502F, 467.325012F), new Vector2(1576.43701F, 504.191986F), new Vector2(1617.01001F, 587.559021F));
                    builder.AddCubicBezier(new Vector2(1639.71204F, 634.205017F), new Vector2(1625.53003F, 695.250977F), new Vector2(1604.53503F, 742.690002F));
                    builder.AddCubicBezier(new Vector2(1590.74194F, 773.856995F), new Vector2(1551.73999F, 786.491028F), new Vector2(1521.78003F, 802.73999F));
                    builder.AddCubicBezier(new Vector2(1472.57202F, 829.427979F), new Vector2(1411.18201F, 834.888977F), new Vector2(1368.26599F, 870.83197F));
                    builder.AddCubicBezier(new Vector2(1346.23804F, 889.281006F), new Vector2(1358.37598F, 929.205994F), new Vector2(1342.32397F, 953.036987F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Shape tree root for layer: C
            // - -  Offset:<1428, 357>
            CanvasGeometry Geometry_26()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-1142.14697F, 192.617004F));
                    builder.AddCubicBezier(new Vector2(-1117.88306F, 182.147995F), new Vector2(-1096.03894F, 166.682999F), new Vector2(-1076.61304F, 146.235001F));
                    builder.AddLine(new Vector2(-1150.19604F, 71.8710022F));
                    builder.AddCubicBezier(new Vector2(-1158.89099F, 82.1009979F), new Vector2(-1169.23804F, 89.7669983F), new Vector2(-1181.23804F, 94.8700027F));
                    builder.AddCubicBezier(new Vector2(-1193.25F, 99.9850006F), new Vector2(-1207.17896F, 102.537003F), new Vector2(-1223.01196F, 102.537003F));
                    builder.AddCubicBezier(new Vector2(-1239.875F, 102.537003F), new Vector2(-1254.953F, 98.7030029F), new Vector2(-1268.23499F, 91.0370026F));
                    builder.AddCubicBezier(new Vector2(-1281.52905F, 83.3710022F), new Vector2(-1291.99597F, 72.901001F), new Vector2(-1299.66101F, 59.6049995F));
                    builder.AddCubicBezier(new Vector2(-1307.32605F, 46.3209991F), new Vector2(-1311.15796F, 30.9869995F), new Vector2(-1311.15796F, 13.6059999F));
                    builder.AddCubicBezier(new Vector2(-1311.15796F, -4.27799988F), new Vector2(-1307.32605F, -19.8630009F), new Vector2(-1299.66101F, -33.1590004F));
                    builder.AddCubicBezier(new Vector2(-1291.99597F, -46.4430008F), new Vector2(-1281.52905F, -56.7929993F), new Vector2(-1268.23499F, -64.2080002F));
                    builder.AddCubicBezier(new Vector2(-1254.953F, -71.6110001F), new Vector2(-1239.875F, -75.3239975F), new Vector2(-1223.01196F, -75.3239975F));
                    builder.AddCubicBezier(new Vector2(-1207.68201F, -75.3239975F), new Vector2(-1194.14905F, -72.8919983F), new Vector2(-1182.38794F, -68.0410004F));
                    builder.AddCubicBezier(new Vector2(-1170.63904F, -63.1780014F), new Vector2(-1160.67505F, -55.8950005F), new Vector2(-1152.495F, -46.1920013F));
                    builder.AddLine(new Vector2(-1078.146F, -121.321999F));
                    builder.AddCubicBezier(new Vector2(-1097.05701F, -140.740005F), new Vector2(-1118.65002F, -155.557007F), new Vector2(-1142.91394F, -165.787003F));
                    builder.AddCubicBezier(new Vector2(-1167.18994F, -176.005005F), new Vector2(-1193.88501F, -181.119995F), new Vector2(-1223.01196F, -181.119995F));
                    builder.AddCubicBezier(new Vector2(-1261.85095F, -181.119995F), new Vector2(-1296.72705F, -172.556F), new Vector2(-1327.63794F, -155.438004F));
                    builder.AddCubicBezier(new Vector2(-1358.56104F, -138.307999F), new Vector2(-1383.08899F, -115.056999F), new Vector2(-1401.22095F, -85.6729965F));
                    builder.AddCubicBezier(new Vector2(-1419.36499F, -56.2770004F), new Vector2(-1428.43103F, -22.9290009F), new Vector2(-1428.43103F, 14.3730001F));
                    builder.AddCubicBezier(new Vector2(-1428.43103F, 51.1720009F), new Vector2(-1419.49695F, 84.2689972F), new Vector2(-1401.604F, 113.653F));
                    builder.AddCubicBezier(new Vector2(-1383.72302F, 143.048996F), new Vector2(-1359.32703F, 166.167999F), new Vector2(-1328.40405F, 183.033997F));
                    builder.AddCubicBezier(new Vector2(-1297.49304F, 199.899994F), new Vector2(-1262.86902F, 208.332993F), new Vector2(-1224.54504F, 208.332993F));
                    builder.AddCubicBezier(new Vector2(-1193.88501F, 208.332993F), new Vector2(-1166.42297F, 203.085999F), new Vector2(-1142.14697F, 192.617004F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Shape tree root for layer: O
            // - -  Offset:<1428, 357>
            CanvasGeometry Geometry_27()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-750.088989F, 182.649994F));
                    builder.AddCubicBezier(new Vector2(-719.429016F, 165.531998F), new Vector2(-695.284973F, 142.149994F), new Vector2(-677.656006F, 112.502998F));
                    builder.AddCubicBezier(new Vector2(-660.026978F, 82.8679962F), new Vector2(-651.211975F, 49.6389999F), new Vector2(-651.211975F, 12.8400002F));
                    builder.AddCubicBezier(new Vector2(-651.211975F, -23.4440002F), new Vector2(-660.15802F, -56.2770004F), new Vector2(-678.039001F, -85.6729965F));
                    builder.AddCubicBezier(new Vector2(-695.932007F, -115.056999F), new Vector2(-720.195007F, -138.307999F), new Vector2(-750.85498F, -155.438004F));
                    builder.AddCubicBezier(new Vector2(-781.513977F, -172.556F), new Vector2(-816.007019F, -181.119995F), new Vector2(-854.330994F, -181.119995F));
                    builder.AddCubicBezier(new Vector2(-892.655029F, -181.119995F), new Vector2(-927.146973F, -172.423004F), new Vector2(-957.807007F, -155.054001F));
                    builder.AddCubicBezier(new Vector2(-988.466003F, -137.673004F), new Vector2(-1012.742F, -114.421997F), new Vector2(-1030.62305F, -85.2900009F));
                    builder.AddCubicBezier(new Vector2(-1048.51599F, -56.1580009F), new Vector2(-1057.44995F, -23.4440002F), new Vector2(-1057.44995F, 12.8400002F));
                    builder.AddCubicBezier(new Vector2(-1057.44995F, 49.6389999F), new Vector2(-1048.38403F, 82.8679962F), new Vector2(-1030.23999F, 112.502998F));
                    builder.AddCubicBezier(new Vector2(-1012.10797F, 142.149994F), new Vector2(-987.83197F, 165.531998F), new Vector2(-957.424011F, 182.649994F));
                    builder.AddCubicBezier(new Vector2(-927.028015F, 199.779999F), new Vector2(-892.403992F, 208.332993F), new Vector2(-853.565002F, 208.332993F));
                    builder.AddCubicBezier(new Vector2(-815.241028F, 208.332993F), new Vector2(-780.749023F, 199.779999F), new Vector2(-750.088989F, 182.649994F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Masks
            // - - Layer: O
            CanvasGeometry Geometry_28()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(-898.286987F, 91.9199982F));
                    builder.AddCubicBezier(new Vector2(-911.065979F, 84.5169983F), new Vector2(-921.163025F, 74.0360031F), new Vector2(-928.564026F, 60.487999F));
                    builder.AddCubicBezier(new Vector2(-935.97699F, 46.9519997F), new Vector2(-939.677979F, 31.4869995F), new Vector2(-939.677979F, 14.1059999F));
                    builder.AddCubicBezier(new Vector2(-939.677979F, -3.26300001F), new Vector2(-935.97699F, -18.5960007F), new Vector2(-928.564026F, -31.8920002F));
                    builder.AddCubicBezier(new Vector2(-921.163025F, -45.1759987F), new Vector2(-911.065979F, -55.6580009F), new Vector2(-898.286987F, -63.3240013F));
                    builder.AddCubicBezier(new Vector2(-885.52002F, -70.9899979F), new Vector2(-870.69397F, -74.8239975F), new Vector2(-853.830994F, -74.8239975F));
                    builder.AddCubicBezier(new Vector2(-836.465027F, -74.8239975F), new Vector2(-821.518982F, -71.1110001F), new Vector2(-808.992004F, -63.7080002F));
                    builder.AddCubicBezier(new Vector2(-796.47699F, -56.2929993F), new Vector2(-786.380005F, -45.9430008F), new Vector2(-778.715027F, -32.6590004F));
                    builder.AddCubicBezier(new Vector2(-771.049988F, -19.3630009F), new Vector2(-767.218018F, -3.77800012F), new Vector2(-767.218018F, 14.1059999F));
                    builder.AddCubicBezier(new Vector2(-767.218018F, 31.4869995F), new Vector2(-770.799011F, 46.9519997F), new Vector2(-777.948975F, 60.487999F));
                    builder.AddCubicBezier(new Vector2(-785.111023F, 74.0360031F), new Vector2(-795.195007F, 84.5169983F), new Vector2(-808.224976F, 91.9199982F));
                    builder.AddCubicBezier(new Vector2(-821.255005F, 99.3349991F), new Vector2(-836.465027F, 103.037003F), new Vector2(-853.830994F, 103.037003F));
                    builder.AddCubicBezier(new Vector2(-870.69397F, 103.037003F), new Vector2(-885.52002F, 99.3349991F), new Vector2(-898.286987F, 91.9199982F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Layer aggregator
            // - - Layer: L
            CanvasGeometry Geometry_29()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-476.451996F, 199.899994F));
                    builder.AddLine(new Vector2(-476.451996F, -356.681F));
                    builder.AddLine(new Vector2(-592.192017F, -356.681F));
                    builder.AddLine(new Vector2(-592.192017F, 199.899994F));
                    builder.AddLine(new Vector2(-476.451996F, 199.899994F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Layer aggregator
            // - - Layer: L
            CanvasGeometry Geometry_30()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-277.165009F, 199.899994F));
                    builder.AddLine(new Vector2(-277.165009F, -356.681F));
                    builder.AddLine(new Vector2(-392.904999F, -356.681F));
                    builder.AddLine(new Vector2(-392.904999F, 199.899994F));
                    builder.AddLine(new Vector2(-277.165009F, 199.899994F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Shape tree root for layer: A
            // - -  Offset:<1428, 357>
            // - Path 2+Path 1.PathGeometry
            CanvasGeometry Geometry_31()
            {
                var result = CanvasGeometry.CreateGroup(
                    null,
                    new CanvasGeometry[] { Geometry_32(), Geometry_33() },
                    CanvasFilledRegionDetermination.Winding);
                return result;
            }

            // - - - - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - - - - Shape tree root for layer: A
            // - - -  Offset:<1428, 357>
            // - - Path 2+Path 1.PathGeometry
            CanvasGeometry Geometry_32()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(178.895996F, 199.899994F));
                    builder.AddLine(new Vector2(178.895996F, -172.686996F));
                    builder.AddLine(new Vector2(65.4550018F, -172.686996F));
                    builder.AddLine(new Vector2(65.4550018F, -80.6900024F));
                    builder.AddLine(new Vector2(82.3180008F, 9.00699997F));
                    builder.AddLine(new Vector2(65.4550018F, 100.237F));
                    builder.AddLine(new Vector2(65.4550018F, 199.899994F));
                    builder.AddLine(new Vector2(178.895996F, 199.899994F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - - - - Shape tree root for layer: A
            // - - -  Offset:<1428, 357>
            // - - Path 2+Path 1.PathGeometry
            CanvasGeometry Geometry_33()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(30.1970005F, 193F));
                    builder.AddCubicBezier(new Vector2(50.6290016F, 183.296997F), new Vector2(67.2389984F, 169.748993F), new Vector2(80.0179977F, 152.367996F));
                    builder.AddCubicBezier(new Vector2(92.7850037F, 134.998993F), new Vector2(99.6839981F, 115.066002F), new Vector2(100.713997F, 92.5699997F));
                    builder.AddLine(new Vector2(100.713997F, -65.3580017F));
                    builder.AddCubicBezier(new Vector2(99.1809998F, -87.3270035F), new Vector2(92.1500015F, -107.008003F), new Vector2(79.6350021F, -124.389F));
                    builder.AddCubicBezier(new Vector2(67.1080017F, -141.757996F), new Vector2(50.7599983F, -155.438004F), new Vector2(30.5799999F, -165.404007F));
                    builder.AddCubicBezier(new Vector2(10.3879995F, -175.369995F), new Vector2(-12.4750004F, -180.352997F), new Vector2(-38.0209999F, -180.352997F));
                    builder.AddCubicBezier(new Vector2(-72.262001F, -180.352997F), new Vector2(-102.920998F, -171.919998F), new Vector2(-129.998993F, -155.054001F));
                    builder.AddCubicBezier(new Vector2(-157.089996F, -138.188004F), new Vector2(-178.287994F, -115.056999F), new Vector2(-193.617996F, -85.6729965F));
                    builder.AddCubicBezier(new Vector2(-208.947998F, -56.2770004F), new Vector2(-216.613007F, -22.9290009F), new Vector2(-216.613007F, 14.3730001F));
                    builder.AddCubicBezier(new Vector2(-216.613007F, 51.1720009F), new Vector2(-208.947998F, 84.1380005F), new Vector2(-193.617996F, 113.269997F));
                    builder.AddCubicBezier(new Vector2(-178.287994F, 142.401993F), new Vector2(-157.089996F, 165.401001F), new Vector2(-129.998993F, 182.266998F));
                    builder.AddCubicBezier(new Vector2(-102.920998F, 199.132996F), new Vector2(-72.262001F, 207.565994F), new Vector2(-38.0209999F, 207.565994F));
                    builder.AddCubicBezier(new Vector2(-12.9899998F, 207.565994F), new Vector2(9.75300026F, 202.703003F), new Vector2(30.1970005F, 193F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Masks
            // - - Layer: A
            CanvasGeometry Geometry_34()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(-57.4490013F, 91.9199982F));
                    builder.AddCubicBezier(new Vector2(-70.2279968F, 84.5169983F), new Vector2(-80.3249969F, 74.0360031F), new Vector2(-87.7259979F, 60.487999F));
                    builder.AddCubicBezier(new Vector2(-95.1389999F, 46.9519997F), new Vector2(-98.8399963F, 31.4869995F), new Vector2(-98.8399963F, 14.1059999F));
                    builder.AddCubicBezier(new Vector2(-98.8399963F, -3.26300001F), new Vector2(-95.1389999F, -18.5960007F), new Vector2(-87.7259979F, -31.8920002F));
                    builder.AddCubicBezier(new Vector2(-80.3249969F, -45.1759987F), new Vector2(-70.2279968F, -55.7779999F), new Vector2(-57.4490013F, -63.7080002F));
                    builder.AddCubicBezier(new Vector2(-44.6819992F, -71.6259995F), new Vector2(-30.1200008F, -75.5899963F), new Vector2(-13.7600002F, -75.5899963F));
                    builder.AddCubicBezier(new Vector2(3.10299993F, -75.5899963F), new Vector2(17.9179993F, -71.6259995F), new Vector2(30.6970005F, -63.7080002F));
                    builder.AddCubicBezier(new Vector2(43.4640007F, -55.7779999F), new Vector2(53.3089981F, -45.3079987F), new Vector2(60.2070007F, -32.2750015F));
                    builder.AddCubicBezier(new Vector2(67.1050034F, -19.2420006F), new Vector2(70.5540009F, -3.77800012F), new Vector2(70.5540009F, 14.1059999F));
                    builder.AddCubicBezier(new Vector2(70.5540009F, 40.6870003F), new Vector2(62.757F, 62.1529999F), new Vector2(47.1759987F, 78.5039978F));
                    builder.AddCubicBezier(new Vector2(31.5830002F, 94.8669968F), new Vector2(11.0200005F, 103.037003F), new Vector2(-14.526F, 103.037003F));
                    builder.AddCubicBezier(new Vector2(-30.3710003F, 103.037003F), new Vector2(-44.6819992F, 99.3349991F), new Vector2(-57.4490013F, 91.9199982F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Shape tree root for layer: P
            // - -  Offset:<1428, 357>
            // - Path 2+Path 1.PathGeometry
            CanvasGeometry Geometry_35()
            {
                var result = CanvasGeometry.CreateGroup(
                    null,
                    new CanvasGeometry[] { Geometry_36(), Geometry_37() },
                    CanvasFilledRegionDetermination.Winding);
                return result;
            }

            // - - - - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - - - - Shape tree root for layer: P
            // - - -  Offset:<1428, 357>
            // - - Path 2+Path 1.PathGeometry
            CanvasGeometry Geometry_36()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(375.882996F, 357.061005F));
                    builder.AddLine(new Vector2(375.882996F, 104.835999F));
                    builder.AddLine(new Vector2(358.253998F, 14.3730001F));
                    builder.AddLine(new Vector2(377.415985F, -76.8570023F));
                    builder.AddLine(new Vector2(377.415985F, -172.686996F));
                    builder.AddLine(new Vector2(261.675995F, -172.686996F));
                    builder.AddLine(new Vector2(261.675995F, 357.061005F));
                    builder.AddLine(new Vector2(375.882996F, 357.061005F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - - - - Shape tree root for layer: P
            // - - -  Offset:<1428, 357>
            // - - Path 2+Path 1.PathGeometry
            CanvasGeometry Geometry_37()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(569.421021F, 182.266998F));
                    builder.AddCubicBezier(new Vector2(596.750977F, 165.401001F), new Vector2(618.213013F, 142.401993F), new Vector2(633.80603F, 113.269997F));
                    builder.AddCubicBezier(new Vector2(649.387024F, 84.1380005F), new Vector2(657.184021F, 51.1720009F), new Vector2(657.184021F, 14.3730001F));
                    builder.AddCubicBezier(new Vector2(657.184021F, -22.9290009F), new Vector2(649.387024F, -56.1580009F), new Vector2(633.80603F, -85.2900009F));
                    builder.AddCubicBezier(new Vector2(618.213013F, -114.421997F), new Vector2(596.750977F, -137.541F), new Vector2(569.421021F, -154.671005F));
                    builder.AddCubicBezier(new Vector2(542.078979F, -171.789001F), new Vector2(510.785004F, -180.352997F), new Vector2(475.526001F, -180.352997F));
                    builder.AddCubicBezier(new Vector2(450.484009F, -180.352997F), new Vector2(427.489014F, -175.106003F), new Vector2(406.541992F, -164.636993F));
                    builder.AddCubicBezier(new Vector2(385.583008F, -154.156006F), new Vector2(368.470001F, -139.973007F), new Vector2(355.187988F, -122.088997F));
                    builder.AddCubicBezier(new Vector2(341.894012F, -104.193001F), new Vector2(334.229004F, -84.0080032F), new Vector2(332.192993F, -61.5239983F));
                    builder.AddLine(new Vector2(332.192993F, 92.5699997F));
                    builder.AddCubicBezier(new Vector2(334.229004F, 115.066002F), new Vector2(341.774994F, 134.867004F), new Vector2(354.804993F, 151.985001F));
                    builder.AddCubicBezier(new Vector2(367.834991F, 169.115005F), new Vector2(384.816986F, 182.651001F), new Vector2(405.776001F, 192.617004F));
                    builder.AddCubicBezier(new Vector2(426.722992F, 202.582993F), new Vector2(449.968994F, 207.565994F), new Vector2(475.526001F, 207.565994F));
                    builder.AddCubicBezier(new Vector2(510.785004F, 207.565994F), new Vector2(542.078979F, 199.132996F), new Vector2(569.421021F, 182.266998F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Masks
            // - - Layer: P
            CanvasGeometry Geometry_38()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(411.257996F, 91.9199982F));
                    builder.AddCubicBezier(new Vector2(398.730988F, 84.5169983F), new Vector2(388.89801F, 74.0360031F), new Vector2(381.747986F, 60.487999F));
                    builder.AddCubicBezier(new Vector2(374.585999F, 46.9519997F), new Vector2(371.018005F, 31.2360001F), new Vector2(371.018005F, 13.3400002F));
                    builder.AddCubicBezier(new Vector2(371.018005F, -4.02899981F), new Vector2(374.585999F, -19.3630009F), new Vector2(381.747986F, -32.6590004F));
                    builder.AddCubicBezier(new Vector2(388.89801F, -45.9430008F), new Vector2(398.862F, -56.4249992F), new Vector2(411.640991F, -64.0910034F));
                    builder.AddCubicBezier(new Vector2(424.40799F, -71.7570038F), new Vector2(439.234985F, -75.5899963F), new Vector2(456.097992F, -75.5899963F));
                    builder.AddCubicBezier(new Vector2(472.446014F, -75.5899963F), new Vector2(487.007996F, -71.7570038F), new Vector2(499.786987F, -64.0910034F));
                    builder.AddCubicBezier(new Vector2(512.554016F, -56.4249992F), new Vector2(522.518982F, -45.8110008F), new Vector2(529.68103F, -32.2750015F));
                    builder.AddCubicBezier(new Vector2(536.830994F, -18.7269993F), new Vector2(540.411011F, -3.26300001F), new Vector2(540.411011F, 14.1059999F));
                    builder.AddCubicBezier(new Vector2(540.411011F, 31.4869995F), new Vector2(536.830994F, 46.9519997F), new Vector2(529.68103F, 60.487999F));
                    builder.AddCubicBezier(new Vector2(522.518982F, 74.0360031F), new Vector2(512.554016F, 84.5169983F), new Vector2(499.786987F, 91.9199982F));
                    builder.AddCubicBezier(new Vector2(487.007996F, 99.3349991F), new Vector2(471.931F, 103.037003F), new Vector2(454.565002F, 103.037003F));
                    builder.AddCubicBezier(new Vector2(438.204987F, 103.037003F), new Vector2(423.77301F, 99.3349991F), new Vector2(411.257996F, 91.9199982F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Layer aggregator
            // - - Layer: S
            CanvasGeometry Geometry_39()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(961.864014F, 176.901001F));
                    builder.AddCubicBezier(new Vector2(989.19397F, 155.434998F), new Vector2(1002.87097F, 126.566002F), new Vector2(1002.87097F, 90.2699966F));
                    builder.AddCubicBezier(new Vector2(1002.87097F, 67.2710037F), new Vector2(998.392029F, 48.487999F), new Vector2(989.45697F, 33.9220009F));
                    builder.AddCubicBezier(new Vector2(980.510986F, 19.3560009F), new Vector2(969.265015F, 7.73699999F), new Vector2(955.731995F, -0.959999979F));
                    builder.AddCubicBezier(new Vector2(942.187012F, -9.64500046F), new Vector2(927.755005F, -16.5440006F), new Vector2(912.424988F, -21.6590004F));
                    builder.AddCubicBezier(new Vector2(897.094971F, -26.7619991F), new Vector2(882.531982F, -31.1100006F), new Vector2(868.734985F, -34.6920013F));
                    builder.AddCubicBezier(new Vector2(854.937988F, -38.262001F), new Vector2(843.559998F, -42.4780006F), new Vector2(834.625977F, -47.3409996F));
                    builder.AddCubicBezier(new Vector2(825.679016F, -52.1920013F), new Vector2(821.213013F, -58.4580002F), new Vector2(821.213013F, -66.1240005F));
                    builder.AddCubicBezier(new Vector2(821.213013F, -73.2750015F), new Vector2(824.661987F, -78.8929977F), new Vector2(831.559998F, -82.9899979F));
                    builder.AddCubicBezier(new Vector2(838.458008F, -87.0749969F), new Vector2(848.54303F, -89.1230011F), new Vector2(861.836975F, -89.1230011F));
                    builder.AddCubicBezier(new Vector2(876.137024F, -89.1230011F), new Vector2(890.580017F, -86.177002F), new Vector2(905.143005F, -80.3069992F));
                    builder.AddCubicBezier(new Vector2(919.705994F, -74.4250031F), new Vector2(932.856018F, -64.5910034F), new Vector2(944.617004F, -50.7910004F));
                    builder.AddLine(new Vector2(1010.53497F, -117.488998F));
                    builder.AddCubicBezier(new Vector2(993.671997F, -138.955002F), new Vector2(971.827026F, -155.175003F), new Vector2(945F, -166.171005F));
                    builder.AddCubicBezier(new Vector2(918.172974F, -177.156006F), new Vector2(889.166992F, -182.653F), new Vector2(858.004028F, -182.653F));
                    builder.AddCubicBezier(new Vector2(827.344971F, -182.653F), new Vector2(801.020996F, -177.539001F), new Vector2(779.05603F, -167.320999F));
                    builder.AddCubicBezier(new Vector2(757.078979F, -157.091003F), new Vector2(739.965027F, -143.171997F), new Vector2(727.700989F, -125.539001F));
                    builder.AddCubicBezier(new Vector2(715.437012F, -107.905998F), new Vector2(709.304993F, -87.3259964F), new Vector2(709.304993F, -63.8240013F));
                    builder.AddCubicBezier(new Vector2(709.304993F, -40.8250008F), new Vector2(713.520996F, -22.2940006F), new Vector2(721.952026F, -8.24300003F));
                    builder.AddCubicBezier(new Vector2(730.382996F, 5.82000017F), new Vector2(741.749023F, 17.0559998F), new Vector2(756.060974F, 25.4890003F));
                    builder.AddCubicBezier(new Vector2(770.361023F, 33.9220009F), new Vector2(785.187012F, 40.5709991F), new Vector2(800.517029F, 45.4220009F));
                    builder.AddCubicBezier(new Vector2(815.846985F, 50.2849998F), new Vector2(830.409973F, 54.5009995F), new Vector2(844.20697F, 58.0709991F));
                    builder.AddCubicBezier(new Vector2(858.004028F, 61.6529999F), new Vector2(869.237976F, 65.8700027F), new Vector2(877.932983F, 70.7210007F));
                    builder.AddCubicBezier(new Vector2(886.616028F, 75.5839996F), new Vector2(890.963013F, 82.8669968F), new Vector2(890.963013F, 92.5699997F));
                    builder.AddCubicBezier(new Vector2(890.963013F, 99.7330017F), new Vector2(887.25F, 105.350998F), new Vector2(879.848999F, 109.435997F));
                    builder.AddCubicBezier(new Vector2(872.435974F, 113.532997F), new Vector2(861.836975F, 115.569F), new Vector2(848.039978F, 115.569F));
                    builder.AddCubicBezier(new Vector2(827.596008F, 115.569F), new Vector2(808.948975F, 111.867996F), new Vector2(792.085999F, 104.453003F));
                    builder.AddCubicBezier(new Vector2(775.223022F, 97.0500031F), new Vector2(760.659973F, 86.6999969F), new Vector2(748.395996F, 73.4039993F));
                    builder.AddLine(new Vector2(683.244995F, 140.102005F));
                    builder.AddCubicBezier(new Vector2(696.012024F, 154.417007F), new Vector2(711.341003F, 166.682999F), new Vector2(729.234009F, 176.901001F));
                    builder.AddCubicBezier(new Vector2(747.11499F, 187.130997F), new Vector2(766.528015F, 195.048004F), new Vector2(787.487F, 200.666F));
                    builder.AddCubicBezier(new Vector2(808.434021F, 206.283997F), new Vector2(829.89502F, 209.098999F), new Vector2(851.872009F, 209.098999F));
                    builder.AddCubicBezier(new Vector2(897.861023F, 209.098999F), new Vector2(934.521973F, 198.367004F), new Vector2(961.864014F, 176.901001F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            // - - - Layer aggregator
            // - - Layer: S
            CanvasGeometry Geometry_40()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(1335.52698F, 192.233002F));
                    builder.AddCubicBezier(new Vector2(1361.58801F, 181.5F), new Vector2(1383.81494F, 165.401001F), new Vector2(1402.21106F, 143.934998F));
                    builder.AddLine(new Vector2(1338.59302F, 80.3040009F));
                    builder.AddCubicBezier(new Vector2(1326.83203F, 93.0849991F), new Vector2(1313.68201F, 102.667999F), new Vector2(1299.11902F, 109.053001F));
                    builder.AddCubicBezier(new Vector2(1284.55603F, 115.449997F), new Vector2(1268.07605F, 118.636002F), new Vector2(1249.68005F, 118.636002F));
                    builder.AddCubicBezier(new Vector2(1229.23596F, 118.636002F), new Vector2(1211.73901F, 114.418999F), new Vector2(1197.17603F, 105.986F));
                    builder.AddCubicBezier(new Vector2(1182.61304F, 97.5530014F), new Vector2(1171.36694F, 85.4189987F), new Vector2(1163.44995F, 69.5709991F));
                    builder.AddCubicBezier(new Vector2(1155.52197F, 53.7350006F), new Vector2(1151.56897F, 34.8209991F), new Vector2(1151.56897F, 12.8400002F));
                    builder.AddCubicBezier(new Vector2(1151.56897F, -8.6260004F), new Vector2(1155.27002F, -27.2770004F), new Vector2(1162.68396F, -43.125F));
                    builder.AddCubicBezier(new Vector2(1170.08496F, -58.9609985F), new Vector2(1180.43201F, -71.1070023F), new Vector2(1193.72595F, -79.5400009F));
                    builder.AddCubicBezier(new Vector2(1207.00806F, -87.9729996F), new Vector2(1223.104F, -92.1900024F), new Vector2(1242.01501F, -92.1900024F));
                    builder.AddCubicBezier(new Vector2(1259.896F, -92.1900024F), new Vector2(1274.84204F, -88.4769974F), new Vector2(1286.854F, -81.0739975F));
                    builder.AddCubicBezier(new Vector2(1298.854F, -73.6589966F), new Vector2(1308.05298F, -62.6739998F), new Vector2(1314.448F, -48.1080017F));
                    builder.AddCubicBezier(new Vector2(1320.83105F, -33.5419998F), new Vector2(1324.28101F, -15.526F), new Vector2(1324.79602F, 5.94000006F));
                    builder.AddLine(new Vector2(1374.61694F, -28.559F));
                    builder.AddLine(new Vector2(1103.28101F, -27.0259991F));
                    builder.AddLine(new Vector2(1103.28101F, 53.4720001F));
                    builder.AddLine(new Vector2(1423.67297F, 51.9379997F));
                    builder.AddCubicBezier(new Vector2(1425.70898F, 42.2350006F), new Vector2(1427.12195F, 33.6710014F), new Vector2(1427.88794F, 26.2560005F));
                    builder.AddCubicBezier(new Vector2(1428.65405F, 18.8530006F), new Vector2(1429.03796F, 11.8210001F), new Vector2(1429.03796F, 5.17299986F));
                    builder.AddCubicBezier(new Vector2(1429.03796F, -31.1110001F), new Vector2(1420.98999F, -63.1769981F), new Vector2(1404.89404F, -91.0400009F));
                    builder.AddCubicBezier(new Vector2(1388.79797F, -118.890999F), new Vector2(1366.68994F, -140.871994F), new Vector2(1338.59302F, -156.970993F));
                    builder.AddCubicBezier(new Vector2(1310.48499F, -173.070007F), new Vector2(1278.04102F, -181.119995F), new Vector2(1241.24902F, -181.119995F));
                    builder.AddCubicBezier(new Vector2(1203.94299F, -181.119995F), new Vector2(1170.21704F, -172.556F), new Vector2(1140.07202F, -155.438004F));
                    builder.AddCubicBezier(new Vector2(1109.91602F, -138.307999F), new Vector2(1086.03503F, -115.056999F), new Vector2(1068.40601F, -85.6729965F));
                    builder.AddCubicBezier(new Vector2(1050.77698F, -56.2770004F), new Vector2(1041.96204F, -23.1930008F), new Vector2(1041.96204F, 13.6059999F));
                    builder.AddCubicBezier(new Vector2(1041.96204F, 50.9199982F), new Vector2(1051.02795F, 84.401001F), new Vector2(1069.172F, 114.036003F));
                    builder.AddCubicBezier(new Vector2(1087.30396F, 143.682999F), new Vector2(1111.96399F, 166.813995F), new Vector2(1143.13794F, 183.417007F));
                    builder.AddCubicBezier(new Vector2(1174.30103F, 200.020004F), new Vector2(1209.823F, 208.332993F), new Vector2(1249.68005F, 208.332993F));
                    builder.AddCubicBezier(new Vector2(1280.84204F, 208.332993F), new Vector2(1309.46594F, 202.966003F), new Vector2(1335.52698F, 192.233002F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer: collapseText
            CanvasGeometry Geometry_41()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(1750.16394F, -41.9669991F));
                    builder.AddLine(new Vector2(-59.6720009F, -41.9669991F));
                    builder.AddLine(new Vector2(-59.6720009F, 721.96698F));
                    builder.AddLine(new Vector2(1750.16394F, 721.96698F));
                    builder.AddLine(new Vector2(1750.16394F, -41.9669991F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_42()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(154.072998F, -1.97599995F));
                    builder.AddCubicBezier(new Vector2(153.544006F, -1.36399996F), new Vector2(152.856003F, -1.05799997F), new Vector2(152.009003F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(151.149002F, -1.05799997F), new Vector2(150.464005F, -1.375F), new Vector2(149.953995F, -2.00900006F));
                    builder.AddCubicBezier(new Vector2(149.444F, -2.64299989F), new Vector2(149.188995F, -3.50699997F), new Vector2(149.188995F, -4.60200024F));
                    builder.AddCubicBezier(new Vector2(149.188995F, -5.80200005F), new Vector2(149.455002F, -6.7420001F), new Vector2(149.987F, -7.42199993F));
                    builder.AddCubicBezier(new Vector2(150.518997F, -8.10200024F), new Vector2(151.248001F, -8.44200039F), new Vector2(152.175995F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(152.955002F, -8.44200039F), new Vector2(153.598999F, -8.17700005F), new Vector2(154.106003F, -7.64499998F));
                    builder.AddCubicBezier(new Vector2(154.613007F, -7.11299992F), new Vector2(154.867004F, -6.46299982F), new Vector2(154.867004F, -5.6960001F));
                    builder.AddLine(new Vector2(154.867004F, -4.29500008F));
                    builder.AddCubicBezier(new Vector2(154.867004F, -3.36100006F), new Vector2(154.602005F, -2.58800006F), new Vector2(154.072998F, -1.97599995F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(156.388F, -14.0640001F));
                    builder.AddLine(new Vector2(154.867004F, -14.0640001F));
                    builder.AddLine(new Vector2(154.867004F, -8.18299961F));
                    builder.AddLine(new Vector2(154.830002F, -8.18299961F));
                    builder.AddCubicBezier(new Vector2(154.235992F, -9.21000004F), new Vector2(153.285995F, -9.72299957F), new Vector2(151.981003F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(150.664001F, -9.72299957F), new Vector2(149.610001F, -9.2489996F), new Vector2(148.817993F, -8.30300045F));
                    builder.AddCubicBezier(new Vector2(148.026001F, -7.35699987F), new Vector2(147.630005F, -6.09800005F), new Vector2(147.630005F, -4.52699995F));
                    builder.AddCubicBezier(new Vector2(147.630005F, -3.06100011F), new Vector2(147.988007F, -1.903F), new Vector2(148.701996F, -1.05299997F));
                    builder.AddCubicBezier(new Vector2(149.416F, -0.202999994F), new Vector2(150.369995F, 0.223000005F), new Vector2(151.563995F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(153.035995F, 0.223000005F), new Vector2(154.125F, -0.388999999F), new Vector2(154.830002F, -1.61399996F));
                    builder.AddLine(new Vector2(154.867004F, -1.61399996F));
                    builder.AddLine(new Vector2(154.867004F, 0F));
                    builder.AddLine(new Vector2(156.388F, 0F));
                    builder.AddLine(new Vector2(156.388F, -14.0640001F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_43()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(139.289993F, -5.65899992F));
                    builder.AddCubicBezier(new Vector2(139.406998F, -6.48799992F), new Vector2(139.716995F, -7.15899992F), new Vector2(140.218002F, -7.67199993F));
                    builder.AddCubicBezier(new Vector2(140.718994F, -8.18500042F), new Vector2(141.326996F, -8.44200039F), new Vector2(142.044998F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(142.787003F, -8.44200039F), new Vector2(143.367996F, -8.19799995F), new Vector2(143.785004F, -7.70900011F));
                    builder.AddCubicBezier(new Vector2(144.201996F, -7.21999979F), new Vector2(144.414001F, -6.53700018F), new Vector2(144.419998F, -5.65899992F));
                    builder.AddLine(new Vector2(139.289993F, -5.65899992F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(145.979004F, -5.16699982F));
                    builder.AddCubicBezier(new Vector2(145.979004F, -6.59600019F), new Vector2(145.638F, -7.71299982F), new Vector2(144.957993F, -8.5170002F));
                    builder.AddCubicBezier(new Vector2(144.278F, -9.3210001F), new Vector2(143.315994F, -9.72299957F), new Vector2(142.072998F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(140.830002F, -9.72299957F), new Vector2(139.789001F, -9.25699997F), new Vector2(138.951004F, -8.32600021F));
                    builder.AddCubicBezier(new Vector2(138.113007F, -7.39499998F), new Vector2(137.694F, -6.19099998F), new Vector2(137.694F, -4.71299982F));
                    builder.AddCubicBezier(new Vector2(137.694F, -3.148F), new Vector2(138.078003F, -1.93499994F), new Vector2(138.845001F, -1.07200003F));
                    builder.AddCubicBezier(new Vector2(139.612F, -0.209000006F), new Vector2(140.666F, 0.223000005F), new Vector2(142.007996F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(143.380997F, 0.223000005F), new Vector2(144.485001F, -0.0810000002F), new Vector2(145.320007F, -0.686999977F));
                    builder.AddLine(new Vector2(145.320007F, -2.11500001F));
                    builder.AddCubicBezier(new Vector2(144.423004F, -1.40999997F), new Vector2(143.440002F, -1.05799997F), new Vector2(142.369995F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(141.417007F, -1.05799997F), new Vector2(140.669006F, -1.34500003F), new Vector2(140.125F, -1.91999996F));
                    builder.AddCubicBezier(new Vector2(139.580994F, -2.49499989F), new Vector2(139.296005F, -3.31200004F), new Vector2(139.270996F, -4.36999989F));
                    builder.AddLine(new Vector2(145.979004F, -4.36999989F));
                    builder.AddLine(new Vector2(145.979004F, -5.16699982F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_44()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(135.106003F, -9.5F));
                    builder.AddLine(new Vector2(132.582993F, -2.56100011F));
                    builder.AddCubicBezier(new Vector2(132.447006F, -2.17700005F), new Vector2(132.348007F, -1.74699998F), new Vector2(132.285995F, -1.27100003F));
                    builder.AddLine(new Vector2(132.248993F, -1.27100003F));
                    builder.AddCubicBezier(new Vector2(132.205994F, -1.648F), new Vector2(132.093994F, -2.09100008F), new Vector2(131.914993F, -2.59800005F));
                    builder.AddLine(new Vector2(129.501999F, -9.5F));
                    builder.AddLine(new Vector2(127.833F, -9.5F));
                    builder.AddLine(new Vector2(131.432007F, 0F));
                    builder.AddLine(new Vector2(132.925995F, 0F));
                    builder.AddLine(new Vector2(136.710999F, -9.5F));
                    builder.AddLine(new Vector2(135.106003F, -9.5F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_45()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(127.591003F, -9.5369997F));
                    builder.AddCubicBezier(new Vector2(127.380997F, -9.6239996F), new Vector2(127.078003F, -9.66699982F), new Vector2(126.681999F, -9.66699982F));
                    builder.AddCubicBezier(new Vector2(126.130997F, -9.66699982F), new Vector2(125.633003F, -9.47999954F), new Vector2(125.188004F, -9.10599995F));
                    builder.AddCubicBezier(new Vector2(124.742996F, -8.73200035F), new Vector2(124.412003F, -8.21000004F), new Vector2(124.195999F, -7.54199982F));
                    builder.AddLine(new Vector2(124.158997F, -7.54199982F));
                    builder.AddLine(new Vector2(124.158997F, -9.5F));
                    builder.AddLine(new Vector2(122.637001F, -9.5F));
                    builder.AddLine(new Vector2(122.637001F, 0F));
                    builder.AddLine(new Vector2(124.158997F, 0F));
                    builder.AddLine(new Vector2(124.158997F, -4.84299994F));
                    builder.AddCubicBezier(new Vector2(124.158997F, -5.90100002F), new Vector2(124.376999F, -6.73600006F), new Vector2(124.813004F, -7.34800005F));
                    builder.AddCubicBezier(new Vector2(125.249001F, -7.96000004F), new Vector2(125.792F, -8.26599979F), new Vector2(126.441002F, -8.26599979F));
                    builder.AddCubicBezier(new Vector2(126.942001F, -8.26599979F), new Vector2(127.324997F, -8.16399956F), new Vector2(127.591003F, -7.96000004F));
                    builder.AddLine(new Vector2(127.591003F, -9.5369997F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_46()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(113.647003F, -5.65899992F));
                    builder.AddCubicBezier(new Vector2(113.764F, -6.48799992F), new Vector2(114.073997F, -7.15899992F), new Vector2(114.574997F, -7.67199993F));
                    builder.AddCubicBezier(new Vector2(115.075996F, -8.18500042F), new Vector2(115.684998F, -8.44200039F), new Vector2(116.403F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(117.144997F, -8.44200039F), new Vector2(117.724998F, -8.19799995F), new Vector2(118.141998F, -7.70900011F));
                    builder.AddCubicBezier(new Vector2(118.558998F, -7.21999979F), new Vector2(118.772003F, -6.53700018F), new Vector2(118.778F, -5.65899992F));
                    builder.AddLine(new Vector2(113.647003F, -5.65899992F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(120.335999F, -5.16699982F));
                    builder.AddCubicBezier(new Vector2(120.335999F, -6.59600019F), new Vector2(119.996002F, -7.71299982F), new Vector2(119.316002F, -8.5170002F));
                    builder.AddCubicBezier(new Vector2(118.636002F, -9.3210001F), new Vector2(117.674004F, -9.72299957F), new Vector2(116.431F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(115.188004F, -9.72299957F), new Vector2(114.147003F, -9.25699997F), new Vector2(113.308998F, -8.32600021F));
                    builder.AddCubicBezier(new Vector2(112.471001F, -7.39499998F), new Vector2(112.052002F, -6.19099998F), new Vector2(112.052002F, -4.71299982F));
                    builder.AddCubicBezier(new Vector2(112.052002F, -3.148F), new Vector2(112.434998F, -1.93499994F), new Vector2(113.202003F, -1.07200003F));
                    builder.AddCubicBezier(new Vector2(113.969002F, -0.209000006F), new Vector2(115.024002F, 0.223000005F), new Vector2(116.365997F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(117.738998F, 0.223000005F), new Vector2(118.843002F, -0.0810000002F), new Vector2(119.678001F, -0.686999977F));
                    builder.AddLine(new Vector2(119.678001F, -2.11500001F));
                    builder.AddCubicBezier(new Vector2(118.780998F, -1.40999997F), new Vector2(117.797997F, -1.05799997F), new Vector2(116.727997F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(115.775002F, -1.05799997F), new Vector2(115.026001F, -1.34500003F), new Vector2(114.482002F, -1.91999996F));
                    builder.AddCubicBezier(new Vector2(113.938004F, -2.49499989F), new Vector2(113.653999F, -3.31200004F), new Vector2(113.628998F, -4.36999989F));
                    builder.AddLine(new Vector2(120.335999F, -4.36999989F));
                    builder.AddLine(new Vector2(120.335999F, -5.16699982F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_47()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(106.606003F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(107.100998F, 0.223000005F), new Vector2(107.572998F, 0.166999996F), new Vector2(108.025002F, 0.0560000017F));
                    builder.AddCubicBezier(new Vector2(108.475998F, -0.0549999997F), new Vector2(108.875999F, -0.225999996F), new Vector2(109.222F, -0.455000013F));
                    builder.AddCubicBezier(new Vector2(109.568001F, -0.684000015F), new Vector2(109.845001F, -0.971000016F), new Vector2(110.052002F, -1.31700003F));
                    builder.AddCubicBezier(new Vector2(110.259003F, -1.66299999F), new Vector2(110.362999F, -2.07200003F), new Vector2(110.362999F, -2.54200006F));
                    builder.AddCubicBezier(new Vector2(110.362999F, -2.92499995F), new Vector2(110.300003F, -3.25699997F), new Vector2(110.172997F, -3.53500009F));
                    builder.AddCubicBezier(new Vector2(110.045998F, -3.81299996F), new Vector2(109.864998F, -4.05900002F), new Vector2(109.629997F, -4.27199984F));
                    builder.AddCubicBezier(new Vector2(109.394997F, -4.48500013F), new Vector2(109.111F, -4.67700005F), new Vector2(108.777F, -4.84700012F));
                    builder.AddCubicBezier(new Vector2(108.443001F, -5.0170002F), new Vector2(108.066002F, -5.18300009F), new Vector2(107.644997F, -5.34399986F));
                    builder.AddCubicBezier(new Vector2(107.330002F, -5.46799994F), new Vector2(107.047997F, -5.58199978F), new Vector2(106.801003F, -5.6869998F));
                    builder.AddCubicBezier(new Vector2(106.554001F, -5.79199982F), new Vector2(106.343002F, -5.90999985F), new Vector2(106.169998F, -6.03999996F));
                    builder.AddCubicBezier(new Vector2(105.997002F, -6.17000008F), new Vector2(105.865997F, -6.31799984F), new Vector2(105.776001F, -6.48500013F));
                    builder.AddCubicBezier(new Vector2(105.685997F, -6.65199995F), new Vector2(105.640999F, -6.85900021F), new Vector2(105.640999F, -7.10599995F));
                    builder.AddCubicBezier(new Vector2(105.640999F, -7.3039999F), new Vector2(105.685997F, -7.48500013F), new Vector2(105.776001F, -7.64900017F));
                    builder.AddCubicBezier(new Vector2(105.865997F, -7.8130002F), new Vector2(105.991997F, -7.95300007F), new Vector2(106.155998F, -8.0710001F));
                    builder.AddCubicBezier(new Vector2(106.32F, -8.18799973F), new Vector2(106.515999F, -8.27999973F), new Vector2(106.745003F, -8.34500027F));
                    builder.AddCubicBezier(new Vector2(106.973999F, -8.40999985F), new Vector2(107.230003F, -8.44200039F), new Vector2(107.514999F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(108.424004F, -8.44200039F), new Vector2(109.227997F, -8.21399975F), new Vector2(109.927002F, -7.75600004F));
                    builder.AddLine(new Vector2(109.927002F, -9.29599953F));
                    builder.AddCubicBezier(new Vector2(109.278F, -9.57999992F), new Vector2(108.541F, -9.72299957F), new Vector2(107.719002F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(107.254997F, -9.72299957F), new Vector2(106.805F, -9.66300011F), new Vector2(106.369003F, -9.54599953F));
                    builder.AddCubicBezier(new Vector2(105.932999F, -9.42800045F), new Vector2(105.544998F, -9.25399971F), new Vector2(105.205002F, -9.02200031F));
                    builder.AddCubicBezier(new Vector2(104.864998F, -8.78999996F), new Vector2(104.593002F, -8.50199986F), new Vector2(104.389F, -8.1590004F));
                    builder.AddCubicBezier(new Vector2(104.184998F, -7.81599998F), new Vector2(104.083F, -7.42199993F), new Vector2(104.083F, -6.97700024F));
                    builder.AddCubicBezier(new Vector2(104.083F, -6.61199999F), new Vector2(104.137001F, -6.29300022F), new Vector2(104.245003F, -6.02099991F));
                    builder.AddCubicBezier(new Vector2(104.352997F, -5.74900007F), new Vector2(104.514F, -5.50600004F), new Vector2(104.726997F, -5.29300022F));
                    builder.AddCubicBezier(new Vector2(104.940002F, -5.07999992F), new Vector2(105.206001F, -4.88800001F), new Vector2(105.525002F, -4.71799994F));
                    builder.AddCubicBezier(new Vector2(105.843002F, -4.54799986F), new Vector2(106.212997F, -4.37900019F), new Vector2(106.634003F, -4.21199989F));
                    builder.AddCubicBezier(new Vector2(106.936996F, -4.09399986F), new Vector2(107.220001F, -3.98099995F), new Vector2(107.483002F, -3.87299991F));
                    builder.AddCubicBezier(new Vector2(107.746002F, -3.7650001F), new Vector2(107.973999F, -3.64299989F), new Vector2(108.168999F, -3.50699997F));
                    builder.AddCubicBezier(new Vector2(108.363998F, -3.37100005F), new Vector2(108.517998F, -3.21399999F), new Vector2(108.633003F, -3.03800011F));
                    builder.AddCubicBezier(new Vector2(108.747002F, -2.86199999F), new Vector2(108.805F, -2.648F), new Vector2(108.805F, -2.39400005F));
                    builder.AddCubicBezier(new Vector2(108.805F, -1.50300002F), new Vector2(108.137001F, -1.05799997F), new Vector2(106.801003F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(105.805F, -1.05799997F), new Vector2(104.892998F, -1.36399996F), new Vector2(104.064003F, -1.97599995F));
                    builder.AddLine(new Vector2(104.064003F, -0.342999995F));
                    builder.AddCubicBezier(new Vector2(104.780998F, 0.0340000018F), new Vector2(105.628998F, 0.223000005F), new Vector2(106.606003F, 0.223000005F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_48()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(95.6490021F, -5.65899992F));
                    builder.AddCubicBezier(new Vector2(95.7659988F, -6.48799992F), new Vector2(96.0759964F, -7.15899992F), new Vector2(96.5770035F, -7.67199993F));
                    builder.AddCubicBezier(new Vector2(97.0780029F, -8.18500042F), new Vector2(97.6869965F, -8.44200039F), new Vector2(98.4049988F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(99.1470032F, -8.44200039F), new Vector2(99.7269974F, -8.19799995F), new Vector2(100.143997F, -7.70900011F));
                    builder.AddCubicBezier(new Vector2(100.560997F, -7.21999979F), new Vector2(100.774002F, -6.53700018F), new Vector2(100.779999F, -5.65899992F));
                    builder.AddLine(new Vector2(95.6490021F, -5.65899992F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(102.337997F, -5.16699982F));
                    builder.AddCubicBezier(new Vector2(102.337997F, -6.59600019F), new Vector2(101.998001F, -7.71299982F), new Vector2(101.318001F, -8.5170002F));
                    builder.AddCubicBezier(new Vector2(100.638F, -9.3210001F), new Vector2(99.6760025F, -9.72299957F), new Vector2(98.4329987F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(97.1900024F, -9.72299957F), new Vector2(96.1490021F, -9.25699997F), new Vector2(95.310997F, -8.32600021F));
                    builder.AddCubicBezier(new Vector2(94.4729996F, -7.39499998F), new Vector2(94.0540009F, -6.19099998F), new Vector2(94.0540009F, -4.71299982F));
                    builder.AddCubicBezier(new Vector2(94.0540009F, -3.148F), new Vector2(94.4369965F, -1.93499994F), new Vector2(95.2040024F, -1.07200003F));
                    builder.AddCubicBezier(new Vector2(95.9710007F, -0.209000006F), new Vector2(97.026001F, 0.223000005F), new Vector2(98.3679962F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(99.7409973F, 0.223000005F), new Vector2(100.845001F, -0.0810000002F), new Vector2(101.68F, -0.686999977F));
                    builder.AddLine(new Vector2(101.68F, -2.11500001F));
                    builder.AddCubicBezier(new Vector2(100.782997F, -1.40999997F), new Vector2(99.7990036F, -1.05799997F), new Vector2(98.7289963F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(97.776001F, -1.05799997F), new Vector2(97.0279999F, -1.34500003F), new Vector2(96.4840012F, -1.91999996F));
                    builder.AddCubicBezier(new Vector2(95.9400024F, -2.49499989F), new Vector2(95.6559982F, -3.31200004F), new Vector2(95.6309967F, -4.36999989F));
                    builder.AddLine(new Vector2(102.337997F, -4.36999989F));
                    builder.AddLine(new Vector2(102.337997F, -5.16699982F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_49()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(93.052002F, -9.5369997F));
                    builder.AddCubicBezier(new Vector2(92.8420029F, -9.6239996F), new Vector2(92.5390015F, -9.66699982F), new Vector2(92.1429977F, -9.66699982F));
                    builder.AddCubicBezier(new Vector2(91.5920029F, -9.66699982F), new Vector2(91.0940018F, -9.47999954F), new Vector2(90.6490021F, -9.10599995F));
                    builder.AddCubicBezier(new Vector2(90.2040024F, -8.73200035F), new Vector2(89.8720016F, -8.21000004F), new Vector2(89.6559982F, -7.54199982F));
                    builder.AddLine(new Vector2(89.6190033F, -7.54199982F));
                    builder.AddLine(new Vector2(89.6190033F, -9.5F));
                    builder.AddLine(new Vector2(88.0979996F, -9.5F));
                    builder.AddLine(new Vector2(88.0979996F, 0F));
                    builder.AddLine(new Vector2(89.6190033F, 0F));
                    builder.AddLine(new Vector2(89.6190033F, -4.84299994F));
                    builder.AddCubicBezier(new Vector2(89.6190033F, -5.90100002F), new Vector2(89.836998F, -6.73600006F), new Vector2(90.2730026F, -7.34800005F));
                    builder.AddCubicBezier(new Vector2(90.7089996F, -7.96000004F), new Vector2(91.2519989F, -8.26599979F), new Vector2(91.901001F, -8.26599979F));
                    builder.AddCubicBezier(new Vector2(92.4020004F, -8.26599979F), new Vector2(92.7860031F, -8.16399956F), new Vector2(93.052002F, -7.96000004F));
                    builder.AddLine(new Vector2(93.052002F, -9.5369997F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_50()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(80.8610001F, -1.39199996F));
                    builder.AddCubicBezier(new Vector2(80.5830002F, -1.18200004F), new Vector2(80.2519989F, -1.07599998F), new Vector2(79.8690033F, -1.07599998F));
                    builder.AddCubicBezier(new Vector2(79.3679962F, -1.07599998F), new Vector2(79.0080032F, -1.21200001F), new Vector2(78.7919998F, -1.48399997F));
                    builder.AddCubicBezier(new Vector2(78.5749969F, -1.75600004F), new Vector2(78.4680023F, -2.21099997F), new Vector2(78.4680023F, -2.84800005F));
                    builder.AddLine(new Vector2(78.4680023F, -8.20100021F));
                    builder.AddLine(new Vector2(80.8610001F, -8.20100021F));
                    builder.AddLine(new Vector2(80.8610001F, -9.5F));
                    builder.AddLine(new Vector2(78.4680023F, -9.5F));
                    builder.AddLine(new Vector2(78.4680023F, -12.3109999F));
                    builder.AddLine(new Vector2(76.9459991F, -11.8190002F));
                    builder.AddLine(new Vector2(76.9459991F, -9.5F));
                    builder.AddLine(new Vector2(75.3130035F, -9.5F));
                    builder.AddLine(new Vector2(75.3130035F, -8.20100021F));
                    builder.AddLine(new Vector2(76.9459991F, -8.20100021F));
                    builder.AddLine(new Vector2(76.9459991F, -2.579F));
                    builder.AddCubicBezier(new Vector2(76.9459991F, -0.723999977F), new Vector2(77.7779999F, 0.203999996F), new Vector2(79.4420013F, 0.203999996F));
                    builder.AddCubicBezier(new Vector2(80.0299988F, 0.203999996F), new Vector2(80.5019989F, 0.104999997F), new Vector2(80.8610001F, -0.0930000022F));
                    builder.AddLine(new Vector2(80.8610001F, -1.39199996F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_51()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(73.5879974F, -5.85400009F));
                    builder.AddCubicBezier(new Vector2(73.5879974F, -8.43299961F), new Vector2(72.5179977F, -9.72299957F), new Vector2(70.3779984F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(69.0299988F, -9.72299957F), new Vector2(67.9909973F, -9.12300014F), new Vector2(67.2610016F, -7.92299986F));
                    builder.AddLine(new Vector2(67.223999F, -7.92299986F));
                    builder.AddLine(new Vector2(67.223999F, -14.0640001F));
                    builder.AddLine(new Vector2(65.7020035F, -14.0640001F));
                    builder.AddLine(new Vector2(65.7020035F, 0F));
                    builder.AddLine(new Vector2(67.223999F, 0F));
                    builder.AddLine(new Vector2(67.223999F, -5.38100004F));
                    builder.AddCubicBezier(new Vector2(67.223999F, -6.27799988F), new Vector2(67.4769974F, -7.01200008F), new Vector2(67.9840012F, -7.58400011F));
                    builder.AddCubicBezier(new Vector2(68.4909973F, -8.15600014F), new Vector2(69.1159973F, -8.44200039F), new Vector2(69.8580017F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(71.3300018F, -8.44200039F), new Vector2(72.0660019F, -7.45300007F), new Vector2(72.0660019F, -5.47399998F));
                    builder.AddLine(new Vector2(72.0660019F, 0F));
                    builder.AddLine(new Vector2(73.5879974F, 0F));
                    builder.AddLine(new Vector2(73.5879974F, -5.85400009F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_52()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(60.2980003F, -1.96700001F));
                    builder.AddCubicBezier(new Vector2(59.7630005F, -1.36099994F), new Vector2(59.0690002F, -1.05799997F), new Vector2(58.2150002F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(57.3740005F, -1.05799997F), new Vector2(56.6980019F, -1.375F), new Vector2(56.1879997F, -2.00900006F));
                    builder.AddCubicBezier(new Vector2(55.6780014F, -2.64299989F), new Vector2(55.4230003F, -3.48799992F), new Vector2(55.4230003F, -4.546F));
                    builder.AddCubicBezier(new Vector2(55.4230003F, -5.77699995F), new Vector2(55.6889992F, -6.73400021F), new Vector2(56.2210007F, -7.41699982F));
                    builder.AddCubicBezier(new Vector2(56.7529984F, -8.10000038F), new Vector2(57.4889984F, -8.44200039F), new Vector2(58.4290009F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(59.1899986F, -8.44200039F), new Vector2(59.8250008F, -8.17300034F), new Vector2(60.3349991F, -7.63500023F));
                    builder.AddCubicBezier(new Vector2(60.8450012F, -7.09700012F), new Vector2(61.1010017F, -6.45100021F), new Vector2(61.1010017F, -5.6960001F));
                    builder.AddLine(new Vector2(61.1010017F, -4.29500008F));
                    builder.AddCubicBezier(new Vector2(61.1010017F, -3.34899998F), new Vector2(60.8330002F, -2.57299995F), new Vector2(60.2980003F, -1.96700001F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(62.6220016F, -9.5F));
                    builder.AddLine(new Vector2(61.1010017F, -9.5F));
                    builder.AddLine(new Vector2(61.1010017F, -8.18299961F));
                    builder.AddLine(new Vector2(61.0629997F, -8.18299961F));
                    builder.AddCubicBezier(new Vector2(60.4440002F, -9.21000004F), new Vector2(59.4949989F, -9.72299957F), new Vector2(58.2150002F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(56.8670006F, -9.72299957F), new Vector2(55.8050003F, -9.24600029F), new Vector2(55.0289993F, -8.29399967F));
                    builder.AddCubicBezier(new Vector2(54.2529984F, -7.34100008F), new Vector2(53.8639984F, -6.05800009F), new Vector2(53.8639984F, -4.44399977F));
                    builder.AddCubicBezier(new Vector2(53.8639984F, -3.02099991F), new Vector2(54.223999F, -1.88800001F), new Vector2(54.9449997F, -1.04400003F));
                    builder.AddCubicBezier(new Vector2(55.6650009F, -0.200000003F), new Vector2(56.6170006F, 0.223000005F), new Vector2(57.7980003F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(59.2509995F, 0.223000005F), new Vector2(60.3390007F, -0.384000003F), new Vector2(61.0629997F, -1.59599996F));
                    builder.AddLine(new Vector2(61.1010017F, -1.59599996F));
                    builder.AddLine(new Vector2(61.1010017F, -0.556999981F));
                    builder.AddCubicBezier(new Vector2(61.1010017F, 1.92900002F), new Vector2(59.9319992F, 3.1730001F), new Vector2(57.5940018F, 3.1730001F));
                    builder.AddCubicBezier(new Vector2(56.6230011F, 3.1730001F), new Vector2(55.6020012F, 2.87599993F), new Vector2(54.5320015F, 2.28200006F));
                    builder.AddLine(new Vector2(54.5320015F, 3.8039999F));
                    builder.AddCubicBezier(new Vector2(55.4099998F, 4.24900007F), new Vector2(56.4370003F, 4.47200012F), new Vector2(57.6119995F, 4.47200012F));
                    builder.AddCubicBezier(new Vector2(60.9519997F, 4.47200012F), new Vector2(62.6220016F, 2.727F), new Vector2(62.6220016F, -0.760999978F));
                    builder.AddLine(new Vector2(62.6220016F, -9.5F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_53()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(51.4339981F, -9.5F));
                    builder.AddLine(new Vector2(49.9119987F, -9.5F));
                    builder.AddLine(new Vector2(49.9119987F, 0F));
                    builder.AddLine(new Vector2(51.4339981F, 0F));
                    builder.AddLine(new Vector2(51.4339981F, -9.5F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(51.401001F, -12.1999998F));
                    builder.AddCubicBezier(new Vector2(51.5960007F, -12.3920002F), new Vector2(51.6930008F, -12.6239996F), new Vector2(51.6930008F, -12.8959999F));
                    builder.AddCubicBezier(new Vector2(51.6930008F, -13.1800003F), new Vector2(51.5960007F, -13.4160004F), new Vector2(51.401001F, -13.6049995F));
                    builder.AddCubicBezier(new Vector2(51.2060013F, -13.7939997F), new Vector2(50.9690018F, -13.8879995F), new Vector2(50.6910019F, -13.8879995F));
                    builder.AddCubicBezier(new Vector2(50.4189987F, -13.8879995F), new Vector2(50.1879997F, -13.7939997F), new Vector2(49.9959984F, -13.6049995F));
                    builder.AddCubicBezier(new Vector2(49.8040009F, -13.4160004F), new Vector2(49.7080002F, -13.1800003F), new Vector2(49.7080002F, -12.8959999F));
                    builder.AddCubicBezier(new Vector2(49.7080002F, -12.6110001F), new Vector2(49.8040009F, -12.3760004F), new Vector2(49.9959984F, -12.1899996F));
                    builder.AddCubicBezier(new Vector2(50.1879997F, -12.0039997F), new Vector2(50.4189987F, -11.9119997F), new Vector2(50.6910019F, -11.9119997F));
                    builder.AddCubicBezier(new Vector2(50.9690018F, -11.9119997F), new Vector2(51.2060013F, -12.0080004F), new Vector2(51.401001F, -12.1999998F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_54()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(48.2610016F, -9.5369997F));
                    builder.AddCubicBezier(new Vector2(48.0509987F, -9.6239996F), new Vector2(47.7480011F, -9.66699982F), new Vector2(47.3520012F, -9.66699982F));
                    builder.AddCubicBezier(new Vector2(46.8009987F, -9.66699982F), new Vector2(46.3030014F, -9.47999954F), new Vector2(45.8580017F, -9.10599995F));
                    builder.AddCubicBezier(new Vector2(45.4129982F, -8.73200035F), new Vector2(45.0810013F, -8.21000004F), new Vector2(44.8650017F, -7.54199982F));
                    builder.AddLine(new Vector2(44.8279991F, -7.54199982F));
                    builder.AddLine(new Vector2(44.8279991F, -9.5F));
                    builder.AddLine(new Vector2(43.3069992F, -9.5F));
                    builder.AddLine(new Vector2(43.3069992F, 0F));
                    builder.AddLine(new Vector2(44.8279991F, 0F));
                    builder.AddLine(new Vector2(44.8279991F, -4.84299994F));
                    builder.AddCubicBezier(new Vector2(44.8279991F, -5.90100002F), new Vector2(45.0460014F, -6.73600006F), new Vector2(45.4819984F, -7.34800005F));
                    builder.AddCubicBezier(new Vector2(45.9179993F, -7.96000004F), new Vector2(46.4609985F, -8.26599979F), new Vector2(47.1100006F, -8.26599979F));
                    builder.AddCubicBezier(new Vector2(47.6110001F, -8.26599979F), new Vector2(47.9949989F, -8.16399956F), new Vector2(48.2610016F, -7.96000004F));
                    builder.AddLine(new Vector2(48.2610016F, -9.5369997F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_55()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(35.0219994F, -14.0640001F));
                    builder.AddLine(new Vector2(33.5F, -14.0640001F));
                    builder.AddLine(new Vector2(33.5F, 0F));
                    builder.AddLine(new Vector2(35.0219994F, 0F));
                    builder.AddLine(new Vector2(35.0219994F, -14.0640001F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_56()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(30.4200001F, -14.0640001F));
                    builder.AddLine(new Vector2(28.8990002F, -14.0640001F));
                    builder.AddLine(new Vector2(28.8990002F, 0F));
                    builder.AddLine(new Vector2(30.4200001F, 0F));
                    builder.AddLine(new Vector2(30.4200001F, -14.0640001F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_57()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(18.8889999F, -5.13000011F));
                    builder.AddLine(new Vector2(20.9580002F, -10.7989998F));
                    builder.AddCubicBezier(new Vector2(21.0380001F, -11.0150003F), new Vector2(21.1089993F, -11.3120003F), new Vector2(21.1709995F, -11.6890001F));
                    builder.AddLine(new Vector2(21.2080002F, -11.6890001F));
                    builder.AddCubicBezier(new Vector2(21.2759991F, -11.2810001F), new Vector2(21.3439999F, -10.9849997F), new Vector2(21.4120007F, -10.7989998F));
                    builder.AddLine(new Vector2(23.5F, -5.13000011F));
                    builder.AddLine(new Vector2(18.8889999F, -5.13000011F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(22.0240002F, -13.3039999F));
                    builder.AddLine(new Vector2(20.4099998F, -13.3039999F));
                    builder.AddLine(new Vector2(15.3079996F, 0F));
                    builder.AddLine(new Vector2(17.0419998F, 0F));
                    builder.AddLine(new Vector2(18.3689995F, -3.72900009F));
                    builder.AddLine(new Vector2(24.0100002F, -3.72900009F));
                    builder.AddLine(new Vector2(25.4200001F, 0F));
                    builder.AddLine(new Vector2(27.1459999F, 0F));
                    builder.AddLine(new Vector2(22.0240002F, -13.3039999F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_58()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(8.70199966F, -5.89099979F));
                    builder.AddLine(new Vector2(3.63700008F, -5.89099979F));
                    builder.AddLine(new Vector2(3.63700008F, -4.69399977F));
                    builder.AddLine(new Vector2(8.70199966F, -4.69399977F));
                    builder.AddLine(new Vector2(8.70199966F, -5.89099979F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_59()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-10.3540001F, -5.65899992F));
                    builder.AddCubicBezier(new Vector2(-10.2370005F, -6.48799992F), new Vector2(-9.92700005F, -7.15899992F), new Vector2(-9.42599964F, -7.67199993F));
                    builder.AddCubicBezier(new Vector2(-8.92500019F, -8.18500042F), new Vector2(-8.31599998F, -8.44200039F), new Vector2(-7.59800005F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(-6.85599995F, -8.44200039F), new Vector2(-6.27600002F, -8.19799995F), new Vector2(-5.85900021F, -7.70900011F));
                    builder.AddCubicBezier(new Vector2(-5.44199991F, -7.21999979F), new Vector2(-5.22900009F, -6.53700018F), new Vector2(-5.22300005F, -5.65899992F));
                    builder.AddLine(new Vector2(-10.3540001F, -5.65899992F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(-3.66499996F, -5.16699982F));
                    builder.AddCubicBezier(new Vector2(-3.66499996F, -6.59600019F), new Vector2(-4.00500011F, -7.71299982F), new Vector2(-4.68499994F, -8.5170002F));
                    builder.AddCubicBezier(new Vector2(-5.36499977F, -9.3210001F), new Vector2(-6.32700014F, -9.72299957F), new Vector2(-7.57000017F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(-8.81299973F, -9.72299957F), new Vector2(-9.85400009F, -9.25699997F), new Vector2(-10.6920004F, -8.32600021F));
                    builder.AddCubicBezier(new Vector2(-11.5299997F, -7.39499998F), new Vector2(-11.9490004F, -6.19099998F), new Vector2(-11.9490004F, -4.71299982F));
                    builder.AddCubicBezier(new Vector2(-11.9490004F, -3.148F), new Vector2(-11.566F, -1.93499994F), new Vector2(-10.7989998F, -1.07200003F));
                    builder.AddCubicBezier(new Vector2(-10.0319996F, -0.209000006F), new Vector2(-8.97700024F, 0.223000005F), new Vector2(-7.63500023F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(-6.26200008F, 0.223000005F), new Vector2(-5.15799999F, -0.0810000002F), new Vector2(-4.32299995F, -0.686999977F));
                    builder.AddLine(new Vector2(-4.32299995F, -2.11500001F));
                    builder.AddCubicBezier(new Vector2(-5.21999979F, -1.40999997F), new Vector2(-6.20300007F, -1.05799997F), new Vector2(-7.27299976F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(-8.22599983F, -1.05799997F), new Vector2(-8.97500038F, -1.34500003F), new Vector2(-9.51900005F, -1.91999996F));
                    builder.AddCubicBezier(new Vector2(-10.0629997F, -2.49499989F), new Vector2(-10.3470001F, -3.31200004F), new Vector2(-10.3719997F, -4.36999989F));
                    builder.AddLine(new Vector2(-3.66499996F, -4.36999989F));
                    builder.AddLine(new Vector2(-3.66499996F, -5.16699982F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_60()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-17.3950005F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(-16.8999996F, 0.223000005F), new Vector2(-16.4279995F, 0.166999996F), new Vector2(-15.9759998F, 0.0560000017F));
                    builder.AddCubicBezier(new Vector2(-15.5249996F, -0.0549999997F), new Vector2(-15.125F, -0.225999996F), new Vector2(-14.7790003F, -0.455000013F));
                    builder.AddCubicBezier(new Vector2(-14.4329996F, -0.684000015F), new Vector2(-14.1549997F, -0.971000016F), new Vector2(-13.948F, -1.31700003F));
                    builder.AddCubicBezier(new Vector2(-13.7410002F, -1.66299999F), new Vector2(-13.6379995F, -2.07200003F), new Vector2(-13.6379995F, -2.54200006F));
                    builder.AddCubicBezier(new Vector2(-13.6379995F, -2.92499995F), new Vector2(-13.7010002F, -3.25699997F), new Vector2(-13.8280001F, -3.53500009F));
                    builder.AddCubicBezier(new Vector2(-13.9549999F, -3.81299996F), new Vector2(-14.1359997F, -4.05900002F), new Vector2(-14.3710003F, -4.27199984F));
                    builder.AddCubicBezier(new Vector2(-14.6059999F, -4.48500013F), new Vector2(-14.8900003F, -4.67700005F), new Vector2(-15.224F, -4.84700012F));
                    builder.AddCubicBezier(new Vector2(-15.5579996F, -5.0170002F), new Vector2(-15.9350004F, -5.18300009F), new Vector2(-16.3560009F, -5.34399986F));
                    builder.AddCubicBezier(new Vector2(-16.6709995F, -5.46799994F), new Vector2(-16.9529991F, -5.58199978F), new Vector2(-17.2000008F, -5.6869998F));
                    builder.AddCubicBezier(new Vector2(-17.4470005F, -5.79199982F), new Vector2(-17.6580009F, -5.90999985F), new Vector2(-17.8309994F, -6.03999996F));
                    builder.AddCubicBezier(new Vector2(-18.0039997F, -6.17000008F), new Vector2(-18.1350002F, -6.31799984F), new Vector2(-18.2250004F, -6.48500013F));
                    builder.AddCubicBezier(new Vector2(-18.3150005F, -6.65199995F), new Vector2(-18.3600006F, -6.85900021F), new Vector2(-18.3600006F, -7.10599995F));
                    builder.AddCubicBezier(new Vector2(-18.3600006F, -7.3039999F), new Vector2(-18.3150005F, -7.48500013F), new Vector2(-18.2250004F, -7.64900017F));
                    builder.AddCubicBezier(new Vector2(-18.1350002F, -7.8130002F), new Vector2(-18.0090008F, -7.95300007F), new Vector2(-17.8449993F, -8.0710001F));
                    builder.AddCubicBezier(new Vector2(-17.6809998F, -8.18799973F), new Vector2(-17.4850006F, -8.27999973F), new Vector2(-17.2560005F, -8.34500027F));
                    builder.AddCubicBezier(new Vector2(-17.0270004F, -8.40999985F), new Vector2(-16.7709999F, -8.44200039F), new Vector2(-16.4860001F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(-15.5769997F, -8.44200039F), new Vector2(-14.7729998F, -8.21399975F), new Vector2(-14.0740004F, -7.75600004F));
                    builder.AddLine(new Vector2(-14.0740004F, -9.29599953F));
                    builder.AddCubicBezier(new Vector2(-14.7229996F, -9.57999992F), new Vector2(-15.46F, -9.72299957F), new Vector2(-16.2819996F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(-16.7460003F, -9.72299957F), new Vector2(-17.1959991F, -9.66300011F), new Vector2(-17.632F, -9.54599953F));
                    builder.AddCubicBezier(new Vector2(-18.0680008F, -9.42800045F), new Vector2(-18.4559994F, -9.25399971F), new Vector2(-18.7959995F, -9.02200031F));
                    builder.AddCubicBezier(new Vector2(-19.1359997F, -8.78999996F), new Vector2(-19.4080009F, -8.50199986F), new Vector2(-19.6119995F, -8.1590004F));
                    builder.AddCubicBezier(new Vector2(-19.816F, -7.81599998F), new Vector2(-19.9179993F, -7.42199993F), new Vector2(-19.9179993F, -6.97700024F));
                    builder.AddCubicBezier(new Vector2(-19.9179993F, -6.61199999F), new Vector2(-19.8640003F, -6.29300022F), new Vector2(-19.7560005F, -6.02099991F));
                    builder.AddCubicBezier(new Vector2(-19.6480007F, -5.74900007F), new Vector2(-19.4869995F, -5.50600004F), new Vector2(-19.2740002F, -5.29300022F));
                    builder.AddCubicBezier(new Vector2(-19.0610008F, -5.07999992F), new Vector2(-18.7950001F, -4.88800001F), new Vector2(-18.4759998F, -4.71799994F));
                    builder.AddCubicBezier(new Vector2(-18.1580009F, -4.54799986F), new Vector2(-17.7880001F, -4.37900019F), new Vector2(-17.3670006F, -4.21199989F));
                    builder.AddCubicBezier(new Vector2(-17.0639992F, -4.09399986F), new Vector2(-16.7810001F, -3.98099995F), new Vector2(-16.5179996F, -3.87299991F));
                    builder.AddCubicBezier(new Vector2(-16.2549992F, -3.7650001F), new Vector2(-16.0270004F, -3.64299989F), new Vector2(-15.8319998F, -3.50699997F));
                    builder.AddCubicBezier(new Vector2(-15.6370001F, -3.37100005F), new Vector2(-15.4829998F, -3.21399999F), new Vector2(-15.368F, -3.03800011F));
                    builder.AddCubicBezier(new Vector2(-15.2539997F, -2.86199999F), new Vector2(-15.1960001F, -2.648F), new Vector2(-15.1960001F, -2.39400005F));
                    builder.AddCubicBezier(new Vector2(-15.1960001F, -1.50300002F), new Vector2(-15.8640003F, -1.05799997F), new Vector2(-17.2000008F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(-18.1959991F, -1.05799997F), new Vector2(-19.1079998F, -1.36399996F), new Vector2(-19.9370003F, -1.97599995F));
                    builder.AddLine(new Vector2(-19.9370003F, -0.342999995F));
                    builder.AddCubicBezier(new Vector2(-19.2199993F, 0.0340000018F), new Vector2(-18.3719997F, 0.223000005F), new Vector2(-17.3950005F, 0.223000005F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_61()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-22.2290001F, -5.80800009F));
                    builder.AddCubicBezier(new Vector2(-22.2290001F, -7.07600021F), new Vector2(-22.5039997F, -8.04500008F), new Vector2(-23.0540009F, -8.7159996F));
                    builder.AddCubicBezier(new Vector2(-23.6049995F, -9.38700008F), new Vector2(-24.3990002F, -9.72299957F), new Vector2(-25.4379997F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(-26.7989998F, -9.72299957F), new Vector2(-27.8390007F, -9.12300014F), new Vector2(-28.5559998F, -7.92299986F));
                    builder.AddLine(new Vector2(-28.5930004F, -7.92299986F));
                    builder.AddLine(new Vector2(-28.5930004F, -9.5F));
                    builder.AddLine(new Vector2(-30.1140003F, -9.5F));
                    builder.AddLine(new Vector2(-30.1140003F, 0F));
                    builder.AddLine(new Vector2(-28.5930004F, 0F));
                    builder.AddLine(new Vector2(-28.5930004F, -5.41800022F));
                    builder.AddCubicBezier(new Vector2(-28.5930004F, -6.28999996F), new Vector2(-28.3439999F, -7.01200008F), new Vector2(-27.8460007F, -7.58400011F));
                    builder.AddCubicBezier(new Vector2(-27.3479996F, -8.15600014F), new Vector2(-26.7189999F, -8.44200039F), new Vector2(-25.9580002F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(-24.4860001F, -8.44200039F), new Vector2(-23.75F, -7.43400002F), new Vector2(-23.75F, -5.41800022F));
                    builder.AddLine(new Vector2(-23.75F, 0F));
                    builder.AddLine(new Vector2(-22.2290001F, 0F));
                    builder.AddLine(new Vector2(-22.2290001F, -5.80800009F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_62()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-39.1040001F, -5.65899992F));
                    builder.AddCubicBezier(new Vector2(-38.9869995F, -6.48799992F), new Vector2(-38.6769981F, -7.15899992F), new Vector2(-38.1759987F, -7.67199993F));
                    builder.AddCubicBezier(new Vector2(-37.6749992F, -8.18500042F), new Vector2(-37.0670013F, -8.44200039F), new Vector2(-36.348999F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(-35.6069984F, -8.44200039F), new Vector2(-35.026001F, -8.19799995F), new Vector2(-34.6090012F, -7.70900011F));
                    builder.AddCubicBezier(new Vector2(-34.1920013F, -7.21999979F), new Vector2(-33.9799995F, -6.53700018F), new Vector2(-33.973999F, -5.65899992F));
                    builder.AddLine(new Vector2(-39.1040001F, -5.65899992F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(-32.4150009F, -5.16699982F));
                    builder.AddCubicBezier(new Vector2(-32.4150009F, -6.59600019F), new Vector2(-32.7560005F, -7.71299982F), new Vector2(-33.4360008F, -8.5170002F));
                    builder.AddCubicBezier(new Vector2(-34.1160011F, -9.3210001F), new Vector2(-35.0779991F, -9.72299957F), new Vector2(-36.3209991F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(-37.5639992F, -9.72299957F), new Vector2(-38.6049995F, -9.25699997F), new Vector2(-39.4430008F, -8.32600021F));
                    builder.AddCubicBezier(new Vector2(-40.2809982F, -7.39499998F), new Vector2(-40.7000008F, -6.19099998F), new Vector2(-40.7000008F, -4.71299982F));
                    builder.AddCubicBezier(new Vector2(-40.7000008F, -3.148F), new Vector2(-40.3160019F, -1.93499994F), new Vector2(-39.5489998F, -1.07200003F));
                    builder.AddCubicBezier(new Vector2(-38.7820015F, -0.209000006F), new Vector2(-37.7280006F, 0.223000005F), new Vector2(-36.3860016F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(-35.0130005F, 0.223000005F), new Vector2(-33.9090004F, -0.0810000002F), new Vector2(-33.0740013F, -0.686999977F));
                    builder.AddLine(new Vector2(-33.0740013F, -2.11500001F));
                    builder.AddCubicBezier(new Vector2(-33.9710007F, -1.40999997F), new Vector2(-34.9539986F, -1.05799997F), new Vector2(-36.0239983F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(-36.9770012F, -1.05799997F), new Vector2(-37.7249985F, -1.34500003F), new Vector2(-38.269001F, -1.91999996F));
                    builder.AddCubicBezier(new Vector2(-38.8129997F, -2.49499989F), new Vector2(-39.0979996F, -3.31200004F), new Vector2(-39.1230011F, -4.36999989F));
                    builder.AddLine(new Vector2(-32.4150009F, -4.36999989F));
                    builder.AddLine(new Vector2(-32.4150009F, -5.16699982F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_63()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-42.3510017F, -1.88300002F));
                    builder.AddCubicBezier(new Vector2(-43.0810013F, -1.33200002F), new Vector2(-43.8600006F, -1.05799997F), new Vector2(-44.6889992F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(-45.6720009F, -1.05799997F), new Vector2(-46.4570007F, -1.37899995F), new Vector2(-47.0410004F, -2.02200007F));
                    builder.AddCubicBezier(new Vector2(-47.625F, -2.66499996F), new Vector2(-47.9169998F, -3.54399991F), new Vector2(-47.9169998F, -4.65700006F));
                    builder.AddCubicBezier(new Vector2(-47.9169998F, -5.78900003F), new Vector2(-47.6069984F, -6.70300007F), new Vector2(-46.9850006F, -7.39900017F));
                    builder.AddCubicBezier(new Vector2(-46.362999F, -8.09500027F), new Vector2(-45.5670013F, -8.44200039F), new Vector2(-44.5960007F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(-43.7919998F, -8.44200039F), new Vector2(-43.0379982F, -8.19499969F), new Vector2(-42.3330002F, -7.69999981F));
                    builder.AddLine(new Vector2(-42.3330002F, -9.25899982F));
                    builder.AddCubicBezier(new Vector2(-42.9700012F, -9.56799984F), new Vector2(-43.7060013F, -9.72299957F), new Vector2(-44.5410004F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(-46.0379982F, -9.72299957F), new Vector2(-47.2340012F, -9.24800014F), new Vector2(-48.1310005F, -8.29899979F));
                    builder.AddCubicBezier(new Vector2(-49.0279999F, -7.3499999F), new Vector2(-49.4760017F, -6.09200001F), new Vector2(-49.4760017F, -4.52699995F));
                    builder.AddCubicBezier(new Vector2(-49.4760017F, -3.12299991F), new Vector2(-49.0600014F, -1.98000002F), new Vector2(-48.2280006F, -1.09899998F));
                    builder.AddCubicBezier(new Vector2(-47.3959999F, -0.217999995F), new Vector2(-46.3030014F, 0.223000005F), new Vector2(-44.9490013F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(-43.9469986F, 0.223000005F), new Vector2(-43.0810013F, 0.00300000003F), new Vector2(-42.3510017F, -0.43599999F));
                    builder.AddLine(new Vector2(-42.3510017F, -1.88300002F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_64()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-51.9070015F, -9.5F));
                    builder.AddLine(new Vector2(-53.4280014F, -9.5F));
                    builder.AddLine(new Vector2(-53.4280014F, 0F));
                    builder.AddLine(new Vector2(-51.9070015F, 0F));
                    builder.AddLine(new Vector2(-51.9070015F, -9.5F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(-51.9389992F, -12.1999998F));
                    builder.AddCubicBezier(new Vector2(-51.7439995F, -12.3920002F), new Vector2(-51.6469994F, -12.6239996F), new Vector2(-51.6469994F, -12.8959999F));
                    builder.AddCubicBezier(new Vector2(-51.6469994F, -13.1800003F), new Vector2(-51.7439995F, -13.4160004F), new Vector2(-51.9389992F, -13.6049995F));
                    builder.AddCubicBezier(new Vector2(-52.1339989F, -13.7939997F), new Vector2(-52.3709984F, -13.8879995F), new Vector2(-52.6489983F, -13.8879995F));
                    builder.AddCubicBezier(new Vector2(-52.9210014F, -13.8879995F), new Vector2(-53.1529999F, -13.7939997F), new Vector2(-53.3450012F, -13.6049995F));
                    builder.AddCubicBezier(new Vector2(-53.5369987F, -13.4160004F), new Vector2(-53.632F, -13.1800003F), new Vector2(-53.632F, -12.8959999F));
                    builder.AddCubicBezier(new Vector2(-53.632F, -12.6110001F), new Vector2(-53.5369987F, -12.3760004F), new Vector2(-53.3450012F, -12.1899996F));
                    builder.AddCubicBezier(new Vector2(-53.1529999F, -12.0039997F), new Vector2(-52.9210014F, -11.9119997F), new Vector2(-52.6489983F, -11.9119997F));
                    builder.AddCubicBezier(new Vector2(-52.3709984F, -11.9119997F), new Vector2(-52.1339989F, -12.0080004F), new Vector2(-51.9389992F, -12.1999998F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_65()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-55.2649994F, -1.40999997F));
                    builder.AddLine(new Vector2(-60.6090012F, -1.40999997F));
                    builder.AddLine(new Vector2(-60.6090012F, -13.3039999F));
                    builder.AddLine(new Vector2(-62.1669998F, -13.3039999F));
                    builder.AddLine(new Vector2(-62.1669998F, 0F));
                    builder.AddLine(new Vector2(-55.2649994F, 0F));
                    builder.AddLine(new Vector2(-55.2649994F, -1.40999997F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_66()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-69.4589996F, -13.3039999F));
                    builder.AddLine(new Vector2(-78.6900024F, -13.3039999F));
                    builder.AddLine(new Vector2(-78.6900024F, -11.8940001F));
                    builder.AddLine(new Vector2(-74.8590012F, -11.8940001F));
                    builder.AddLine(new Vector2(-74.8590012F, 0F));
                    builder.AddLine(new Vector2(-73.3000031F, 0F));
                    builder.AddLine(new Vector2(-73.3000031F, -11.8940001F));
                    builder.AddLine(new Vector2(-69.4589996F, -11.8940001F));
                    builder.AddLine(new Vector2(-69.4589996F, -13.3039999F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_67()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-80.8239975F, -13.3039999F));
                    builder.AddLine(new Vector2(-82.3830032F, -13.3039999F));
                    builder.AddLine(new Vector2(-82.3830032F, 0F));
                    builder.AddLine(new Vector2(-80.8239975F, 0F));
                    builder.AddLine(new Vector2(-80.8239975F, -13.3039999F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_68()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-85.8799973F, -13.3039999F));
                    builder.AddLine(new Vector2(-87.8290024F, -13.3039999F));
                    builder.AddLine(new Vector2(-91.9940033F, -4.0630002F));
                    builder.AddCubicBezier(new Vector2(-92.1549988F, -3.704F), new Vector2(-92.3679962F, -3.16100001F), new Vector2(-92.6340027F, -2.43099999F));
                    builder.AddLine(new Vector2(-92.6900024F, -2.43099999F));
                    builder.AddCubicBezier(new Vector2(-92.7829971F, -2.78399992F), new Vector2(-92.9869995F, -3.31500006F), new Vector2(-93.302002F, -4.02600002F));
                    builder.AddLine(new Vector2(-97.3840027F, -13.3039999F));
                    builder.AddLine(new Vector2(-99.4440002F, -13.3039999F));
                    builder.AddLine(new Vector2(-99.4440002F, 0F));
                    builder.AddLine(new Vector2(-97.9410019F, 0F));
                    builder.AddLine(new Vector2(-97.9410019F, -8.90600014F));
                    builder.AddCubicBezier(new Vector2(-97.9410019F, -10.1120005F), new Vector2(-97.9660034F, -10.9809999F), new Vector2(-98.0149994F, -11.5129995F));
                    builder.AddLine(new Vector2(-97.9779968F, -11.5129995F));
                    builder.AddCubicBezier(new Vector2(-97.8420029F, -10.901F), new Vector2(-97.7089996F, -10.4460001F), new Vector2(-97.5790024F, -10.1490002F));
                    builder.AddLine(new Vector2(-93.0419998F, 0F));
                    builder.AddLine(new Vector2(-92.2819977F, 0F));
                    builder.AddLine(new Vector2(-87.7360001F, -10.224F));
                    builder.AddCubicBezier(new Vector2(-87.6190033F, -10.4840002F), new Vector2(-87.4850006F, -10.9130001F), new Vector2(-87.336998F, -11.5129995F));
                    builder.AddLine(new Vector2(-87.3000031F, -11.5129995F));
                    builder.AddCubicBezier(new Vector2(-87.387001F, -10.4919996F), new Vector2(-87.4300003F, -9.63000011F), new Vector2(-87.4300003F, -8.92500019F));
                    builder.AddLine(new Vector2(-87.4300003F, 0F));
                    builder.AddLine(new Vector2(-85.8799973F, 0F));
                    builder.AddLine(new Vector2(-85.8799973F, -13.3039999F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_69()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-106.503998F, -9.5369997F));
                    builder.AddCubicBezier(new Vector2(-106.713997F, -9.6239996F), new Vector2(-107.016998F, -9.66699982F), new Vector2(-107.413002F, -9.66699982F));
                    builder.AddCubicBezier(new Vector2(-107.963997F, -9.66699982F), new Vector2(-108.461998F, -9.47999954F), new Vector2(-108.906998F, -9.10599995F));
                    builder.AddCubicBezier(new Vector2(-109.351997F, -8.73200035F), new Vector2(-109.682999F, -8.21000004F), new Vector2(-109.899002F, -7.54199982F));
                    builder.AddLine(new Vector2(-109.936996F, -7.54199982F));
                    builder.AddLine(new Vector2(-109.936996F, -9.5F));
                    builder.AddLine(new Vector2(-111.458F, -9.5F));
                    builder.AddLine(new Vector2(-111.458F, 0F));
                    builder.AddLine(new Vector2(-109.936996F, 0F));
                    builder.AddLine(new Vector2(-109.936996F, -4.84299994F));
                    builder.AddCubicBezier(new Vector2(-109.936996F, -5.90100002F), new Vector2(-109.718002F, -6.73600006F), new Vector2(-109.281998F, -7.34800005F));
                    builder.AddCubicBezier(new Vector2(-108.846001F, -7.96000004F), new Vector2(-108.303001F, -8.26599979F), new Vector2(-107.653999F, -8.26599979F));
                    builder.AddCubicBezier(new Vector2(-107.153F, -8.26599979F), new Vector2(-106.769997F, -8.16399956F), new Vector2(-106.503998F, -7.96000004F));
                    builder.AddLine(new Vector2(-106.503998F, -9.5369997F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_70()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-120.447998F, -5.65899992F));
                    builder.AddCubicBezier(new Vector2(-120.331001F, -6.48799992F), new Vector2(-120.021004F, -7.15899992F), new Vector2(-119.519997F, -7.67199993F));
                    builder.AddCubicBezier(new Vector2(-119.018997F, -8.18500042F), new Vector2(-118.410004F, -8.44200039F), new Vector2(-117.692001F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(-116.949997F, -8.44200039F), new Vector2(-116.370003F, -8.19799995F), new Vector2(-115.953003F, -7.70900011F));
                    builder.AddCubicBezier(new Vector2(-115.536003F, -7.21999979F), new Vector2(-115.322998F, -6.53700018F), new Vector2(-115.317001F, -5.65899992F));
                    builder.AddLine(new Vector2(-120.447998F, -5.65899992F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(-113.759003F, -5.16699982F));
                    builder.AddCubicBezier(new Vector2(-113.759003F, -6.59600019F), new Vector2(-114.098999F, -7.71299982F), new Vector2(-114.778999F, -8.5170002F));
                    builder.AddCubicBezier(new Vector2(-115.459F, -9.3210001F), new Vector2(-116.421997F, -9.72299957F), new Vector2(-117.665001F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(-118.907997F, -9.72299957F), new Vector2(-119.947998F, -9.25699997F), new Vector2(-120.786003F, -8.32600021F));
                    builder.AddCubicBezier(new Vector2(-121.624001F, -7.39499998F), new Vector2(-122.042999F, -6.19099998F), new Vector2(-122.042999F, -4.71299982F));
                    builder.AddCubicBezier(new Vector2(-122.042999F, -3.148F), new Vector2(-121.660004F, -1.93499994F), new Vector2(-120.892998F, -1.07200003F));
                    builder.AddCubicBezier(new Vector2(-120.125999F, -0.209000006F), new Vector2(-119.070999F, 0.223000005F), new Vector2(-117.728996F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(-116.356003F, 0.223000005F), new Vector2(-115.251999F, -0.0810000002F), new Vector2(-114.417F, -0.686999977F));
                    builder.AddLine(new Vector2(-114.417F, -2.11500001F));
                    builder.AddCubicBezier(new Vector2(-115.314003F, -1.40999997F), new Vector2(-116.297997F, -1.05799997F), new Vector2(-117.367996F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(-118.320999F, -1.05799997F), new Vector2(-119.069F, -1.34500003F), new Vector2(-119.612999F, -1.91999996F));
                    builder.AddCubicBezier(new Vector2(-120.156998F, -2.49499989F), new Vector2(-120.441002F, -3.31200004F), new Vector2(-120.466003F, -4.36999989F));
                    builder.AddLine(new Vector2(-113.759003F, -4.36999989F));
                    builder.AddLine(new Vector2(-113.759003F, -5.16699982F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_71()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-126.789001F, -1.97599995F));
                    builder.AddCubicBezier(new Vector2(-127.318001F, -1.36399996F), new Vector2(-128.005997F, -1.05799997F), new Vector2(-128.852997F, -1.05799997F));
                    builder.AddCubicBezier(new Vector2(-129.712997F, -1.05799997F), new Vector2(-130.397995F, -1.375F), new Vector2(-130.908005F, -2.00900006F));
                    builder.AddCubicBezier(new Vector2(-131.417999F, -2.64299989F), new Vector2(-131.673004F, -3.50699997F), new Vector2(-131.673004F, -4.60200024F));
                    builder.AddCubicBezier(new Vector2(-131.673004F, -5.80200005F), new Vector2(-131.406998F, -6.7420001F), new Vector2(-130.875F, -7.42199993F));
                    builder.AddCubicBezier(new Vector2(-130.343002F, -8.10200024F), new Vector2(-129.613998F, -8.44200039F), new Vector2(-128.686005F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(-127.906998F, -8.44200039F), new Vector2(-127.263F, -8.17700005F), new Vector2(-126.755997F, -7.64499998F));
                    builder.AddCubicBezier(new Vector2(-126.249001F, -7.11299992F), new Vector2(-125.996002F, -6.46299982F), new Vector2(-125.996002F, -5.6960001F));
                    builder.AddLine(new Vector2(-125.996002F, -4.29500008F));
                    builder.AddCubicBezier(new Vector2(-125.996002F, -3.36100006F), new Vector2(-126.260002F, -2.58800006F), new Vector2(-126.789001F, -1.97599995F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(-124.473999F, -14.0640001F));
                    builder.AddLine(new Vector2(-125.996002F, -14.0640001F));
                    builder.AddLine(new Vector2(-125.996002F, -8.18299961F));
                    builder.AddLine(new Vector2(-126.032997F, -8.18299961F));
                    builder.AddCubicBezier(new Vector2(-126.626999F, -9.21000004F), new Vector2(-127.575996F, -9.72299957F), new Vector2(-128.880997F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(-130.197998F, -9.72299957F), new Vector2(-131.251999F, -9.2489996F), new Vector2(-132.044006F, -8.30300045F));
                    builder.AddCubicBezier(new Vector2(-132.835999F, -7.35699987F), new Vector2(-133.231995F, -6.09800005F), new Vector2(-133.231995F, -4.52699995F));
                    builder.AddCubicBezier(new Vector2(-133.231995F, -3.06100011F), new Vector2(-132.873993F, -1.903F), new Vector2(-132.160004F, -1.05299997F));
                    builder.AddCubicBezier(new Vector2(-131.445999F, -0.202999994F), new Vector2(-130.492004F, 0.223000005F), new Vector2(-129.298004F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(-127.825996F, 0.223000005F), new Vector2(-126.737999F, -0.388999999F), new Vector2(-126.032997F, -1.61399996F));
                    builder.AddLine(new Vector2(-125.996002F, -1.61399996F));
                    builder.AddLine(new Vector2(-125.996002F, 0F));
                    builder.AddLine(new Vector2(-124.473999F, 0F));
                    builder.AddLine(new Vector2(-124.473999F, -14.0640001F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_72()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-135.449005F, -5.80800009F));
                    builder.AddCubicBezier(new Vector2(-135.449005F, -7.07600021F), new Vector2(-135.725006F, -8.04500008F), new Vector2(-136.274994F, -8.7159996F));
                    builder.AddCubicBezier(new Vector2(-136.826004F, -9.38700008F), new Vector2(-137.619995F, -9.72299957F), new Vector2(-138.658997F, -9.72299957F));
                    builder.AddCubicBezier(new Vector2(-140.020004F, -9.72299957F), new Vector2(-141.059006F, -9.12300014F), new Vector2(-141.776001F, -7.92299986F));
                    builder.AddLine(new Vector2(-141.813004F, -7.92299986F));
                    builder.AddLine(new Vector2(-141.813004F, -9.5F));
                    builder.AddLine(new Vector2(-143.335007F, -9.5F));
                    builder.AddLine(new Vector2(-143.335007F, 0F));
                    builder.AddLine(new Vector2(-141.813004F, 0F));
                    builder.AddLine(new Vector2(-141.813004F, -5.41800022F));
                    builder.AddCubicBezier(new Vector2(-141.813004F, -6.28999996F), new Vector2(-141.565002F, -7.01200008F), new Vector2(-141.067001F, -7.58400011F));
                    builder.AddCubicBezier(new Vector2(-140.569F, -8.15600014F), new Vector2(-139.940002F, -8.44200039F), new Vector2(-139.179001F, -8.44200039F));
                    builder.AddCubicBezier(new Vector2(-137.707001F, -8.44200039F), new Vector2(-136.970993F, -7.43400002F), new Vector2(-136.970993F, -5.41800022F));
                    builder.AddLine(new Vector2(-136.970993F, 0F));
                    builder.AddLine(new Vector2(-135.449005F, 0F));
                    builder.AddLine(new Vector2(-135.449005F, -5.80800009F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_73()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(-146.451996F, -13.3039999F));
                    builder.AddLine(new Vector2(-148.011002F, -13.3039999F));
                    builder.AddLine(new Vector2(-148.011002F, -5.13000011F));
                    builder.AddCubicBezier(new Vector2(-148.011002F, -2.49499989F), new Vector2(-149.121002F, -1.17799997F), new Vector2(-151.341003F, -1.17799997F));
                    builder.AddCubicBezier(new Vector2(-153.641998F, -1.17799997F), new Vector2(-154.792007F, -2.54200006F), new Vector2(-154.792007F, -5.26999998F));
                    builder.AddLine(new Vector2(-154.792007F, -13.3039999F));
                    builder.AddLine(new Vector2(-156.350998F, -13.3039999F));
                    builder.AddLine(new Vector2(-156.350998F, -5.16699982F));
                    builder.AddCubicBezier(new Vector2(-156.350998F, -1.57299995F), new Vector2(-154.737F, 0.223000005F), new Vector2(-151.507996F, 0.223000005F));
                    builder.AddCubicBezier(new Vector2(-148.136993F, 0.223000005F), new Vector2(-146.451996F, -1.64499998F), new Vector2(-146.451996F, -5.38100004F));
                    builder.AddLine(new Vector2(-146.451996F, -13.3039999F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_74()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(248.675003F, -11.3970003F));
                    builder.AddLine(new Vector2(248.675003F, -13.7749996F));
                    builder.AddLine(new Vector2(239.279007F, -13.7749996F));
                    builder.AddLine(new Vector2(239.279007F, -11.3970003F));
                    builder.AddLine(new Vector2(248.675003F, -11.3970003F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(245.281998F, 0F));
                    builder.AddLine(new Vector2(245.281998F, -19.5459995F));
                    builder.AddLine(new Vector2(242.671997F, -19.5459995F));
                    builder.AddLine(new Vector2(242.671997F, 0F));
                    builder.AddLine(new Vector2(245.281998F, 0F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_75()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(235.031006F, -0.319000006F));
                    builder.AddCubicBezier(new Vector2(235.949005F, -0.725000024F), new Vector2(236.727005F, -1.29499996F), new Vector2(237.365005F, -2.02999997F));
                    builder.AddLine(new Vector2(235.654007F, -3.76999998F));
                    builder.AddCubicBezier(new Vector2(235.209F, -3.26699996F), new Vector2(234.682999F, -2.88499999F), new Vector2(234.074005F, -2.62400007F));
                    builder.AddCubicBezier(new Vector2(233.464996F, -2.36299992F), new Vector2(232.792999F, -2.23300004F), new Vector2(232.057999F, -2.23300004F));
                    builder.AddCubicBezier(new Vector2(231.188004F, -2.23300004F), new Vector2(230.414993F, -2.43600011F), new Vector2(229.738007F, -2.84200001F));
                    builder.AddCubicBezier(new Vector2(229.061005F, -3.24799991F), new Vector2(228.529999F, -3.79900002F), new Vector2(228.143005F, -4.49499989F));
                    builder.AddCubicBezier(new Vector2(227.755997F, -5.19099998F), new Vector2(227.563004F, -5.99300003F), new Vector2(227.563004F, -6.90199995F));
                    builder.AddCubicBezier(new Vector2(227.563004F, -7.81099987F), new Vector2(227.755997F, -8.61299992F), new Vector2(228.143005F, -9.30900002F));
                    builder.AddCubicBezier(new Vector2(228.529999F, -10.0050001F), new Vector2(229.061005F, -10.5509996F), new Vector2(229.738007F, -10.9469995F));
                    builder.AddCubicBezier(new Vector2(230.414993F, -11.3430004F), new Vector2(231.188004F, -11.5419998F), new Vector2(232.057999F, -11.5419998F));
                    builder.AddCubicBezier(new Vector2(232.772995F, -11.5419998F), new Vector2(233.440002F, -11.4110003F), new Vector2(234.059006F, -11.1499996F));
                    builder.AddCubicBezier(new Vector2(234.677994F, -10.8889999F), new Vector2(235.199997F, -10.5080004F), new Vector2(235.625F, -10.0050001F));
                    builder.AddLine(new Vector2(237.365005F, -11.7449999F));
                    builder.AddCubicBezier(new Vector2(236.707993F, -12.4989996F), new Vector2(235.925003F, -13.0749998F), new Vector2(235.016006F, -13.4709997F));
                    builder.AddCubicBezier(new Vector2(234.106995F, -13.8669996F), new Vector2(233.121002F, -14.0649996F), new Vector2(232.057999F, -14.0649996F));
                    builder.AddCubicBezier(new Vector2(230.705002F, -14.0649996F), new Vector2(229.481003F, -13.75F), new Vector2(228.389008F, -13.1219997F));
                    builder.AddCubicBezier(new Vector2(227.296997F, -12.4940004F), new Vector2(226.436996F, -11.6389999F), new Vector2(225.809006F, -10.5559998F));
                    builder.AddCubicBezier(new Vector2(225.181F, -9.47299957F), new Vector2(224.865997F, -8.25500011F), new Vector2(224.865997F, -6.90199995F));
                    builder.AddCubicBezier(new Vector2(224.865997F, -5.56799984F), new Vector2(225.181F, -4.35400009F), new Vector2(225.809006F, -3.26200008F));
                    builder.AddCubicBezier(new Vector2(226.436996F, -2.17000008F), new Vector2(227.296997F, -1.30499995F), new Vector2(228.389008F, -0.666999996F));
                    builder.AddCubicBezier(new Vector2(229.481003F, -0.0289999992F), new Vector2(230.705002F, 0.289999992F), new Vector2(232.057999F, 0.289999992F));
                    builder.AddCubicBezier(new Vector2(233.121002F, 0.289999992F), new Vector2(234.113007F, 0.0869999975F), new Vector2(235.031006F, -0.319000006F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_76()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(218.863007F, -0.319000006F));
                    builder.AddCubicBezier(new Vector2(219.809998F, -0.725000024F), new Vector2(220.621994F, -1.31500006F), new Vector2(221.298996F, -2.08800006F));
                    builder.AddLine(new Vector2(219.617004F, -3.79900002F));
                    builder.AddCubicBezier(new Vector2(219.153F, -3.2579999F), new Vector2(218.598007F, -2.852F), new Vector2(217.949997F, -2.58100009F));
                    builder.AddCubicBezier(new Vector2(217.302002F, -2.30999994F), new Vector2(216.591003F, -2.17499995F), new Vector2(215.817993F, -2.17499995F));
                    builder.AddCubicBezier(new Vector2(214.889999F, -2.17499995F), new Vector2(214.067993F, -2.37400007F), new Vector2(213.352997F, -2.76999998F));
                    builder.AddCubicBezier(new Vector2(212.638F, -3.16599989F), new Vector2(212.087006F, -3.727F), new Vector2(211.699997F, -4.45200014F));
                    builder.AddCubicBezier(new Vector2(211.313004F, -5.17700005F), new Vector2(211.119995F, -6.02199984F), new Vector2(211.119995F, -6.98899984F));
                    builder.AddCubicBezier(new Vector2(211.119995F, -7.93599987F), new Vector2(211.304001F, -8.75800037F), new Vector2(211.671005F, -9.45400047F));
                    builder.AddCubicBezier(new Vector2(212.037994F, -10.1499996F), new Vector2(212.559998F, -10.691F), new Vector2(213.237F, -11.0780001F));
                    builder.AddCubicBezier(new Vector2(213.914001F, -11.4650002F), new Vector2(214.697006F, -11.658F), new Vector2(215.585999F, -11.658F));
                    builder.AddCubicBezier(new Vector2(216.436996F, -11.658F), new Vector2(217.162003F, -11.4799995F), new Vector2(217.761002F, -11.1219997F));
                    builder.AddCubicBezier(new Vector2(218.360001F, -10.7639999F), new Vector2(218.824005F, -10.2609997F), new Vector2(219.153F, -9.61299992F));
                    builder.AddCubicBezier(new Vector2(219.481995F, -8.96500015F), new Vector2(219.645996F, -8.17800045F), new Vector2(219.645996F, -7.25F));
                    builder.AddLine(new Vector2(220.602997F, -8.0909996F));
                    builder.AddLine(new Vector2(210.365997F, -8.0909996F));
                    builder.AddLine(new Vector2(210.365997F, -5.91599989F));
                    builder.AddLine(new Vector2(221.994995F, -5.91599989F));
                    builder.AddCubicBezier(new Vector2(222.052994F, -6.1869998F), new Vector2(222.091995F, -6.43400002F), new Vector2(222.110992F, -6.65600014F));
                    builder.AddCubicBezier(new Vector2(222.130005F, -6.87799978F), new Vector2(222.139999F, -7.08599997F), new Vector2(222.139999F, -7.27899981F));
                    builder.AddCubicBezier(new Vector2(222.139999F, -8.59399986F), new Vector2(221.865005F, -9.76299953F), new Vector2(221.313995F, -10.7880001F));
                    builder.AddCubicBezier(new Vector2(220.763F, -11.8129997F), new Vector2(219.994003F, -12.6149998F), new Vector2(219.007996F, -13.1949997F));
                    builder.AddCubicBezier(new Vector2(218.022003F, -13.7749996F), new Vector2(216.901001F, -14.0649996F), new Vector2(215.643997F, -14.0649996F));
                    builder.AddCubicBezier(new Vector2(214.309998F, -14.0649996F), new Vector2(213.106995F, -13.75F), new Vector2(212.033997F, -13.1219997F));
                    builder.AddCubicBezier(new Vector2(210.960999F, -12.4940004F), new Vector2(210.110001F, -11.6389999F), new Vector2(209.481995F, -10.5559998F));
                    builder.AddCubicBezier(new Vector2(208.854004F, -9.47299957F), new Vector2(208.539001F, -8.25500011F), new Vector2(208.539001F, -6.90199995F));
                    builder.AddCubicBezier(new Vector2(208.539001F, -5.52899981F), new Vector2(208.858002F, -4.30200005F), new Vector2(209.496002F, -3.2190001F));
                    builder.AddCubicBezier(new Vector2(210.134003F, -2.13599992F), new Vector2(211F, -1.27999997F), new Vector2(212.091995F, -0.65200001F));
                    builder.AddCubicBezier(new Vector2(213.184006F, -0.0240000002F), new Vector2(214.425995F, 0.289999992F), new Vector2(215.817993F, 0.289999992F));
                    builder.AddCubicBezier(new Vector2(216.901001F, 0.289999992F), new Vector2(217.916F, 0.0869999975F), new Vector2(218.863007F, -0.319000006F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_77()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(204.740005F, -17.0079994F));
                    builder.AddCubicBezier(new Vector2(205.048996F, -17.3269997F), new Vector2(205.203995F, -17.7290001F), new Vector2(205.203995F, -18.2119999F));
                    builder.AddCubicBezier(new Vector2(205.203995F, -18.6760006F), new Vector2(205.048996F, -19.0680008F), new Vector2(204.740005F, -19.3869991F));
                    builder.AddCubicBezier(new Vector2(204.431F, -19.7059994F), new Vector2(204.033997F, -19.8649998F), new Vector2(203.550995F, -19.8649998F));
                    builder.AddCubicBezier(new Vector2(203.087006F, -19.8649998F), new Vector2(202.695007F, -19.7059994F), new Vector2(202.376007F, -19.3869991F));
                    builder.AddCubicBezier(new Vector2(202.057007F, -19.0680008F), new Vector2(201.897995F, -18.6760006F), new Vector2(201.897995F, -18.2119999F));
                    builder.AddCubicBezier(new Vector2(201.897995F, -17.7290001F), new Vector2(202.057007F, -17.3269997F), new Vector2(202.376007F, -17.0079994F));
                    builder.AddCubicBezier(new Vector2(202.695007F, -16.6889992F), new Vector2(203.087006F, -16.5300007F), new Vector2(203.550995F, -16.5300007F));
                    builder.AddCubicBezier(new Vector2(204.033997F, -16.5300007F), new Vector2(204.431F, -16.6889992F), new Vector2(204.740005F, -17.0079994F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(202.507004F, 5.5250001F));
                    builder.AddCubicBezier(new Vector2(203.203003F, 5.16699982F), new Vector2(203.764008F, 4.6500001F), new Vector2(204.188995F, 3.97300005F));
                    builder.AddCubicBezier(new Vector2(204.613998F, 3.296F), new Vector2(204.826996F, 2.45499992F), new Vector2(204.826996F, 1.45000005F));
                    builder.AddLine(new Vector2(204.826996F, -13.7749996F));
                    builder.AddLine(new Vector2(202.216995F, -13.7749996F));
                    builder.AddLine(new Vector2(202.216995F, 1.47899997F));
                    builder.AddCubicBezier(new Vector2(202.216995F, 2.19400001F), new Vector2(202.003998F, 2.73099995F), new Vector2(201.578995F, 3.08899999F));
                    builder.AddCubicBezier(new Vector2(201.154007F, 3.44700003F), new Vector2(200.651001F, 3.625F), new Vector2(200.070999F, 3.625F));
                    builder.AddCubicBezier(new Vector2(199.626007F, 3.625F), new Vector2(199.244995F, 3.5480001F), new Vector2(198.925995F, 3.39299989F));
                    builder.AddCubicBezier(new Vector2(198.606995F, 3.23799992F), new Vector2(198.311996F, 2.99699998F), new Vector2(198.041F, 2.66799998F));
                    builder.AddLine(new Vector2(196.358994F, 4.37900019F));
                    builder.AddCubicBezier(new Vector2(196.841995F, 4.92000008F), new Vector2(197.388F, 5.33599997F), new Vector2(197.996994F, 5.62599993F));
                    builder.AddCubicBezier(new Vector2(198.606003F, 5.91599989F), new Vector2(199.356003F, 6.06099987F), new Vector2(200.244995F, 6.06099987F));
                    builder.AddCubicBezier(new Vector2(201.057007F, 6.06099987F), new Vector2(201.811005F, 5.8829999F), new Vector2(202.507004F, 5.5250001F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_78()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(188.789993F, -2.84200001F));
                    builder.AddCubicBezier(new Vector2(188.113007F, -3.24799991F), new Vector2(187.582001F, -3.8039999F), new Vector2(187.195007F, -4.51000023F));
                    builder.AddCubicBezier(new Vector2(186.807999F, -5.21600008F), new Vector2(186.615005F, -6.02199984F), new Vector2(186.615005F, -6.93100023F));
                    builder.AddCubicBezier(new Vector2(186.615005F, -7.82000017F), new Vector2(186.807999F, -8.61299992F), new Vector2(187.195007F, -9.30900002F));
                    builder.AddCubicBezier(new Vector2(187.582001F, -10.0050001F), new Vector2(188.113007F, -10.5509996F), new Vector2(188.789993F, -10.9469995F));
                    builder.AddCubicBezier(new Vector2(189.466995F, -11.3430004F), new Vector2(190.229996F, -11.5419998F), new Vector2(191.080994F, -11.5419998F));
                    builder.AddCubicBezier(new Vector2(191.970001F, -11.5419998F), new Vector2(192.748001F, -11.3430004F), new Vector2(193.414993F, -10.9469995F));
                    builder.AddCubicBezier(new Vector2(194.082001F, -10.5509996F), new Vector2(194.613998F, -10.0050001F), new Vector2(195.009995F, -9.30900002F));
                    builder.AddCubicBezier(new Vector2(195.406006F, -8.61299992F), new Vector2(195.604996F, -7.82000017F), new Vector2(195.604996F, -6.93100023F));
                    builder.AddCubicBezier(new Vector2(195.604996F, -6.02199984F), new Vector2(195.412003F, -5.21600008F), new Vector2(195.024994F, -4.51000023F));
                    builder.AddCubicBezier(new Vector2(194.638F, -3.8039999F), new Vector2(194.106995F, -3.24799991F), new Vector2(193.429993F, -2.84200001F));
                    builder.AddCubicBezier(new Vector2(192.753006F, -2.43600011F), new Vector2(191.970001F, -2.23300004F), new Vector2(191.080994F, -2.23300004F));
                    builder.AddCubicBezier(new Vector2(190.229996F, -2.23300004F), new Vector2(189.466995F, -2.43600011F), new Vector2(188.789993F, -2.84200001F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(194.748993F, -0.666999996F));
                    builder.AddCubicBezier(new Vector2(195.841003F, -1.30499995F), new Vector2(196.707001F, -2.17000008F), new Vector2(197.345001F, -3.26200008F));
                    builder.AddCubicBezier(new Vector2(197.983002F, -4.35400009F), new Vector2(198.302002F, -5.57800007F), new Vector2(198.302002F, -6.93100023F));
                    builder.AddCubicBezier(new Vector2(198.302002F, -8.26500034F), new Vector2(197.983002F, -9.47299957F), new Vector2(197.345001F, -10.5559998F));
                    builder.AddCubicBezier(new Vector2(196.707001F, -11.6389999F), new Vector2(195.841003F, -12.4940004F), new Vector2(194.748993F, -13.1219997F));
                    builder.AddCubicBezier(new Vector2(193.656998F, -13.75F), new Vector2(192.434006F, -14.0649996F), new Vector2(191.080994F, -14.0649996F));
                    builder.AddCubicBezier(new Vector2(189.746994F, -14.0649996F), new Vector2(188.539001F, -13.7460003F), new Vector2(187.455994F, -13.1079998F));
                    builder.AddCubicBezier(new Vector2(186.373001F, -12.4700003F), new Vector2(185.513F, -11.6149998F), new Vector2(184.875F, -10.5419998F));
                    builder.AddCubicBezier(new Vector2(184.237F, -9.46899986F), new Vector2(183.917999F, -8.26500034F), new Vector2(183.917999F, -6.93100023F));
                    builder.AddCubicBezier(new Vector2(183.917999F, -5.57800007F), new Vector2(184.237F, -4.35400009F), new Vector2(184.875F, -3.26200008F));
                    builder.AddCubicBezier(new Vector2(185.513F, -2.17000008F), new Vector2(186.373001F, -1.30499995F), new Vector2(187.455994F, -0.666999996F));
                    builder.AddCubicBezier(new Vector2(188.539001F, -0.0289999992F), new Vector2(189.746994F, 0.289999992F), new Vector2(191.080994F, 0.289999992F));
                    builder.AddCubicBezier(new Vector2(192.434006F, 0.289999992F), new Vector2(193.656998F, -0.0289999992F), new Vector2(194.748993F, -0.666999996F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_79()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(176.378006F, -10.6719999F));
                    builder.AddCubicBezier(new Vector2(176.996994F, -11.2910004F), new Vector2(177.789001F, -11.6000004F), new Vector2(178.755997F, -11.6000004F));
                    builder.AddCubicBezier(new Vector2(179.220001F, -11.6000004F), new Vector2(179.626007F, -11.5319996F), new Vector2(179.973999F, -11.3970003F));
                    builder.AddCubicBezier(new Vector2(180.322006F, -11.2620001F), new Vector2(180.641006F, -11.0389996F), new Vector2(180.931F, -10.7299995F));
                    builder.AddLine(new Vector2(182.641998F, -12.4989996F));
                    builder.AddCubicBezier(new Vector2(182.158997F, -13.0600004F), new Vector2(181.636993F, -13.46F), new Vector2(181.076004F, -13.7019997F));
                    builder.AddCubicBezier(new Vector2(180.514999F, -13.9440002F), new Vector2(179.886993F, -14.0649996F), new Vector2(179.190994F, -14.0649996F));
                    builder.AddCubicBezier(new Vector2(177.664001F, -14.0649996F), new Vector2(176.494003F, -13.5430002F), new Vector2(175.682007F, -12.4989996F));
                    builder.AddCubicBezier(new Vector2(174.869995F, -11.4549999F), new Vector2(174.464005F, -10.0530005F), new Vector2(174.464005F, -8.29399967F));
                    builder.AddLine(new Vector2(175.449997F, -7.82999992F));
                    builder.AddCubicBezier(new Vector2(175.449997F, -9.10599995F), new Vector2(175.759003F, -10.0530005F), new Vector2(176.378006F, -10.6719999F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(175.449997F, 0F));
                    builder.AddLine(new Vector2(175.449997F, -13.7749996F));
                    builder.AddLine(new Vector2(172.839996F, -13.7749996F));
                    builder.AddLine(new Vector2(172.839996F, 0F));
                    builder.AddLine(new Vector2(175.449997F, 0F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_80()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(157.673004F, 0F));
                    builder.AddLine(new Vector2(157.673004F, -20.1259995F));
                    builder.AddLine(new Vector2(154.947006F, -20.1259995F));
                    builder.AddLine(new Vector2(154.947006F, 0F));
                    builder.AddLine(new Vector2(157.673004F, 0F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(162.399994F, -7.82999992F));
                    builder.AddCubicBezier(new Vector2(163.636993F, -7.82999992F), new Vector2(164.748993F, -8.08100033F), new Vector2(165.735001F, -8.58399963F));
                    builder.AddCubicBezier(new Vector2(166.720993F, -9.08699989F), new Vector2(167.5F, -9.80200005F), new Vector2(168.070007F, -10.7299995F));
                    builder.AddCubicBezier(new Vector2(168.639999F, -11.658F), new Vector2(168.925003F, -12.7410002F), new Vector2(168.925003F, -13.9779997F));
                    builder.AddCubicBezier(new Vector2(168.925003F, -15.2349997F), new Vector2(168.639999F, -16.3220005F), new Vector2(168.070007F, -17.2399998F));
                    builder.AddCubicBezier(new Vector2(167.5F, -18.1580009F), new Vector2(166.720993F, -18.8689995F), new Vector2(165.735001F, -19.3719997F));
                    builder.AddCubicBezier(new Vector2(164.748993F, -19.875F), new Vector2(163.636993F, -20.1259995F), new Vector2(162.399994F, -20.1259995F));
                    builder.AddLine(new Vector2(156.774002F, -20.1259995F));
                    builder.AddLine(new Vector2(156.774002F, -17.7479992F));
                    builder.AddLine(new Vector2(162.255005F, -17.7479992F));
                    builder.AddCubicBezier(new Vector2(163.009003F, -17.7479992F), new Vector2(163.681F, -17.5930004F), new Vector2(164.270996F, -17.2840004F));
                    builder.AddCubicBezier(new Vector2(164.860992F, -16.9750004F), new Vector2(165.324997F, -16.5400009F), new Vector2(165.662994F, -15.9790001F));
                    builder.AddCubicBezier(new Vector2(166.001007F, -15.4180002F), new Vector2(166.169998F, -14.7510004F), new Vector2(166.169998F, -13.9779997F));
                    builder.AddCubicBezier(new Vector2(166.169998F, -13.2049999F), new Vector2(166.001007F, -12.5380001F), new Vector2(165.662994F, -11.9770002F));
                    builder.AddCubicBezier(new Vector2(165.324997F, -11.4160004F), new Vector2(164.860992F, -10.9809999F), new Vector2(164.270996F, -10.6719999F));
                    builder.AddCubicBezier(new Vector2(163.681F, -10.3629999F), new Vector2(163.009003F, -10.2080002F), new Vector2(162.255005F, -10.2080002F));
                    builder.AddLine(new Vector2(156.774002F, -10.2080002F));
                    builder.AddLine(new Vector2(156.774002F, -7.82999992F));
                    builder.AddLine(new Vector2(162.399994F, -7.82999992F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_81()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(140.882004F, -0.275000006F));
                    builder.AddCubicBezier(new Vector2(141.867996F, -0.671000004F), new Vector2(142.709F, -1.28600001F), new Vector2(143.404999F, -2.1170001F));
                    builder.AddLine(new Vector2(140.940002F, -4.55299997F));
                    builder.AddCubicBezier(new Vector2(140.514999F, -4.07000017F), new Vector2(140.022003F, -3.70700002F), new Vector2(139.460999F, -3.46499991F));
                    builder.AddCubicBezier(new Vector2(138.899994F, -3.22300005F), new Vector2(138.281998F, -3.10299993F), new Vector2(137.604996F, -3.10299993F));
                    builder.AddCubicBezier(new Vector2(136.850998F, -3.10299993F), new Vector2(136.199005F, -3.26200008F), new Vector2(135.647995F, -3.58100009F));
                    builder.AddCubicBezier(new Vector2(135.097F, -3.9000001F), new Vector2(134.671997F, -4.36000013F), new Vector2(134.371994F, -4.95900011F));
                    builder.AddCubicBezier(new Vector2(134.072006F, -5.55800009F), new Vector2(133.921997F, -6.26399994F), new Vector2(133.921997F, -7.07600021F));
                    builder.AddCubicBezier(new Vector2(133.921997F, -7.88800001F), new Vector2(134.061996F, -8.58399963F), new Vector2(134.341995F, -9.16399956F));
                    builder.AddCubicBezier(new Vector2(134.621994F, -9.74400043F), new Vector2(135.020004F, -10.198F), new Vector2(135.531998F, -10.5270004F));
                    builder.AddCubicBezier(new Vector2(136.044006F, -10.8559999F), new Vector2(136.647995F, -11.0200005F), new Vector2(137.343994F, -11.0200005F));
                    builder.AddCubicBezier(new Vector2(137.981995F, -11.0200005F), new Vector2(138.528F, -10.875F), new Vector2(138.981995F, -10.585F));
                    builder.AddCubicBezier(new Vector2(139.436005F, -10.2950001F), new Vector2(139.779999F, -9.88500023F), new Vector2(140.011993F, -9.35299969F));
                    builder.AddCubicBezier(new Vector2(140.244003F, -8.8210001F), new Vector2(140.369995F, -8.1590004F), new Vector2(140.389008F, -7.36600018F));
                    builder.AddLine(new Vector2(142.302994F, -8.67099953F));
                    builder.AddLine(new Vector2(132.095001F, -8.61299992F));
                    builder.AddLine(new Vector2(132.095001F, -5.53900003F));
                    builder.AddLine(new Vector2(144.216995F, -5.59700012F));
                    builder.AddCubicBezier(new Vector2(144.313995F, -5.94500017F), new Vector2(144.376999F, -6.26000023F), new Vector2(144.406006F, -6.53999996F));
                    builder.AddCubicBezier(new Vector2(144.434998F, -6.82000017F), new Vector2(144.449005F, -7.09499979F), new Vector2(144.449005F, -7.36600018F));
                    builder.AddCubicBezier(new Vector2(144.449005F, -8.73900032F), new Vector2(144.139999F, -9.95699978F), new Vector2(143.520996F, -11.0200005F));
                    builder.AddCubicBezier(new Vector2(142.901993F, -12.0830002F), new Vector2(142.061005F, -12.915F), new Vector2(140.998001F, -13.5139999F));
                    builder.AddCubicBezier(new Vector2(139.934998F, -14.1129999F), new Vector2(138.697006F, -14.4130001F), new Vector2(137.285995F, -14.4130001F));
                    builder.AddCubicBezier(new Vector2(135.854996F, -14.4130001F), new Vector2(134.570007F, -14.0939999F), new Vector2(133.429001F, -13.4560003F));
                    builder.AddCubicBezier(new Vector2(132.287994F, -12.8179998F), new Vector2(131.389008F, -11.9429998F), new Vector2(130.731995F, -10.8310003F));
                    builder.AddCubicBezier(new Vector2(130.074997F, -9.71899986F), new Vector2(129.746002F, -8.46800041F), new Vector2(129.746002F, -7.07600021F));
                    builder.AddCubicBezier(new Vector2(129.746002F, -5.64499998F), new Vector2(130.084F, -4.36899996F), new Vector2(130.761002F, -3.24799991F));
                    builder.AddCubicBezier(new Vector2(131.438004F, -2.12700009F), new Vector2(132.365997F, -1.25199997F), new Vector2(133.544998F, -0.624000013F));
                    builder.AddCubicBezier(new Vector2(134.723999F, 0.00400000019F), new Vector2(136.078003F, 0.319000006F), new Vector2(137.604996F, 0.319000006F));
                    builder.AddCubicBezier(new Vector2(138.804001F, 0.319000006F), new Vector2(139.895996F, 0.120999999F), new Vector2(140.882004F, -0.275000006F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_82()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(125.932999F, -0.855000019F));
                    builder.AddCubicBezier(new Vector2(126.967003F, -1.67700005F), new Vector2(127.484001F, -2.77399993F), new Vector2(127.484001F, -4.14699984F));
                    builder.AddCubicBezier(new Vector2(127.484001F, -5.03599977F), new Vector2(127.32F, -5.75600004F), new Vector2(126.990997F, -6.30700016F));
                    builder.AddCubicBezier(new Vector2(126.662003F, -6.8579998F), new Vector2(126.237F, -7.30299997F), new Vector2(125.714996F, -7.64099979F));
                    builder.AddCubicBezier(new Vector2(125.193001F, -7.97900009F), new Vector2(124.641998F, -8.23999977F), new Vector2(124.061996F, -8.42399979F));
                    builder.AddCubicBezier(new Vector2(123.482002F, -8.6079998F), new Vector2(122.935997F, -8.76799965F), new Vector2(122.424004F, -8.90299988F));
                    builder.AddCubicBezier(new Vector2(121.912003F, -9.03800011F), new Vector2(121.486F, -9.19299984F), new Vector2(121.148003F, -9.36699963F));
                    builder.AddCubicBezier(new Vector2(120.809998F, -9.54100037F), new Vector2(120.639999F, -9.77299976F), new Vector2(120.639999F, -10.0629997F));
                    builder.AddCubicBezier(new Vector2(120.639999F, -10.3140001F), new Vector2(120.765999F, -10.5170002F), new Vector2(121.016998F, -10.6719999F));
                    builder.AddCubicBezier(new Vector2(121.267998F, -10.8269997F), new Vector2(121.644997F, -10.9040003F), new Vector2(122.148003F, -10.9040003F));
                    builder.AddCubicBezier(new Vector2(122.669998F, -10.9040003F), new Vector2(123.207001F, -10.7919998F), new Vector2(123.758003F, -10.5699997F));
                    builder.AddCubicBezier(new Vector2(124.308998F, -10.3479996F), new Vector2(124.806F, -9.97599983F), new Vector2(125.250999F, -9.45400047F));
                    builder.AddLine(new Vector2(127.774002F, -12.0349998F));
                    builder.AddCubicBezier(new Vector2(127.154999F, -12.8470001F), new Vector2(126.334F, -13.46F), new Vector2(125.308998F, -13.8760004F));
                    builder.AddCubicBezier(new Vector2(124.283997F, -14.2919998F), new Vector2(123.181999F, -14.5F), new Vector2(122.002998F, -14.5F));
                    builder.AddCubicBezier(new Vector2(120.823997F, -14.5F), new Vector2(119.808998F, -14.3070002F), new Vector2(118.958F, -13.9200001F));
                    builder.AddCubicBezier(new Vector2(118.107002F, -13.533F), new Vector2(117.449997F, -13.007F), new Vector2(116.986F, -12.3400002F));
                    builder.AddCubicBezier(new Vector2(116.522003F, -11.6730003F), new Vector2(116.290001F, -10.8850002F), new Vector2(116.290001F, -9.97599983F));
                    builder.AddCubicBezier(new Vector2(116.290001F, -9.10599995F), new Vector2(116.454002F, -8.39999962F), new Vector2(116.782997F, -7.85900021F));
                    builder.AddCubicBezier(new Vector2(117.112F, -7.31799984F), new Vector2(117.537003F, -6.8920002F), new Vector2(118.058998F, -6.58300018F));
                    builder.AddCubicBezier(new Vector2(118.581001F, -6.27400017F), new Vector2(119.132004F, -6.02799988F), new Vector2(119.711998F, -5.84399986F));
                    builder.AddCubicBezier(new Vector2(120.292F, -5.65999985F), new Vector2(120.848F, -5.49499989F), new Vector2(121.379997F, -5.3499999F));
                    builder.AddCubicBezier(new Vector2(121.912003F, -5.20499992F), new Vector2(122.341003F, -5.05100012F), new Vector2(122.669998F, -4.88700008F));
                    builder.AddCubicBezier(new Vector2(122.999001F, -4.72300005F), new Vector2(123.163002F, -4.46600008F), new Vector2(123.163002F, -4.11800003F));
                    builder.AddCubicBezier(new Vector2(123.163002F, -3.82800007F), new Vector2(123.023003F, -3.60599995F), new Vector2(122.742996F, -3.45099998F));
                    builder.AddCubicBezier(new Vector2(122.462997F, -3.296F), new Vector2(122.080002F, -3.2190001F), new Vector2(121.597F, -3.2190001F));
                    builder.AddCubicBezier(new Vector2(120.823997F, -3.2190001F), new Vector2(120.123001F, -3.35899997F), new Vector2(119.495003F, -3.63899994F));
                    builder.AddCubicBezier(new Vector2(118.866997F, -3.91899991F), new Vector2(118.309998F, -4.31099987F), new Vector2(117.827003F, -4.81400013F));
                    builder.AddLine(new Vector2(115.275002F, -2.26200008F));
                    builder.AddCubicBezier(new Vector2(115.778F, -1.74000001F), new Vector2(116.366997F, -1.28100002F), new Vector2(117.043999F, -0.88499999F));
                    builder.AddCubicBezier(new Vector2(117.721001F, -0.488999993F), new Vector2(118.464996F, -0.178000003F), new Vector2(119.277F, 0.0439999998F));
                    builder.AddCubicBezier(new Vector2(120.088997F, 0.266000003F), new Vector2(120.911003F, 0.377000004F), new Vector2(121.741997F, 0.377000004F));
                    builder.AddCubicBezier(new Vector2(123.500999F, 0.377000004F), new Vector2(124.899002F, -0.0329999998F), new Vector2(125.932999F, -0.855000019F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_83()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(104.269997F, -4.16099977F));
                    builder.AddCubicBezier(new Vector2(103.795998F, -4.44099998F), new Vector2(103.424004F, -4.83300018F), new Vector2(103.153F, -5.33599997F));
                    builder.AddCubicBezier(new Vector2(102.882004F, -5.83900023F), new Vector2(102.747002F, -6.40899992F), new Vector2(102.747002F, -7.04699993F));
                    builder.AddCubicBezier(new Vector2(102.747002F, -7.68499994F), new Vector2(102.882004F, -8.25500011F), new Vector2(103.153F, -8.75800037F));
                    builder.AddCubicBezier(new Vector2(103.424004F, -9.26099968F), new Vector2(103.795998F, -9.65200043F), new Vector2(104.269997F, -9.93200016F));
                    builder.AddCubicBezier(new Vector2(104.744003F, -10.2119999F), new Vector2(105.289001F, -10.3529997F), new Vector2(105.907997F, -10.3529997F));
                    builder.AddCubicBezier(new Vector2(106.527F, -10.3529997F), new Vector2(107.068001F, -10.2119999F), new Vector2(107.531998F, -9.93200016F));
                    builder.AddCubicBezier(new Vector2(107.996002F, -9.65200043F), new Vector2(108.367996F, -9.26099968F), new Vector2(108.648003F, -8.75800037F));
                    builder.AddCubicBezier(new Vector2(108.928001F, -8.25500011F), new Vector2(109.069F, -7.68499994F), new Vector2(109.069F, -7.04699993F));
                    builder.AddCubicBezier(new Vector2(109.069F, -6.40899992F), new Vector2(108.928001F, -5.83900023F), new Vector2(108.648003F, -5.33599997F));
                    builder.AddCubicBezier(new Vector2(108.367996F, -4.83300018F), new Vector2(107.990997F, -4.44099998F), new Vector2(107.516998F, -4.16099977F));
                    builder.AddCubicBezier(new Vector2(107.042999F, -3.88100004F), new Vector2(106.498001F, -3.74099994F), new Vector2(105.878998F, -3.74099994F));
                    builder.AddCubicBezier(new Vector2(105.279999F, -3.74099994F), new Vector2(104.744003F, -3.88100004F), new Vector2(104.269997F, -4.16099977F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(102.949997F, 5.94500017F));
                    builder.AddLine(new Vector2(102.949997F, -3.625F));
                    builder.AddLine(new Vector2(102.282997F, -7.04699993F));
                    builder.AddLine(new Vector2(103.008003F, -10.4980001F));
                    builder.AddLine(new Vector2(103.008003F, -14.0939999F));
                    builder.AddLine(new Vector2(98.5419998F, -14.0939999F));
                    builder.AddLine(new Vector2(98.5419998F, 5.94500017F));
                    builder.AddLine(new Vector2(102.949997F, 5.94500017F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(110.258003F, -0.681999981F));
                    builder.AddCubicBezier(new Vector2(111.302002F, -1.33000004F), new Vector2(112.117996F, -2.20000005F), new Vector2(112.708F, -3.29200006F));
                    builder.AddCubicBezier(new Vector2(113.297997F, -4.38399982F), new Vector2(113.593002F, -5.63600016F), new Vector2(113.593002F, -7.04699993F));
                    builder.AddCubicBezier(new Vector2(113.593002F, -8.45800018F), new Vector2(113.292999F, -9.71500015F), new Vector2(112.694F, -10.8170004F));
                    builder.AddCubicBezier(new Vector2(112.095001F, -11.9189997F), new Vector2(111.277F, -12.7889996F), new Vector2(110.242996F, -13.427F));
                    builder.AddCubicBezier(new Vector2(109.209F, -14.0649996F), new Vector2(108.035004F, -14.3839998F), new Vector2(106.720001F, -14.3839998F));
                    builder.AddCubicBezier(new Vector2(105.773003F, -14.3839998F), new Vector2(104.903F, -14.191F), new Vector2(104.110001F, -13.8039999F));
                    builder.AddCubicBezier(new Vector2(103.317001F, -13.4169998F), new Vector2(102.669998F, -12.8900003F), new Vector2(102.167F, -12.2229996F));
                    builder.AddCubicBezier(new Vector2(101.664001F, -11.5559998F), new Vector2(101.374001F, -10.7980003F), new Vector2(101.296997F, -9.94699955F));
                    builder.AddLine(new Vector2(101.296997F, -4.00199986F));
                    builder.AddCubicBezier(new Vector2(101.374001F, -3.171F), new Vector2(101.658997F, -2.43099999F), new Vector2(102.152F, -1.78299999F));
                    builder.AddCubicBezier(new Vector2(102.644997F, -1.13499999F), new Vector2(103.288002F, -0.628000021F), new Vector2(104.081001F, -0.261000007F));
                    builder.AddCubicBezier(new Vector2(104.874001F, 0.105999999F), new Vector2(105.752998F, 0.289999992F), new Vector2(106.720001F, 0.289999992F));
                    builder.AddCubicBezier(new Vector2(108.035004F, 0.289999992F), new Vector2(109.213997F, -0.0340000018F), new Vector2(110.258003F, -0.681999981F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_84()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(94.6559982F, 0F));
                    builder.AddLine(new Vector2(94.6559982F, -14.0939999F));
                    builder.AddLine(new Vector2(90.2770004F, -14.0939999F));
                    builder.AddLine(new Vector2(90.2770004F, -10.6429996F));
                    builder.AddLine(new Vector2(90.9440002F, -7.25F));
                    builder.AddLine(new Vector2(90.2770004F, -3.79900002F));
                    builder.AddLine(new Vector2(90.2770004F, 0F));
                    builder.AddLine(new Vector2(94.6559982F, 0F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(85.7099991F, -4.16099977F));
                    builder.AddCubicBezier(new Vector2(85.2360001F, -4.44099998F), new Vector2(84.8590012F, -4.83300018F), new Vector2(84.5790024F, -5.33599997F));
                    builder.AddCubicBezier(new Vector2(84.2990036F, -5.83900023F), new Vector2(84.1579971F, -6.40899992F), new Vector2(84.1579971F, -7.04699993F));
                    builder.AddCubicBezier(new Vector2(84.1579971F, -7.68499994F), new Vector2(84.2990036F, -8.25500011F), new Vector2(84.5790024F, -8.75800037F));
                    builder.AddCubicBezier(new Vector2(84.8590012F, -9.26099968F), new Vector2(85.2360001F, -9.65200043F), new Vector2(85.7099991F, -9.93200016F));
                    builder.AddCubicBezier(new Vector2(86.1839981F, -10.2119999F), new Vector2(86.7200012F, -10.3529997F), new Vector2(87.3190002F, -10.3529997F));
                    builder.AddCubicBezier(new Vector2(87.9380035F, -10.3529997F), new Vector2(88.4889984F, -10.2119999F), new Vector2(88.9720001F, -9.93200016F));
                    builder.AddCubicBezier(new Vector2(89.4550018F, -9.65200043F), new Vector2(89.822998F, -9.26599979F), new Vector2(90.0739975F, -8.77299976F));
                    builder.AddCubicBezier(new Vector2(90.3249969F, -8.27999973F), new Vector2(90.4509964F, -7.71400023F), new Vector2(90.4509964F, -7.07600021F));
                    builder.AddCubicBezier(new Vector2(90.4509964F, -6.09000015F), new Vector2(90.1660004F, -5.28800011F), new Vector2(89.5960007F, -4.66900015F));
                    builder.AddCubicBezier(new Vector2(89.026001F, -4.05000019F), new Vector2(88.2659988F, -3.74099994F), new Vector2(87.3190002F, -3.74099994F));
                    builder.AddCubicBezier(new Vector2(86.7200012F, -3.74099994F), new Vector2(86.1839981F, -3.88100004F), new Vector2(85.7099991F, -4.16099977F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(88.9860001F, -0.261000007F));
                    builder.AddCubicBezier(new Vector2(89.75F, -0.628000021F), new Vector2(90.3679962F, -1.13499999F), new Vector2(90.8420029F, -1.78299999F));
                    builder.AddCubicBezier(new Vector2(91.3160019F, -2.43099999F), new Vector2(91.5820007F, -3.171F), new Vector2(91.6399994F, -4.00199986F));
                    builder.AddLine(new Vector2(91.6399994F, -10.092F));
                    builder.AddCubicBezier(new Vector2(91.5820007F, -10.9230003F), new Vector2(91.310997F, -11.6630001F), new Vector2(90.8280029F, -12.3109999F));
                    builder.AddCubicBezier(new Vector2(90.3450012F, -12.9589996F), new Vector2(89.7220001F, -13.4659996F), new Vector2(88.9580002F, -13.8330002F));
                    builder.AddCubicBezier(new Vector2(88.1940002F, -14.1999998F), new Vector2(87.3379974F, -14.3839998F), new Vector2(86.3909988F, -14.3839998F));
                    builder.AddCubicBezier(new Vector2(85.0960007F, -14.3839998F), new Vector2(83.9410019F, -14.0690002F), new Vector2(82.9260025F, -13.441F));
                    builder.AddCubicBezier(new Vector2(81.9110031F, -12.8129997F), new Vector2(81.1080017F, -11.9429998F), new Vector2(80.5179977F, -10.8310003F));
                    builder.AddCubicBezier(new Vector2(79.9280014F, -9.71899986F), new Vector2(79.6340027F, -8.45800018F), new Vector2(79.6340027F, -7.04699993F));
                    builder.AddCubicBezier(new Vector2(79.6340027F, -5.63600016F), new Vector2(79.9280014F, -4.38399982F), new Vector2(80.5179977F, -3.29200006F));
                    builder.AddCubicBezier(new Vector2(81.1080017F, -2.20000005F), new Vector2(81.9150009F, -1.33000004F), new Vector2(82.9400024F, -0.681999981F));
                    builder.AddCubicBezier(new Vector2(83.9649963F, -0.0340000018F), new Vector2(85.1149979F, 0.289999992F), new Vector2(86.3909988F, 0.289999992F));
                    builder.AddCubicBezier(new Vector2(87.3580017F, 0.289999992F), new Vector2(88.2220001F, 0.105999999F), new Vector2(88.9860001F, -0.261000007F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_85()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(76.5889969F, 0F));
                    builder.AddLine(new Vector2(76.5889969F, -21.0540009F));
                    builder.AddLine(new Vector2(72.1230011F, -21.0540009F));
                    builder.AddLine(new Vector2(72.1230011F, 0F));
                    builder.AddLine(new Vector2(76.5889969F, 0F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_86()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(68.2080002F, 0F));
                    builder.AddLine(new Vector2(68.2080002F, -21.0540009F));
                    builder.AddLine(new Vector2(63.7420006F, -21.0540009F));
                    builder.AddLine(new Vector2(63.7420006F, 0F));
                    builder.AddLine(new Vector2(68.2080002F, 0F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_87()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(51.4169998F, -4.14699984F));
                    builder.AddCubicBezier(new Vector2(50.9339981F, -4.41800022F), new Vector2(50.5569992F, -4.80999994F), new Vector2(50.2859993F, -5.32200003F));
                    builder.AddCubicBezier(new Vector2(50.0149994F, -5.83400011F), new Vector2(49.8800011F, -6.41900015F), new Vector2(49.8800011F, -7.07600021F));
                    builder.AddCubicBezier(new Vector2(49.8800011F, -7.69500017F), new Vector2(50.0149994F, -8.25500011F), new Vector2(50.2859993F, -8.75800037F));
                    builder.AddCubicBezier(new Vector2(50.5569992F, -9.26099968F), new Vector2(50.9339981F, -9.65200043F), new Vector2(51.4169998F, -9.93200016F));
                    builder.AddCubicBezier(new Vector2(51.9000015F, -10.2119999F), new Vector2(52.4510002F, -10.3529997F), new Vector2(53.0699997F, -10.3529997F));
                    builder.AddCubicBezier(new Vector2(53.6889992F, -10.3529997F), new Vector2(54.2340012F, -10.2119999F), new Vector2(54.7080002F, -9.93200016F));
                    builder.AddCubicBezier(new Vector2(55.1819992F, -9.65200043F), new Vector2(55.5540009F, -9.26599979F), new Vector2(55.8250008F, -8.77299976F));
                    builder.AddCubicBezier(new Vector2(56.0960007F, -8.27999973F), new Vector2(56.230999F, -7.71400023F), new Vector2(56.230999F, -7.07600021F));
                    builder.AddCubicBezier(new Vector2(56.230999F, -6.41900015F), new Vector2(56.1010017F, -5.83400011F), new Vector2(55.8400002F, -5.32200003F));
                    builder.AddCubicBezier(new Vector2(55.5789986F, -4.80999994F), new Vector2(55.2060013F, -4.41800022F), new Vector2(54.7229996F, -4.14699984F));
                    builder.AddCubicBezier(new Vector2(54.2400017F, -3.87599993F), new Vector2(53.6889992F, -3.74099994F), new Vector2(53.0699997F, -3.74099994F));
                    builder.AddCubicBezier(new Vector2(52.4510002F, -3.74099994F), new Vector2(51.9000015F, -3.87599993F), new Vector2(51.4169998F, -4.14699984F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(57.0139999F, -0.666999996F));
                    builder.AddCubicBezier(new Vector2(58.1739998F, -1.324F), new Vector2(59.0919991F, -2.20799994F), new Vector2(59.769001F, -3.31999993F));
                    builder.AddCubicBezier(new Vector2(60.4459991F, -4.43200016F), new Vector2(60.7840004F, -5.69399977F), new Vector2(60.7840004F, -7.10500002F));
                    builder.AddCubicBezier(new Vector2(60.7840004F, -8.47799969F), new Vector2(60.4459991F, -9.71899986F), new Vector2(59.769001F, -10.8310003F));
                    builder.AddCubicBezier(new Vector2(59.0919991F, -11.9429998F), new Vector2(58.1689987F, -12.8179998F), new Vector2(56.9990005F, -13.4560003F));
                    builder.AddCubicBezier(new Vector2(55.8289986F, -14.0939999F), new Vector2(54.5200005F, -14.4130001F), new Vector2(53.0699997F, -14.4130001F));
                    builder.AddCubicBezier(new Vector2(51.5810013F, -14.4130001F), new Vector2(50.257F, -14.0889997F), new Vector2(49.0970001F, -13.441F));
                    builder.AddCubicBezier(new Vector2(47.9370003F, -12.7930002F), new Vector2(47.0239983F, -11.9189997F), new Vector2(46.3569984F, -10.8170004F));
                    builder.AddCubicBezier(new Vector2(45.6899986F, -9.71500015F), new Vector2(45.355999F, -8.47799969F), new Vector2(45.355999F, -7.10500002F));
                    builder.AddCubicBezier(new Vector2(45.355999F, -5.71299982F), new Vector2(45.6940002F, -4.45599985F), new Vector2(46.3709984F, -3.33500004F));
                    builder.AddCubicBezier(new Vector2(47.0480003F, -2.21399999F), new Vector2(47.9659996F, -1.324F), new Vector2(49.1259995F, -0.666999996F));
                    builder.AddCubicBezier(new Vector2(50.2859993F, -0.00999999978F), new Vector2(51.6010017F, 0.319000006F), new Vector2(53.0699997F, 0.319000006F));
                    builder.AddCubicBezier(new Vector2(54.5390015F, 0.319000006F), new Vector2(55.8540001F, -0.00999999978F), new Vector2(57.0139999F, -0.666999996F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_88()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(40.368F, -0.449000001F));
                    builder.AddCubicBezier(new Vector2(41.644001F, -0.961000025F), new Vector2(42.7649994F, -1.68200004F), new Vector2(43.7319984F, -2.6099999F));
                    builder.AddLine(new Vector2(40.6290016F, -5.71299982F));
                    builder.AddCubicBezier(new Vector2(40.0880013F, -5.11399984F), new Vector2(39.4300003F, -4.65399981F), new Vector2(38.6570015F, -4.33500004F));
                    builder.AddCubicBezier(new Vector2(37.8839989F, -4.01599979F), new Vector2(36.9749985F, -3.85700011F), new Vector2(35.9309998F, -3.85700011F));
                    builder.AddCubicBezier(new Vector2(35.0419998F, -3.85700011F), new Vector2(34.2299995F, -4.00699997F), new Vector2(33.4949989F, -4.30700016F));
                    builder.AddCubicBezier(new Vector2(32.7599983F, -4.60699987F), new Vector2(32.1220016F, -5.04099989F), new Vector2(31.5809994F, -5.61100006F));
                    builder.AddCubicBezier(new Vector2(31.0400009F, -6.18100023F), new Vector2(30.6240005F, -6.8579998F), new Vector2(30.3339996F, -7.64099979F));
                    builder.AddCubicBezier(new Vector2(30.0440006F, -8.42399979F), new Vector2(29.8990002F, -9.29899979F), new Vector2(29.8990002F, -10.2659998F));
                    builder.AddCubicBezier(new Vector2(29.8990002F, -11.1940002F), new Vector2(30.0440006F, -12.0539999F), new Vector2(30.3339996F, -12.8470001F));
                    builder.AddCubicBezier(new Vector2(30.6240005F, -13.6400003F), new Vector2(31.0400009F, -14.316F), new Vector2(31.5809994F, -14.8769999F));
                    builder.AddCubicBezier(new Vector2(32.1220016F, -15.4379997F), new Vector2(32.7599983F, -15.8669996F), new Vector2(33.4949989F, -16.1669998F));
                    builder.AddCubicBezier(new Vector2(34.2299995F, -16.4669991F), new Vector2(35.0419998F, -16.6170006F), new Vector2(35.9309998F, -16.6170006F));
                    builder.AddCubicBezier(new Vector2(36.9360008F, -16.6170006F), new Vector2(37.8209991F, -16.4529991F), new Vector2(38.5849991F, -16.1240005F));
                    builder.AddCubicBezier(new Vector2(39.348999F, -15.7950001F), new Vector2(39.9910011F, -15.3409996F), new Vector2(40.5130005F, -14.7609997F));
                    builder.AddLine(new Vector2(43.5870018F, -17.8640003F));
                    builder.AddCubicBezier(new Vector2(42.6399994F, -18.7730007F), new Vector2(41.5320015F, -19.4880009F), new Vector2(40.2659988F, -20.0100002F));
                    builder.AddCubicBezier(new Vector2(39F, -20.5319996F), new Vector2(37.5550003F, -20.7929993F), new Vector2(35.9309998F, -20.7929993F));
                    builder.AddCubicBezier(new Vector2(34.3839989F, -20.7929993F), new Vector2(32.9630013F, -20.5270004F), new Vector2(31.6679993F, -19.9950008F));
                    builder.AddCubicBezier(new Vector2(30.3729992F, -19.4629993F), new Vector2(29.2360001F, -18.7240009F), new Vector2(28.2600002F, -17.7770004F));
                    builder.AddCubicBezier(new Vector2(27.2840004F, -16.8299999F), new Vector2(26.5249996F, -15.7139997F), new Vector2(25.9839993F, -14.4280005F));
                    builder.AddCubicBezier(new Vector2(25.4430008F, -13.1420002F), new Vector2(25.1720009F, -11.7550001F), new Vector2(25.1720009F, -10.2659998F));
                    builder.AddCubicBezier(new Vector2(25.1720009F, -8.77700043F), new Vector2(25.4430008F, -7.39099979F), new Vector2(25.9839993F, -6.10500002F));
                    builder.AddCubicBezier(new Vector2(26.5249996F, -4.81899977F), new Vector2(27.2840004F, -3.69300008F), new Vector2(28.2600002F, -2.72600007F));
                    builder.AddCubicBezier(new Vector2(29.2360001F, -1.75899994F), new Vector2(30.3770008F, -1.01100004F), new Vector2(31.6819992F, -0.479000002F));
                    builder.AddCubicBezier(new Vector2(32.9869995F, 0.0529999994F), new Vector2(34.4039993F, 0.319000006F), new Vector2(35.9309998F, 0.319000006F));
                    builder.AddCubicBezier(new Vector2(37.612999F, 0.319000006F), new Vector2(39.0919991F, 0.063000001F), new Vector2(40.368F, -0.449000001F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - Layer aggregator
            // - - Layer: Under MIT License - All right reserved Outlines
            CanvasGeometry Geometry_89()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.SetFilledRegionDetermination(CanvasFilledRegionDetermination.Winding);
                    builder.BeginFigure(new Vector2(7.11999989F, -2.48000002F));
                    builder.AddCubicBezier(new Vector2(5.796F, -3.28200006F), new Vector2(4.76000023F, -4.36399984F), new Vector2(4.01599979F, -5.72700024F));
                    builder.AddCubicBezier(new Vector2(3.27200007F, -7.09000015F), new Vector2(2.9000001F, -8.62300014F), new Vector2(2.9000001F, -10.3240004F));
                    builder.AddCubicBezier(new Vector2(2.9000001F, -12.0249996F), new Vector2(3.26699996F, -13.5469999F), new Vector2(4.00199986F, -14.8909998F));
                    builder.AddCubicBezier(new Vector2(4.73699999F, -16.2350006F), new Vector2(5.77099991F, -17.2980003F), new Vector2(7.10500002F, -18.0809994F));
                    builder.AddCubicBezier(new Vector2(8.43900013F, -18.8640003F), new Vector2(9.97599983F, -19.2560005F), new Vector2(11.7159996F, -19.2560005F));
                    builder.AddCubicBezier(new Vector2(13.4560003F, -19.2560005F), new Vector2(14.993F, -18.8600006F), new Vector2(16.3269997F, -18.0669994F));
                    builder.AddCubicBezier(new Vector2(17.6609993F, -17.2740002F), new Vector2(18.7049999F, -16.2070007F), new Vector2(19.4589996F, -14.8629999F));
                    builder.AddCubicBezier(new Vector2(20.2129993F, -13.5190001F), new Vector2(20.5900002F, -12.0059996F), new Vector2(20.5900002F, -10.3240004F));
                    builder.AddCubicBezier(new Vector2(20.5900002F, -8.62300014F), new Vector2(20.2129993F, -7.09000015F), new Vector2(19.4589996F, -5.72700024F));
                    builder.AddCubicBezier(new Vector2(18.7049999F, -4.36399984F), new Vector2(17.6609993F, -3.28200006F), new Vector2(16.3269997F, -2.48000002F));
                    builder.AddCubicBezier(new Vector2(14.993F, -1.67799997F), new Vector2(13.4560003F, -1.27600002F), new Vector2(11.7159996F, -1.27600002F));
                    builder.AddCubicBezier(new Vector2(9.97599983F, -1.27600002F), new Vector2(8.44400024F, -1.67799997F), new Vector2(7.11999989F, -2.48000002F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(14.6020002F, -4.81400013F));
                    builder.AddCubicBezier(new Vector2(15.3459997F, -5.12300014F), new Vector2(16.0370007F, -5.58699989F), new Vector2(16.6749992F, -6.20599985F));
                    builder.AddLine(new Vector2(14.1230001F, -8.75800037F));
                    builder.AddCubicBezier(new Vector2(13.9099998F, -8.48700047F), new Vector2(13.6400003F, -8.27499962F), new Vector2(13.3109999F, -8.11999989F));
                    builder.AddCubicBezier(new Vector2(12.9820004F, -7.96500015F), new Vector2(12.6049995F, -7.88800001F), new Vector2(12.1800003F, -7.88800001F));
                    builder.AddCubicBezier(new Vector2(11.7550001F, -7.88800001F), new Vector2(11.3579998F, -7.98999977F), new Vector2(10.9910002F, -8.19299984F));
                    builder.AddCubicBezier(new Vector2(10.6239996F, -8.39599991F), new Vector2(10.3380003F, -8.67599964F), new Vector2(10.1350002F, -9.0340004F));
                    builder.AddCubicBezier(new Vector2(9.93200016F, -9.3920002F), new Vector2(9.83100033F, -9.80200005F), new Vector2(9.83100033F, -10.2659998F));
                    builder.AddCubicBezier(new Vector2(9.83100033F, -10.7489996F), new Vector2(9.93200016F, -11.165F), new Vector2(10.1350002F, -11.5129995F));
                    builder.AddCubicBezier(new Vector2(10.3380003F, -11.8610001F), new Vector2(10.6239996F, -12.1370001F), new Vector2(10.9910002F, -12.3400002F));
                    builder.AddCubicBezier(new Vector2(11.3579998F, -12.5430002F), new Vector2(11.7550001F, -12.6440001F), new Vector2(12.1800003F, -12.6440001F));
                    builder.AddCubicBezier(new Vector2(12.5860004F, -12.6440001F), new Vector2(12.9440002F, -12.5860004F), new Vector2(13.2530003F, -12.4700003F));
                    builder.AddCubicBezier(new Vector2(13.5620003F, -12.3540001F), new Vector2(13.8330002F, -12.1610003F), new Vector2(14.0649996F, -11.8900003F));
                    builder.AddLine(new Vector2(16.5879993F, -14.4420004F));
                    builder.AddCubicBezier(new Vector2(16.0079994F, -15.0220003F), new Vector2(15.3459997F, -15.4619999F), new Vector2(14.6020002F, -15.7620001F));
                    builder.AddCubicBezier(new Vector2(13.8579998F, -16.0620003F), new Vector2(13.0500002F, -16.2110004F), new Vector2(12.1800003F, -16.2110004F));
                    builder.AddCubicBezier(new Vector2(10.9809999F, -16.2110004F), new Vector2(9.89900017F, -15.9499998F), new Vector2(8.93200016F, -15.4280005F));
                    builder.AddCubicBezier(new Vector2(7.96500015F, -14.9060001F), new Vector2(7.20200014F, -14.1999998F), new Vector2(6.64099979F, -13.3109999F));
                    builder.AddCubicBezier(new Vector2(6.07999992F, -12.4219999F), new Vector2(5.80000019F, -11.4069996F), new Vector2(5.80000019F, -10.2659998F));
                    builder.AddCubicBezier(new Vector2(5.80000019F, -9.14500046F), new Vector2(6.07499981F, -8.13500023F), new Vector2(6.62599993F, -7.23600006F));
                    builder.AddCubicBezier(new Vector2(7.17700005F, -6.33699989F), new Vector2(7.93200016F, -5.63000011F), new Vector2(8.88899994F, -5.11800003F));
                    builder.AddCubicBezier(new Vector2(9.84599972F, -4.60599995F), new Vector2(10.9230003F, -4.3499999F), new Vector2(12.1219997F, -4.3499999F));
                    builder.AddCubicBezier(new Vector2(13.0310001F, -4.3499999F), new Vector2(13.8579998F, -4.50500011F), new Vector2(14.6020002F, -4.81400013F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    builder.BeginFigure(new Vector2(15.9650002F, -0.522000015F));
                    builder.AddCubicBezier(new Vector2(17.2310009F, -1.06299996F), new Vector2(18.3330002F, -1.81700003F), new Vector2(19.2709999F, -2.78399992F));
                    builder.AddCubicBezier(new Vector2(20.2089996F, -3.75099993F), new Vector2(20.9279995F, -4.87699986F), new Vector2(21.4309998F, -6.16300011F));
                    builder.AddCubicBezier(new Vector2(21.934F, -7.44899988F), new Vector2(22.1849995F, -8.82600021F), new Vector2(22.1849995F, -10.2950001F));
                    builder.AddCubicBezier(new Vector2(22.1849995F, -11.7840004F), new Vector2(21.934F, -13.1660004F), new Vector2(21.4309998F, -14.4420004F));
                    builder.AddCubicBezier(new Vector2(20.9279995F, -15.7180004F), new Vector2(20.2129993F, -16.8349991F), new Vector2(19.2849998F, -17.7919998F));
                    builder.AddCubicBezier(new Vector2(18.3570004F, -18.7490005F), new Vector2(17.2450008F, -19.493F), new Vector2(15.9499998F, -20.0249996F));
                    builder.AddCubicBezier(new Vector2(14.6549997F, -20.5569992F), new Vector2(13.243F, -20.8220005F), new Vector2(11.7159996F, -20.8220005F));
                    builder.AddCubicBezier(new Vector2(10.1890001F, -20.8220005F), new Vector2(8.7869997F, -20.5510006F), new Vector2(7.51100016F, -20.0100002F));
                    builder.AddCubicBezier(new Vector2(6.23500013F, -19.4689999F), new Vector2(5.12900019F, -18.7240009F), new Vector2(4.19099998F, -17.7770004F));
                    builder.AddCubicBezier(new Vector2(3.25300002F, -16.8299999F), new Vector2(2.53299999F, -15.7180004F), new Vector2(2.02999997F, -14.4420004F));
                    builder.AddCubicBezier(new Vector2(1.52699995F, -13.1660004F), new Vector2(1.27600002F, -11.7930002F), new Vector2(1.27600002F, -10.3240004F));
                    builder.AddCubicBezier(new Vector2(1.27600002F, -8.83500004F), new Vector2(1.53699994F, -7.44899988F), new Vector2(2.05900002F, -6.16300011F));
                    builder.AddCubicBezier(new Vector2(2.58100009F, -4.87699986F), new Vector2(3.31100011F, -3.75099993F), new Vector2(4.24900007F, -2.78399992F));
                    builder.AddCubicBezier(new Vector2(5.1869998F, -1.81700003F), new Vector2(6.29300022F, -1.06299996F), new Vector2(7.56899977F, -0.522000015F));
                    builder.AddCubicBezier(new Vector2(8.84500027F, 0.0189999994F), new Vector2(10.2469997F, 0.289999992F), new Vector2(11.7740002F, 0.289999992F));
                    builder.AddCubicBezier(new Vector2(13.3009996F, 0.289999992F), new Vector2(14.6990004F, 0.0189999994F), new Vector2(15.9650002F, -0.522000015F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // Color
            ColorKeyFrameAnimation ColorAnimation_TransparentWhite_to_TransparentWhite()
            {
                // Frame 0.
                var result = CreateColorKeyFrameAnimation(0F, _fgTransparentColor, HoldThenStepEasingFunction());
                // Frame 15.
                // White
                result.InsertKeyFrame(0.0250000004F, _fgColor, CubicBezierEasingFunction_0());
                // Frame 241.
                // White
                result.InsertKeyFrame(0.401666671F, _fgColor, CubicBezierEasingFunction_0());
                // Frame 256.
                // TransparentWhite
                result.InsertKeyFrame(0.426666677F, _fgTransparentColor, CubicBezierEasingFunction_0());
                return result;
            }

            CompositionColorBrush AnimatedColorBrush_TransparentWhite_to_TransparentWhite()
            {
                if (_animatedColorBrush_TransparentWhite_to_TransparentWhite != null) { return _animatedColorBrush_TransparentWhite_to_TransparentWhite; }
                var result = _animatedColorBrush_TransparentWhite_to_TransparentWhite = _c.CreateColorBrush();
                return result;
            }

            CompositionColorBrush ColorBrush_Black()
            {
                return (_colorBrush_Black == null)
                    ? _colorBrush_Black = _c.CreateColorBrush(Color.FromArgb(0xFF, 0x00, 0x00, 0x00))
                    : _colorBrush_Black;
            }

            CompositionColorBrush ColorBrush_White()
            {
                return (_colorBrush_White == null)
                    ? _colorBrush_White = _c.CreateColorBrush(_fgColor)
                    : _colorBrush_White;
            }

            // Stop 0
            CompositionColorGradientStop GradientStop_0_AlmostAqua_FF04E3FB()
            {
                return (_gradientStop_0_AlmostAqua_FF04E3FB == null)
                    ? _gradientStop_0_AlmostAqua_FF04E3FB = _c.CreateColorGradientStop(0F, Color.FromArgb(0xFF, 0x04, 0xE3, 0xFB))
                    : _gradientStop_0_AlmostAqua_FF04E3FB;
            }

            // Stop 0
            CompositionColorGradientStop GradientStop_0_AlmostLawnGreen_FF7CFB04()
            {
                return (_gradientStop_0_AlmostLawnGreen_FF7CFB04 == null)
                    ? _gradientStop_0_AlmostLawnGreen_FF7CFB04 = _c.CreateColorGradientStop(0F, Color.FromArgb(0xFF, 0x7C, 0xFB, 0x04))
                    : _gradientStop_0_AlmostLawnGreen_FF7CFB04;
            }

            // Stop 0
            CompositionColorGradientStop GradientStop_0_AlmostOrchid_FFFB4BDC()
            {
                return (_gradientStop_0_AlmostOrchid_FFFB4BDC == null)
                    ? _gradientStop_0_AlmostOrchid_FFFB4BDC = _c.CreateColorGradientStop(0F, Color.FromArgb(0xFF, 0xFB, 0x4B, 0xDC))
                    : _gradientStop_0_AlmostOrchid_FFFB4BDC;
            }

            // Stop 1
            CompositionColorGradientStop GradientStop_0p563_AlmostDarkOrange_FFFB841C()
            {
                return (_gradientStop_0p563_AlmostDarkOrange_FFFB841C == null)
                    ? _gradientStop_0p563_AlmostDarkOrange_FFFB841C = _c.CreateColorGradientStop(0.563000023F, Color.FromArgb(0xFF, 0xFB, 0x84, 0x1C))
                    : _gradientStop_0p563_AlmostDarkOrange_FFFB841C;
            }

            // Stop 1
            CompositionColorGradientStop GradientStop_1_AlmostDodgerBlue_FF046CFB()
            {
                return (_gradientStop_1_AlmostDodgerBlue_FF046CFB == null)
                    ? _gradientStop_1_AlmostDodgerBlue_FF046CFB = _c.CreateColorGradientStop(1F, Color.FromArgb(0xFF, 0x04, 0x6C, 0xFB))
                    : _gradientStop_1_AlmostDodgerBlue_FF046CFB;
            }

            // Stop 1
            CompositionColorGradientStop GradientStop_1_AlmostYellow_FFEBFB04()
            {
                return (_gradientStop_1_AlmostYellow_FFEBFB04 == null)
                    ? _gradientStop_1_AlmostYellow_FFEBFB04 = _c.CreateColorGradientStop(1F, Color.FromArgb(0xFF, 0xEB, 0xFB, 0x04))
                    : _gradientStop_1_AlmostYellow_FFEBFB04;
            }

            // - - - - - PreComp layer: Crescent
            // Masks
            // Layer: Crescent
            CompositionContainerShape ContainerShape_0()
            {
                if (_containerShape_0 != null) { return _containerShape_0; }
                var result = _containerShape_0 = _c.CreateContainerShape();
                result.CenterPoint = new Vector2(980.041992F, 528.40802F);
                result.Offset = new Vector2(0.958007812F, 4.59197998F);
                var shapes = result.Shapes;
                shapes.Add(SpriteShape_33());
                shapes.Add(SpriteShape_34());
                return result;
            }

            // Layer: CrescentColor
            CompositionContainerShape ContainerShape_1()
            {
                if (_containerShape_1 != null) { return _containerShape_1; }
                var result = _containerShape_1 = _c.CreateContainerShape();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(960F, 540F));
                result.CenterPoint = new Vector2(960F, 540F);
                var shapes = result.Shapes;
                shapes.Add(SpriteShape_35());
                shapes.Add(SpriteShape_36());
                BindProperty(_containerShape_1, "Offset", "Vector2(my.Position.X-960,my.Position.Y-540)", "my", _containerShape_1);
                return result;
            }

            // Layer: collapseText
            CompositionContainerShape ContainerShape_2()
            {
                if (_containerShape_2 != null) { return _containerShape_2; }
                var result = _containerShape_2 = _c.CreateContainerShape();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(1181F, 651F));
                result.CenterPoint = new Vector2(1579F, 560F);
                result.Scale = new Vector2(0.449999988F, 0.449999988F);
                result.Shapes.Add(SpriteShape_48());
                BindProperty(_containerShape_2, "Offset", "Vector2(my.Position.X-1579,my.Position.Y-560)", "my", _containerShape_2);
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_00()
            {
                var effectFactory = EffectFactory_0();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_00());
                result.SetSourceParameter("source", SurfaceBrush_01());
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_01()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_02());
                result.SetSourceParameter("source", SurfaceBrush_03());
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_02()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_04());
                result.SetSourceParameter("source", SurfaceBrush_05());
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_03()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_06());
                result.SetSourceParameter("source", SurfaceBrush_07());
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_04()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_08());
                result.SetSourceParameter("source", SurfaceBrush_09());
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_05()
            {
                var effectFactory = EffectFactory_0();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_10());
                result.SetSourceParameter("source", SurfaceBrush_11());
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_06()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_12());
                result.SetSourceParameter("source", SurfaceBrush_13());
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_07()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_14());
                result.SetSourceParameter("source", SurfaceBrush_15());
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_08()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_16());
                result.SetSourceParameter("source", SurfaceBrush_17());
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_09()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_18());
                result.SetSourceParameter("source", SurfaceBrush_19());
                return result;
            }

            // PreComp layer: Crescent
            CompositionEffectBrush EffectBrush_10()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_20());
                result.SetSourceParameter("source", SurfaceBrush_31());
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_11()
            {
                var effectFactory = EffectFactory_0();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_21());
                result.SetSourceParameter("source", SurfaceBrush_22());
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_12()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_23());
                result.SetSourceParameter("source", SurfaceBrush_24());
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_13()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_25());
                result.SetSourceParameter("source", SurfaceBrush_26());
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_14()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_27());
                result.SetSourceParameter("source", SurfaceBrush_28());
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - Transforms for Crescent
            CompositionEffectBrush EffectBrush_15()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_29());
                result.SetSourceParameter("source", SurfaceBrush_30());
                return result;
            }

            // PreComp layer: CrescentColor
            CompositionEffectBrush EffectBrush_16()
            {
                var effectFactory = EffectFactory_0();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_32());
                result.SetSourceParameter("source", SurfaceBrush_34());
                return result;
            }

            // PreComp layer: collapseText
            CompositionEffectBrush EffectBrush_17()
            {
                var effectFactory = EffectFactory_1();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_35());
                result.SetSourceParameter("source", SurfaceBrush_42());
                return result;
            }

            // - - PreComp layer: NewLogoDraftTitle
            CompositionEffectBrush EffectBrush_18()
            {
                var effectFactory = EffectFactory_0();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_36());
                result.SetSourceParameter("source", SurfaceBrush_37());
                return result;
            }

            // - - PreComp layer: NewLogoDraftTitle
            CompositionEffectBrush EffectBrush_19()
            {
                var effectFactory = EffectFactory_0();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_38());
                result.SetSourceParameter("source", SurfaceBrush_39());
                return result;
            }

            // - - PreComp layer: NewLogoDraftTitle
            CompositionEffectBrush EffectBrush_20()
            {
                var effectFactory = EffectFactory_0();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_40());
                result.SetSourceParameter("source", SurfaceBrush_41());
                return result;
            }

            CompositionEffectFactory EffectFactory_0()
            {
                var compositeEffect = new CompositeEffect();
                compositeEffect.Mode = CanvasComposite.DestinationOut;
                compositeEffect.Sources.Add(new CompositionEffectSourceParameter("destination"));
                compositeEffect.Sources.Add(new CompositionEffectSourceParameter("source"));
                if (_effectFactory_0 != null) { return _effectFactory_0; }
                var result = _effectFactory_0 = _c.CreateEffectFactory(compositeEffect);
                return result;
            }

            CompositionEffectFactory EffectFactory_1()
            {
                var compositeEffect = new CompositeEffect();
                compositeEffect.Mode = CanvasComposite.DestinationIn;
                compositeEffect.Sources.Add(new CompositionEffectSourceParameter("destination"));
                compositeEffect.Sources.Add(new CompositionEffectSourceParameter("source"));
                if (_effectFactory_1 != null) { return _effectFactory_1; }
                var result = _effectFactory_1 = _c.CreateEffectFactory(compositeEffect);
                return result;
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            // - Opacity for layer: Lime
            // Offset:<960, 540>
            CompositionLinearGradientBrush LinearGradientBrush_0()
            {
                var result = _c.CreateLinearGradientBrush();
                var colorStops = result.ColorStops;
                colorStops.Add(GradientStop_0_AlmostLawnGreen_FF7CFB04());
                colorStops.Add(GradientStop_1_AlmostYellow_FFEBFB04());
                result.MappingMode = CompositionMappingMode.Absolute;
                result.StartPoint = new Vector2(319F, -141F);
                result.EndPoint = new Vector2(94F, -311F);
                return result;
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            // - Opacity for layer: Lime
            // Offset:<960, 540>
            CompositionLinearGradientBrush LinearGradientBrush_1()
            {
                var result = _c.CreateLinearGradientBrush();
                var colorStops = result.ColorStops;
                colorStops.Add(GradientStop_0_AlmostLawnGreen_FF7CFB04());
                colorStops.Add(GradientStop_1_AlmostYellow_FFEBFB04());
                result.MappingMode = CompositionMappingMode.Absolute;
                result.StartPoint = new Vector2(319F, -141F);
                result.EndPoint = new Vector2(94F, -311F);
                return result;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            // - Opacity for layer: Lime
            // Offset:<960, 540>
            CompositionLinearGradientBrush LinearGradientBrush_2()
            {
                var result = _c.CreateLinearGradientBrush();
                var colorStops = result.ColorStops;
                colorStops.Add(GradientStop_0_AlmostLawnGreen_FF7CFB04());
                colorStops.Add(GradientStop_1_AlmostYellow_FFEBFB04());
                result.MappingMode = CompositionMappingMode.Absolute;
                result.StartPoint = new Vector2(319F, -141F);
                result.EndPoint = new Vector2(94F, -311F);
                return result;
            }

            CompositionPath Path_0()
            {
                if (_path_0 != null) { return _path_0; }
                var result = _path_0 = new CompositionPath(Geometry_02());
                return result;
            }

            CompositionPath Path_1()
            {
                if (_path_1 != null) { return _path_1; }
                var result = _path_1 = new CompositionPath(Geometry_05());
                return result;
            }

            CompositionPath Path_2()
            {
                if (_path_2 != null) { return _path_2; }
                var result = _path_2 = new CompositionPath(Geometry_07());
                return result;
            }

            CompositionPath Path_3()
            {
                if (_path_3 != null) { return _path_3; }
                var result = _path_3 = new CompositionPath(Geometry_09());
                return result;
            }

            CompositionPath Path_4()
            {
                if (_path_4 != null) { return _path_4; }
                var result = _path_4 = new CompositionPath(Geometry_11());
                return result;
            }

            CompositionPath Path_5()
            {
                if (_path_5 != null) { return _path_5; }
                var result = _path_5 = new CompositionPath(Geometry_13());
                return result;
            }

            CompositionPath Path_6()
            {
                if (_path_6 != null) { return _path_6; }
                var result = _path_6 = new CompositionPath(Geometry_15());
                return result;
            }

            CompositionPath Path_7()
            {
                if (_path_7 != null) { return _path_7; }
                var result = _path_7 = new CompositionPath(Geometry_21());
                return result;
            }

            CompositionPathGeometry PathGeometry_00()
            {
                return (_pathGeometry_00 == null)
                    ? _pathGeometry_00 = _c.CreatePathGeometry(new CompositionPath(Geometry_00()))
                    : _pathGeometry_00;
            }

            CompositionPathGeometry PathGeometry_01()
            {
                return (_pathGeometry_01 == null)
                    ? _pathGeometry_01 = _c.CreatePathGeometry(new CompositionPath(Geometry_01()))
                    : _pathGeometry_01;
            }

            CompositionPathGeometry PathGeometry_02()
            {
                if (_pathGeometry_02 != null) { return _pathGeometry_02; }
                var result = _pathGeometry_02 = _c.CreatePathGeometry();
                return result;
            }

            CompositionPathGeometry PathGeometry_03()
            {
                return (_pathGeometry_03 == null)
                    ? _pathGeometry_03 = _c.CreatePathGeometry(new CompositionPath(Geometry_04()))
                    : _pathGeometry_03;
            }

            CompositionPathGeometry PathGeometry_04()
            {
                if (_pathGeometry_04 != null) { return _pathGeometry_04; }
                var result = _pathGeometry_04 = _c.CreatePathGeometry();
                return result;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Crescent1
            // Offset:<1149, 314>
            CompositionPathGeometry PathGeometry_05()
            {
                if (_pathGeometry_05 != null) { return _pathGeometry_05; }
                var result = _pathGeometry_05 = _c.CreatePathGeometry(Path_2());
                return result;
            }

            CompositionPathGeometry PathGeometry_06()
            {
                return (_pathGeometry_06 == null)
                    ? _pathGeometry_06 = _c.CreatePathGeometry(new CompositionPath(Geometry_08()))
                    : _pathGeometry_06;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Crescent2
            // Offset:<973, 516>
            CompositionPathGeometry PathGeometry_07()
            {
                if (_pathGeometry_07 != null) { return _pathGeometry_07; }
                var result = _pathGeometry_07 = _c.CreatePathGeometry(Path_3());
                return result;
            }

            CompositionPathGeometry PathGeometry_08()
            {
                return (_pathGeometry_08 == null)
                    ? _pathGeometry_08 = _c.CreatePathGeometry(new CompositionPath(Geometry_10()))
                    : _pathGeometry_08;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Crescent3
            // Offset:<863, 539>
            CompositionPathGeometry PathGeometry_09()
            {
                if (_pathGeometry_09 != null) { return _pathGeometry_09; }
                var result = _pathGeometry_09 = _c.CreatePathGeometry(Path_4());
                return result;
            }

            CompositionPathGeometry PathGeometry_10()
            {
                return (_pathGeometry_10 == null)
                    ? _pathGeometry_10 = _c.CreatePathGeometry(new CompositionPath(Geometry_12()))
                    : _pathGeometry_10;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Crescent1
            // Offset:<1149, 314>
            CompositionPathGeometry PathGeometry_11()
            {
                if (_pathGeometry_11 != null) { return _pathGeometry_11; }
                var result = _pathGeometry_11 = _c.CreatePathGeometry(Path_2());
                return result;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Crescent2
            // Offset:<973, 516>
            CompositionPathGeometry PathGeometry_12()
            {
                if (_pathGeometry_12 != null) { return _pathGeometry_12; }
                var result = _pathGeometry_12 = _c.CreatePathGeometry(Path_3());
                return result;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Crescent3
            // Offset:<863, 539>
            CompositionPathGeometry PathGeometry_13()
            {
                if (_pathGeometry_13 != null) { return _pathGeometry_13; }
                var result = _pathGeometry_13 = _c.CreatePathGeometry(Path_4());
                return result;
            }

            // - - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Crescent1
            // Offset:<1149, 314>
            CompositionPathGeometry PathGeometry_14()
            {
                if (_pathGeometry_14 != null) { return _pathGeometry_14; }
                var result = _pathGeometry_14 = _c.CreatePathGeometry(Path_2());
                return result;
            }

            // - - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Crescent2
            // Offset:<973, 516>
            CompositionPathGeometry PathGeometry_15()
            {
                if (_pathGeometry_15 != null) { return _pathGeometry_15; }
                var result = _pathGeometry_15 = _c.CreatePathGeometry(Path_3());
                return result;
            }

            // - - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Crescent3
            // Offset:<863, 539>
            CompositionPathGeometry PathGeometry_16()
            {
                if (_pathGeometry_16 != null) { return _pathGeometry_16; }
                var result = _pathGeometry_16 = _c.CreatePathGeometry(Path_4());
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - Masks
            // - Layer: Crescent
            CompositionPathGeometry PathGeometry_17()
            {
                if (_pathGeometry_17 != null) { return _pathGeometry_17; }
                var result = _pathGeometry_17 = _c.CreatePathGeometry();
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - Masks
            // - Layer: Crescent
            CompositionPathGeometry PathGeometry_18()
            {
                if (_pathGeometry_18 != null) { return _pathGeometry_18; }
                var result = _pathGeometry_18 = _c.CreatePathGeometry();
                return result;
            }

            // - Layer: CrescentColor
            CompositionPathGeometry PathGeometry_19()
            {
                if (_pathGeometry_19 != null) { return _pathGeometry_19; }
                var result = _pathGeometry_19 = _c.CreatePathGeometry();
                return result;
            }

            // - Layer: CrescentColor
            CompositionPathGeometry PathGeometry_20()
            {
                if (_pathGeometry_20 != null) { return _pathGeometry_20; }
                var result = _pathGeometry_20 = _c.CreatePathGeometry();
                return result;
            }

            // - - - PreComp layer: NewLogoDraftTitle
            // - Shape tree root for layer: C
            // Offset:<1428, 357>
            CompositionPathGeometry PathGeometry_21()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_26()));
            }

            // - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - Shape tree root for layer: O
            // Offset:<1428, 357>
            CompositionPathGeometry PathGeometry_22()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_27()));
            }

            // - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - Masks
            // Layer: O
            CompositionPathGeometry PathGeometry_23()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_28()));
            }

            // - - - PreComp layer: NewLogoDraftTitle
            // - Layer aggregator
            // Layer: L
            CompositionPathGeometry PathGeometry_24()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_29()));
            }

            // - - - PreComp layer: NewLogoDraftTitle
            // - Layer aggregator
            // Layer: L
            CompositionPathGeometry PathGeometry_25()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_30()));
            }

            // - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - Shape tree root for layer: A
            // Offset:<1428, 357>
            // Path 2+Path 1.PathGeometry
            CompositionPathGeometry PathGeometry_26()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_31()));
            }

            // - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - Masks
            // Layer: A
            CompositionPathGeometry PathGeometry_27()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_34()));
            }

            // - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - Shape tree root for layer: P
            // Offset:<1428, 357>
            // Path 2+Path 1.PathGeometry
            CompositionPathGeometry PathGeometry_28()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_35()));
            }

            // - - - - - - - - PreComp layer: NewLogoDraftTitle
            // - Masks
            // Layer: P
            CompositionPathGeometry PathGeometry_29()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_38()));
            }

            // - - - PreComp layer: NewLogoDraftTitle
            // - Layer aggregator
            // Layer: S
            CompositionPathGeometry PathGeometry_30()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_39()));
            }

            // - - - PreComp layer: NewLogoDraftTitle
            // - Layer aggregator
            // Layer: S
            CompositionPathGeometry PathGeometry_31()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_40()));
            }

            // - Layer: collapseText
            CompositionPathGeometry PathGeometry_32()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_41()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_33()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_42()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_34()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_43()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_35()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_44()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_36()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_45()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_37()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_46()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_38()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_47()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_39()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_48()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_40()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_49()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_41()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_50()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_42()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_51()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_43()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_52()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_44()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_53()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_45()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_54()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_46()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_55()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_47()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_56()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_48()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_57()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_49()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_58()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_50()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_59()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_51()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_60()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_52()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_61()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_53()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_62()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_54()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_63()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_55()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_64()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_56()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_65()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_57()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_66()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_58()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_67()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_59()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_68()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_60()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_69()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_61()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_70()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_62()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_71()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_63()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_72()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_64()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_73()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_65()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_74()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_66()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_75()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_67()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_76()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_68()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_77()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_69()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_78()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_70()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_79()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_71()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_80()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_72()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_81()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_73()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_82()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_74()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_83()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_75()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_84()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_76()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_85()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_77()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_86()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_78()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_87()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_79()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_88()));
            }

            // - Layer aggregator
            // Layer: Under MIT License - All right reserved Outlines
            CompositionPathGeometry PathGeometry_80()
            {
                return _c.CreatePathGeometry(new CompositionPath(Geometry_89()));
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Orange
            // Offset:<960, 540>
            CompositionRadialGradientBrush RadialGradientBrush_0()
            {
                var result = _c.CreateRadialGradientBrush();
                var colorStops = result.ColorStops;
                colorStops.Add(GradientStop_0_AlmostOrchid_FFFB4BDC());
                colorStops.Add(GradientStop_0p563_AlmostDarkOrange_FFFB841C());
                result.MappingMode = CompositionMappingMode.Absolute;
                result.EllipseCenter = new Vector2(-180F, -221F);
                result.EllipseRadius = new Vector2(644.94342F, 644.94342F);
                return result;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Blue
            // Offset:<960, 540>
            CompositionRadialGradientBrush RadialGradientBrush_1()
            {
                var result = _c.CreateRadialGradientBrush();
                var colorStops = result.ColorStops;
                colorStops.Add(GradientStop_0_AlmostAqua_FF04E3FB());
                colorStops.Add(GradientStop_1_AlmostDodgerBlue_FF046CFB());
                result.MappingMode = CompositionMappingMode.Absolute;
                result.EllipseCenter = new Vector2(-409F, -392F);
                result.EllipseRadius = new Vector2(897.285889F, 897.285889F);
                return result;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Orange
            // Offset:<960, 540>
            CompositionRadialGradientBrush RadialGradientBrush_2()
            {
                var result = _c.CreateRadialGradientBrush();
                var colorStops = result.ColorStops;
                colorStops.Add(GradientStop_0_AlmostOrchid_FFFB4BDC());
                colorStops.Add(GradientStop_0p563_AlmostDarkOrange_FFFB841C());
                result.MappingMode = CompositionMappingMode.Absolute;
                result.EllipseCenter = new Vector2(-180F, -221F);
                result.EllipseRadius = new Vector2(644.94342F, 644.94342F);
                return result;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Blue
            // Offset:<960, 540>
            CompositionRadialGradientBrush RadialGradientBrush_3()
            {
                var result = _c.CreateRadialGradientBrush();
                var colorStops = result.ColorStops;
                colorStops.Add(GradientStop_0_AlmostAqua_FF04E3FB());
                colorStops.Add(GradientStop_1_AlmostDodgerBlue_FF046CFB());
                result.MappingMode = CompositionMappingMode.Absolute;
                result.EllipseCenter = new Vector2(-409F, -392F);
                result.EllipseRadius = new Vector2(897.285889F, 897.285889F);
                return result;
            }

            // - - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Orange
            // Offset:<960, 540>
            CompositionRadialGradientBrush RadialGradientBrush_4()
            {
                var result = _c.CreateRadialGradientBrush();
                var colorStops = result.ColorStops;
                colorStops.Add(GradientStop_0_AlmostOrchid_FFFB4BDC());
                colorStops.Add(GradientStop_0p563_AlmostDarkOrange_FFFB841C());
                result.MappingMode = CompositionMappingMode.Absolute;
                result.EllipseCenter = new Vector2(-180F, -221F);
                result.EllipseRadius = new Vector2(644.94342F, 644.94342F);
                return result;
            }

            // - - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - - Transforms for Crescent
            // - Shape tree root for layer: Blue
            // Offset:<960, 540>
            CompositionRadialGradientBrush RadialGradientBrush_5()
            {
                var result = _c.CreateRadialGradientBrush();
                var colorStops = result.ColorStops;
                colorStops.Add(GradientStop_0_AlmostAqua_FF04E3FB());
                colorStops.Add(GradientStop_1_AlmostDodgerBlue_FF046CFB());
                result.MappingMode = CompositionMappingMode.Absolute;
                result.EllipseCenter = new Vector2(-409F, -392F);
                result.EllipseRadius = new Vector2(897.285889F, 897.285889F);
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            // Opacity for layer: Lime
            // Path 1
            CompositionSpriteShape SpriteShape_00()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_00();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), LinearGradientBrush_0());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Orange
            // Path 1
            CompositionSpriteShape SpriteShape_01()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_01();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), RadialGradientBrush_0());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<960, 540>
            CompositionSpriteShape SpriteShape_02()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_02();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Blue
            // Path 1
            CompositionSpriteShape SpriteShape_03()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_03();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), RadialGradientBrush_1());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<960, 540>
            CompositionSpriteShape SpriteShape_04()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_04();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent1
            // Path 1
            CompositionSpriteShape SpriteShape_05()
            {
                // Offset:<1149, 314>
                var result = CreateSpriteShape(PathGeometry_05(), new Matrix3x2(1F, 0F, 0F, 1F, 1149F, 314F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeMiterLimit = 2F;
                result.StrokeThickness = 100F;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<1149, 314>
            CompositionSpriteShape SpriteShape_06()
            {
                // Offset:<1149, 314>
                var geometry = PathGeometry_06();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1149F, 314F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent2
            // Path 1
            CompositionSpriteShape SpriteShape_07()
            {
                // Offset:<973, 516>
                var result = CreateSpriteShape(PathGeometry_07(), new Matrix3x2(1F, 0F, 0F, 1F, 973F, 516F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeMiterLimit = 5F;
                result.StrokeThickness = 105F;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<973, 516>
            CompositionSpriteShape SpriteShape_08()
            {
                // Offset:<973, 516>
                var geometry = PathGeometry_08();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 973F, 516F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent3
            // Path 1
            CompositionSpriteShape SpriteShape_09()
            {
                // Offset:<863, 539>
                var result = CreateSpriteShape(PathGeometry_09(), new Matrix3x2(1F, 0F, 0F, 1F, 863F, 539F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeMiterLimit = 5F;
                result.StrokeThickness = 100F;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<863, 539>
            CompositionSpriteShape SpriteShape_10()
            {
                // Offset:<863, 539>
                var geometry = PathGeometry_10();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 863F, 539F), ColorBrush_Black());;
                return result;
            }

            // - - PreComp layer: Crescent
            // - Transforms for Crescent
            // Opacity for layer: Lime
            // Path 1
            CompositionSpriteShape SpriteShape_11()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_00();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), LinearGradientBrush_1());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Orange
            // Path 1
            CompositionSpriteShape SpriteShape_12()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_01();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), RadialGradientBrush_2());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<960, 540>
            CompositionSpriteShape SpriteShape_13()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_02();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Blue
            // Path 1
            CompositionSpriteShape SpriteShape_14()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_03();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), RadialGradientBrush_3());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<960, 540>
            CompositionSpriteShape SpriteShape_15()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_04();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent1
            // Path 1
            CompositionSpriteShape SpriteShape_16()
            {
                // Offset:<1149, 314>
                var result = CreateSpriteShape(PathGeometry_11(), new Matrix3x2(1F, 0F, 0F, 1F, 1149F, 314F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeMiterLimit = 2F;
                result.StrokeThickness = 100F;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<1149, 314>
            CompositionSpriteShape SpriteShape_17()
            {
                // Offset:<1149, 314>
                var geometry = PathGeometry_06();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1149F, 314F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent2
            // Path 1
            CompositionSpriteShape SpriteShape_18()
            {
                // Offset:<973, 516>
                var result = CreateSpriteShape(PathGeometry_12(), new Matrix3x2(1F, 0F, 0F, 1F, 973F, 516F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeMiterLimit = 5F;
                result.StrokeThickness = 105F;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<973, 516>
            CompositionSpriteShape SpriteShape_19()
            {
                // Offset:<973, 516>
                var geometry = PathGeometry_08();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 973F, 516F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent3
            // Path 1
            CompositionSpriteShape SpriteShape_20()
            {
                // Offset:<863, 539>
                var result = CreateSpriteShape(PathGeometry_13(), new Matrix3x2(1F, 0F, 0F, 1F, 863F, 539F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeMiterLimit = 5F;
                result.StrokeThickness = 100F;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<863, 539>
            CompositionSpriteShape SpriteShape_21()
            {
                // Offset:<863, 539>
                var geometry = PathGeometry_10();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 863F, 539F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - PreComp layer: Crescent
            // - Transforms for Crescent
            // Opacity for layer: Lime
            // Path 1
            CompositionSpriteShape SpriteShape_22()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_00();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), LinearGradientBrush_2());;
                return result;
            }

            // - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Orange
            // Path 1
            CompositionSpriteShape SpriteShape_23()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_01();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), RadialGradientBrush_4());;
                return result;
            }

            // - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<960, 540>
            CompositionSpriteShape SpriteShape_24()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_02();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Blue
            // Path 1
            CompositionSpriteShape SpriteShape_25()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_03();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), RadialGradientBrush_5());;
                return result;
            }

            // - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<960, 540>
            CompositionSpriteShape SpriteShape_26()
            {
                // Offset:<960, 540>
                var geometry = PathGeometry_04();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 960F, 540F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent1
            // Path 1
            CompositionSpriteShape SpriteShape_27()
            {
                // Offset:<1149, 314>
                var result = CreateSpriteShape(PathGeometry_14(), new Matrix3x2(1F, 0F, 0F, 1F, 1149F, 314F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeMiterLimit = 2F;
                result.StrokeThickness = 100F;
                return result;
            }

            // - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<1149, 314>
            CompositionSpriteShape SpriteShape_28()
            {
                // Offset:<1149, 314>
                var geometry = PathGeometry_06();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1149F, 314F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent2
            // Path 1
            CompositionSpriteShape SpriteShape_29()
            {
                // Offset:<973, 516>
                var result = CreateSpriteShape(PathGeometry_15(), new Matrix3x2(1F, 0F, 0F, 1F, 973F, 516F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeMiterLimit = 5F;
                result.StrokeThickness = 105F;
                return result;
            }

            // - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<973, 516>
            CompositionSpriteShape SpriteShape_30()
            {
                // Offset:<973, 516>
                var geometry = PathGeometry_08();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 973F, 516F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent3
            // Path 1
            CompositionSpriteShape SpriteShape_31()
            {
                // Offset:<863, 539>
                var result = CreateSpriteShape(PathGeometry_16(), new Matrix3x2(1F, 0F, 0F, 1F, 863F, 539F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeMiterLimit = 5F;
                result.StrokeThickness = 100F;
                return result;
            }

            // - - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - - Transforms for Crescent
            // Masks
            // Offset:<863, 539>
            CompositionSpriteShape SpriteShape_32()
            {
                // Offset:<863, 539>
                var geometry = PathGeometry_10();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 863F, 539F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - Masks
            // Layer: Crescent
            CompositionSpriteShape SpriteShape_33()
            {
                var result = _c.CreateSpriteShape(PathGeometry_17());
                result.FillBrush = ColorBrush_Black();
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - Masks
            // Layer: Crescent
            CompositionSpriteShape SpriteShape_34()
            {
                var result = _c.CreateSpriteShape(PathGeometry_18());
                result.FillBrush = ColorBrush_Black();
                return result;
            }

            // Layer: CrescentColor
            CompositionSpriteShape SpriteShape_35()
            {
                var result = _c.CreateSpriteShape(PathGeometry_19());
                result.FillBrush = ColorBrush_Black();
                return result;
            }

            // Layer: CrescentColor
            CompositionSpriteShape SpriteShape_36()
            {
                var result = _c.CreateSpriteShape(PathGeometry_20());
                result.FillBrush = ColorBrush_Black();
                return result;
            }

            // - - PreComp layer: NewLogoDraftTitle
            // Shape tree root for layer: C
            // Path 1
            CompositionSpriteShape SpriteShape_37()
            {
                // Offset:<1428, 357>
                var geometry = PathGeometry_21();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1428F, 357F), ColorBrush_White());;
                return result;
            }

            // - - - - - - - PreComp layer: NewLogoDraftTitle
            // Shape tree root for layer: O
            // Path 1
            CompositionSpriteShape SpriteShape_38()
            {
                // Offset:<1428, 357>
                var geometry = PathGeometry_22();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1428F, 357F), ColorBrush_White());;
                return result;
            }

            // - - - - - - - PreComp layer: NewLogoDraftTitle
            // Masks
            // Offset:<1428, 357>
            CompositionSpriteShape SpriteShape_39()
            {
                // Offset:<1428, 357>
                var geometry = PathGeometry_23();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1428F, 357F), ColorBrush_Black());;
                return result;
            }

            // - - PreComp layer: NewLogoDraftTitle
            // Layer aggregator
            // Path 1
            CompositionSpriteShape SpriteShape_40()
            {
                // Offset:<1428, 357>
                var geometry = PathGeometry_24();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1428F, 357F), ColorBrush_White());;
                return result;
            }

            // - - PreComp layer: NewLogoDraftTitle
            // Layer aggregator
            // Path 1
            CompositionSpriteShape SpriteShape_41()
            {
                // Offset:<1428, 357>
                var geometry = PathGeometry_25();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1428F, 357F), ColorBrush_White());;
                return result;
            }

            // - - - - - - - PreComp layer: NewLogoDraftTitle
            // Shape tree root for layer: A
            // Path 2+Path 1
            CompositionSpriteShape SpriteShape_42()
            {
                // Offset:<1428, 357>
                var geometry = PathGeometry_26();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1428F, 357F), ColorBrush_White());;
                return result;
            }

            // - - - - - - - PreComp layer: NewLogoDraftTitle
            // Masks
            // Offset:<1428, 357>
            CompositionSpriteShape SpriteShape_43()
            {
                // Offset:<1428, 357>
                var geometry = PathGeometry_27();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1428F, 357F), ColorBrush_Black());;
                return result;
            }

            // - - - - - - - PreComp layer: NewLogoDraftTitle
            // Shape tree root for layer: P
            // Path 2+Path 1
            CompositionSpriteShape SpriteShape_44()
            {
                // Offset:<1428, 357>
                var geometry = PathGeometry_28();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1428F, 357F), ColorBrush_White());;
                return result;
            }

            // - - - - - - - PreComp layer: NewLogoDraftTitle
            // Masks
            // Offset:<1428, 357>
            CompositionSpriteShape SpriteShape_45()
            {
                // Offset:<1428, 357>
                var geometry = PathGeometry_29();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1428F, 357F), ColorBrush_Black());;
                return result;
            }

            // - - PreComp layer: NewLogoDraftTitle
            // Layer aggregator
            // Path 1
            CompositionSpriteShape SpriteShape_46()
            {
                // Offset:<1427.9999, 357>
                var geometry = PathGeometry_30();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1427.99988F, 357F), ColorBrush_White());;
                return result;
            }

            // - - PreComp layer: NewLogoDraftTitle
            // Layer aggregator
            // Path 1
            CompositionSpriteShape SpriteShape_47()
            {
                // Offset:<1428, 357>
                var geometry = PathGeometry_31();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 1428F, 357F), ColorBrush_White());;
                return result;
            }

            // Layer: collapseText
            CompositionSpriteShape SpriteShape_48()
            {
                var result = _c.CreateSpriteShape(PathGeometry_32());
                result.FillBrush = ColorBrush_Black();
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_49()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_33();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_50()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_34();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // v
            CompositionSpriteShape SpriteShape_51()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_35();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // r
            CompositionSpriteShape SpriteShape_52()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_36();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_53()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_37();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // s
            CompositionSpriteShape SpriteShape_54()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_38();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_55()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_39();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // r
            CompositionSpriteShape SpriteShape_56()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_40();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // t
            CompositionSpriteShape SpriteShape_57()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_41();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // h
            CompositionSpriteShape SpriteShape_58()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_42();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_59()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_43();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_60()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_44();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // r
            CompositionSpriteShape SpriteShape_61()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_45();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // l
            CompositionSpriteShape SpriteShape_62()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_46();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // l
            CompositionSpriteShape SpriteShape_63()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_47();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_64()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_48();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // -
            CompositionSpriteShape SpriteShape_65()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_49();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_66()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_50();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // s
            CompositionSpriteShape SpriteShape_67()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_51();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // n
            CompositionSpriteShape SpriteShape_68()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_52();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_69()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_53();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // c
            CompositionSpriteShape SpriteShape_70()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_54();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_71()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_55();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // L
            CompositionSpriteShape SpriteShape_72()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_56();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // T
            CompositionSpriteShape SpriteShape_73()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_57();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // I
            CompositionSpriteShape SpriteShape_74()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_58();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // M
            CompositionSpriteShape SpriteShape_75()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_59();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // r
            CompositionSpriteShape SpriteShape_76()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_60();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_77()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_61();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: d
            CompositionSpriteShape SpriteShape_78()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_62();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // n
            CompositionSpriteShape SpriteShape_79()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_63();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // U
            CompositionSpriteShape SpriteShape_80()
            {
                // Offset:<251.688, 123.043>
                var geometry = PathGeometry_64();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, 251.688004F, 123.042999F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: t
            CompositionSpriteShape SpriteShape_81()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_65();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // c
            CompositionSpriteShape SpriteShape_82()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_66();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // e
            CompositionSpriteShape SpriteShape_83()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_67();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: t
            CompositionSpriteShape SpriteShape_84()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_68();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: t
            CompositionSpriteShape SpriteShape_85()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_69();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: t
            CompositionSpriteShape SpriteShape_86()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_70();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: t
            CompositionSpriteShape SpriteShape_87()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_71();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // e
            CompositionSpriteShape SpriteShape_88()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_72();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // s
            CompositionSpriteShape SpriteShape_89()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_73();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: t
            CompositionSpriteShape SpriteShape_90()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_74();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: t
            CompositionSpriteShape SpriteShape_91()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_75();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // l
            CompositionSpriteShape SpriteShape_92()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_76();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // l
            CompositionSpriteShape SpriteShape_93()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_77();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: t
            CompositionSpriteShape SpriteShape_94()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_78();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // C
            CompositionSpriteShape SpriteShape_95()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_79();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // Layer aggregator
            // ShapeGroup: t
            CompositionSpriteShape SpriteShape_96()
            {
                // Offset:<92, 95.25>, Rotation:-0.004266087439530114 degrees, Scale:<0.87, 0.87>
                var geometry = PathGeometry_80();
                var result = CreateSpriteShape(geometry, new Matrix3x2(0.870000005F, 0F, 0F, 0.870000005F, 92F, 95.25F), AnimatedColorBrush_TransparentWhite_to_TransparentWhite());;
                return result;
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_00()
            {
                return _c.CreateSurfaceBrush(VisualSurface_00());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_01()
            {
                return _c.CreateSurfaceBrush(VisualSurface_01());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_02()
            {
                return _c.CreateSurfaceBrush(VisualSurface_02());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_03()
            {
                return _c.CreateSurfaceBrush(VisualSurface_03());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_04()
            {
                return _c.CreateSurfaceBrush(VisualSurface_04());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_05()
            {
                return _c.CreateSurfaceBrush(VisualSurface_05());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_06()
            {
                return _c.CreateSurfaceBrush(VisualSurface_06());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_07()
            {
                return _c.CreateSurfaceBrush(VisualSurface_07());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_08()
            {
                return _c.CreateSurfaceBrush(VisualSurface_08());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_09()
            {
                return _c.CreateSurfaceBrush(VisualSurface_09());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_10()
            {
                return _c.CreateSurfaceBrush(VisualSurface_10());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_11()
            {
                return _c.CreateSurfaceBrush(VisualSurface_11());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_12()
            {
                return _c.CreateSurfaceBrush(VisualSurface_12());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_13()
            {
                return _c.CreateSurfaceBrush(VisualSurface_13());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_14()
            {
                return _c.CreateSurfaceBrush(VisualSurface_14());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_15()
            {
                return _c.CreateSurfaceBrush(VisualSurface_15());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_16()
            {
                return _c.CreateSurfaceBrush(VisualSurface_16());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_17()
            {
                return _c.CreateSurfaceBrush(VisualSurface_17());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_18()
            {
                return _c.CreateSurfaceBrush(VisualSurface_18());
            }

            // - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_19()
            {
                return _c.CreateSurfaceBrush(VisualSurface_19());
            }

            // - PreComp layer: Crescent
            CompositionSurfaceBrush SurfaceBrush_20()
            {
                return _c.CreateSurfaceBrush(VisualSurface_20());
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_21()
            {
                return _c.CreateSurfaceBrush(VisualSurface_21());
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_22()
            {
                return _c.CreateSurfaceBrush(VisualSurface_22());
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_23()
            {
                return _c.CreateSurfaceBrush(VisualSurface_23());
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_24()
            {
                return _c.CreateSurfaceBrush(VisualSurface_24());
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_25()
            {
                return _c.CreateSurfaceBrush(VisualSurface_25());
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_26()
            {
                return _c.CreateSurfaceBrush(VisualSurface_26());
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_27()
            {
                return _c.CreateSurfaceBrush(VisualSurface_27());
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_28()
            {
                return _c.CreateSurfaceBrush(VisualSurface_28());
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_29()
            {
                return _c.CreateSurfaceBrush(VisualSurface_29());
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - Transforms for Crescent
            CompositionSurfaceBrush SurfaceBrush_30()
            {
                return _c.CreateSurfaceBrush(VisualSurface_30());
            }

            // - PreComp layer: Crescent
            CompositionSurfaceBrush SurfaceBrush_31()
            {
                return _c.CreateSurfaceBrush(VisualSurface_31());
            }

            // - PreComp layer: CrescentColor
            CompositionSurfaceBrush SurfaceBrush_32()
            {
                return _c.CreateSurfaceBrush(VisualSurface_32());
            }

            // - - Opacity for layer: CrescentColor
            // Image layer: NewLogoDraft-1024p256colors.png
            CompositionSurfaceBrush SurfaceBrush_33()
            {
                return _c.CreateSurfaceBrush(_image_image_0);
            }

            // - PreComp layer: CrescentColor
            CompositionSurfaceBrush SurfaceBrush_34()
            {
                return _c.CreateSurfaceBrush(VisualSurface_33());
            }

            // - PreComp layer: collapseText
            CompositionSurfaceBrush SurfaceBrush_35()
            {
                return _c.CreateSurfaceBrush(VisualSurface_34());
            }

            // - - - PreComp layer: NewLogoDraftTitle
            CompositionSurfaceBrush SurfaceBrush_36()
            {
                return _c.CreateSurfaceBrush(VisualSurface_35());
            }

            // - - - PreComp layer: NewLogoDraftTitle
            CompositionSurfaceBrush SurfaceBrush_37()
            {
                return _c.CreateSurfaceBrush(VisualSurface_36());
            }

            // - - - PreComp layer: NewLogoDraftTitle
            CompositionSurfaceBrush SurfaceBrush_38()
            {
                return _c.CreateSurfaceBrush(VisualSurface_37());
            }

            // - - - PreComp layer: NewLogoDraftTitle
            CompositionSurfaceBrush SurfaceBrush_39()
            {
                return _c.CreateSurfaceBrush(VisualSurface_38());
            }

            // - - - PreComp layer: NewLogoDraftTitle
            CompositionSurfaceBrush SurfaceBrush_40()
            {
                return _c.CreateSurfaceBrush(VisualSurface_39());
            }

            // - - - PreComp layer: NewLogoDraftTitle
            CompositionSurfaceBrush SurfaceBrush_41()
            {
                return _c.CreateSurfaceBrush(VisualSurface_40());
            }

            // - PreComp layer: collapseText
            CompositionSurfaceBrush SurfaceBrush_42()
            {
                return _c.CreateSurfaceBrush(VisualSurface_41());
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_00()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_02();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_01()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_03();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_02()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_04();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_03()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_05();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_04()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_06();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_05()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_07();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_06()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_08();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_07()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_09();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_08()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_10();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_09()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_11();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_10()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_14();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_11()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_15();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_12()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_16();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_13()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_17();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_14()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_18();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_15()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_19();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_16()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_20();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_17()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_21();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_18()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_22();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_19()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_23();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - PreComp layer: Crescent
            CompositionVisualSurface VisualSurface_20()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_24();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - - - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_21()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_27();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - - - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_22()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_28();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - - - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_23()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_29();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - - - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_24()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_30();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - - - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_25()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_31();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - - - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_26()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_32();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - - - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_27()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_33();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - - - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_28()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_34();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - - - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_29()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_35();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - - - - - - PreComp layer: Crescent
            // - - - Transforms for Crescent
            CompositionVisualSurface VisualSurface_30()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_36();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - PreComp layer: Crescent
            CompositionVisualSurface VisualSurface_31()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_37();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - PreComp layer: CrescentColor
            CompositionVisualSurface VisualSurface_32()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_38();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - PreComp layer: CrescentColor
            CompositionVisualSurface VisualSurface_33()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_42();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - PreComp layer: collapseText
            CompositionVisualSurface VisualSurface_34()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_43();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // - - - - PreComp layer: NewLogoDraftTitle
            CompositionVisualSurface VisualSurface_35()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_49();
                result.SourceSize = new Vector2(2856F, 714F);
                return result;
            }

            // - - - - PreComp layer: NewLogoDraftTitle
            CompositionVisualSurface VisualSurface_36()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_50();
                result.SourceSize = new Vector2(2856F, 714F);
                return result;
            }

            // - - - - PreComp layer: NewLogoDraftTitle
            CompositionVisualSurface VisualSurface_37()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_51();
                result.SourceSize = new Vector2(2856F, 714F);
                return result;
            }

            // - - - - PreComp layer: NewLogoDraftTitle
            CompositionVisualSurface VisualSurface_38()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_52();
                result.SourceSize = new Vector2(2856F, 714F);
                return result;
            }

            // - - - - PreComp layer: NewLogoDraftTitle
            CompositionVisualSurface VisualSurface_39()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_53();
                result.SourceSize = new Vector2(2856F, 714F);
                return result;
            }

            // - - - - PreComp layer: NewLogoDraftTitle
            CompositionVisualSurface VisualSurface_40()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_54();
                result.SourceSize = new Vector2(2856F, 714F);
                return result;
            }

            // - - PreComp layer: collapseText
            CompositionVisualSurface VisualSurface_41()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_55();
                result.SourceSize = new Vector2(1920F, 1080F);
                return result;
            }

            // PreComp layer: Crescent
            ContainerVisual ContainerVisual_00()
            {
                if (_containerVisual_00 != null) { return _containerVisual_00; }
                var result = _containerVisual_00 = _c.CreateContainerVisual();
                result.CenterPoint = new Vector3(1003F, 500F, 0F);
                result.Scale = new Vector3(2.48000002F, 2.48000002F, 0F);
                // Transforms for Crescent
                result.Children.InsertAtTop(ContainerVisual_01());
                return result;
            }

            // PreComp layer: Crescent
            // Transforms for Crescent
            ContainerVisual ContainerVisual_01()
            {
                var result = _c.CreateContainerVisual();
                result.Clip = InsetClip_0();
                result.Size = new Vector2(1920F, 1080F);
                var children = result.Children;
                // Opacity for layer: Lime
                children.InsertAtTop(ShapeVisual_00());
                children.InsertAtTop(SpriteVisual_00());
                children.InsertAtTop(SpriteVisual_01());
                children.InsertAtTop(SpriteVisual_02());
                children.InsertAtTop(SpriteVisual_03());
                children.InsertAtTop(SpriteVisual_04());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_02()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Orange
                result.Children.InsertAtTop(ShapeVisual_01());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_03()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_02());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_04()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Blue
                result.Children.InsertAtTop(ShapeVisual_03());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_05()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_04());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_06()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Crescent1
                result.Children.InsertAtTop(ShapeVisual_05());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_07()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_06());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_08()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Crescent2
                result.Children.InsertAtTop(ShapeVisual_07());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_09()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_08());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_10()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Crescent3
                result.Children.InsertAtTop(ShapeVisual_09());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_11()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_10());
                return result;
            }

            // PreComp layer: Crescent
            ContainerVisual ContainerVisual_12()
            {
                if (_containerVisual_12 != null) { return _containerVisual_12; }
                var result = _containerVisual_12 = _c.CreateContainerVisual();
                result.CenterPoint = new Vector3(960F, 540F, 0F);
                result.IsVisible = false;
                result.Scale = new Vector3(1.99000001F, 1.99000001F, 0F);
                // Transforms for Crescent
                result.Children.InsertAtTop(ContainerVisual_13());
                return result;
            }

            // PreComp layer: Crescent
            // Transforms for Crescent
            ContainerVisual ContainerVisual_13()
            {
                var result = _c.CreateContainerVisual();
                result.Clip = InsetClip_0();
                result.Size = new Vector2(1920F, 1080F);
                var children = result.Children;
                // Opacity for layer: Lime
                children.InsertAtTop(ShapeVisual_11());
                children.InsertAtTop(SpriteVisual_05());
                children.InsertAtTop(SpriteVisual_06());
                children.InsertAtTop(SpriteVisual_07());
                children.InsertAtTop(SpriteVisual_08());
                children.InsertAtTop(SpriteVisual_09());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_14()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Orange
                result.Children.InsertAtTop(ShapeVisual_12());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_15()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_13());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_16()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Blue
                result.Children.InsertAtTop(ShapeVisual_14());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_17()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_15());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_18()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Crescent1
                result.Children.InsertAtTop(ShapeVisual_16());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_19()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_17());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_20()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Crescent2
                result.Children.InsertAtTop(ShapeVisual_18());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_21()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_19());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_22()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Crescent3
                result.Children.InsertAtTop(ShapeVisual_20());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_23()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_21());
                return result;
            }

            // - - - PreComp layer: Crescent
            ContainerVisual ContainerVisual_24()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                result.Children.InsertAtTop(ContainerVisual_25());
                return result;
            }

            // - - - - PreComp layer: Crescent
            ContainerVisual ContainerVisual_25()
            {
                if (_containerVisual_25 != null) { return _containerVisual_25; }
                var result = _containerVisual_25 = _c.CreateContainerVisual();
                result.CenterPoint = new Vector3(980.041992F, 528.40802F, 0F);
                result.IsVisible = false;
                result.Offset = new Vector3(0.958007812F, 4.59197998F, 0F);
                result.Scale = new Vector3(1F, 1F, 0F);
                // Transforms for Crescent
                result.Children.InsertAtTop(ContainerVisual_26());
                return result;
            }

            // - - - - - PreComp layer: Crescent
            // Transforms for Crescent
            ContainerVisual ContainerVisual_26()
            {
                var result = _c.CreateContainerVisual();
                result.Clip = InsetClip_0();
                result.Size = new Vector2(1920F, 1080F);
                var children = result.Children;
                // Opacity for layer: Lime
                children.InsertAtTop(ShapeVisual_22());
                children.InsertAtTop(SpriteVisual_11());
                children.InsertAtTop(SpriteVisual_12());
                children.InsertAtTop(SpriteVisual_13());
                children.InsertAtTop(SpriteVisual_14());
                children.InsertAtTop(SpriteVisual_15());
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_27()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Orange
                result.Children.InsertAtTop(ShapeVisual_23());
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_28()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_24());
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_29()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Blue
                result.Children.InsertAtTop(ShapeVisual_25());
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_30()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_26());
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_31()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Crescent1
                result.Children.InsertAtTop(ShapeVisual_27());
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_32()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_28());
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_33()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Crescent2
                result.Children.InsertAtTop(ShapeVisual_29());
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_34()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_30());
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_35()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: Crescent3
                result.Children.InsertAtTop(ShapeVisual_31());
                return result;
            }

            // - - - - - - - - - - PreComp layer: Crescent
            // - - - - Transforms for Crescent
            ContainerVisual ContainerVisual_36()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_32());
                return result;
            }

            // - - - PreComp layer: Crescent
            ContainerVisual ContainerVisual_37()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_33());
                return result;
            }

            // - - - PreComp layer: CrescentColor
            ContainerVisual ContainerVisual_38()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                result.Children.InsertAtTop(ContainerVisual_39());
                return result;
            }

            // - - - - PreComp layer: CrescentColor
            ContainerVisual ContainerVisual_39()
            {
                if (_containerVisual_39 != null) { return _containerVisual_39; }
                var result = _containerVisual_39 = _c.CreateContainerVisual();
                result.IsVisible = false;
                // Opacity for layer: CrescentColor
                result.Children.InsertAtTop(ContainerVisual_40());
                return result;
            }

            // Transforms for CrescentColor
            ContainerVisual ContainerVisual_40()
            {
                if (_containerVisual_40 != null) { return _containerVisual_40; }
                var result = _containerVisual_40 = _c.CreateContainerVisual();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(960F, 540F));
                result.CenterPoint = new Vector3(960F, 540F, 0F);
                result.Children.InsertAtTop(ContainerVisual_41());
                BindProperty(_containerVisual_40, "Offset", "Vector3(my.Position.X-960,my.Position.Y-540,0)", "my", _containerVisual_40);
                return result;
            }

            // Opacity for layer: CrescentColor
            ContainerVisual ContainerVisual_41()
            {
                var result = _c.CreateContainerVisual();
                result.Clip = InsetClip_0();
                result.Size = new Vector2(1920F, 1080F);
                // Image layer: NewLogoDraft-1024p256colors.png
                result.Children.InsertAtTop(SpriteVisual_17());
                return result;
            }

            // - - - PreComp layer: CrescentColor
            ContainerVisual ContainerVisual_42()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_34());
                return result;
            }

            // - - - PreComp layer: collapseText
            ContainerVisual ContainerVisual_43()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                result.Children.InsertAtTop(ContainerVisual_44());
                return result;
            }

            // - - - - PreComp layer: collapseText
            ContainerVisual ContainerVisual_44()
            {
                if (_containerVisual_44 != null) { return _containerVisual_44; }
                var result = _containerVisual_44 = _c.CreateContainerVisual();
                result.IsVisible = false;
                // Opacity for layer: collapseText
                result.Children.InsertAtTop(ContainerVisual_45());
                return result;
            }

            // Transforms for collapseText
            ContainerVisual ContainerVisual_45()
            {
                if (_containerVisual_45 != null) { return _containerVisual_45; }
                var result = _containerVisual_45 = _c.CreateContainerVisual();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(1181F, 651F));
                result.CenterPoint = new Vector3(1579F, 560F, 0F);
                result.Scale = new Vector3(0.449999988F, 0.449999988F, 0F);
                result.Children.InsertAtTop(ContainerVisual_46());
                BindProperty(_containerVisual_45, "Offset", "Vector3(my.Position.X-1579,my.Position.Y-560,0)", "my", _containerVisual_45);
                return result;
            }

            // Opacity for layer: collapseText
            ContainerVisual ContainerVisual_46()
            {
                var result = _c.CreateContainerVisual();
                result.Clip = InsetClip_0();
                result.Size = new Vector2(1920F, 1080F);
                // PreComp layer: NewLogoDraftTitle
                result.Children.InsertAtTop(ContainerVisual_47());
                return result;
            }

            // Transforms for NewLogoDraftTitle
            ContainerVisual ContainerVisual_47()
            {
                if (_containerVisual_47 != null) { return _containerVisual_47; }
                var result = _containerVisual_47 = _c.CreateContainerVisual();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(960F, 964F));
                result.CenterPoint = new Vector3(1428F, 357F, 0F);
                result.Scale = new Vector3(0.5F, 0.5F, 0F);
                result.Children.InsertAtTop(ContainerVisual_48());
                BindProperty(_containerVisual_47, "Offset", "Vector3(my.Position.X-1428,my.Position.Y-357,0)", "my", _containerVisual_47);
                return result;
            }

            // PreComp layer: NewLogoDraftTitle
            ContainerVisual ContainerVisual_48()
            {
                var result = _c.CreateContainerVisual();
                result.Clip = InsetClip_0();
                result.Size = new Vector2(2856F, 714F);
                var children = result.Children;
                // Shape tree root for layer: C
                children.InsertAtTop(ShapeVisual_35());
                children.InsertAtTop(SpriteVisual_19());
                // Layer aggregator
                children.InsertAtTop(ShapeVisual_38());
                children.InsertAtTop(SpriteVisual_20());
                children.InsertAtTop(SpriteVisual_21());
                // Layer aggregator
                children.InsertAtTop(ShapeVisual_43());
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            ContainerVisual ContainerVisual_49()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: O
                result.Children.InsertAtTop(ShapeVisual_36());
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            ContainerVisual ContainerVisual_50()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_37());
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            ContainerVisual ContainerVisual_51()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: A
                result.Children.InsertAtTop(ShapeVisual_39());
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            ContainerVisual ContainerVisual_52()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_40());
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            ContainerVisual ContainerVisual_53()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Shape tree root for layer: P
                result.Children.InsertAtTop(ShapeVisual_41());
                return result;
            }

            // - - - - - PreComp layer: NewLogoDraftTitle
            ContainerVisual ContainerVisual_54()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_42());
                return result;
            }

            // - - - PreComp layer: collapseText
            ContainerVisual ContainerVisual_55()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_44());
                return result;
            }

            // The root of the composition.
            ContainerVisual Root()
            {
                if (_root != null) { return _root; }
                var result = _root = _c.CreateContainerVisual();
                var propertySet = result.Properties;
                propertySet.InsertScalar("Progress", 0F);
                var children = result.Children;
                // PreComp layer: Crescent
                children.InsertAtTop(ContainerVisual_00());
                // PreComp layer: Crescent
                children.InsertAtTop(ContainerVisual_12());
                // PreComp layer: Crescent
                children.InsertAtTop(SpriteVisual_10());
                // PreComp layer: CrescentColor
                children.InsertAtTop(SpriteVisual_16());
                // PreComp layer: collapseText
                children.InsertAtTop(SpriteVisual_18());
                // Layer aggregator
                children.InsertAtTop(ShapeVisual_45());
                return result;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_0()
            {
                return (_cubicBezierEasingFunction_0 == null)
                    ? _cubicBezierEasingFunction_0 = _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0.166999996F), new Vector2(0.833000004F, 0.833000004F))
                    : _cubicBezierEasingFunction_0;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_1()
            {
                return (_cubicBezierEasingFunction_1 == null)
                    ? _cubicBezierEasingFunction_1 = _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0F, 1F))
                    : _cubicBezierEasingFunction_1;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_2()
            {
                return (_cubicBezierEasingFunction_2 == null)
                    ? _cubicBezierEasingFunction_2 = _c.CreateCubicBezierEasingFunction(new Vector2(0.0299999993F, 0F), new Vector2(0.0549999997F, 1F))
                    : _cubicBezierEasingFunction_2;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_3()
            {
                return (_cubicBezierEasingFunction_3 == null)
                    ? _cubicBezierEasingFunction_3 = _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0F), new Vector2(0.0549999997F, 1F))
                    : _cubicBezierEasingFunction_3;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_4()
            {
                return (_cubicBezierEasingFunction_4 == null)
                    ? _cubicBezierEasingFunction_4 = _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0F), new Vector2(0F, 1F))
                    : _cubicBezierEasingFunction_4;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_5()
            {
                return (_cubicBezierEasingFunction_5 == null)
                    ? _cubicBezierEasingFunction_5 = _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0.833000004F, 1F))
                    : _cubicBezierEasingFunction_5;
            }

            InsetClip InsetClip_0()
            {
                if (_insetClip_0 != null) { return _insetClip_0; }
                var result = _insetClip_0 = _c.CreateInsetClip();
                return result;
            }

            // Path
            PathKeyFrameAnimation PathKeyFrameAnimation_0()
            {
                // Frame 0.
                var result = CreatePathKeyFrameAnimation(0F, Path_0(), StepThenHoldEasingFunction());
                // Frame 146.
                result.InsertKeyFrame(0.24333334F, Path_0(), HoldThenStepEasingFunction());
                // Frame 218.
                result.InsertKeyFrame(0.363333344F, new CompositionPath(Geometry_03()), _c.CreateCubicBezierEasingFunction(new Vector2(0.0920000002F, 0F), new Vector2(0F, 1F)));
                return result;
            }

            // Path
            PathKeyFrameAnimation PathKeyFrameAnimation_1()
            {
                // Frame 0.
                var result = CreatePathKeyFrameAnimation(0F, Path_1(), StepThenHoldEasingFunction());
                // Frame 111.
                result.InsertKeyFrame(0.185000002F, Path_1(), HoldThenStepEasingFunction());
                // Frame 164.
                result.InsertKeyFrame(0.273333341F, new CompositionPath(Geometry_06()), _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0.666999996F, 1F)));
                return result;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - Masks
            // - - Layer: Crescent
            // Path
            PathKeyFrameAnimation PathKeyFrameAnimation_2()
            {
                // Frame 0.
                var result = CreatePathKeyFrameAnimation(0F, Path_5(), StepThenHoldEasingFunction());
                // Frame 271.
                result.InsertKeyFrame(0.451666653F, Path_5(), HoldThenStepEasingFunction());
                // Frame 281.
                result.InsertKeyFrame(0.468333334F, new CompositionPath(Geometry_14()), CubicBezierEasingFunction_1());
                return result;
            }

            // - - - - - - - - PreComp layer: Crescent
            // - - - Masks
            // - - Layer: Crescent
            // Path
            PathKeyFrameAnimation PathKeyFrameAnimation_3()
            {
                // Frame 0.
                var result = CreatePathKeyFrameAnimation(0F, Path_6(), StepThenHoldEasingFunction());
                // Frame 271.
                result.InsertKeyFrame(0.451666653F, Path_6(), HoldThenStepEasingFunction());
                // Frame 273.
                result.InsertKeyFrame(0.455000013F, new CompositionPath(Geometry_16()), CubicBezierEasingFunction_0());
                // Frame 276.
                result.InsertKeyFrame(0.460000008F, new CompositionPath(Geometry_17()), CubicBezierEasingFunction_0());
                // Frame 278.
                result.InsertKeyFrame(0.463333338F, new CompositionPath(Geometry_18()), CubicBezierEasingFunction_0());
                // Frame 281.
                result.InsertKeyFrame(0.468333334F, new CompositionPath(Geometry_19()), CubicBezierEasingFunction_0());
                return result;
            }

            // - - Layer: CrescentColor
            // Path
            PathKeyFrameAnimation PathKeyFrameAnimation_4()
            {
                // Frame 0.
                var result = CreatePathKeyFrameAnimation(0F, Path_5(), StepThenHoldEasingFunction());
                // Frame 270.
                result.InsertKeyFrame(0.449999988F, Path_5(), HoldThenStepEasingFunction());
                // Frame 280.
                result.InsertKeyFrame(0.466666669F, new CompositionPath(Geometry_20()), CubicBezierEasingFunction_1());
                return result;
            }

            // - - Layer: CrescentColor
            // Path
            PathKeyFrameAnimation PathKeyFrameAnimation_5()
            {
                // Frame 0.
                var result = CreatePathKeyFrameAnimation(0F, Path_7(), StepThenHoldEasingFunction());
                // Frame 270.
                result.InsertKeyFrame(0.449999988F, Path_7(), HoldThenStepEasingFunction());
                // Frame 272.
                result.InsertKeyFrame(0.453333348F, new CompositionPath(Geometry_22()), CubicBezierEasingFunction_0());
                // Frame 275.
                result.InsertKeyFrame(0.458333343F, new CompositionPath(Geometry_23()), CubicBezierEasingFunction_0());
                // Frame 277.
                result.InsertKeyFrame(0.461666673F, new CompositionPath(Geometry_24()), CubicBezierEasingFunction_0());
                // Frame 280.
                result.InsertKeyFrame(0.466666669F, new CompositionPath(Geometry_25()), CubicBezierEasingFunction_0());
                return result;
            }

            // Layer opacity animation
            ScalarKeyFrameAnimation OpacityScalarAnimation_0_to_1()
            {
                // Frame 0.
                if (_opacityScalarAnimation_0_to_1 != null) { return _opacityScalarAnimation_0_to_1; }
                var result = _opacityScalarAnimation_0_to_1 = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 54.
                result.InsertKeyFrame(0.0900000036F, 0F, HoldThenStepEasingFunction());
                // Frame 74.
                result.InsertKeyFrame(0.123333335F, 1F, CubicBezierEasingFunction_0());
                return result;
            }

            // - - - - - PreComp layer: CrescentColor
            // Layer opacity animation
            ScalarKeyFrameAnimation OpacityScalarAnimation_1_to_0_0()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 1F, StepThenHoldEasingFunction());
                // Frame 579.
                result.InsertKeyFrame(0.964999974F, 1F, HoldThenStepEasingFunction());
                // Frame 599.
                result.InsertKeyFrame(0.998333335F, 0F, CubicBezierEasingFunction_0());
                return result;
            }

            // - - - - - PreComp layer: collapseText
            // Layer opacity animation
            ScalarKeyFrameAnimation OpacityScalarAnimation_1_to_0_1()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 1F, StepThenHoldEasingFunction());
                // Frame 550.
                result.InsertKeyFrame(0.916666687F, 1F, HoldThenStepEasingFunction());
                // Frame 570.
                result.InsertKeyFrame(0.949999988F, 0F, CubicBezierEasingFunction_0());
                return result;
            }

            // Position.X
            ScalarKeyFrameAnimation PositionXScalarAnimation_960_to_227()
            {
                // Frame 0.
                if (_positionXScalarAnimation_960_to_227 != null) { return _positionXScalarAnimation_960_to_227; }
                var result = _positionXScalarAnimation_960_to_227 = CreateScalarKeyFrameAnimation(0F, 960F, StepThenHoldEasingFunction());
                // Frame 320.
                result.InsertKeyFrame(0.533333361F, 960F, HoldThenStepEasingFunction());
                // Frame 391.
                result.InsertKeyFrame(0.651666641F, 227F, _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0.00400000019F, 1F)));
                return result;
            }

            // Position.X
            ScalarKeyFrameAnimation PositionXScalarAnimation_1181_to_1016()
            {
                // Frame 0.
                if (_positionXScalarAnimation_1181_to_1016 != null) { return _positionXScalarAnimation_1181_to_1016; }
                var result = _positionXScalarAnimation_1181_to_1016 = CreateScalarKeyFrameAnimation(0F, 1181F, StepThenHoldEasingFunction());
                // Frame 375.
                result.InsertKeyFrame(0.625F, 1181F, HoldThenStepEasingFunction());
                // Frame 411.
                result.InsertKeyFrame(0.685000002F, 1016F, CubicBezierEasingFunction_1());
                return result;
            }

            // Position.Y
            ScalarKeyFrameAnimation PositionYScalarAnimation_540_to_540()
            {
                // Frame 0.
                if (_positionYScalarAnimation_540_to_540 != null) { return _positionYScalarAnimation_540_to_540; }
                var result = _positionYScalarAnimation_540_to_540 = CreateScalarKeyFrameAnimation(0F, 540F, StepThenHoldEasingFunction());
                // Frame 305.
                result.InsertKeyFrame(0.508333325F, 540F, HoldThenStepEasingFunction());
                // Frame 345.
                result.InsertKeyFrame(0.574999988F, 540F, CubicBezierEasingFunction_5());
                return result;
            }

            // Position.Y
            ScalarKeyFrameAnimation PositionYScalarAnimation_651_to_594()
            {
                // Frame 0.
                if (_positionYScalarAnimation_651_to_594 != null) { return _positionYScalarAnimation_651_to_594; }
                var result = _positionYScalarAnimation_651_to_594 = CreateScalarKeyFrameAnimation(0F, 651F, StepThenHoldEasingFunction());
                // Frame 339.
                result.InsertKeyFrame(0.564999998F, 651F, HoldThenStepEasingFunction());
                // Frame 375.
                result.InsertKeyFrame(0.625F, 594F, CubicBezierEasingFunction_1());
                // Frame 420.
                result.InsertKeyFrame(0.699999988F, 594F, _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0F), new Vector2(0.144999996F, 1F)));
                // Frame 450.
                result.InsertKeyFrame(0.75F, 574F, CubicBezierEasingFunction_1());
                // Frame 453.
                result.InsertKeyFrame(0.754999995F, 574F, CubicBezierEasingFunction_1());
                // Frame 468.
                result.InsertKeyFrame(0.779999971F, 594F, CubicBezierEasingFunction_1());
                return result;
            }

            // - PreComp layer: NewLogoDraftTitle
            // Position.Y
            ScalarKeyFrameAnimation PositionYScalarAnimation_964_to_540()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 964F, StepThenHoldEasingFunction());
                // Frame 339.
                result.InsertKeyFrame(0.564999998F, 964F, HoldThenStepEasingFunction());
                // Frame 379.
                result.InsertKeyFrame(0.63166666F, 540F, CubicBezierEasingFunction_1());
                return result;
            }

            // Rotation
            ScalarKeyFrameAnimation RotationAngleInDegreesScalarAnimation_0_to_0()
            {
                // Frame 0.
                if (_rotationAngleInDegreesScalarAnimation_0_to_0 != null) { return _rotationAngleInDegreesScalarAnimation_0_to_0; }
                var result = _rotationAngleInDegreesScalarAnimation_0_to_0 = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 240.
                result.InsertKeyFrame(0.400000006F, 0F, HoldThenStepEasingFunction());
                // Frame 270.
                result.InsertKeyFrame(0.449999988F, -14F, CubicBezierEasingFunction_1());
                // Frame 281.
                result.InsertKeyFrame(0.468333334F, 25F, CubicBezierEasingFunction_2());
                // Frame 286.
                result.InsertKeyFrame(0.476666659F, 25F, CubicBezierEasingFunction_3());
                // Frame 310.
                result.InsertKeyFrame(0.516666651F, 0F, CubicBezierEasingFunction_4());
                // Frame 320.
                result.InsertKeyFrame(0.533333361F, 0F, CubicBezierEasingFunction_1());
                return result;
            }

            // Rotation
            ScalarKeyFrameAnimation RotationAngleInDegreesScalarAnimation_0_to_m360()
            {
                // Frame 0.
                if (_rotationAngleInDegreesScalarAnimation_0_to_m360 != null) { return _rotationAngleInDegreesScalarAnimation_0_to_m360; }
                var result = _rotationAngleInDegreesScalarAnimation_0_to_m360 = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 240.
                result.InsertKeyFrame(0.400000006F, 0F, HoldThenStepEasingFunction());
                // Frame 270.
                result.InsertKeyFrame(0.449999988F, -14F, CubicBezierEasingFunction_1());
                // Frame 281.
                result.InsertKeyFrame(0.468333334F, 25F, CubicBezierEasingFunction_2());
                // Frame 286.
                result.InsertKeyFrame(0.476666659F, 25F, CubicBezierEasingFunction_3());
                // Frame 310.
                result.InsertKeyFrame(0.516666651F, 0F, CubicBezierEasingFunction_4());
                // Frame 320.
                result.InsertKeyFrame(0.533333361F, 0F, CubicBezierEasingFunction_1());
                // Frame 391.
                result.InsertKeyFrame(0.651666641F, -360F, CubicBezierEasingFunction_1());
                // Frame 420.
                result.InsertKeyFrame(0.699999988F, -360F, _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0F), new Vector2(0.833000004F, 1F)));
                // Frame 450.
                result.InsertKeyFrame(0.75F, -366F, CubicBezierEasingFunction_1());
                // Frame 466.
                result.InsertKeyFrame(0.776666641F, -346F, CubicBezierEasingFunction_2());
                // Frame 490.
                result.InsertKeyFrame(0.816666663F, -360F, CubicBezierEasingFunction_4());
                return result;
            }

            // Rotation
            ScalarKeyFrameAnimation RotationAngleInDegreesScalarAnimation_m4_to_0()
            {
                // Frame 0.
                if (_rotationAngleInDegreesScalarAnimation_m4_to_0 != null) { return _rotationAngleInDegreesScalarAnimation_m4_to_0; }
                var result = _rotationAngleInDegreesScalarAnimation_m4_to_0 = CreateScalarKeyFrameAnimation(0F, -4F, StepThenHoldEasingFunction());
                // Frame 420.
                result.InsertKeyFrame(0.699999988F, -4F, HoldThenStepEasingFunction());
                // Frame 450.
                result.InsertKeyFrame(0.75F, 0F, CubicBezierEasingFunction_1());
                // Frame 453.
                result.InsertKeyFrame(0.754999995F, 0F, CubicBezierEasingFunction_5());
                return result;
            }

            // Scale
            ScalarKeyFrameAnimation ScalarAnimation_1_to_0p29()
            {
                // Frame 0.
                if (_scalarAnimation_1_to_0p29 != null) { return _scalarAnimation_1_to_0p29; }
                var result = _scalarAnimation_1_to_0p29 = CreateScalarKeyFrameAnimation(0F, 1F, StepThenHoldEasingFunction());
                // Frame 320.
                result.InsertKeyFrame(0.533333361F, 1F, HoldThenStepEasingFunction());
                // Frame 391.
                result.InsertKeyFrame(0.651666641F, 0.289999992F, _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0.00499999989F, 1F)));
                return result;
            }

            // TrimStart
            ScalarKeyFrameAnimation TrimStartScalarAnimation_1_to_0_0()
            {
                // Frame 0.
                if (_trimStartScalarAnimation_1_to_0_0 != null) { return _trimStartScalarAnimation_1_to_0_0; }
                var result = _trimStartScalarAnimation_1_to_0_0 = CreateScalarKeyFrameAnimation(0F, 1F, HoldThenStepEasingFunction());
                // Frame 90.
                result.InsertKeyFrame(0.150000006F, 0F, _c.CreateCubicBezierEasingFunction(new Vector2(0.064000003F, 0F), new Vector2(0F, 1F)));
                return result;
            }

            // TrimStart
            ScalarKeyFrameAnimation TrimStartScalarAnimation_1_to_0_1()
            {
                // Frame 0.
                if (_trimStartScalarAnimation_1_to_0_1 != null) { return _trimStartScalarAnimation_1_to_0_1; }
                var result = _trimStartScalarAnimation_1_to_0_1 = CreateScalarKeyFrameAnimation(0F, 1F, StepThenHoldEasingFunction());
                // Frame 60.
                result.InsertKeyFrame(0.100000001F, 1F, HoldThenStepEasingFunction());
                // Frame 195.
                result.InsertKeyFrame(0.324999988F, 0F, CubicBezierEasingFunction_1());
                return result;
            }

            // TrimStart
            ScalarKeyFrameAnimation TrimStartScalarAnimation_1_to_0_2()
            {
                // Frame 0.
                if (_trimStartScalarAnimation_1_to_0_2 != null) { return _trimStartScalarAnimation_1_to_0_2; }
                var result = _trimStartScalarAnimation_1_to_0_2 = CreateScalarKeyFrameAnimation(0F, 1F, StepThenHoldEasingFunction());
                // Frame 87.
                result.InsertKeyFrame(0.144999996F, 1F, HoldThenStepEasingFunction());
                // Frame 203.
                result.InsertKeyFrame(0.338333338F, 0F, CubicBezierEasingFunction_1());
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            // Shape tree root for layer: Lime
            ShapeVisual ShapeVisual_00()
            {
                if (_shapeVisual_00 != null) { return _shapeVisual_00; }
                var result = _shapeVisual_00 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_00());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Orange
            ShapeVisual ShapeVisual_01()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_01());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_02()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_02());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Blue
            ShapeVisual ShapeVisual_03()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_03());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_04()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_04());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent1
            ShapeVisual ShapeVisual_05()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<1149, 314>
                result.Shapes.Add(SpriteShape_05());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_06()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<1149, 314>
                result.Shapes.Add(SpriteShape_06());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent2
            ShapeVisual ShapeVisual_07()
            {
                if (_shapeVisual_07 != null) { return _shapeVisual_07; }
                var result = _shapeVisual_07 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<973, 516>
                result.Shapes.Add(SpriteShape_07());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_08()
            {
                if (_shapeVisual_08 != null) { return _shapeVisual_08; }
                var result = _shapeVisual_08 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Layer: Crescent2
                result.Shapes.Add(SpriteShape_08());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent3
            ShapeVisual ShapeVisual_09()
            {
                if (_shapeVisual_09 != null) { return _shapeVisual_09; }
                var result = _shapeVisual_09 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<863, 539>
                result.Shapes.Add(SpriteShape_09());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_10()
            {
                if (_shapeVisual_10 != null) { return _shapeVisual_10; }
                var result = _shapeVisual_10 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Layer: Crescent3
                result.Shapes.Add(SpriteShape_10());
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            // Shape tree root for layer: Lime
            ShapeVisual ShapeVisual_11()
            {
                if (_shapeVisual_11 != null) { return _shapeVisual_11; }
                var result = _shapeVisual_11 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_11());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Orange
            ShapeVisual ShapeVisual_12()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_12());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_13()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_13());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Blue
            ShapeVisual ShapeVisual_14()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_14());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_15()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_15());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent1
            ShapeVisual ShapeVisual_16()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<1149, 314>
                result.Shapes.Add(SpriteShape_16());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_17()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<1149, 314>
                result.Shapes.Add(SpriteShape_17());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent2
            ShapeVisual ShapeVisual_18()
            {
                if (_shapeVisual_18 != null) { return _shapeVisual_18; }
                var result = _shapeVisual_18 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<973, 516>
                result.Shapes.Add(SpriteShape_18());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_19()
            {
                if (_shapeVisual_19 != null) { return _shapeVisual_19; }
                var result = _shapeVisual_19 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Layer: Crescent2
                result.Shapes.Add(SpriteShape_19());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent3
            ShapeVisual ShapeVisual_20()
            {
                if (_shapeVisual_20 != null) { return _shapeVisual_20; }
                var result = _shapeVisual_20 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<863, 539>
                result.Shapes.Add(SpriteShape_20());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_21()
            {
                if (_shapeVisual_21 != null) { return _shapeVisual_21; }
                var result = _shapeVisual_21 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Layer: Crescent3
                result.Shapes.Add(SpriteShape_21());
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // Transforms for Crescent
            // Shape tree root for layer: Lime
            ShapeVisual ShapeVisual_22()
            {
                if (_shapeVisual_22 != null) { return _shapeVisual_22; }
                var result = _shapeVisual_22 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_22());
                return result;
            }

            // - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Orange
            ShapeVisual ShapeVisual_23()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_23());
                return result;
            }

            // - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_24()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_24());
                return result;
            }

            // - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Blue
            ShapeVisual ShapeVisual_25()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_25());
                return result;
            }

            // - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_26()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<960, 540>
                result.Shapes.Add(SpriteShape_26());
                return result;
            }

            // - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent1
            ShapeVisual ShapeVisual_27()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<1149, 314>
                result.Shapes.Add(SpriteShape_27());
                return result;
            }

            // - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_28()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<1149, 314>
                result.Shapes.Add(SpriteShape_28());
                return result;
            }

            // - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent2
            ShapeVisual ShapeVisual_29()
            {
                if (_shapeVisual_29 != null) { return _shapeVisual_29; }
                var result = _shapeVisual_29 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<973, 516>
                result.Shapes.Add(SpriteShape_29());
                return result;
            }

            // - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_30()
            {
                if (_shapeVisual_30 != null) { return _shapeVisual_30; }
                var result = _shapeVisual_30 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Layer: Crescent2
                result.Shapes.Add(SpriteShape_30());
                return result;
            }

            // - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Shape tree root for layer: Crescent3
            ShapeVisual ShapeVisual_31()
            {
                if (_shapeVisual_31 != null) { return _shapeVisual_31; }
                var result = _shapeVisual_31 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Offset:<863, 539>
                result.Shapes.Add(SpriteShape_31());
                return result;
            }

            // - - - - - - - - - - - PreComp layer: Crescent
            // - - - - - Transforms for Crescent
            // Masks
            ShapeVisual ShapeVisual_32()
            {
                if (_shapeVisual_32 != null) { return _shapeVisual_32; }
                var result = _shapeVisual_32 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Layer: Crescent3
                result.Shapes.Add(SpriteShape_32());
                return result;
            }

            // - - - - PreComp layer: Crescent
            // Masks
            ShapeVisual ShapeVisual_33()
            {
                if (_shapeVisual_33 != null) { return _shapeVisual_33; }
                var result = _shapeVisual_33 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Layer: Crescent
                result.Shapes.Add(ContainerShape_0());
                return result;
            }

            // - - - - PreComp layer: CrescentColor
            // Masks
            ShapeVisual ShapeVisual_34()
            {
                if (_shapeVisual_34 != null) { return _shapeVisual_34; }
                var result = _shapeVisual_34 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Layer: CrescentColor
                result.Shapes.Add(ContainerShape_1());
                return result;
            }

            // - PreComp layer: NewLogoDraftTitle
            // Shape tree root for layer: C
            ShapeVisual ShapeVisual_35()
            {
                if (_shapeVisual_35 != null) { return _shapeVisual_35; }
                var result = _shapeVisual_35 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(2856F, 714F);
                // Offset:<1428, 357>
                result.Shapes.Add(SpriteShape_37());
                return result;
            }

            // - - - - - - PreComp layer: NewLogoDraftTitle
            // Shape tree root for layer: O
            ShapeVisual ShapeVisual_36()
            {
                if (_shapeVisual_36 != null) { return _shapeVisual_36; }
                var result = _shapeVisual_36 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(2856F, 714F);
                // Offset:<1428, 357>
                result.Shapes.Add(SpriteShape_38());
                return result;
            }

            // - - - - - - PreComp layer: NewLogoDraftTitle
            // Masks
            ShapeVisual ShapeVisual_37()
            {
                if (_shapeVisual_37 != null) { return _shapeVisual_37; }
                var result = _shapeVisual_37 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(2856F, 714F);
                // Layer: O
                result.Shapes.Add(SpriteShape_39());
                return result;
            }

            // - PreComp layer: NewLogoDraftTitle
            // Layer aggregator
            ShapeVisual ShapeVisual_38()
            {
                if (_shapeVisual_38 != null) { return _shapeVisual_38; }
                var result = _shapeVisual_38 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(2856F, 714F);
                var shapes = result.Shapes;
                // Layer: L
                shapes.Add(SpriteShape_40());
                // Layer: L
                shapes.Add(SpriteShape_41());
                return result;
            }

            // - - - - - - PreComp layer: NewLogoDraftTitle
            // Shape tree root for layer: A
            ShapeVisual ShapeVisual_39()
            {
                if (_shapeVisual_39 != null) { return _shapeVisual_39; }
                var result = _shapeVisual_39 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(2856F, 714F);
                // Offset:<1428, 357>
                result.Shapes.Add(SpriteShape_42());
                return result;
            }

            // - - - - - - PreComp layer: NewLogoDraftTitle
            // Masks
            ShapeVisual ShapeVisual_40()
            {
                if (_shapeVisual_40 != null) { return _shapeVisual_40; }
                var result = _shapeVisual_40 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(2856F, 714F);
                // Layer: A
                result.Shapes.Add(SpriteShape_43());
                return result;
            }

            // - - - - - - PreComp layer: NewLogoDraftTitle
            // Shape tree root for layer: P
            ShapeVisual ShapeVisual_41()
            {
                if (_shapeVisual_41 != null) { return _shapeVisual_41; }
                var result = _shapeVisual_41 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(2856F, 714F);
                // Offset:<1428, 357>
                result.Shapes.Add(SpriteShape_44());
                return result;
            }

            // - - - - - - PreComp layer: NewLogoDraftTitle
            // Masks
            ShapeVisual ShapeVisual_42()
            {
                if (_shapeVisual_42 != null) { return _shapeVisual_42; }
                var result = _shapeVisual_42 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(2856F, 714F);
                // Layer: P
                result.Shapes.Add(SpriteShape_45());
                return result;
            }

            // - PreComp layer: NewLogoDraftTitle
            // Layer aggregator
            ShapeVisual ShapeVisual_43()
            {
                if (_shapeVisual_43 != null) { return _shapeVisual_43; }
                var result = _shapeVisual_43 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(2856F, 714F);
                var shapes = result.Shapes;
                // Layer: S
                shapes.Add(SpriteShape_46());
                // Layer: S
                shapes.Add(SpriteShape_47());
                return result;
            }

            // - - - - PreComp layer: collapseText
            // Masks
            ShapeVisual ShapeVisual_44()
            {
                if (_shapeVisual_44 != null) { return _shapeVisual_44; }
                var result = _shapeVisual_44 = _c.CreateShapeVisual();
                result.IsVisible = false;
                result.Size = new Vector2(1920F, 1080F);
                // Layer: collapseText
                result.Shapes.Add(ContainerShape_2());
                return result;
            }

            // Layer aggregator
            ShapeVisual ShapeVisual_45()
            {
                if (_shapeVisual_45 != null) { return _shapeVisual_45; }
                var result = _shapeVisual_45 = _c.CreateShapeVisual();
                result.Size = new Vector2(1920F, 1080F);
                var shapes = result.Shapes;
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_49());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_50());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_51());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_52());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_53());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_54());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_55());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_56());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_57());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_58());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_59());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_60());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_61());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_62());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_63());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_64());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_65());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_66());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_67());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_68());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_69());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_70());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_71());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_72());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_73());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_74());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_75());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_76());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_77());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_78());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_79());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_80());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_81());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_82());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_83());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_84());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_85());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_86());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_87());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_88());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_89());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_90());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_91());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_92());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_93());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_94());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_95());
                // Layer: Under MIT License - All right reserved Outlines
                shapes.Add(SpriteShape_96());
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_00()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_00();
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_01()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_01();
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_02()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_02();
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_03()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_03();
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_04()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_04();
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_05()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_05();
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_06()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_06();
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_07()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_07();
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_08()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_08();
                return result;
            }

            // - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_09()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_09();
                return result;
            }

            // PreComp layer: Crescent
            SpriteVisual SpriteVisual_10()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_10();
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_11()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_11();
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_12()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_12();
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_13()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_13();
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_14()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_14();
                return result;
            }

            // - - - - - - PreComp layer: Crescent
            // Transforms for Crescent
            SpriteVisual SpriteVisual_15()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_15();
                return result;
            }

            // PreComp layer: CrescentColor
            SpriteVisual SpriteVisual_16()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_16();
                return result;
            }

            // - Opacity for layer: CrescentColor
            // Transforms for NewLogoDraft-1024p256colors.png Scale(1,1,0), Offset(448,28,0)
            SpriteVisual SpriteVisual_17()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1024F, 1024F);
                // Offset:<448, 28>, Scale:<1, 1>
                result.TransformMatrix = new Matrix4x4(1F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 448F, 28F, 0F, 1F);
                result.Brush = SurfaceBrush_33();
                return result;
            }

            // PreComp layer: collapseText
            SpriteVisual SpriteVisual_18()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(1920F, 1080F);
                result.Brush = EffectBrush_17();
                return result;
            }

            // - PreComp layer: NewLogoDraftTitle
            SpriteVisual SpriteVisual_19()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(2856F, 714F);
                result.Brush = EffectBrush_18();
                return result;
            }

            // - PreComp layer: NewLogoDraftTitle
            SpriteVisual SpriteVisual_20()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(2856F, 714F);
                result.Brush = EffectBrush_19();
                return result;
            }

            // - PreComp layer: NewLogoDraftTitle
            SpriteVisual SpriteVisual_21()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(2856F, 714F);
                result.Brush = EffectBrush_20();
                return result;
            }

            StepEasingFunction HoldThenStepEasingFunction()
            {
                if (_holdThenStepEasingFunction != null) { return _holdThenStepEasingFunction; }
                var result = _holdThenStepEasingFunction = _c.CreateStepEasingFunction();
                result.IsFinalStepSingleFrame = true;
                return result;
            }

            StepEasingFunction StepThenHoldEasingFunction()
            {
                if (_stepThenHoldEasingFunction != null) { return _stepThenHoldEasingFunction; }
                var result = _stepThenHoldEasingFunction = _c.CreateStepEasingFunction();
                result.IsInitialStepSingleFrame = true;
                return result;
            }

            // PreComp layer: Crescent
            // Offset
            Vector3KeyFrameAnimation OffsetVector3Animation_0()
            {
                // Frame 0.
                var result = CreateVector3KeyFrameAnimation(0F, new Vector3(-441F, 494F, 0F), HoldThenStepEasingFunction());
                // Frame 60.
                result.InsertKeyFrame(0.100000001F, new Vector3(-401F, 494F, 0F), CubicBezierEasingFunction_0());
                return result;
            }

            // PreComp layer: Crescent
            // Offset
            Vector3KeyFrameAnimation OffsetVector3Animation_1()
            {
                // Frame 0.
                var result = CreateVector3KeyFrameAnimation(0F, new Vector3(158F, -227F, 0F), StepThenHoldEasingFunction());
                // Frame 60.
                result.InsertKeyFrame(0.100000001F, new Vector3(158F, -227F, 0F), HoldThenStepEasingFunction());
                // Frame 165.
                result.InsertKeyFrame(0.275000006F, new Vector3(35.1430016F, -263F, 0F), CubicBezierEasingFunction_0());
                return result;
            }

            internal NewLogoTitleIntro_AnimatedVisual(
                Compositor compositor,
                LoadedImageSurface image_image_0
                )
            {
                _c = compositor;
                _image_image_0 = image_image_0;
                _reusableExpressionAnimation = compositor.CreateExpressionAnimation();
                Root();
            }

            public Visual RootVisual => _root;
            public TimeSpan Duration => TimeSpan.FromTicks(c_durationTicks);
            public Vector2 Size => new Vector2(1920F, 1080F);
            void IDisposable.Dispose() => _root?.Dispose();

            public void CreateAnimations()
            {
                _animatedColorBrush_TransparentWhite_to_TransparentWhite.StartAnimation("Color", ColorAnimation_TransparentWhite_to_TransparentWhite(), AnimationController_0());
                _containerShape_0.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_0_to_0(), AnimationController_0());
                _containerShape_1.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_0_to_m360(), AnimationController_0());
                _containerShape_1.StartAnimation("Scale.X", ScalarAnimation_1_to_0p29(), AnimationController_0());
                _containerShape_1.StartAnimation("Scale.Y", ScalarAnimation_1_to_0p29(), AnimationController_0());
                _containerShape_1.Properties.StartAnimation("Position.X", PositionXScalarAnimation_960_to_227(), AnimationController_0());
                _containerShape_1.Properties.StartAnimation("Position.Y", PositionYScalarAnimation_540_to_540(), AnimationController_0());
                _containerShape_2.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_m4_to_0(), AnimationController_0());
                _containerShape_2.Properties.StartAnimation("Position.X", PositionXScalarAnimation_1181_to_1016(), AnimationController_0());
                _containerShape_2.Properties.StartAnimation("Position.Y", PositionYScalarAnimation_651_to_594(), AnimationController_0());
                _pathGeometry_02.StartAnimation("Path", PathKeyFrameAnimation_0(), AnimationController_0());
                _pathGeometry_04.StartAnimation("Path", PathKeyFrameAnimation_1(), AnimationController_0());
                _pathGeometry_05.StartAnimation("TrimStart", TrimStartScalarAnimation_1_to_0_0(), AnimationController_0());
                _pathGeometry_07.StartAnimation("TrimStart", TrimStartScalarAnimation_1_to_0_1(), AnimationController_0());
                _pathGeometry_09.StartAnimation("TrimStart", TrimStartScalarAnimation_1_to_0_2(), AnimationController_0());
                _pathGeometry_11.StartAnimation("TrimStart", TrimStartScalarAnimation_1_to_0_0(), AnimationController_0());
                _pathGeometry_12.StartAnimation("TrimStart", TrimStartScalarAnimation_1_to_0_1(), AnimationController_0());
                _pathGeometry_13.StartAnimation("TrimStart", TrimStartScalarAnimation_1_to_0_2(), AnimationController_0());
                _pathGeometry_14.StartAnimation("TrimStart", TrimStartScalarAnimation_1_to_0_0(), AnimationController_0());
                _pathGeometry_15.StartAnimation("TrimStart", TrimStartScalarAnimation_1_to_0_1(), AnimationController_0());
                _pathGeometry_16.StartAnimation("TrimStart", TrimStartScalarAnimation_1_to_0_2(), AnimationController_0());
                _pathGeometry_17.StartAnimation("Path", PathKeyFrameAnimation_2(), AnimationController_0());
                _pathGeometry_18.StartAnimation("Path", PathKeyFrameAnimation_3(), AnimationController_0());
                _pathGeometry_19.StartAnimation("Path", PathKeyFrameAnimation_4(), AnimationController_0());
                _pathGeometry_20.StartAnimation("Path", PathKeyFrameAnimation_5(), AnimationController_0());
                _containerVisual_00.StartAnimation("IsVisible", IsVisibleBooleanAnimation_3(), AnimationController_0());
                _containerVisual_00.StartAnimation("Offset", OffsetVector3Animation_0(), AnimationController_0());
                _containerVisual_12.StartAnimation("IsVisible", IsVisibleBooleanAnimation_4(), AnimationController_0());
                _containerVisual_12.StartAnimation("Offset", OffsetVector3Animation_1(), AnimationController_0());
                _containerVisual_25.StartAnimation("IsVisible", IsVisibleBooleanAnimation_5(), AnimationController_0());
                _containerVisual_25.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_0_to_0(), AnimationController_0());
                _containerVisual_39.StartAnimation("Opacity", OpacityScalarAnimation_1_to_0_0(), AnimationController_0());
                _containerVisual_39.StartAnimation("IsVisible", IsVisibleBooleanAnimation_6(), AnimationController_0());
                _containerVisual_40.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_0_to_m360(), AnimationController_0());
                _containerVisual_40.StartAnimation("Scale.X", ScalarAnimation_1_to_0p29(), AnimationController_0());
                _containerVisual_40.StartAnimation("Scale.Y", ScalarAnimation_1_to_0p29(), AnimationController_0());
                _containerVisual_40.Properties.StartAnimation("Position.X", PositionXScalarAnimation_960_to_227(), AnimationController_0());
                _containerVisual_40.Properties.StartAnimation("Position.Y", PositionYScalarAnimation_540_to_540(), AnimationController_0());
                _containerVisual_44.StartAnimation("Opacity", OpacityScalarAnimation_1_to_0_1(), AnimationController_0());
                _containerVisual_44.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _containerVisual_45.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_m4_to_0(), AnimationController_0());
                _containerVisual_45.Properties.StartAnimation("Position.X", PositionXScalarAnimation_1181_to_1016(), AnimationController_0());
                _containerVisual_45.Properties.StartAnimation("Position.Y", PositionYScalarAnimation_651_to_594(), AnimationController_0());
                _containerVisual_47.Properties.StartAnimation("Position.Y", PositionYScalarAnimation_964_to_540(), AnimationController_0());
                _shapeVisual_00.StartAnimation("Opacity", OpacityScalarAnimation_0_to_1(), AnimationController_0());
                _shapeVisual_00.StartAnimation("IsVisible", IsVisibleBooleanAnimation_0(), AnimationController_0());
                _shapeVisual_07.StartAnimation("IsVisible", IsVisibleBooleanAnimation_1(), AnimationController_0());
                _shapeVisual_08.StartAnimation("IsVisible", IsVisibleBooleanAnimation_1(), AnimationController_0());
                _shapeVisual_09.StartAnimation("IsVisible", IsVisibleBooleanAnimation_2(), AnimationController_0());
                _shapeVisual_10.StartAnimation("IsVisible", IsVisibleBooleanAnimation_2(), AnimationController_0());
                _shapeVisual_11.StartAnimation("Opacity", OpacityScalarAnimation_0_to_1(), AnimationController_0());
                _shapeVisual_11.StartAnimation("IsVisible", IsVisibleBooleanAnimation_0(), AnimationController_0());
                _shapeVisual_18.StartAnimation("IsVisible", IsVisibleBooleanAnimation_1(), AnimationController_0());
                _shapeVisual_19.StartAnimation("IsVisible", IsVisibleBooleanAnimation_1(), AnimationController_0());
                _shapeVisual_20.StartAnimation("IsVisible", IsVisibleBooleanAnimation_2(), AnimationController_0());
                _shapeVisual_21.StartAnimation("IsVisible", IsVisibleBooleanAnimation_2(), AnimationController_0());
                _shapeVisual_22.StartAnimation("Opacity", OpacityScalarAnimation_0_to_1(), AnimationController_0());
                _shapeVisual_22.StartAnimation("IsVisible", IsVisibleBooleanAnimation_0(), AnimationController_0());
                _shapeVisual_29.StartAnimation("IsVisible", IsVisibleBooleanAnimation_1(), AnimationController_0());
                _shapeVisual_30.StartAnimation("IsVisible", IsVisibleBooleanAnimation_1(), AnimationController_0());
                _shapeVisual_31.StartAnimation("IsVisible", IsVisibleBooleanAnimation_2(), AnimationController_0());
                _shapeVisual_32.StartAnimation("IsVisible", IsVisibleBooleanAnimation_2(), AnimationController_0());
                _shapeVisual_33.StartAnimation("IsVisible", IsVisibleBooleanAnimation_5(), AnimationController_0());
                _shapeVisual_34.StartAnimation("IsVisible", IsVisibleBooleanAnimation_6(), AnimationController_0());
                _shapeVisual_35.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _shapeVisual_36.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _shapeVisual_37.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _shapeVisual_38.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _shapeVisual_39.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _shapeVisual_40.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _shapeVisual_41.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _shapeVisual_42.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _shapeVisual_43.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _shapeVisual_44.StartAnimation("IsVisible", IsVisibleBooleanAnimation_7(), AnimationController_0());
                _shapeVisual_45.StartAnimation("IsVisible", IsVisibleBooleanAnimation_8(), AnimationController_0());
            }

            public void DestroyAnimations()
            {
                _animatedColorBrush_TransparentWhite_to_TransparentWhite.StopAnimation("Color");
                _containerShape_0.StopAnimation("RotationAngleInDegrees");
                _containerShape_1.StopAnimation("RotationAngleInDegrees");
                _containerShape_1.StopAnimation("Scale.X");
                _containerShape_1.StopAnimation("Scale.Y");
                _containerShape_1.Properties.StopAnimation("Position.X");
                _containerShape_1.Properties.StopAnimation("Position.Y");
                _containerShape_2.StopAnimation("RotationAngleInDegrees");
                _containerShape_2.Properties.StopAnimation("Position.X");
                _containerShape_2.Properties.StopAnimation("Position.Y");
                _pathGeometry_02.StopAnimation("Path");
                _pathGeometry_04.StopAnimation("Path");
                _pathGeometry_05.StopAnimation("TrimStart");
                _pathGeometry_07.StopAnimation("TrimStart");
                _pathGeometry_09.StopAnimation("TrimStart");
                _pathGeometry_11.StopAnimation("TrimStart");
                _pathGeometry_12.StopAnimation("TrimStart");
                _pathGeometry_13.StopAnimation("TrimStart");
                _pathGeometry_14.StopAnimation("TrimStart");
                _pathGeometry_15.StopAnimation("TrimStart");
                _pathGeometry_16.StopAnimation("TrimStart");
                _pathGeometry_17.StopAnimation("Path");
                _pathGeometry_18.StopAnimation("Path");
                _pathGeometry_19.StopAnimation("Path");
                _pathGeometry_20.StopAnimation("Path");
                _containerVisual_00.StopAnimation("IsVisible");
                _containerVisual_00.StopAnimation("Offset");
                _containerVisual_12.StopAnimation("IsVisible");
                _containerVisual_12.StopAnimation("Offset");
                _containerVisual_25.StopAnimation("IsVisible");
                _containerVisual_25.StopAnimation("RotationAngleInDegrees");
                _containerVisual_39.StopAnimation("Opacity");
                _containerVisual_39.StopAnimation("IsVisible");
                _containerVisual_40.StopAnimation("RotationAngleInDegrees");
                _containerVisual_40.StopAnimation("Scale.X");
                _containerVisual_40.StopAnimation("Scale.Y");
                _containerVisual_40.Properties.StopAnimation("Position.X");
                _containerVisual_40.Properties.StopAnimation("Position.Y");
                _containerVisual_44.StopAnimation("Opacity");
                _containerVisual_44.StopAnimation("IsVisible");
                _containerVisual_45.StopAnimation("RotationAngleInDegrees");
                _containerVisual_45.Properties.StopAnimation("Position.X");
                _containerVisual_45.Properties.StopAnimation("Position.Y");
                _containerVisual_47.Properties.StopAnimation("Position.Y");
                _shapeVisual_00.StopAnimation("Opacity");
                _shapeVisual_00.StopAnimation("IsVisible");
                _shapeVisual_07.StopAnimation("IsVisible");
                _shapeVisual_08.StopAnimation("IsVisible");
                _shapeVisual_09.StopAnimation("IsVisible");
                _shapeVisual_10.StopAnimation("IsVisible");
                _shapeVisual_11.StopAnimation("Opacity");
                _shapeVisual_11.StopAnimation("IsVisible");
                _shapeVisual_18.StopAnimation("IsVisible");
                _shapeVisual_19.StopAnimation("IsVisible");
                _shapeVisual_20.StopAnimation("IsVisible");
                _shapeVisual_21.StopAnimation("IsVisible");
                _shapeVisual_22.StopAnimation("Opacity");
                _shapeVisual_22.StopAnimation("IsVisible");
                _shapeVisual_29.StopAnimation("IsVisible");
                _shapeVisual_30.StopAnimation("IsVisible");
                _shapeVisual_31.StopAnimation("IsVisible");
                _shapeVisual_32.StopAnimation("IsVisible");
                _shapeVisual_33.StopAnimation("IsVisible");
                _shapeVisual_34.StopAnimation("IsVisible");
                _shapeVisual_35.StopAnimation("IsVisible");
                _shapeVisual_36.StopAnimation("IsVisible");
                _shapeVisual_37.StopAnimation("IsVisible");
                _shapeVisual_38.StopAnimation("IsVisible");
                _shapeVisual_39.StopAnimation("IsVisible");
                _shapeVisual_40.StopAnimation("IsVisible");
                _shapeVisual_41.StopAnimation("IsVisible");
                _shapeVisual_42.StopAnimation("IsVisible");
                _shapeVisual_43.StopAnimation("IsVisible");
                _shapeVisual_44.StopAnimation("IsVisible");
                _shapeVisual_45.StopAnimation("IsVisible");
            }

        }
    }
}
