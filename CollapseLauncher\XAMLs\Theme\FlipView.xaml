﻿<!--  Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. See LICENSE in the project root for license information.  -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!--  ReSharper disable Xaml.InvalidResourceType  -->
    <!--  ReSharper disable Xaml.StaticResourceNotResolved  -->
    <ResourceDictionary.ThemeDictionaries>
        <ResourceDictionary x:Key="Default">
            <Thickness x:Key="FlipViewButtonBorderThemeThickness">0</Thickness>
            <StaticResource x:Key="FlipViewBackground"
                            ResourceKey="SolidBackgroundFillColorBaseBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBackground"
                            ResourceKey="AcrylicInAppFillColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBackgroundPointerOver"
                            ResourceKey="AcrylicInAppFillColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBackgroundPressed"
                            ResourceKey="AcrylicInAppFillColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousArrowForeground"
                            ResourceKey="ControlStrongFillColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousArrowForegroundPointerOver"
                            ResourceKey="TextFillColorSecondaryBrush" />
            <StaticResource x:Key="FlipViewNextPreviousArrowForegroundPressed"
                            ResourceKey="TextFillColorSecondaryBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBorderBrush"
                            ResourceKey="ControlStrokeColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBorderBrushPointerOver"
                            ResourceKey="ControlStrokeColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBorderBrushPressed"
                            ResourceKey="ControlStrokeColorDefaultBrush" />
            <StaticResource x:Key="FlipViewItemBackground"
                            ResourceKey="SubtleFillColorTransparentBrush" />
            <SolidColorBrush x:Key="FlipViewButtonBackgroundThemeBrush"
                             Color="#59D5D5D5" />
            <SolidColorBrush x:Key="FlipViewButtonBorderThemeBrush"
                             Color="#59D5D5D5" />
            <SolidColorBrush x:Key="FlipViewButtonForegroundThemeBrush"
                             Color="#99000000" />
            <SolidColorBrush x:Key="FlipViewButtonPointerOverBackgroundThemeBrush"
                             Color="#F0D7D7D7" />
            <SolidColorBrush x:Key="FlipViewButtonPointerOverBorderThemeBrush"
                             Color="#9EC1C1C1" />
            <SolidColorBrush x:Key="FlipViewButtonPointerOverForegroundThemeBrush"
                             Color="#FF000000" />
            <SolidColorBrush x:Key="FlipViewButtonPressedBackgroundThemeBrush"
                             Color="#BD292929" />
            <SolidColorBrush x:Key="FlipViewButtonPressedBorderThemeBrush"
                             Color="#BD292929" />
            <SolidColorBrush x:Key="FlipViewButtonPressedForegroundThemeBrush"
                             Color="#FFFFFFFF" />
        </ResourceDictionary>
        <ResourceDictionary x:Key="HighContrast">
            <Thickness x:Key="FlipViewButtonBorderThemeThickness">1</Thickness>
            <StaticResource x:Key="FlipViewBackground"
                            ResourceKey="SystemColorButtonFaceColorBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBackground"
                            ResourceKey="SystemColorButtonFaceColorBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBackgroundPointerOver"
                            ResourceKey="SystemColorHighlightColorBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBackgroundPressed"
                            ResourceKey="SystemColorHighlightColorBrush" />
            <StaticResource x:Key="FlipViewNextPreviousArrowForeground"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="FlipViewNextPreviousArrowForegroundPointerOver"
                            ResourceKey="SystemColorHighlightTextColorBrush" />
            <StaticResource x:Key="FlipViewNextPreviousArrowForegroundPressed"
                            ResourceKey="SystemColorHighlightTextColorBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBorderBrush"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBorderBrushPointerOver"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBorderBrushPressed"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="FlipViewItemBackground"
                            ResourceKey="SystemControlTransparentBrush" />
            <SolidColorBrush x:Key="FlipViewButtonBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonFaceColor}" />
            <SolidColorBrush x:Key="FlipViewButtonBorderThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="FlipViewButtonForegroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="FlipViewButtonPointerOverBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightColor}" />
            <SolidColorBrush x:Key="FlipViewButtonPointerOverBorderThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="FlipViewButtonPointerOverForegroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightTextColor}" />
            <SolidColorBrush x:Key="FlipViewButtonPressedBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="FlipViewButtonPressedBorderThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="FlipViewButtonPressedForegroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonFaceColor}" />
        </ResourceDictionary>
        <ResourceDictionary x:Key="Light">
            <Thickness x:Key="FlipViewButtonBorderThemeThickness">0</Thickness>
            <StaticResource x:Key="FlipViewBackground"
                            ResourceKey="SolidBackgroundFillColorBaseBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBackground"
                            ResourceKey="AcrylicInAppFillColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBackgroundPointerOver"
                            ResourceKey="AcrylicInAppFillColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBackgroundPressed"
                            ResourceKey="AcrylicInAppFillColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousArrowForeground"
                            ResourceKey="ControlStrongFillColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousArrowForegroundPointerOver"
                            ResourceKey="TextFillColorSecondaryBrush" />
            <StaticResource x:Key="FlipViewNextPreviousArrowForegroundPressed"
                            ResourceKey="TextFillColorSecondaryBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBorderBrush"
                            ResourceKey="ControlStrokeColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBorderBrushPointerOver"
                            ResourceKey="ControlStrokeColorDefaultBrush" />
            <StaticResource x:Key="FlipViewNextPreviousButtonBorderBrushPressed"
                            ResourceKey="ControlStrokeColorDefaultBrush" />
            <StaticResource x:Key="FlipViewItemBackground"
                            ResourceKey="SubtleFillColorTransparentBrush" />
            <SolidColorBrush x:Key="FlipViewButtonBackgroundThemeBrush"
                             Color="#59D5D5D5" />
            <SolidColorBrush x:Key="FlipViewButtonBorderThemeBrush"
                             Color="#59D5D5D5" />
            <SolidColorBrush x:Key="FlipViewButtonForegroundThemeBrush"
                             Color="#99000000" />
            <SolidColorBrush x:Key="FlipViewButtonPointerOverBackgroundThemeBrush"
                             Color="#F0D7D7D7" />
            <SolidColorBrush x:Key="FlipViewButtonPointerOverBorderThemeBrush"
                             Color="#9EC1C1C1" />
            <SolidColorBrush x:Key="FlipViewButtonPointerOverForegroundThemeBrush"
                             Color="#FF000000" />
            <SolidColorBrush x:Key="FlipViewButtonPressedBackgroundThemeBrush"
                             Color="#BD292929" />
            <SolidColorBrush x:Key="FlipViewButtonPressedBorderThemeBrush"
                             Color="#BD292929" />
            <SolidColorBrush x:Key="FlipViewButtonPressedForegroundThemeBrush"
                             Color="#FFFFFFFF" />
        </ResourceDictionary>
    </ResourceDictionary.ThemeDictionaries>
    <x:Double x:Key="FlipViewButtonFontSize">8</x:Double>
    <x:Double x:Key="FlipViewButtonScalePressed">0.875</x:Double>
    <Style BasedOn="{StaticResource CollapseFlipViewStyle}"
           TargetType="FlipView" />
    <Style x:Key="CollapseFlipViewStyle"
           TargetType="FlipView">
        <Setter Property="Background" Value="{ThemeResource FlipViewBackground}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="TabNavigation" Value="Once" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Hidden" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Hidden" />
        <Setter Property="ScrollViewer.IsHorizontalRailEnabled" Value="False" />
        <Setter Property="ScrollViewer.IsVerticalRailEnabled" Value="False" />
        <Setter Property="ScrollViewer.IsHorizontalScrollChainingEnabled" Value="True" />
        <Setter Property="ScrollViewer.IsVerticalScrollChainingEnabled" Value="True" />
        <Setter Property="ScrollViewer.IsDeferredScrollingEnabled" Value="False" />
        <Setter Property="ScrollViewer.BringIntoViewOnFocusChange" Value="True" />
        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <VirtualizingStackPanel AreScrollSnapPointsRegular="True"
                                            Orientation="Horizontal" />
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="FlipView">
                    <Grid Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          CornerRadius="{TemplateBinding CornerRadius}">
                        <Grid.Resources>
                            <ControlTemplate x:Key="HorizontalNextTemplate"
                                             TargetType="Button">
                                <Border x:Name="Root"
                                        Margin="0,32"
                                        Background="{ThemeResource FlipViewNextPreviousButtonBackground}"
                                        BorderBrush="{ThemeResource FlipViewNextPreviousButtonBorderBrush}"
                                        BorderThickness="{ThemeResource FlipViewButtonBorderThemeThickness}"
                                        CornerRadius="8,0,0,8"
                                        Opacity="1">
                                    <VisualStateManager.VisualStateGroups>
                                        <VisualStateGroup x:Name="CommonStates">
                                            <VisualState x:Name="Normal" />
                                            <VisualState x:Name="PointerOver">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBackgroundPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="BorderBrush">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBorderBrushPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                   Storyboard.TargetProperty="Foreground">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousArrowForegroundPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                            <VisualState x:Name="Pressed">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBackgroundPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="BorderBrush">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBorderBrushPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                   Storyboard.TargetProperty="Foreground">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousArrowForegroundPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                   Storyboard.TargetName="ScaleTransform"
                                                                                   Storyboard.TargetProperty="ScaleX">
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                   Storyboard.TargetName="ScaleTransform"
                                                                                   Storyboard.TargetProperty="ScaleY">
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                        </VisualStateGroup>
                                    </VisualStateManager.VisualStateGroups>
                                    <FontIcon x:Name="Arrow"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              FontSize="16"
                                              Foreground="{ThemeResource FlipViewNextPreviousArrowForeground}"
                                              Glyph="&#xf105;"
                                              MirroredWhenRightToLeft="True"
                                              RenderTransformOrigin="0.5, 0.5"
                                              UseLayoutRounding="False">
                                        <FontIcon.RenderTransform>
                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                        </FontIcon.RenderTransform>
                                    </FontIcon>
                                </Border>
                            </ControlTemplate>
                            <ControlTemplate x:Key="HorizontalPreviousTemplate"
                                             TargetType="Button">
                                <Border x:Name="Root"
                                        Margin="0,32"
                                        Background="{ThemeResource FlipViewNextPreviousButtonBackground}"
                                        BorderBrush="{ThemeResource FlipViewNextPreviousButtonBorderBrush}"
                                        BorderThickness="{ThemeResource FlipViewButtonBorderThemeThickness}"
                                        CornerRadius="0,8,8,0"
                                        Opacity="1">
                                    <VisualStateManager.VisualStateGroups>
                                        <VisualStateGroup x:Name="CommonStates">
                                            <VisualState x:Name="Normal" />
                                            <VisualState x:Name="PointerOver">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBackgroundPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="BorderBrush">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBorderBrushPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                   Storyboard.TargetProperty="Foreground">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousArrowForegroundPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                            <VisualState x:Name="Pressed">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBackgroundPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="BorderBrush">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBorderBrushPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                   Storyboard.TargetProperty="Foreground">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousArrowForegroundPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                   Storyboard.TargetName="ScaleTransform"
                                                                                   Storyboard.TargetProperty="ScaleX">
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                   Storyboard.TargetName="ScaleTransform"
                                                                                   Storyboard.TargetProperty="ScaleY">
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                        </VisualStateGroup>
                                    </VisualStateManager.VisualStateGroups>
                                    <FontIcon x:Name="Arrow"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              FontSize="16"
                                              Foreground="{ThemeResource FlipViewNextPreviousArrowForeground}"
                                              Glyph="&#xf104;"
                                              MirroredWhenRightToLeft="True"
                                              RenderTransformOrigin="0.5, 0.5"
                                              UseLayoutRounding="False">
                                        <FontIcon.RenderTransform>
                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                        </FontIcon.RenderTransform>
                                    </FontIcon>
                                </Border>
                            </ControlTemplate>
                            <ControlTemplate x:Key="VerticalNextTemplate"
                                             TargetType="Button">
                                <Border x:Name="Root"
                                        Padding="32,8"
                                        Background="{ThemeResource FlipViewNextPreviousButtonBackground}"
                                        BorderBrush="{ThemeResource FlipViewNextPreviousButtonBorderBrush}"
                                        BorderThickness="{ThemeResource FlipViewButtonBorderThemeThickness}"
                                        CornerRadius="8">
                                    <VisualStateManager.VisualStateGroups>
                                        <VisualStateGroup x:Name="CommonStates">
                                            <VisualState x:Name="Normal" />
                                            <VisualState x:Name="PointerOver">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBackgroundPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="BorderBrush">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBorderBrushPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                   Storyboard.TargetProperty="Foreground">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousArrowForegroundPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                            <VisualState x:Name="Pressed">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBackgroundPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="BorderBrush">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBorderBrushPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                   Storyboard.TargetProperty="Foreground">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousArrowForegroundPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                   Storyboard.TargetName="ScaleTransform"
                                                                                   Storyboard.TargetProperty="ScaleX">
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                   Storyboard.TargetName="ScaleTransform"
                                                                                   Storyboard.TargetProperty="ScaleY">
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                        </VisualStateGroup>
                                    </VisualStateManager.VisualStateGroups>
                                    <FontIcon x:Name="Arrow"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              FontSize="16"
                                              Foreground="{ThemeResource FlipViewNextPreviousArrowForeground}"
                                              Glyph="&#xf107;"
                                              RenderTransformOrigin="0.5, 0.5"
                                              UseLayoutRounding="False">
                                        <FontIcon.RenderTransform>
                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                        </FontIcon.RenderTransform>
                                    </FontIcon>
                                </Border>
                            </ControlTemplate>
                            <ControlTemplate x:Key="VerticalPreviousTemplate"
                                             TargetType="Button">
                                <Border x:Name="Root"
                                        Padding="32,8"
                                        Background="{ThemeResource FlipViewNextPreviousButtonBackground}"
                                        BorderBrush="{ThemeResource FlipViewNextPreviousButtonBorderBrush}"
                                        BorderThickness="{ThemeResource FlipViewButtonBorderThemeThickness}"
                                        CornerRadius="8">
                                    <VisualStateManager.VisualStateGroups>
                                        <VisualStateGroup x:Name="CommonStates">
                                            <VisualState x:Name="Normal" />
                                            <VisualState x:Name="PointerOver">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBackgroundPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="BorderBrush">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBorderBrushPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                   Storyboard.TargetProperty="Foreground">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousArrowForegroundPointerOver}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                            <VisualState x:Name="Pressed">
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="Background">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBackgroundPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                                   Storyboard.TargetProperty="BorderBrush">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousButtonBorderBrushPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                   Storyboard.TargetProperty="Foreground">
                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                Value="{ThemeResource FlipViewNextPreviousArrowForegroundPressed}" />
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                   Storyboard.TargetName="ScaleTransform"
                                                                                   Storyboard.TargetProperty="ScaleX">
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                   Storyboard.TargetName="ScaleTransform"
                                                                                   Storyboard.TargetProperty="ScaleY">
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                Value="{ThemeResource FlipViewButtonScalePressed}" />
                                                    </DoubleAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </VisualState>
                                        </VisualStateGroup>
                                    </VisualStateManager.VisualStateGroups>
                                    <FontIcon x:Name="Arrow"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              FontSize="16"
                                              Foreground="{ThemeResource FlipViewNextPreviousArrowForeground}"
                                              Glyph="&#xf106;"
                                              RenderTransformOrigin="0.5, 0.5"
                                              UseLayoutRounding="False">
                                        <FontIcon.RenderTransform>
                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                        </FontIcon.RenderTransform>
                                    </FontIcon>
                                </Border>
                            </ControlTemplate>
                        </Grid.Resources>
                        <ScrollViewer x:Name="ScrollingHost"
                                      Padding="{TemplateBinding Padding}"
                                      AutomationProperties.AccessibilityView="Raw"
                                      BringIntoViewOnFocusChange="{TemplateBinding ScrollViewer.BringIntoViewOnFocusChange}"
                                      HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                      HorizontalScrollMode="{TemplateBinding ScrollViewer.HorizontalScrollMode}"
                                      HorizontalSnapPointsType="MandatorySingle"
                                      IsDeferredScrollingEnabled="{TemplateBinding ScrollViewer.IsDeferredScrollingEnabled}"
                                      IsHorizontalRailEnabled="{TemplateBinding ScrollViewer.IsHorizontalRailEnabled}"
                                      IsHorizontalScrollChainingEnabled="{TemplateBinding ScrollViewer.IsHorizontalScrollChainingEnabled}"
                                      IsTabStop="False"
                                      IsVerticalRailEnabled="{TemplateBinding ScrollViewer.IsVerticalRailEnabled}"
                                      IsVerticalScrollChainingEnabled="{TemplateBinding ScrollViewer.IsVerticalScrollChainingEnabled}"
                                      TabNavigation="{TemplateBinding TabNavigation}"
                                      VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                                      VerticalScrollMode="{TemplateBinding ScrollViewer.VerticalScrollMode}"
                                      VerticalSnapPointsType="MandatorySingle"
                                      ZoomMode="Disabled">
                            <ItemsPresenter />
                        </ScrollViewer>
                        <Button x:Name="PreviousButtonHorizontal"
                                Width="38"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Stretch"
                                IsTabStop="False"
                                Template="{StaticResource HorizontalPreviousTemplate}"
                                UseSystemFocusVisuals="False" />
                        <Button x:Name="NextButtonHorizontal"
                                Width="38"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Stretch"
                                IsTabStop="False"
                                Template="{StaticResource HorizontalNextTemplate}"
                                UseSystemFocusVisuals="False" />
                        <Button x:Name="PreviousButtonVertical"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Top"
                                IsTabStop="False"
                                Template="{StaticResource VerticalPreviousTemplate}"
                                UseSystemFocusVisuals="False" />
                        <Button x:Name="NextButtonVertical"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Bottom"
                                IsTabStop="False"
                                Template="{StaticResource VerticalNextTemplate}"
                                UseSystemFocusVisuals="False" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
