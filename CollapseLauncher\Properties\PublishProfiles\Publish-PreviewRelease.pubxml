﻿<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project>
    <PropertyGroup>
        <!-- Configuration and Platform -->
        <Configuration>Release</Configuration>
        <DefineConstants>$(DefineConstants);DISABLE_XAML_GENERATED_MAIN;USEVELOPACK;USENEWZIPDECOMPRESS;ENABLEHTTPREPAIR;DISABLE_XAML_GENERATED_BREAK_ON_UNHANDLED_EXCEPTION;PREVIEW;MHYPLUGINSUPPORT</DefineConstants>
        <Platform>x64</Platform>

        <!-- Publish Directory and Protocol -->
        <PublishDir>..\..\CollapseLauncher-ReleaseRepo\preview-build</PublishDir>
        <PublishProtocol>FileSystem</PublishProtocol>

        <!-- Target Framework and Runtime Identifier -->
        <TargetFramework>net9.0-windows10.0.26100.0</TargetFramework>
        <RuntimeIdentifier>win-x64</RuntimeIdentifier>

        <!-- Publish Options -->
        <SelfContained>true</SelfContained>
        <PublishReadyToRun>true</PublishReadyToRun>
        <PublishTrimmed>true</PublishTrimmed>
        <TrimMode>full</TrimMode>
        <TrimmerRemoveSymbols>true</TrimmerRemoveSymbols>
        <PublishSingleFile>false</PublishSingleFile>
        <PublishAoT>false</PublishAoT>
        <RunAOTCompilation>false</RunAOTCompilation>
        <GitSkipCache>true</GitSkipCache>

        <!-- Compilation and Optimization -->
        <Optimize>true</Optimize>
        <Deterministic>true</Deterministic>
        <IlcOptimizationPreference>Speed</IlcOptimizationPreference>
        <IlcDisableReflection>false</IlcDisableReflection>
        <OptimizationPreference>Speed</OptimizationPreference>
        <CsWinRTAotOptimizerEnabled>true</CsWinRTAotOptimizerEnabled>
        <IlcDisableSingleFile>true</IlcDisableSingleFile>
        <IlcGenerateMstatFile>true</IlcGenerateMstatFile>
        <IlcGenerateDgmlFile>true</IlcGenerateDgmlFile>

        <!-- Debugging and Metadata -->
        <DebuggerSupport>true</DebuggerSupport>
        <MetadataUpdaterSupport>false</MetadataUpdaterSupport>
        <StackTraceSupport>true</StackTraceSupport>
        <TrimmerRemoveSymbols>false</TrimmerRemoveSymbols>

        <!-- Security and Resource Management -->
        <AutoreleasePoolSupport>false</AutoreleasePoolSupport>
        <EnableUnsafeBinaryFormatterSerialization>false</EnableUnsafeBinaryFormatterSerialization>
        <EnableUnsafeUTF7Encoding>false</EnableUnsafeUTF7Encoding>
        <EventSourceSupport>false</EventSourceSupport>
        <HttpActivityPropagationSupport>false</HttpActivityPropagationSupport>
        <UseSystemResourceKeys>true</UseSystemResourceKeys>

        <!-- Runtime Patch -->
        <TargetLatestRuntimePatch>true</TargetLatestRuntimePatch>

        <!-- Configure Sentry org and project -->
        <SentryOrg>collapse</SentryOrg>
        <SentryProject>collapse-launcher</SentryProject>
        <!-- Automatically creates a release when building your application. -->
        <SentryCreateRelease>false</SentryCreateRelease>
        <!-- Automatically associates commits with the release. -->
        <SentrySetCommits>false</SentrySetCommits>
        <!-- Optionally provide explicit flags to the set-commits command -->
        <SentrySetCommitOptions>--local</SentrySetCommitOptions>
        <!-- Sends symbols to Sentry, enabling symbolication of stack traces. -->
        <SentryUploadSymbols>true</SentryUploadSymbols>
        <!-- Sends sources to Sentry, enabling display of source context. -->
        <SentryUploadSources>true</SentryUploadSources>
    </PropertyGroup>
</Project>