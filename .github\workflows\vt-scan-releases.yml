name: VirusTotal Scan for Releases

on:
  release:
    types: [published]

jobs:
  virustotal:
    runs-on: ubuntu-latest
    permissions:
        actions: read
        contents: write
        pull-requests: write
        checks: write
        security-events: write
        deployments: write
        packages: write
        statuses: write
        
    steps:
      - name: VirusTotal Scan Executables
        uses: crazy-max/ghaction-virustotal@v4
        with:
          vt_api_key: ${{ secrets.VT_API_KEY }}
          update_release_body: true
          github_token: ${{ secrets.GITHUB_TOKEN }}
          files: |
            ^.*\.[eE][xX][eE]$

      - name: VirusTotal Scan Archive
        uses: crazy-max/ghaction-virustotal@v4
        with:
          vt_api_key: ${{ secrets.VT_API_KEY }}
          update_release_body: true
          github_token: ${{ secrets.GITHUB_TOKEN }}
          files: |
            ^.*\.[7][zZ]$
