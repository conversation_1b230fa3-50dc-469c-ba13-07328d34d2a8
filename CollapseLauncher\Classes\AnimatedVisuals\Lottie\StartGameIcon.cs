﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//       LottieGen version:
//           8.0.280225.1+7cd366a738
//       
//       Command:
//           LottieGen -Language CSharp -Namespace CollapseLauncher.AnimatedVisuals.Lottie -Public -WinUIVersion 3.0 -InputFile StartGameIcon.lottie
//       
//       Input file:
//           StartGameIcon.lottie (1259 bytes created 0:25+07:00 Jun 2 2024)
//       
//       LottieGen source:
//           http://aka.ms/Lottie
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
// ____________________________________
// |       Object stats       | Count |
// |__________________________|_______|
// | All CompositionObjects   |    70 |
// |--------------------------+-------|
// | Expression animators     |     9 |
// | KeyFrame animators       |     6 |
// | Reference parameters     |     9 |
// | Expression operations    |     6 |
// |--------------------------+-------|
// | Animated brushes         |     3 |
// | Animated gradient stops  |     - |
// | ExpressionAnimations     |     9 |
// | PathKeyFrameAnimations   |     - |
// |--------------------------+-------|
// | ContainerVisuals         |     1 |
// | ShapeVisuals             |     1 |
// |--------------------------+-------|
// | ContainerShapes          |     3 |
// | CompositionSpriteShapes  |     3 |
// |--------------------------+-------|
// | Brushes                  |     3 |
// | Gradient stops           |     - |
// | CompositionVisualSurface |     - |
// ------------------------------------
using Microsoft.UI.Composition;
using System;
using System.Collections.Generic;
using System.Numerics;
using Windows.UI;

namespace CollapseLauncher.AnimatedVisuals.Lottie
{
    // Name:        StartGameIcon
    // Frame rate:  60 fps
    // Frame count: 40
    // Duration:    666.7 mS
    sealed partial class StartGameIcon
        : Microsoft.UI.Xaml.Controls.IAnimatedVisualSource
        , Microsoft.UI.Xaml.Controls.IAnimatedVisualSource2
    {
        // Animation duration: 0.667 seconds.
        internal const long c_durationTicks = 6666666;

        public Microsoft.UI.Xaml.Controls.IAnimatedVisual TryCreateAnimatedVisual(Compositor compositor)
        {
            object ignored = null;
            return TryCreateAnimatedVisual(compositor, out ignored);
        }

        public Microsoft.UI.Xaml.Controls.IAnimatedVisual TryCreateAnimatedVisual(Compositor compositor, out object diagnostics)
        {
            diagnostics = null;

            var res = 
                new StartGameIcon_AnimatedVisual(
                    compositor
                    );
                res.CreateAnimations();
                return res;
        }

        /// <summary>
        /// Gets the number of frames in the animation.
        /// </summary>
        public double FrameCount => 40d;

        /// <summary>
        /// Gets the frame rate of the animation.
        /// </summary>
        public double Framerate => 60d;

        /// <summary>
        /// Gets the duration of the animation.
        /// </summary>
        public TimeSpan Duration => TimeSpan.FromTicks(6666666);

        /// <summary>
        /// Converts a zero-based frame number to the corresponding progress value denoting the
        /// start of the frame.
        /// </summary>
        public double FrameToProgress(double frameNumber)
        {
            return frameNumber / 40d;
        }

        /// <summary>
        /// Returns a map from marker names to corresponding progress values.
        /// </summary>
        public IReadOnlyDictionary<string, double> Markers =>
            new Dictionary<string, double>
            {
            };

        /// <summary>
        /// Sets the color property with the given name, or does nothing if no such property
        /// exists.
        /// </summary>
        public void SetColorProperty(string propertyName, Color value)
        {
        }

        /// <summary>
        /// Sets the scalar property with the given name, or does nothing if no such property
        /// exists.
        /// </summary>
        public void SetScalarProperty(string propertyName, double value)
        {
        }

        sealed partial class StartGameIcon_AnimatedVisual
            : Microsoft.UI.Xaml.Controls.IAnimatedVisual
            , Microsoft.UI.Xaml.Controls.IAnimatedVisual2
        {
            const long c_durationTicks = 6666666;
            readonly Color _color = InnerLauncherConfig.IsAppThemeLight ? Color.FromArgb(0xFF, 0xFF, 0xFF, 0xFF) : Color.FromArgb(0xFF, 0x00, 0x00, 0x00);
            readonly Color _colorTransparent = InnerLauncherConfig.IsAppThemeLight ? Color.FromArgb(0x00, 0xFF, 0xFF, 0xFF) : Color.FromArgb(0x00, 0x00, 0x00, 0x00);
            readonly Compositor _c;
            readonly ExpressionAnimation _reusableExpressionAnimation;
            AnimationController _animationController_0;
            AnimationController _animationController_1;
            AnimationController _animationController_2;
            AnimationController _animationController_3;
            AnimationController _animationController_4;
            AnimationController _animationController_5;
            CompositionColorBrush _animatedColorBrush_TransparentWhite_to_White;
            CompositionColorBrush _animatedColorBrush_White_to_TransparentWhite;
            CompositionColorBrush _animatedColorBrush_White_to_White;
            CompositionContainerShape _containerShape_0;
            CompositionContainerShape _containerShape_1;
            CompositionContainerShape _containerShape_2;
            CompositionRectangleGeometry _rectangle_1036;
            ContainerVisual _root;
            CubicBezierEasingFunction _cubicBezierEasingFunction_0;
            CubicBezierEasingFunction _cubicBezierEasingFunction_1;
            ScalarKeyFrameAnimation _positionXScalarAnimation_m92_to_993;
            StepEasingFunction _holdThenStepEasingFunction;

            void BindProperty(
                CompositionObject target,
                string animatedPropertyName,
                string expression,
                string referenceParameterName,
                CompositionObject referencedObject)
            {
                _reusableExpressionAnimation.ClearAllParameters();
                _reusableExpressionAnimation.Expression = expression;
                _reusableExpressionAnimation.SetReferenceParameter(referenceParameterName, referencedObject);
                target.StartAnimation(animatedPropertyName, _reusableExpressionAnimation);
            }

            ColorKeyFrameAnimation CreateColorKeyFrameAnimation(float initialProgress, Color initialValue, CompositionEasingFunction initialEasingFunction)
            {
                var result = _c.CreateColorKeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InterpolationColorSpace = CompositionColorSpace.Rgb;
                result.InsertKeyFrame(initialProgress, initialValue, initialEasingFunction);
                return result;
            }

            ScalarKeyFrameAnimation CreateScalarKeyFrameAnimation(float initialProgress, float initialValue, CompositionEasingFunction initialEasingFunction)
            {
                var result = _c.CreateScalarKeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InsertKeyFrame(initialProgress, initialValue, initialEasingFunction);
                return result;
            }

            CompositionSpriteShape CreateSpriteShape(CompositionGeometry geometry, Matrix3x2 transformMatrix)
            {
                var result = _c.CreateSpriteShape(geometry);
                result.TransformMatrix = transformMatrix;
                return result;
            }

            // - ShapeGroup: Rectangle 1 Offset:<30, 198>
            AnimationController AnimationController_0()
            {
                if (_animationController_0 != null) { return _animationController_0; }
                var result = _animationController_0 = _c.CreateAnimationController();
                result.Pause();
                BindProperty(_animationController_0, "Progress", "_.Progress*0.4+0.6", "_", _root);
                return result;
            }

            AnimationController AnimationController_1()
            {
                if (_animationController_1 != null) { return _animationController_1; }
                var result = _animationController_1 = _c.CreateAnimationController();
                result.Pause();
                BindProperty(_animationController_1, "Progress", "_.Progress*0.3333333+0.6666667", "_", _root);
                return result;
            }

            // - ShapeGroup: Rectangle 1 Offset:<30, 198>
            AnimationController AnimationController_2()
            {
                if (_animationController_2 != null) { return _animationController_2; }
                var result = _animationController_2 = _c.CreateAnimationController();
                result.Pause();
                BindProperty(_animationController_2, "Progress", "_.Progress*0.5728853+0.2864427", "_", _root);
                return result;
            }

            AnimationController AnimationController_3()
            {
                if (_animationController_3 != null) { return _animationController_3; }
                var result = _animationController_3 = _c.CreateAnimationController();
                result.Pause();
                BindProperty(_animationController_3, "Progress", "_.Progress*0.3333333+0.3333333", "_", _root);
                return result;
            }

            // - ShapeGroup: Rectangle 1 Offset:<30, 198>
            AnimationController AnimationController_4()
            {
                if (_animationController_4 != null) { return _animationController_4; }
                var result = _animationController_4 = _c.CreateAnimationController();
                result.Pause();
                BindProperty(_animationController_4, "Progress", "_.Progress*0.4453252", "_", _root);
                return result;
            }

            AnimationController AnimationController_5()
            {
                if (_animationController_5 != null) { return _animationController_5; }
                var result = _animationController_5 = _c.CreateAnimationController();
                result.Pause();
                BindProperty(_animationController_5, "Progress", "_.Progress*0.3333333", "_", _root);
                return result;
            }

            // - ShapeGroup: Rectangle 1 Offset:<30, 198>
            // Color
            ColorKeyFrameAnimation ColorAnimation_TransparentWhite_to_White()
            {
                // Frame 0.
                var result = CreateColorKeyFrameAnimation(0F, _colorTransparent, HoldThenStepEasingFunction());
                // Frame 8.91.
                // White
                result.InsertKeyFrame(0.222662598F, _color, CubicBezierEasingFunction_0());
                // Frame 40.
                // White
                result.InsertKeyFrame(1F, _color, CubicBezierEasingFunction_1());
                return result;
            }

            // - ShapeGroup: Rectangle 1 Offset:<30, 198>
            // Color
            ColorKeyFrameAnimation ColorAnimation_White_to_TransparentWhite()
            {
                // Frame 0.
                var result = CreateColorKeyFrameAnimation(0F, _color, CubicBezierEasingFunction_0());
                // Frame 27.93.
                // White
                result.InsertKeyFrame(0.698220015F, _color, CubicBezierEasingFunction_1());
                // Frame 40.
                // TransparentWhite
                result.InsertKeyFrame(0.99998045F, _colorTransparent, CubicBezierEasingFunction_0());
                return result;
            }

            // - ShapeGroup: Rectangle 1 Offset:<30, 198>
            // Color
            ColorKeyFrameAnimation ColorAnimation_White_to_White()
            {
                // Frame 0.
                var result = CreateColorKeyFrameAnimation(0F, _color, CubicBezierEasingFunction_0());
                // Frame 40.
                // White
                result.InsertKeyFrame(1F, _color, CubicBezierEasingFunction_1());
                return result;
            }

            // ShapeGroup: Rectangle 1 Offset:<30, 198>
            CompositionColorBrush AnimatedColorBrush_TransparentWhite_to_White()
            {
                if (_animatedColorBrush_TransparentWhite_to_White != null) { return _animatedColorBrush_TransparentWhite_to_White; }
                var result = _animatedColorBrush_TransparentWhite_to_White = _c.CreateColorBrush();
                return result;
            }

            // ShapeGroup: Rectangle 1 Offset:<30, 198>
            CompositionColorBrush AnimatedColorBrush_White_to_TransparentWhite()
            {
                if (_animatedColorBrush_White_to_TransparentWhite != null) { return _animatedColorBrush_White_to_TransparentWhite; }
                var result = _animatedColorBrush_White_to_TransparentWhite = _c.CreateColorBrush();
                return result;
            }

            // ShapeGroup: Rectangle 1 Offset:<30, 198>
            CompositionColorBrush AnimatedColorBrush_White_to_White()
            {
                if (_animatedColorBrush_White_to_White != null) { return _animatedColorBrush_White_to_White; }
                var result = _animatedColorBrush_White_to_White = _c.CreateColorBrush();
                return result;
            }

            CompositionContainerShape ContainerShape_0()
            {
                if (_containerShape_0 != null) { return _containerShape_0; }
                var result = _containerShape_0 = _c.CreateContainerShape();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(-92F, 1024F));
                result.CenterPoint = new Vector2(30F, 198F);
                result.RotationAngleInDegrees = 45F;
                // ShapeGroup: Rectangle 1 Offset:<30, 198>
                result.Shapes.Add(SpriteShape_0());
                BindProperty(_containerShape_0, "Offset", "Vector2(my.Position.X-30,my.Position.Y-198)", "my", _containerShape_0);
                return result;
            }

            CompositionContainerShape ContainerShape_1()
            {
                if (_containerShape_1 != null) { return _containerShape_1; }
                var result = _containerShape_1 = _c.CreateContainerShape();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(-92F, 1024F));
                result.CenterPoint = new Vector2(30F, 198F);
                result.RotationAngleInDegrees = 45F;
                // ShapeGroup: Rectangle 1 Offset:<30, 198>
                result.Shapes.Add(SpriteShape_1());
                BindProperty(_containerShape_1, "Offset", "Vector2(my.Position.X-30,my.Position.Y-198)", "my", _containerShape_1);
                return result;
            }

            CompositionContainerShape ContainerShape_2()
            {
                if (_containerShape_2 != null) { return _containerShape_2; }
                var result = _containerShape_2 = _c.CreateContainerShape();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(-92F, 1024F));
                result.CenterPoint = new Vector2(30F, 198F);
                result.RotationAngleInDegrees = 45F;
                // ShapeGroup: Rectangle 1 Offset:<30, 198>
                result.Shapes.Add(SpriteShape_2());
                BindProperty(_containerShape_2, "Offset", "Vector2(my.Position.X-30,my.Position.Y-198)", "my", _containerShape_2);
                return result;
            }

            // Rectangle Path 1.RectangleGeometry
            CompositionRectangleGeometry Rectangle_1036()
            {
                if (_rectangle_1036 != null) { return _rectangle_1036; }
                var result = _rectangle_1036 = _c.CreateRectangleGeometry();
                result.TrimEnd = 0.629999995F;
                result.TrimOffset = 0.75F;
                result.TrimStart = 0.370000005F;
                result.Offset = new Vector2(-518F, -518F);
                result.Size = new Vector2(1036F, 1036F);
                return result;
            }

            // Rectangle Path 1
            CompositionSpriteShape SpriteShape_0()
            {
                // Offset:<30, 198>
                var result = CreateSpriteShape(Rectangle_1036(), new Matrix3x2(1F, 0F, 0F, 1F, 30F, 198F));;
                result.StrokeBrush = AnimatedColorBrush_White_to_TransparentWhite();
                result.StrokeDashCap = CompositionStrokeCap.Round;
                result.StrokeStartCap = CompositionStrokeCap.Round;
                result.StrokeEndCap = CompositionStrokeCap.Round;
                result.StrokeMiterLimit = 2F;
                result.StrokeThickness = 128F;
                return result;
            }

            // Rectangle Path 1
            CompositionSpriteShape SpriteShape_1()
            {
                // Offset:<30, 198>
                var result = CreateSpriteShape(Rectangle_1036(), new Matrix3x2(1F, 0F, 0F, 1F, 30F, 198F));;
                result.StrokeBrush = AnimatedColorBrush_White_to_White();
                result.StrokeDashCap = CompositionStrokeCap.Round;
                result.StrokeStartCap = CompositionStrokeCap.Round;
                result.StrokeEndCap = CompositionStrokeCap.Round;
                result.StrokeMiterLimit = 2F;
                result.StrokeThickness = 128F;
                return result;
            }

            // Rectangle Path 1
            CompositionSpriteShape SpriteShape_2()
            {
                // Offset:<30, 198>
                var result = CreateSpriteShape(Rectangle_1036(), new Matrix3x2(1F, 0F, 0F, 1F, 30F, 198F));;
                result.StrokeBrush = AnimatedColorBrush_TransparentWhite_to_White();
                result.StrokeDashCap = CompositionStrokeCap.Round;
                result.StrokeStartCap = CompositionStrokeCap.Round;
                result.StrokeEndCap = CompositionStrokeCap.Round;
                result.StrokeMiterLimit = 2F;
                result.StrokeThickness = 128F;
                return result;
            }

            // The root of the composition.
            ContainerVisual Root()
            {
                if (_root != null) { return _root; }
                var result = _root = _c.CreateContainerVisual();
                var propertySet = result.Properties;
                propertySet.InsertScalar("Progress", 0F);
                // Layer aggregator
                result.Children.InsertAtTop(ShapeVisual_0());
                return result;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_0()
            {
                return (_cubicBezierEasingFunction_0 == null)
                    ? _cubicBezierEasingFunction_0 = _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0F, 1F))
                    : _cubicBezierEasingFunction_0;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_1()
            {
                return (_cubicBezierEasingFunction_1 == null)
                    ? _cubicBezierEasingFunction_1 = _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0.412999988F, 1F))
                    : _cubicBezierEasingFunction_1;
            }

            // Position.X
            ScalarKeyFrameAnimation PositionXScalarAnimation_m92_to_993()
            {
                // Frame 0.
                if (_positionXScalarAnimation_m92_to_993 != null) { return _positionXScalarAnimation_m92_to_993; }
                var result = _positionXScalarAnimation_m92_to_993 = CreateScalarKeyFrameAnimation(0F, -92F, HoldThenStepEasingFunction());
                // Frame 40.
                result.InsertKeyFrame(1F, 993F, _c.CreateCubicBezierEasingFunction(new Vector2(0.47299999F, 0.0659999996F), new Vector2(0.521000028F, 1F)));
                return result;
            }

            // Layer aggregator
            ShapeVisual ShapeVisual_0()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(2048F, 2048F);
                var shapes = result.Shapes;
                shapes.Add(ContainerShape_0());
                shapes.Add(ContainerShape_1());
                shapes.Add(ContainerShape_2());
                return result;
            }

            StepEasingFunction HoldThenStepEasingFunction()
            {
                if (_holdThenStepEasingFunction != null) { return _holdThenStepEasingFunction; }
                var result = _holdThenStepEasingFunction = _c.CreateStepEasingFunction();
                result.IsFinalStepSingleFrame = true;
                return result;
            }

            internal StartGameIcon_AnimatedVisual(
                Compositor compositor
                )
            {
                _c = compositor;
                _reusableExpressionAnimation = compositor.CreateExpressionAnimation();
                Root();
            }

            public Visual RootVisual => _root;
            public TimeSpan Duration => TimeSpan.FromTicks(c_durationTicks);
            public Vector2 Size => new Vector2(2048F, 2048F);
            void IDisposable.Dispose() => _root?.Dispose();

            public void CreateAnimations()
            {
                _animatedColorBrush_TransparentWhite_to_White.StartAnimation("Color", ColorAnimation_TransparentWhite_to_White(), AnimationController_4());
                _animatedColorBrush_White_to_TransparentWhite.StartAnimation("Color", ColorAnimation_White_to_TransparentWhite(), AnimationController_0());
                _animatedColorBrush_White_to_White.StartAnimation("Color", ColorAnimation_White_to_White(), AnimationController_2());
                _containerShape_0.Properties.StartAnimation("Position.X", PositionXScalarAnimation_m92_to_993(), AnimationController_1());
                _containerShape_1.Properties.StartAnimation("Position.X", PositionXScalarAnimation_m92_to_993(), AnimationController_3());
                _containerShape_2.Properties.StartAnimation("Position.X", PositionXScalarAnimation_m92_to_993(), AnimationController_5());
            }

            public void DestroyAnimations()
            {
                _animatedColorBrush_TransparentWhite_to_White.StopAnimation("Color");
                _animatedColorBrush_White_to_TransparentWhite.StopAnimation("Color");
                _animatedColorBrush_White_to_White.StopAnimation("Color");
                _containerShape_0.Properties.StopAnimation("Position.X");
                _containerShape_1.Properties.StopAnimation("Position.X");
                _containerShape_2.Properties.StopAnimation("Position.X");
            }

        }
    }
}
