﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//       LottieGen version:
//           8.0.280225.1+7cd366a738
//       
//       Command:
//           LottieGen -Language CSharp -Namespace CollapseLauncher.AnimatedVisuals.Lottie -Public -WinUIVersion 3.0 -InputFile LoadingSprite.lottie
//       
//       Input file:
//           LoadingSprite.lottie (1342 bytes created 20:17+07:00 May 23 2024)
//       
//       LottieGen source:
//           http://aka.ms/Lottie
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
// ____________________________________
// |       Object stats       | Count |
// |__________________________|_______|
// | All CompositionObjects   |    92 |
// |--------------------------+-------|
// | Expression animators     |     6 |
// | KeyFrame animators       |    17 |
// | Reference parameters     |     6 |
// | Expression operations    |     0 |
// |--------------------------+-------|
// | Animated brushes         |     - |
// | Animated gradient stops  |     - |
// | ExpressionAnimations     |     6 |
// | PathKeyFrameAnimations   |     - |
// |--------------------------+-------|
// | ContainerVisuals         |     1 |
// | ShapeVisuals             |     1 |
// |--------------------------+-------|
// | ContainerShapes          |     4 |
// | CompositionSpriteShapes  |     3 |
// |--------------------------+-------|
// | Brushes                  |     2 |
// | Gradient stops           |     - |
// | CompositionVisualSurface |     - |
// ------------------------------------
using Microsoft.UI.Composition;
using System;
using System.Collections.Generic;
using System.Numerics;
using Windows.UI;

namespace CollapseLauncher.AnimatedVisuals.Lottie
{
    // Name:        LoadingSprite
    // Frame rate:  60 fps
    // Frame count: 180
    // Duration:    3000.0 mS
    sealed partial class LoadingSprite
        : Microsoft.UI.Xaml.Controls.IAnimatedVisualSource
        , Microsoft.UI.Xaml.Controls.IAnimatedVisualSource2
    {
        // Animation duration: 3.000 seconds.
        internal const long c_durationTicks = 30000000;

        public Microsoft.UI.Xaml.Controls.IAnimatedVisual TryCreateAnimatedVisual(Compositor compositor)
        {
            object ignored = null;
            return TryCreateAnimatedVisual(compositor, out ignored);
        }

        public Microsoft.UI.Xaml.Controls.IAnimatedVisual TryCreateAnimatedVisual(Compositor compositor, out object diagnostics)
        {
            diagnostics = null;

            var res = 
                new LoadingSprite_AnimatedVisual(
                    compositor
                    );
                res.CreateAnimations();
                return res;
        }

        /// <summary>
        /// Gets the number of frames in the animation.
        /// </summary>
        public double FrameCount => 180d;

        /// <summary>
        /// Gets the frame rate of the animation.
        /// </summary>
        public double Framerate => 60d;

        /// <summary>
        /// Gets the duration of the animation.
        /// </summary>
        public TimeSpan Duration => TimeSpan.FromTicks(30000000);

        /// <summary>
        /// Converts a zero-based frame number to the corresponding progress value denoting the
        /// start of the frame.
        /// </summary>
        public double FrameToProgress(double frameNumber)
        {
            return frameNumber / 180d;
        }

        /// <summary>
        /// Returns a map from marker names to corresponding progress values.
        /// </summary>
        public IReadOnlyDictionary<string, double> Markers =>
            new Dictionary<string, double>
            {
            };

        /// <summary>
        /// Sets the color property with the given name, or does nothing if no such property
        /// exists.
        /// </summary>
        public void SetColorProperty(string propertyName, Color value)
        {
        }

        /// <summary>
        /// Sets the scalar property with the given name, or does nothing if no such property
        /// exists.
        /// </summary>
        public void SetScalarProperty(string propertyName, double value)
        {
        }

        sealed partial class LoadingSprite_AnimatedVisual
            : Microsoft.UI.Xaml.Controls.IAnimatedVisual
            , Microsoft.UI.Xaml.Controls.IAnimatedVisual2
        {
            const long c_durationTicks = 30000000;
            readonly Compositor _c;
            readonly ExpressionAnimation _reusableExpressionAnimation;
            AnimationController _animationController_0;
            CompositionColorBrush _colorBrush_White;
            CompositionContainerShape _containerShape_0;
            CompositionContainerShape _containerShape_1;
            CompositionContainerShape _containerShape_2;
            CompositionContainerShape _containerShape_3;
            CompositionEllipseGeometry _ellipse_84p25_0;
            CompositionEllipseGeometry _ellipse_84p25_1;
            CompositionRoundedRectangleGeometry _roundedRectangle_291;
            ContainerVisual _root;
            CubicBezierEasingFunction _cubicBezierEasingFunction_0;
            CubicBezierEasingFunction _cubicBezierEasingFunction_1;
            CubicBezierEasingFunction _cubicBezierEasingFunction_2;
            CubicBezierEasingFunction _cubicBezierEasingFunction_3;
            CubicBezierEasingFunction _cubicBezierEasingFunction_4;
            ScalarKeyFrameAnimation _scalarAnimation_0p85_to_1;
            ScalarKeyFrameAnimation _scalarAnimation_1_to_1_0;
            ScalarKeyFrameAnimation _scalarAnimation_1_to_1_1;
            StepEasingFunction _holdThenStepEasingFunction;
            StepEasingFunction _stepThenHoldEasingFunction;

            void BindProperty(
                CompositionObject target,
                string animatedPropertyName,
                string expression,
                string referenceParameterName,
                CompositionObject referencedObject)
            {
                _reusableExpressionAnimation.ClearAllParameters();
                _reusableExpressionAnimation.Expression = expression;
                _reusableExpressionAnimation.SetReferenceParameter(referenceParameterName, referencedObject);
                target.StartAnimation(animatedPropertyName, _reusableExpressionAnimation);
            }

            ScalarKeyFrameAnimation CreateScalarKeyFrameAnimation(float initialProgress, float initialValue, CompositionEasingFunction initialEasingFunction)
            {
                var result = _c.CreateScalarKeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InsertKeyFrame(initialProgress, initialValue, initialEasingFunction);
                return result;
            }

            Vector2KeyFrameAnimation CreateVector2KeyFrameAnimation(float initialProgress, Vector2 initialValue, CompositionEasingFunction initialEasingFunction)
            {
                var result = _c.CreateVector2KeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InsertKeyFrame(initialProgress, initialValue, initialEasingFunction);
                return result;
            }

            CompositionSpriteShape CreateSpriteShape(CompositionGeometry geometry, Matrix3x2 transformMatrix)
            {
                var result = _c.CreateSpriteShape(geometry);
                result.TransformMatrix = transformMatrix;
                return result;
            }

            CompositionSpriteShape CreateSpriteShape(CompositionGeometry geometry, Matrix3x2 transformMatrix, CompositionBrush fillBrush)
            {
                var result = _c.CreateSpriteShape(geometry);
                result.TransformMatrix = transformMatrix;
                result.FillBrush = fillBrush;
                return result;
            }

            AnimationController AnimationController_0()
            {
                if (_animationController_0 != null) { return _animationController_0; }
                var result = _animationController_0 = _c.CreateAnimationController();
                result.Pause();
                BindProperty(_animationController_0, "Progress", "_.Progress", "_", _root);
                return result;
            }

            // - - Layer aggregator
            // ShapeGroup: Rectangle 1 Offset:<-2.5, -4>
            CompositionColorBrush ColorBrush_SemiTransparentBlack()
            {
                return _c.CreateColorBrush(Color.FromArgb(0x4C, 0x00, 0x00, 0x00));
            }

            CompositionColorBrush ColorBrush_White()
            {
                return (_colorBrush_White == null)
                    ? _colorBrush_White = _c.CreateColorBrush(Color.FromArgb(0xFF, 0xFF, 0xFF, 0xFF))
                    : _colorBrush_White;
            }

            // Layer aggregator
            CompositionContainerShape ContainerShape_0()
            {
                if (_containerShape_0 != null) { return _containerShape_0; }
                var result = _containerShape_0 = _c.CreateContainerShape();
                result.CenterPoint = new Vector2(-2.5F, -4F);
                result.Offset = new Vector2(322.5F, 324F);
                // ShapeGroup: Rectangle 1 Offset:<-2.5, -4>
                result.Shapes.Add(SpriteShape_0());
                return result;
            }

            // Layer aggregator
            CompositionContainerShape ContainerShape_1()
            {
                if (_containerShape_1 != null) { return _containerShape_1; }
                var result = _containerShape_1 = _c.CreateContainerShape();
                result.CenterPoint = new Vector2(15.75F, 4.75F);
                result.Offset = new Vector2(304.25F, 315.25F);
                // ShapeGroup: Ellipse 1 Offset:<15.75, 4.75>
                result.Shapes.Add(SpriteShape_1());
                return result;
            }

            // Layer aggregator
            // Layer: Shape Layer 4
            CompositionContainerShape ContainerShape_2()
            {
                if (_containerShape_2 != null) { return _containerShape_2; }
                var result = _containerShape_2 = _c.CreateContainerShape();
                result.Scale = new Vector2(0F, 0F);
                result.Shapes.Add(ContainerShape_3());
                return result;
            }

            // - Layer aggregator
            // Layer: Shape Layer 4
            CompositionContainerShape ContainerShape_3()
            {
                if (_containerShape_3 != null) { return _containerShape_3; }
                var result = _containerShape_3 = _c.CreateContainerShape();
                result.CenterPoint = new Vector2(15.75F, 4.75F);
                result.Offset = new Vector2(304.25F, 315.25F);
                // ShapeGroup: Ellipse 1 Offset:<15.75, 4.75>
                result.Shapes.Add(SpriteShape_2());
                return result;
            }

            // Ellipse Path 1.EllipseGeometry
            CompositionEllipseGeometry Ellipse_84p25_0()
            {
                if (_ellipse_84p25_0 != null) { return _ellipse_84p25_0; }
                var result = _ellipse_84p25_0 = _c.CreateEllipseGeometry();
                var propertySet = result.Properties;
                propertySet.InsertScalar("TEnd", 0F);
                propertySet.InsertScalar("TStart", 0F);
                result.Radius = new Vector2(84.25F, 84.25F);
                BindProperty(_ellipse_84p25_0, "TrimStart", "Min(my.TStart,my.TEnd)", "my", _ellipse_84p25_0);
                BindProperty(_ellipse_84p25_0, "TrimEnd", "Max(my.TStart,my.TEnd)", "my", _ellipse_84p25_0);
                return result;
            }

            // Ellipse Path 1.EllipseGeometry
            CompositionEllipseGeometry Ellipse_84p25_1()
            {
                if (_ellipse_84p25_1 != null) { return _ellipse_84p25_1; }
                var result = _ellipse_84p25_1 = _c.CreateEllipseGeometry();
                var propertySet = result.Properties;
                propertySet.InsertScalar("TEnd", 0F);
                propertySet.InsertScalar("TStart", 0F);
                result.Radius = new Vector2(84.25F, 84.25F);
                BindProperty(_ellipse_84p25_1, "TrimStart", "Min(my.TStart,my.TEnd)", "my", _ellipse_84p25_1);
                BindProperty(_ellipse_84p25_1, "TrimEnd", "Max(my.TStart,my.TEnd)", "my", _ellipse_84p25_1);
                return result;
            }

            // Rectangle Path 1.RectangleGeometry
            CompositionRoundedRectangleGeometry RoundedRectangle_291()
            {
                if (_roundedRectangle_291 != null) { return _roundedRectangle_291; }
                var result = _roundedRectangle_291 = _c.CreateRoundedRectangleGeometry();
                var propertySet = result.Properties;
                propertySet.InsertScalar("Roundness", 25F);
                result.CornerRadius = new Vector2(0F, 0F);
                result.Offset = new Vector2(-145.5F, -145.5F);
                result.Size = new Vector2(291F, 291F);
                BindProperty(_roundedRectangle_291, "CornerRadius", "Vector2(Min(my.Roundness,145.5),Min(my.Roundness,145.5))", "my", _roundedRectangle_291);
                return result;
            }

            // - Layer aggregator
            // Rectangle Path 1
            CompositionSpriteShape SpriteShape_0()
            {
                // Offset:<-2.5, -4>
                var geometry = RoundedRectangle_291();
                var result = CreateSpriteShape(geometry, new Matrix3x2(1F, 0F, 0F, 1F, -2.5F, -4F), ColorBrush_SemiTransparentBlack());;
                return result;
            }

            // - Layer aggregator
            // Ellipse Path 1
            CompositionSpriteShape SpriteShape_1()
            {
                // Offset:<15.75, 4.75>
                var result = CreateSpriteShape(Ellipse_84p25_0(), new Matrix3x2(1F, 0F, 0F, 1F, 15.75F, 4.75F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeDashCap = CompositionStrokeCap.Round;
                result.StrokeStartCap = CompositionStrokeCap.Round;
                result.StrokeEndCap = CompositionStrokeCap.Round;
                result.StrokeThickness = 26F;
                return result;
            }

            // - - Layer aggregator
            // - Layer: Shape Layer 4
            // Ellipse Path 1
            CompositionSpriteShape SpriteShape_2()
            {
                // Offset:<15.75, 4.75>
                var result = CreateSpriteShape(Ellipse_84p25_1(), new Matrix3x2(1F, 0F, 0F, 1F, 15.75F, 4.75F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeDashCap = CompositionStrokeCap.Round;
                result.StrokeStartCap = CompositionStrokeCap.Round;
                result.StrokeEndCap = CompositionStrokeCap.Round;
                result.StrokeThickness = 26F;
                return result;
            }

            // The root of the composition.
            ContainerVisual Root()
            {
                if (_root != null) { return _root; }
                var result = _root = _c.CreateContainerVisual();
                var propertySet = result.Properties;
                propertySet.InsertScalar("Progress", 0F);
                // Layer aggregator
                result.Children.InsertAtTop(ShapeVisual_0());
                return result;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_0()
            {
                return (_cubicBezierEasingFunction_0 == null)
                    ? _cubicBezierEasingFunction_0 = _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0F, 1F))
                    : _cubicBezierEasingFunction_0;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_1()
            {
                return (_cubicBezierEasingFunction_1 == null)
                    ? _cubicBezierEasingFunction_1 = _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0F), new Vector2(0F, 1F))
                    : _cubicBezierEasingFunction_1;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_2()
            {
                return (_cubicBezierEasingFunction_2 == null)
                    ? _cubicBezierEasingFunction_2 = _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0.833000004F, 1F))
                    : _cubicBezierEasingFunction_2;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_3()
            {
                return (_cubicBezierEasingFunction_3 == null)
                    ? _cubicBezierEasingFunction_3 = _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0F), new Vector2(0.833000004F, 1F))
                    : _cubicBezierEasingFunction_3;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_4()
            {
                return (_cubicBezierEasingFunction_4 == null)
                    ? _cubicBezierEasingFunction_4 = _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0.166999996F), new Vector2(0.833000004F, 0.833000004F))
                    : _cubicBezierEasingFunction_4;
            }

            // - Layer aggregator
            // Rotation
            ScalarKeyFrameAnimation RotationAngleInDegreesScalarAnimation_45_to_135()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 45F, HoldThenStepEasingFunction());
                // Frame 60.
                result.InsertKeyFrame(0.333333343F, 90F, CubicBezierEasingFunction_0());
                // Frame 90.
                result.InsertKeyFrame(0.5F, 90F, CubicBezierEasingFunction_1());
                // Frame 150.
                result.InsertKeyFrame(0.833333313F, 135F, CubicBezierEasingFunction_0());
                return result;
            }

            // - Layer aggregator
            // Rotation
            ScalarKeyFrameAnimation RotationAngleInDegreesScalarAnimation_180_to_900_0()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 180F, HoldThenStepEasingFunction());
                // Frame 90.
                result.InsertKeyFrame(0.5F, 900F, CubicBezierEasingFunction_4());
                return result;
            }

            // - - Layer aggregator
            // - Layer: Shape Layer 4
            // Rotation
            ScalarKeyFrameAnimation RotationAngleInDegreesScalarAnimation_180_to_900_1()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 180F, StepThenHoldEasingFunction());
                // Frame 90.
                result.InsertKeyFrame(0.5F, 180F, HoldThenStepEasingFunction());
                // Frame 180.
                result.InsertKeyFrame(1F, 900F, CubicBezierEasingFunction_4());
                return result;
            }

            // Rectangle Path 1.RectangleGeometry
            // Roundness
            ScalarKeyFrameAnimation RoundnessScalarAnimation_25_to_25()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 25F, HoldThenStepEasingFunction());
                // Frame 60.
                result.InsertKeyFrame(0.333333343F, 125F, CubicBezierEasingFunction_0());
                // Frame 90.
                result.InsertKeyFrame(0.5F, 125F, CubicBezierEasingFunction_1());
                // Frame 150.
                result.InsertKeyFrame(0.833333313F, 25F, CubicBezierEasingFunction_0());
                return result;
            }

            // Scale
            ScalarKeyFrameAnimation ScalarAnimation_0p85_to_1()
            {
                // Frame 0.
                if (_scalarAnimation_0p85_to_1 != null) { return _scalarAnimation_0p85_to_1; }
                var result = _scalarAnimation_0p85_to_1 = CreateScalarKeyFrameAnimation(0F, 0.850000024F, StepThenHoldEasingFunction());
                // Frame 100.
                result.InsertKeyFrame(0.555555582F, 0.850000024F, HoldThenStepEasingFunction());
                // Frame 120.
                result.InsertKeyFrame(0.666666687F, 1F, CubicBezierEasingFunction_0());
                return result;
            }

            // Scale
            ScalarKeyFrameAnimation ScalarAnimation_1_to_1_0()
            {
                // Frame 0.
                if (_scalarAnimation_1_to_1_0 != null) { return _scalarAnimation_1_to_1_0; }
                var result = _scalarAnimation_1_to_1_0 = CreateScalarKeyFrameAnimation(0F, 1F, StepThenHoldEasingFunction());
                // Frame 10.
                result.InsertKeyFrame(0.055555556F, 1F, HoldThenStepEasingFunction());
                // Frame 30.
                result.InsertKeyFrame(0.166666672F, 0.75F, CubicBezierEasingFunction_0());
                // Frame 90.
                result.InsertKeyFrame(0.5F, 0.75F, _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0F), new Vector2(0.666999996F, 1F)));
                // Frame 110.
                result.InsertKeyFrame(0.611111104F, 1F, _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0.666999996F, 1F)));
                return result;
            }

            // Scale
            ScalarKeyFrameAnimation ScalarAnimation_1_to_1_1()
            {
                // Frame 0.
                if (_scalarAnimation_1_to_1_1 != null) { return _scalarAnimation_1_to_1_1; }
                var result = _scalarAnimation_1_to_1_1 = CreateScalarKeyFrameAnimation(0F, 1F, HoldThenStepEasingFunction());
                // Frame 20.
                result.InsertKeyFrame(0.111111112F, 0.850000024F, _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0.833000004F, 0.833000004F)));
                // Frame 90.
                result.InsertKeyFrame(0.5F, 0.850000024F, _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0F), new Vector2(0.460000008F, 1F)));
                // Frame 110.
                result.InsertKeyFrame(0.611111104F, 1F, CubicBezierEasingFunction_0());
                return result;
            }

            // Ellipse Path 1.EllipseGeometry
            // TEnd
            ScalarKeyFrameAnimation TEndScalarAnimation_0_to_0p5_0()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 60.
                result.InsertKeyFrame(0.333333343F, 0F, HoldThenStepEasingFunction());
                // Frame 90.
                result.InsertKeyFrame(0.5F, 0.5F, CubicBezierEasingFunction_2());
                return result;
            }

            // Ellipse Path 1.EllipseGeometry
            // TEnd
            ScalarKeyFrameAnimation TEndScalarAnimation_0_to_0p5_1()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 150.
                result.InsertKeyFrame(0.833333313F, 0F, HoldThenStepEasingFunction());
                // Frame 180.
                result.InsertKeyFrame(1F, 0.5F, CubicBezierEasingFunction_2());
                return result;
            }

            // Ellipse Path 1.EllipseGeometry
            // TrimOffset
            ScalarKeyFrameAnimation TrimOffsetScalarAnimation_0_to_0p5_0()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 30.
                result.InsertKeyFrame(0.166666672F, 0F, HoldThenStepEasingFunction());
                // Frame 60.
                result.InsertKeyFrame(0.333333343F, 0.5F, CubicBezierEasingFunction_4());
                return result;
            }

            // Ellipse Path 1.EllipseGeometry
            // TrimOffset
            ScalarKeyFrameAnimation TrimOffsetScalarAnimation_0_to_0p5_1()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 120.
                result.InsertKeyFrame(0.666666687F, 0F, HoldThenStepEasingFunction());
                // Frame 150.
                result.InsertKeyFrame(0.833333313F, 0.5F, CubicBezierEasingFunction_4());
                return result;
            }

            // Ellipse Path 1.EllipseGeometry
            // TStart
            ScalarKeyFrameAnimation TStartScalarAnimation_0_to_0p5_0()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0F, HoldThenStepEasingFunction());
                // Frame 30.
                result.InsertKeyFrame(0.166666672F, 0.5F, CubicBezierEasingFunction_0());
                // Frame 60.
                result.InsertKeyFrame(0.333333343F, 0.5F, CubicBezierEasingFunction_2());
                // Frame 90.
                result.InsertKeyFrame(0.5F, 0.5F, CubicBezierEasingFunction_3());
                return result;
            }

            // Ellipse Path 1.EllipseGeometry
            // TStart
            ScalarKeyFrameAnimation TStartScalarAnimation_0_to_0p5_1()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 90.
                result.InsertKeyFrame(0.5F, 0F, HoldThenStepEasingFunction());
                // Frame 120.
                result.InsertKeyFrame(0.666666687F, 0.5F, CubicBezierEasingFunction_0());
                // Frame 150.
                result.InsertKeyFrame(0.833333313F, 0.5F, CubicBezierEasingFunction_2());
                // Frame 180.
                result.InsertKeyFrame(1F, 0.5F, CubicBezierEasingFunction_3());
                return result;
            }

            // Layer aggregator
            ShapeVisual ShapeVisual_0()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(640F, 640F);
                var shapes = result.Shapes;
                shapes.Add(ContainerShape_0());
                shapes.Add(ContainerShape_1());
                // Layer: Shape Layer 4
                shapes.Add(ContainerShape_2());
                return result;
            }

            StepEasingFunction HoldThenStepEasingFunction()
            {
                if (_holdThenStepEasingFunction != null) { return _holdThenStepEasingFunction; }
                var result = _holdThenStepEasingFunction = _c.CreateStepEasingFunction();
                result.IsFinalStepSingleFrame = true;
                return result;
            }

            StepEasingFunction StepThenHoldEasingFunction()
            {
                if (_stepThenHoldEasingFunction != null) { return _stepThenHoldEasingFunction; }
                var result = _stepThenHoldEasingFunction = _c.CreateStepEasingFunction();
                result.IsInitialStepSingleFrame = true;
                return result;
            }

            // - Layer aggregator
            // Layer: Shape Layer 4
            Vector2KeyFrameAnimation ShapeVisibilityAnimation()
            {
                // Frame 0.
                var result = CreateVector2KeyFrameAnimation(0F, new Vector2(0F, 0F), HoldThenStepEasingFunction());
                // Frame 90.
                result.InsertKeyFrame(0.5F, new Vector2(1F, 1F), HoldThenStepEasingFunction());
                return result;
            }

            internal LoadingSprite_AnimatedVisual(
                Compositor compositor
                )
            {
                _c = compositor;
                _reusableExpressionAnimation = compositor.CreateExpressionAnimation();
                Root();
            }

            public Visual RootVisual => _root;
            public TimeSpan Duration => TimeSpan.FromTicks(c_durationTicks);
            public Vector2 Size => new Vector2(640F, 640F);
            void IDisposable.Dispose() => _root?.Dispose();

            public void CreateAnimations()
            {
                _containerShape_0.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_45_to_135(), AnimationController_0());
                _containerShape_0.StartAnimation("Scale.X", ScalarAnimation_1_to_1_0(), AnimationController_0());
                _containerShape_0.StartAnimation("Scale.Y", ScalarAnimation_1_to_1_0(), AnimationController_0());
                _containerShape_1.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_180_to_900_0(), AnimationController_0());
                _containerShape_1.StartAnimation("Scale.X", ScalarAnimation_1_to_1_1(), AnimationController_0());
                _containerShape_1.StartAnimation("Scale.Y", ScalarAnimation_1_to_1_1(), AnimationController_0());
                _containerShape_2.StartAnimation("Scale", ShapeVisibilityAnimation(), AnimationController_0());
                _containerShape_3.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_180_to_900_1(), AnimationController_0());
                _containerShape_3.StartAnimation("Scale.X", ScalarAnimation_0p85_to_1(), AnimationController_0());
                _containerShape_3.StartAnimation("Scale.Y", ScalarAnimation_0p85_to_1(), AnimationController_0());
                _ellipse_84p25_0.StartAnimation("TStart", TStartScalarAnimation_0_to_0p5_0(), AnimationController_0());
                _ellipse_84p25_0.StartAnimation("TEnd", TEndScalarAnimation_0_to_0p5_0(), AnimationController_0());
                _ellipse_84p25_0.StartAnimation("TrimOffset", TrimOffsetScalarAnimation_0_to_0p5_0(), AnimationController_0());
                _ellipse_84p25_1.StartAnimation("TStart", TStartScalarAnimation_0_to_0p5_1(), AnimationController_0());
                _ellipse_84p25_1.StartAnimation("TEnd", TEndScalarAnimation_0_to_0p5_1(), AnimationController_0());
                _ellipse_84p25_1.StartAnimation("TrimOffset", TrimOffsetScalarAnimation_0_to_0p5_1(), AnimationController_0());
                _roundedRectangle_291.StartAnimation("Roundness", RoundnessScalarAnimation_25_to_25(), AnimationController_0());
            }

            public void DestroyAnimations()
            {
                _containerShape_0.StopAnimation("RotationAngleInDegrees");
                _containerShape_0.StopAnimation("Scale.X");
                _containerShape_0.StopAnimation("Scale.Y");
                _containerShape_1.StopAnimation("RotationAngleInDegrees");
                _containerShape_1.StopAnimation("Scale.X");
                _containerShape_1.StopAnimation("Scale.Y");
                _containerShape_2.StopAnimation("Scale");
                _containerShape_3.StopAnimation("RotationAngleInDegrees");
                _containerShape_3.StopAnimation("Scale.X");
                _containerShape_3.StopAnimation("Scale.Y");
                _ellipse_84p25_0.StopAnimation("TStart");
                _ellipse_84p25_0.StopAnimation("TEnd");
                _ellipse_84p25_0.StopAnimation("TrimOffset");
                _ellipse_84p25_1.StopAnimation("TStart");
                _ellipse_84p25_1.StopAnimation("TEnd");
                _ellipse_84p25_1.StopAnimation("TrimOffset");
                _roundedRectangle_291.StopAnimation("Roundness");
            }

        }
    }
}
