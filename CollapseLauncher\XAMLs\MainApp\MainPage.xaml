﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable Xaml.RedundantResource  -->
<Page x:Class="CollapseLauncher.MainPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:animatedvisuals="using:Microsoft.UI.Xaml.Controls.AnimatedVisuals"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:extension="using:CollapseLauncher.Extension"
      xmlns:helper="using:Hi3Helper"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      Unloaded="Page_Unloaded"
      mc:Ignorable="d">
    <Page.Resources>
        <ThemeShadow x:Name="SharedShadow" />
        <Thickness x:Key="NavigationViewBorderThickness">0</Thickness>
        <Thickness x:Key="NavigationViewContentGridBorderThickness">0</Thickness>
        <ListView x:Name="KeyboardHandler" />
    </Page.Resources>
    <Grid x:Name="MainPageGrid"
          Background="{ThemeResource WindowBackground}">
        <Grid x:Name="BackgroundNewBackGrid" />
        <Grid x:Name="BackgroundNewMediaPlayerGrid">
            <MediaPlayerElement HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Stretch="UniformToFill"
                                Tag="MediaPlayer" />
        </Grid>
        <Grid x:Name="BackgroundAcrylicMask"
              Background="{ThemeResource BackgroundImageMaskAcrylicBrush}"
              Opacity="0" />
        <Grid x:Name="BackgroundOverlayTitleBar"
              Height="64"
              HorizontalAlignment="Stretch"
              VerticalAlignment="Top"
              Background="{ThemeResource BackgroundOverlayTitleBarBrush}" />
        <Border x:Name="NavViewPaneBackground"
                Width="48"
                Margin="0"
                HorizontalAlignment="Left"
                VerticalAlignment="Stretch"
                Background="{ThemeResource NavigationViewUnfoldedPaneBackground}"
                BorderBrush="{ThemeResource AccentControlElevationBorderBrush}"
                BorderThickness="0"
                CornerRadius="0,8,8,0"
                Opacity="0"
                Shadow="{ThemeResource SharedShadow}"
                Translation="-48,0,0" />
        <NavigationView x:Name="NavigationViewControl"
                        BackRequested="NavigationViewControl_BackRequested"
                        Background="Transparent"
                        IsBackButtonVisible="Visible"
                        IsBackEnabled="{x:Bind LauncherFrame.CanGoBack, Mode=OneWay}"
                        IsPaneOpen="False"
                        IsSettingsVisible="true"
                        ItemInvoked="NavView_ItemInvoked"
                        Loaded="NavView_Loaded"
                        OpenPaneLength="300"
                        PaneClosing="NavigationPanelClosing_Event"
                        PaneDisplayMode="LeftCompact"
                        PaneOpening="NavigationPanelOpening_Event"
                        PaneTitle="{x:Bind helper:Locale.Lang._MainPage.NavigationMenu}"
                        Visibility="Visible">
            <Grid Background="Transparent">
                <Frame x:Name="LauncherFrame">
                    <Frame.ContentTransitions>
                        <NavigationThemeTransition />
                    </Frame.ContentTransitions>
                </Frame>
            </Grid>
        </NavigationView>
        <Grid Height="47"
              HorizontalAlignment="Stretch"
              VerticalAlignment="Top">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="152" />
                <ColumnDefinition Width="0" />
                <ColumnDefinition x:Name="GridBG_RegionMargin" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition />
                <ColumnDefinition Width="18" />
                <ColumnDefinition Width="32" />
                <ColumnDefinition Width="100" />
            </Grid.ColumnDefinitions>
            <Grid x:Name="GridBG_IconGrid"
                  Grid.Column="0"
                  Grid.ColumnSpan="2" />
            <Grid x:Name="TitleBarDrag1"
                  Grid.Column="1"
                  Grid.ColumnSpan="2" />
            <Grid x:Name="TitleBarDrag2"
                  Grid.Column="4"
                  Grid.ColumnSpan="2" />
            <Grid Name="GridBG_RegionGrid"
                  Grid.Column="2"
                  Grid.ColumnSpan="3"
                  Height="47"
                  Margin="0,1,0,0"
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center">
                <StackPanel Name="GridBG_RegionInner"
                            Grid.Column="0"
                            Margin="0,0"
                            VerticalAlignment="Center"
                            CanDrag="True"
                            Orientation="Horizontal">
                    <Grid VerticalAlignment="Top"
                          Canvas.ZIndex="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Grid x:Name="ComboBoxGameGridShadow"
                              CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(ComboBoxGameGridShadow)}"
                              Shadow="{ThemeResource SharedShadow}"
                              Translation="0,0,12" />
                        <Grid x:Name="ComboBoxGameGrid"
                              Grid.Column="0"
                              CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(ComboBoxGameGrid)}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <ComboBox x:Name="ComboBoxGameCategory"
                                      MinWidth="167"
                                      Canvas.ZIndex="2"
                                      CornerRadius="15,0,0,15"
                                      DropDownClosed="GameComboBox_OnDropDownClosed"
                                      DropDownOpened="GameComboBox_OnDropDownOpened"
                                      PlaceholderText="{x:Bind helper:Locale.Lang._GameClientTitles['Honkai Impact 3rd']}"
                                      SelectionChanged="SetGameCategoryChange"
                                      Style="{ThemeResource AcrylicComboBoxStyle}" />
                            <ComboBox x:Name="ComboBoxGameRegion"
                                      Grid.Column="1"
                                      MinWidth="148"
                                      Canvas.ZIndex="2"
                                      CornerRadius="0,15,15,0"
                                      DropDownClosed="GameComboBox_OnDropDownClosed"
                                      DropDownOpened="GameComboBox_OnDropDownOpened"
                                      PlaceholderText="{x:Bind helper:Locale.Lang._GameClientRegions['Southeast Asia']}"
                                      SelectionChanged="EnableRegionChangeButton"
                                      Style="{ThemeResource AcrylicComboBoxStyle}" />
                        </Grid>
                        <Grid x:Name="ChangeGameBtnGridShadow"
                              Grid.Column="1"
                              Margin="8,0,0,0"
                              CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(ChangeGameBtnGridShadow)}"
                              Shadow="{ThemeResource SharedShadow}"
                              Translation="0,0,12" />
                        <Grid x:Name="ChangeGameBtnGrid"
                              Grid.Column="1"
                              Margin="8,0,0,0"
                              CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(ChangeGameBtnGrid)}">
                            <Button x:Name="ChangeRegionConfirmBtnNoWarning"
                                    Click="ChangeRegionNoWarning"
                                    CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(ChangeRegionConfirmBtnNoWarning)}"
                                    IsEnabled="False"
                                    Style="{ThemeResource NewAccentButtonStyle}"
                                    Visibility="Collapsed">
                                <Button.Content>
                                    <Grid Margin="0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Margin="4,0,0,0"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"
                                                   FontWeight="SemiBold"
                                                   Text="{x:Bind helper:Locale.Lang._Misc.Change}" />
                                        <Grid Grid.Column="1"
                                              Margin="4,0,0,0"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              RenderTransformOrigin="0.5, 0.5">
                                            <Grid.RenderTransform>
                                                <RotateTransform Angle="-90" />
                                            </Grid.RenderTransform>
                                            <AnimatedIcon Width="16"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center">
                                                <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                                <AnimatedIcon.FallbackIconSource>
                                                    <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                                    Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                                    Glyph="&#xE70D;" />
                                                </AnimatedIcon.FallbackIconSource>
                                            </AnimatedIcon>
                                        </Grid>
                                    </Grid>
                                </Button.Content>
                            </Button>
                            <Button x:Name="ChangeRegionConfirmBtn"
                                    CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(ChangeRegionConfirmBtn)}"
                                    IsEnabled="False"
                                    Style="{ThemeResource NewAccentButtonStyle}">
                                <Button.Content>
                                    <Grid Margin="0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Margin="4,0,0,0"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"
                                                   FontWeight="SemiBold"
                                                   Text="{x:Bind helper:Locale.Lang._Misc.Change}" />
                                        <Grid Grid.Column="1"
                                              Margin="4,0,0,0"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              RenderTransformOrigin="0.5, 0.5">
                                            <Grid.RenderTransform>
                                                <RotateTransform Angle="-90" />
                                            </Grid.RenderTransform>
                                            <AnimatedIcon Width="16"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center">
                                                <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                                <AnimatedIcon.FallbackIconSource>
                                                    <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                                    Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                                    Glyph="&#xE70D;" />
                                                </AnimatedIcon.FallbackIconSource>
                                            </AnimatedIcon>
                                        </Grid>
                                    </Grid>
                                </Button.Content>
                                <Button.Flyout>
                                    <Flyout>
                                        <StackPanel>
                                            <TextBlock Width="200"
                                                       Margin="0,0,0,12"
                                                       FontWeight="SemiBold"
                                                       Text="{x:Bind helper:Locale.Lang._MainPage.RegionChangeConfirm}"
                                                       TextWrapping="Wrap" />
                                            <StackPanel x:Name="ChangeRegionWarning"
                                                        Visibility="Collapsed">
                                                <TextBlock Width="200"
                                                           FontSize="14"
                                                           FontWeight="SemiBold"
                                                           Style="{ThemeResource BaseTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._MainPage.RegionChangeWarnTitle}" />
                                                <TextBlock x:Name="ChangeRegionWarningText"
                                                           Width="200"
                                                           Margin="0,0,0,12"
                                                           FontSize="12"
                                                           FontWeight="Normal"
                                                           Style="{ThemeResource BaseTextBlockStyle}"
                                                           Text="" />
                                            </StackPanel>
                                            <Button x:Name="ChangeRegionBtn"
                                                    HorizontalAlignment="Stretch"
                                                    HorizontalContentAlignment="Stretch"
                                                    Click="ChangeRegion"
                                                    CornerRadius="16"
                                                    Style="{ThemeResource AccentButtonStyle}">
                                                <Button.Content>
                                                    <Grid Margin="-36,-8"
                                                          Padding="36,8">
                                                        <ProgressBar x:Name="ChangeRegionConfirmProgressBar"
                                                                     Margin="-36,0,-36,-28"
                                                                     IsIndeterminate="True"
                                                                     ShowError="False"
                                                                     ShowPaused="False"
                                                                     Visibility="Collapsed" />
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition />
                                                                <ColumnDefinition Width="Auto" />
                                                            </Grid.ColumnDefinitions>
                                                            <Grid Grid.Column="1"
                                                                  Margin="-0,-8,-4,-8"
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"
                                                                  RenderTransformOrigin="0.5, 0.5">
                                                                <Grid.RenderTransform>
                                                                    <RotateTransform Angle="-180" />
                                                                </Grid.RenderTransform>
                                                                <AnimatedIcon x:Name="SearchAnimatedIcon"
                                                                              Width="18"
                                                                              Height="18">
                                                                    <AnimatedIcon.Source>
                                                                        <animatedvisuals:AnimatedBackVisualSource />
                                                                    </AnimatedIcon.Source>
                                                                    <AnimatedIcon.FallbackIconSource>
                                                                        <SymbolIconSource Symbol="Find" />
                                                                    </AnimatedIcon.FallbackIconSource>
                                                                </AnimatedIcon>
                                                            </Grid>
                                                            <TextBlock Grid.Column="0"
                                                                       HorizontalAlignment="Left"
                                                                       VerticalAlignment="Center"
                                                                       FontWeight="SemiBold"
                                                                       Text="{x:Bind helper:Locale.Lang._MainPage.RegionChangeConfirmBtn}" />
                                                        </Grid>
                                                    </Grid>
                                                </Button.Content>
                                            </Button>
                                        </StackPanel>
                                    </Flyout>
                                </Button.Flyout>
                            </Button>
                        </Grid>
                    </Grid>
                </StackPanel>
            </Grid>
            <Grid Name="GridBG_NotifBtn"
                  Grid.Column="6"
                  Width="32"
                  Height="32"
                  Margin="0,1,0,0"
                  HorizontalAlignment="Right"
                  VerticalAlignment="Stretch"
                  CornerRadius="0,0,0,0">
                <ToggleButton x:Name="ToggleNotificationPanelBtn"
                              Padding="0"
                              HorizontalAlignment="Stretch"
                              VerticalAlignment="Stretch"
                              Click="ToggleNotificationPanelBtnClick"
                              Shadow="{ThemeResource SharedShadow}"
                              Style="{ThemeResource AcrylicToggleButtonStyle}">
                    <FontIcon FontFamily="{ThemeResource FontAwesome}"
                              FontSize="18"
                              Glyph="&#xf0f3;" />
                </ToggleButton>
                <InfoBadge x:Name="NewNotificationCountBadge"
                           Margin="0,-4,-4,0"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Top"
                           Style="{StaticResource AttentionValueInfoBadgeStyle}"
                           Visibility="Collapsed"
                           Value="0" />
            </Grid>
            <Grid Name="GridBG_WindowBtn"
                  Grid.Column="7"
                  Height="31"
                  HorizontalAlignment="Stretch"
                  VerticalAlignment="Top"
                  CornerRadius="0,0,0,0" />
        </Grid>
        <Button x:Name="GridBG_Icon"
                Grid.Column="0"
                Height="33"
                Margin="58,8,0,0"
                VerticalAlignment="Top"
                Click="GridBG_Icon_Click"
                PointerEntered="GridBG_Icon_PointerEntered"
                PointerExited="GridBG_Icon_PointerExited"
                Shadow="{ThemeResource SharedShadow}"
                Style="{ThemeResource AcrylicButtonStyle}"
                Translation="0,0,8">
            <Button.Transitions>
                <RepositionThemeTransition />
            </Button.Transitions>
            <Button.ContentTransitions>
                <RepositionThemeTransition />
            </Button.ContentTransitions>
            <StackPanel Margin="0,0,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                <!--  ReSharper disable once Xaml.InvalidResourceType  -->
                <Image x:Name="GridBG_IconImg"
                       Width="Auto"
                       Margin="-2,-2,0,-2"
                       VerticalAlignment="Stretch"
                       Opacity="0.8"
                       Source="{ThemeResource AppLogo}">
                    <Image.OpacityTransition>
                        <ScalarTransition />
                    </Image.OpacityTransition>
                </Image>
                <StackPanel Name="GridBG_IconTitle"
                            Width="0"
                            Margin="6,-8,0,0"
                            VerticalAlignment="Center"
                            Opacity="0">
                    <StackPanel.OpacityTransition>
                        <ScalarTransition />
                    </StackPanel.OpacityTransition>
                    <TextBlock Margin="0,0,0,0"
                               HorizontalAlignment="Left"
                               FontSize="10"
                               FontWeight="Medium"
                               Text="Collapse"
                               Visibility="Visible" />
                    <TextBlock Margin="0,-3,0,-8"
                               HorizontalAlignment="Left"
                               FontSize="12"
                               FontWeight="Bold"
                               Foreground="{ThemeResource AccentColor}"
                               Text="Launcher"
                               Visibility="Visible" />
                </StackPanel>
                <StackPanel x:Name="PreviewBuildIndicator"
                            Margin="-10,-12,0,0"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Background="{ThemeResource AccentColor}"
                            CornerRadius="3"
                            Visibility="Visible">
                    <StackPanel.Transitions>
                        <RepositionThemeTransition />
                    </StackPanel.Transitions>
                    <TextBlock x:Name="VersionNumberIndicator"
                               Padding="3,0,3,0"
                               VerticalAlignment="Center"
                               FontSize="8"
                               FontWeight="Bold"
                               Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                               Text="PRE" />
                </StackPanel>
            </StackPanel>
        </Button>
        <Grid x:Name="NavViewPaneBackgroundHoverArea"
              Width="48"
              HorizontalAlignment="Left"
              VerticalAlignment="Stretch" />
        <Grid x:Name="NotificationLostFocusBackground"
              VerticalAlignment="Stretch"
              Opacity="0"
              PointerPressed="NotificationContainerBackground_PointerPressed"
              Visibility="Collapsed">
            <Grid.OpacityTransition>
                <ScalarTransition />
            </Grid.OpacityTransition>
            <Grid Background="{ThemeResource NotificationLostFocusBackgroundGradientBrush}"
                  Opacity="0.00000001" />
            <Grid Background="{ThemeResource NotificationLostFocusBackgroundGradientBrush}" />
        </Grid>
        <Grid x:Name="NotificationPanel"
              Margin="0,48,-700,0"
              HorizontalAlignment="Right"
              Background="{ThemeResource NotificationPanelBrush}"
              CornerRadius="8,0,0,8"
              Shadow="{ThemeResource SharedShadow}">
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Grid.Transitions>
                <RepositionThemeTransition />
            </Grid.Transitions>
            <Grid>
                <ScrollViewer x:Name="NotificationPanelScrollViewer"
                              CanContentRenderOutsideBounds="True"
                              HorizontalScrollBarVisibility="Disabled">
                    <Grid MinWidth="608"
                          MinHeight="200">
                        <StackPanel x:Name="NoNotificationIndicator"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Orientation="Vertical">
                            <StackPanel.OpacityTransition>
                                <ScalarTransition />
                            </StackPanel.OpacityTransition>
                            <Image Width="200"
                                   Margin="0,0,0,16"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Opacity="1"
                                   Source="ms-appx:///Assets/Images/GameMascot/PaimonSleep-MonoTransparent.png" />
                            <Grid Padding="24,8"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  CornerRadius="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <TextBlock VerticalAlignment="Center"
                                           FontSize="20"
                                           FontWeight="SemiBold"
                                           Opacity="0.50"
                                           Text="{x:Bind helper: Locale.Lang._MainPage.NotifNoNewNotifs}"
                                           TextAlignment="Center" />
                            </Grid>
                        </StackPanel>
                        <StackPanel x:Name="NotificationContainer"
                                    Margin="0,0,0,7">
                            <StackPanel.ChildrenTransitions>
                                <TransitionCollection>
                                    <PopupThemeTransition />
                                    <RepositionThemeTransition />
                                </TransitionCollection>
                            </StackPanel.ChildrenTransitions>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </Grid>
            <Grid x:Name="NotificationPanelClearAllGrid"
                  Grid.Row="1"
                  Padding="8"
                  VerticalAlignment="Bottom"
                  Background="{ThemeResource NotificationPanelBrush}"
                  Visibility="Collapsed">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Button x:Name="NotificationPanelClearAllBtn"
                        Grid.Column="1"
                        HorizontalAlignment="Right"
                        Click="ClearAllNotification"
                        CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(NotificationPanelClearAllBtn)}"
                        Style="{ThemeResource TransparentDefaultButtonStyle}">
                    <StackPanel Margin="4,0"
                                Orientation="Horizontal">
                        <TextBlock Margin="0,-1,8,1"
                                   FontWeight="Medium"
                                   Text="{x:Bind helper: Locale.Lang._MainPage.NotifClearAll}" />
                        <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                  FontSize="16"
                                  Glyph="&#xf2ed;" />
                    </StackPanel>
                </Button>
            </Grid>
        </Grid>
        <Frame x:Name="WebView2Frame"
               x:FieldModifier="internal" />
    </Grid>
</Page>
