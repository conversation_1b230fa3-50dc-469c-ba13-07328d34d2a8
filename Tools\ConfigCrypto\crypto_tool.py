#!/usr/bin/env python3
"""
CollapseLauncher Configuration Crypto Tool

This tool provides encryption and decryption capabilities for 
CollapseLauncher configuration files.
"""

import argparse
import base64
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Union, List

from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend


class CollapseDecryptor:
    """Decryptor for CollapseLauncher encrypted configuration fields."""
    
    def __init__(self, master_key_path: Optional[str] = None):
        """
        Initialize the decryptor.
        
        Args:
            master_key_path: Path to the master key configuration file
        """
        self.private_key = None
        self.master_key_config = None
        
        if master_key_path:
            self.load_master_key(master_key_path)
    
    def load_master_key(self, key_path: str) -> None:
        """
        Load master key from configuration file.

        Args:
            key_path: Path to the master key configuration file
        """
        try:
            with open(key_path, 'r', encoding='utf-8') as f:
                self.master_key_config = json.load(f)

            # Key field is base64 encoded byte array (DER format RSA private key)
            key_base64 = self.master_key_config['Key']
            key_bytes = base64.b64decode(key_base64)

            # Check if it's ServeV3 format (compressed)
            if self._is_serve_v3_data(key_bytes):
                # Decompress ServeV3 data
                key_bytes = self._serve_v3_data(key_bytes)

            # Import DER format RSA private key
            self.private_key = serialization.load_der_private_key(
                key_bytes,
                password=None,
                backend=default_backend()
            )

            print(f"✓ Master key loaded successfully from: {key_path}")
            print(f"  Key size: {self.private_key.key_size} bits")

        except Exception as e:
            raise ValueError(f"Failed to load master key: {e}")

    def _is_serve_v3_data(self, data: bytes) -> bool:
        """
        Check if data is ServeV3 format (same as C# IsServeV3Data).

        Args:
            data: Data to check

        Returns:
            True if ServeV3 format, False otherwise
        """
        if len(data) < 8:
            return False

        # CollapseSignature = 7310310183885631299
        collapse_signature = 7310310183885631299
        signature = int.from_bytes(data[:8], byteorder='little')
        return signature == collapse_signature

    def _serve_v3_data(self, data: bytes) -> bytes:
        """
        Decompress ServeV3 data (simplified version of C# ServeV3Data).

        Args:
            data: ServeV3 format data

        Returns:
            Decompressed data
        """
        if len(data) < 32:
            raise ValueError("ServeV3 data format is corrupted!")

        # Read sizes
        compressed_size = int.from_bytes(data[16:24], byteorder='little')
        decompressed_size = int.from_bytes(data[24:32], byteorder='little')

        # Read attributes
        attrib_number = int.from_bytes(data[8:16], byteorder='little')
        compression_type = attrib_number & 0xFF
        is_use_encryption = (attrib_number >> 8) & 0xFF == 1

        # Extract raw data (skip 32 byte header)
        raw_data = data[32:]

        if is_use_encryption:
            raise NotImplementedError("Encrypted ServeV3 data is not supported yet")

        # Handle compression
        if compression_type == 0:  # None
            return raw_data[:decompressed_size]
        elif compression_type == 1:  # Brotli
            import brotli
            return brotli.decompress(raw_data)
        elif compression_type == 2:  # Zstd
            import zstandard as zstd
            dctx = zstd.ZstdDecompressor()
            return dctx.decompress(raw_data)
        else:
            raise ValueError(f"Unsupported compression type: {compression_type}")


    
    def decrypt_field(self, encrypted_data: str) -> str:
        """
        Decrypt a single encrypted field using ServeV3Data logic.

        Args:
            encrypted_data: Base64 encoded ServeV3 format data

        Returns:
            Decrypted plaintext string
        """
        if not self.private_key:
            raise ValueError("Master key not loaded. Call load_master_key() first.")

        try:
            # The encrypted_data should be base64 encoded ServeV3 format
            if not self._is_base64_valid(encrypted_data):
                return encrypted_data  # Return as-is if not base64

            # Decode base64
            data_bytes = base64.b64decode(encrypted_data)

            # Check if it's ServeV3 format
            if not self._is_serve_v3_data(data_bytes):
                return encrypted_data  # Return as-is if not ServeV3

            # Process ServeV3 data
            decrypted_data = self._serve_v3_data_with_decryption(data_bytes)

            # Convert to string
            return decrypted_data.decode('utf-8')

        except Exception as e:
            raise ValueError(f"Decryption failed: {e}")

    def _is_base64_valid(self, data: str) -> bool:
        """Check if string is valid base64."""
        try:
            base64.b64decode(data, validate=True)
            return True
        except Exception:
            return False

    def _serve_v3_data_with_decryption(self, data: bytes) -> bytes:
        """
        Process ServeV3 data with decryption support.

        Args:
            data: ServeV3 format data

        Returns:
            Processed data
        """
        if len(data) < 32:
            raise ValueError("ServeV3 data format is corrupted!")

        # Read sizes
        compressed_size = int.from_bytes(data[16:24], byteorder='little')
        decompressed_size = int.from_bytes(data[24:32], byteorder='little')

        # Read attributes
        attrib_number = int.from_bytes(data[8:16], byteorder='little')
        compression_type = attrib_number & 0xFF
        is_use_encryption = (attrib_number >> 8) & 0xFF == 1

        # Extract raw data (skip 32 byte header)
        raw_data = data[32:]

        # Handle encryption first
        if is_use_encryption:
            raw_data = self._decrypt_rsa_chunks(raw_data)

        # Then handle compression
        if compression_type == 0:  # None
            return raw_data[:decompressed_size]
        elif compression_type == 1:  # Brotli
            import brotli
            return brotli.decompress(raw_data)
        elif compression_type == 2:  # Zstd
            import zstandard as zstd
            dctx = zstd.ZstdDecompressor()
            return dctx.decompress(raw_data)
        else:
            raise ValueError(f"Unsupported compression type: {compression_type}")

    def _decrypt_rsa_chunks(self, encrypted_data: bytes) -> bytes:
        """
        Decrypt RSA encrypted data in chunks.

        Args:
            encrypted_data: RSA encrypted data

        Returns:
            Decrypted data
        """
        enc_bit_length = self.master_key_config['BitSize']
        decrypted_chunks = []

        offset = 0
        while offset < len(encrypted_data):
            # Extract chunk
            chunk = encrypted_data[offset:offset + enc_bit_length]

            # Decrypt chunk
            decrypted_chunk = self.private_key.decrypt(chunk, padding.PKCS1v15())
            decrypted_chunks.append(decrypted_chunk)

            offset += enc_bit_length

        return b''.join(decrypted_chunks)
    
    def encrypt_field(self, plaintext: str) -> str:
        """
        Encrypt a plaintext string using ServeV3 format.

        Args:
            plaintext: Plain text to encrypt

        Returns:
            Base64 encoded ServeV3 format encrypted data
        """
        if not self.private_key:
            raise ValueError("Master key not loaded. Call load_master_key() first.")

        try:
            # Convert plaintext to bytes
            plaintext_bytes = plaintext.encode('utf-8')

            # Create ServeV3 format encrypted data
            encrypted_data = self._create_serve_v3_data(plaintext_bytes, use_encryption=True, compression_type=1)

            # Encode to base64
            return base64.b64encode(encrypted_data).decode('utf-8')

        except Exception as e:
            raise ValueError(f"Encryption failed: {e}")

    def _create_serve_v3_data(self, data: bytes, use_encryption: bool = True,
                             compression_type: int = 0) -> bytes:
        """
        Create ServeV3 format data with optional encryption and compression.

        Args:
            data: Data to process
            use_encryption: Whether to encrypt the data
            compression_type: 0=None, 1=Brotli, 2=Zstd

        Returns:
            ServeV3 format data
        """
        processed_data = data

        # Apply compression first
        if compression_type == 1:  # Brotli
            import brotli
            processed_data = brotli.compress(processed_data)
        elif compression_type == 2:  # Zstd
            import zstandard as zstd
            cctx = zstd.ZstdCompressor()
            processed_data = cctx.compress(processed_data)

        # Apply encryption
        if use_encryption:
            processed_data = self._encrypt_rsa_chunks(processed_data)

        # Create ServeV3 header
        collapse_signature = 7310310183885631299

        # Attributes: compression_type + (is_use_encryption << 8)
        attributes = compression_type | ((1 if use_encryption else 0) << 8)

        compressed_size = len(processed_data)
        decompressed_size = len(data)

        # Build header (32 bytes)
        header = bytearray(32)
        header[0:8] = collapse_signature.to_bytes(8, byteorder='little')
        header[8:16] = attributes.to_bytes(8, byteorder='little')
        header[16:24] = compressed_size.to_bytes(8, byteorder='little')
        header[24:32] = decompressed_size.to_bytes(8, byteorder='little')

        # Combine header and data
        return bytes(header) + processed_data

    def _encrypt_rsa_chunks(self, data: bytes) -> bytes:
        """
        Encrypt data in RSA chunks.

        Args:
            data: Data to encrypt

        Returns:
            Encrypted data
        """
        public_key = self.private_key.public_key()
        key_size_bytes = self.private_key.key_size // 8
        max_chunk_size = key_size_bytes - 11  # PKCS1v15 padding overhead

        encrypted_chunks = []
        offset = 0

        while offset < len(data):
            # Extract chunk
            chunk = data[offset:offset + max_chunk_size]

            # Encrypt chunk
            encrypted_chunk = public_key.encrypt(chunk, padding.PKCS1v15())
            encrypted_chunks.append(encrypted_chunk)

            offset += max_chunk_size

        return b''.join(encrypted_chunks)


    
    def decrypt_config_field(self, config: Dict[str, Any], field_name: str) -> Optional[str]:
        """
        Decrypt a specific field in a configuration dictionary.
        
        Args:
            config: Configuration dictionary
            field_name: Name of the field to decrypt
            
        Returns:
            Decrypted value or None if field doesn't exist
        """
        if field_name not in config:
            return None
        
        encrypted_value = config[field_name]
        if not isinstance(encrypted_value, str):
            return None
        
        try:
            return self.decrypt_field(encrypted_value)
        except Exception as e:
            print(f"Warning: Failed to decrypt field '{field_name}': {e}")
            return None
    
    def decrypt_config_file(self, input_path: str, output_path: str, 
                           fields_to_decrypt: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Decrypt specified fields in a configuration file.
        
        Args:
            input_path: Path to input configuration file
            output_path: Path to output decrypted file
            fields_to_decrypt: List of field names to decrypt (None for all known fields)
            
        Returns:
            Decrypted configuration dictionary
        """
        # Default fields that are typically encrypted
        if fields_to_decrypt is None:
            fields_to_decrypt = [
                'GameDispatchURL',
                'GameDispatchURLTemplate',
                'GameGatewayURLTemplate',
                'GameGatewayDefault',
                'LauncherSpriteURL',
                'LauncherResourceURL',
                'LauncherPluginURL',
                'LauncherNewsURL',
                'LauncherCPSType',
                'LauncherId',
                'LauncherGameId',
                'LauncherBizName',
                'DispatcherKey',
                'GameDispatchChannelName',
                'GameDispatchArrayURL',
                'LauncherResourceChunksURL'  # This contains nested encrypted fields
            ]
        
        # Load configuration file
        with open(input_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        decrypted_config = config.copy()
        decryption_results = {}
        
        print(f"Decrypting configuration file: {input_path}")
        print("-" * 50)
        
        # Decrypt individual fields
        for field_name in fields_to_decrypt:
            if field_name in config:
                field_value = config[field_name]
                
                if isinstance(field_value, str):
                    # Single string field
                    try:
                        decrypted_value = self.decrypt_field(field_value)
                        decrypted_config[field_name] = decrypted_value
                        decryption_results[field_name] = "✓ Decrypted"
                        print(f"✓ {field_name}: {decrypted_value}")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt")
                
                elif isinstance(field_value, list):
                    # Array of strings
                    try:
                        decrypted_array = []
                        for item in field_value:
                            if isinstance(item, str):
                                decrypted_item = self.decrypt_field(item)
                                decrypted_array.append(decrypted_item)
                            else:
                                decrypted_array.append(item)
                        
                        decrypted_config[field_name] = decrypted_array
                        decryption_results[field_name] = "✓ Decrypted (array)"
                        print(f"✓ {field_name}: {len(decrypted_array)} items decrypted")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt array")
                
                elif isinstance(field_value, dict):
                    # Nested object - decrypt string values
                    try:
                        decrypted_object = {}
                        decrypted_count = 0

                        for key, value in field_value.items():
                            if isinstance(value, str):
                                try:
                                    decrypted_value = self.decrypt_field(value)
                                    decrypted_object[key] = decrypted_value
                                    if decrypted_value != value:  # Only count if actually decrypted
                                        decrypted_count += 1
                                        print(f"  ✓ {field_name}.{key}: {decrypted_value}")
                                    else:
                                        decrypted_object[key] = value
                                except Exception as e:
                                    print(f"  ⚠️ {field_name}.{key}: Failed to decrypt - {e}")
                                    decrypted_object[key] = value
                            else:
                                decrypted_object[key] = value

                        decrypted_config[field_name] = decrypted_object
                        if decrypted_count > 0:
                            decryption_results[field_name] = f"✓ Decrypted ({decrypted_count} fields)"
                            print(f"✓ {field_name}: {decrypted_count} nested fields decrypted")
                        else:
                            decryption_results[field_name] = "○ No encrypted fields found"
                            print(f"○ {field_name}: No encrypted fields found")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt object")
        
        # Save decrypted configuration
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(decrypted_config, f, indent=2, ensure_ascii=False)
        
        print("-" * 50)
        print(f"✓ Decrypted configuration saved to: {output_path}")
        
        return decrypted_config

    def encrypt_config_file(self, input_path: str, output_path: str,
                           fields_to_encrypt: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Encrypt specified fields in a configuration file.

        Args:
            input_path: Path to input configuration file (plaintext)
            output_path: Path to output encrypted file
            fields_to_encrypt: List of field names to encrypt (None for all known fields)

        Returns:
            Encrypted configuration dictionary
        """
        # Default fields that should be encrypted
        if fields_to_encrypt is None:
            fields_to_encrypt = [
                'GameDispatchURL',
                'GameDispatchURLTemplate',
                'GameGatewayURLTemplate',
                'GameGatewayDefault',
                'LauncherSpriteURL',
                'LauncherResourceURL',
                'LauncherPluginURL',
                'LauncherNewsURL',
                'LauncherCPSType',
                'LauncherId',
                'LauncherGameId',
                'LauncherBizName',
                'DispatcherKey',
                'GameDispatchChannelName',
                'GameDispatchArrayURL',
                'LauncherResourceChunksURL'  # This contains nested fields to encrypt
            ]

        # Load configuration file
        with open(input_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        encrypted_config = config.copy()
        encryption_results = {}

        print(f"Encrypting configuration file: {input_path}")
        print("-" * 50)

        # Encrypt individual fields
        for field_name in fields_to_encrypt:
            if field_name in config:
                field_value = config[field_name]

                if isinstance(field_value, str):
                    # Single string field
                    try:
                        encrypted_value = self.encrypt_field(field_value)
                        encrypted_config[field_name] = encrypted_value
                        encryption_results[field_name] = "✓ Encrypted"
                        print(f"✓ {field_name}: Encrypted ({len(field_value)} chars -> {len(encrypted_value)} chars)")
                    except Exception as e:
                        encryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to encrypt - {e}")

                elif isinstance(field_value, list):
                    # Array of strings
                    try:
                        encrypted_array = []
                        encrypted_count = 0
                        for item in field_value:
                            if isinstance(item, str):
                                encrypted_item = self.encrypt_field(item)
                                encrypted_array.append(encrypted_item)
                                encrypted_count += 1
                            else:
                                encrypted_array.append(item)

                        encrypted_config[field_name] = encrypted_array
                        encryption_results[field_name] = f"✓ Encrypted ({encrypted_count} items)"
                        print(f"✓ {field_name}: {encrypted_count} items encrypted")
                    except Exception as e:
                        encryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to encrypt array - {e}")

                elif isinstance(field_value, dict):
                    # Nested object - encrypt string values
                    try:
                        encrypted_object = {}
                        encrypted_count = 0

                        for key, value in field_value.items():
                            if isinstance(value, str):
                                try:
                                    encrypted_value = self.encrypt_field(value)
                                    encrypted_object[key] = encrypted_value
                                    encrypted_count += 1
                                    print(f"  ✓ {field_name}.{key}: Encrypted ({len(value)} chars -> {len(encrypted_value)} chars)")
                                except Exception as e:
                                    print(f"  ⚠️ {field_name}.{key}: Failed to encrypt - {e}")
                                    encrypted_object[key] = value
                            else:
                                encrypted_object[key] = value

                        encrypted_config[field_name] = encrypted_object
                        if encrypted_count > 0:
                            encryption_results[field_name] = f"✓ Encrypted ({encrypted_count} fields)"
                            print(f"✓ {field_name}: {encrypted_count} nested fields encrypted")
                        else:
                            encryption_results[field_name] = "○ No fields to encrypt"
                            print(f"○ {field_name}: No string fields found to encrypt")
                    except Exception as e:
                        encryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to encrypt object - {e}")

        # Update Hash field after encryption
        if 'Hash' in encrypted_config:
            old_hash = encrypted_config['Hash']
            new_hash = self._calculate_config_hash(encrypted_config)
            encrypted_config['Hash'] = new_hash
            print(f"✓ Hash updated: {old_hash} -> {new_hash}")

        # Save encrypted configuration
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(encrypted_config, f, indent=2, ensure_ascii=False)

        print("-" * 50)
        print(f"✓ Encrypted configuration saved to: {output_path}")

        return encrypted_config

    def _calculate_config_hash(self, config: Dict[str, Any]) -> int:
        """
        Calculate hash for configuration file (same logic as C# implementation).

        Args:
            config: Configuration dictionary

        Returns:
            Hash value as signed 64-bit integer
        """
        # Create a copy without the Hash field for calculation
        config_for_hash = {k: v for k, v in config.items() if k != 'Hash'}

        # Convert to JSON string with consistent formatting
        config_str = json.dumps(config_for_hash, sort_keys=True, separators=(',', ':'), ensure_ascii=False)

        # Calculate hash using a simple algorithm (similar to C# GetHashCode)
        hash_value = 0
        for char in config_str:
            hash_value = ((hash_value << 5) + hash_value + ord(char)) & 0xFFFFFFFFFFFFFFFF

        # Convert to signed 64-bit integer
        if hash_value >= 2**63:
            hash_value -= 2**64

        return hash_value


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="CollapseLauncher Configuration Crypto Tool"
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Decrypt single field command
    decrypt_parser = subparsers.add_parser('decrypt', help='Decrypt a single field')
    decrypt_parser.add_argument('--field', required=True, help='Field name')
    decrypt_parser.add_argument('--data', required=True, help='Encrypted data')
    decrypt_parser.add_argument('--master-key', required=True, help='Master key file path')
    
    # Encrypt single field command
    encrypt_parser = subparsers.add_parser('encrypt', help='Encrypt a single field')
    encrypt_parser.add_argument('--data', required=True, help='Plain text data')
    encrypt_parser.add_argument('--master-key', required=True, help='Master key file path')

    # Decrypt config file command
    decrypt_config_parser = subparsers.add_parser('decrypt-config', help='Decrypt configuration file')
    decrypt_config_parser.add_argument('--input', required=True, help='Input config file')
    decrypt_config_parser.add_argument('--output', required=True, help='Output decrypted file')
    decrypt_config_parser.add_argument('--master-key', required=True, help='Master key file path')
    decrypt_config_parser.add_argument('--fields', nargs='*', help='Specific fields to decrypt')

    # Encrypt config file command
    encrypt_config_parser = subparsers.add_parser('encrypt-config', help='Encrypt configuration file')
    encrypt_config_parser.add_argument('--input', required=True, help='Input config file (plaintext)')
    encrypt_config_parser.add_argument('--output', required=True, help='Output encrypted file')
    encrypt_config_parser.add_argument('--master-key', required=True, help='Master key file path')
    encrypt_config_parser.add_argument('--fields', nargs='*', help='Specific fields to encrypt')

    # Batch decrypt command
    batch_decrypt_parser = subparsers.add_parser('batch-decrypt', help='Batch decrypt multiple files')
    batch_decrypt_parser.add_argument('--input-dir', required=True, help='Input directory')
    batch_decrypt_parser.add_argument('--output-dir', required=True, help='Output directory')
    batch_decrypt_parser.add_argument('--master-key', required=True, help='Master key file path')

    # Batch encrypt command
    batch_encrypt_parser = subparsers.add_parser('batch-encrypt', help='Batch encrypt multiple files')
    batch_encrypt_parser.add_argument('--input-dir', required=True, help='Input directory (plaintext files)')
    batch_encrypt_parser.add_argument('--output-dir', required=True, help='Output directory (encrypted files)')
    batch_encrypt_parser.add_argument('--master-key', required=True, help='Master key file path')
    batch_encrypt_parser.add_argument('--fields', nargs='*', help='Specific fields to encrypt')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        decryptor = CollapseDecryptor(args.master_key)
        
        if args.command == 'decrypt':
            result = decryptor.decrypt_field(args.data)
            print(f"Field: {args.field}")
            print(f"Decrypted: {result}")
        
        elif args.command == 'encrypt':
            result = decryptor.encrypt_field(args.data)
            print(f"Plaintext: {args.data}")
            print(f"Encrypted: {result}")
        
        elif args.command == 'decrypt-config':
            decryptor.decrypt_config_file(args.input, args.output, args.fields)

        elif args.command == 'encrypt-config':
            decryptor.encrypt_config_file(args.input, args.output, args.fields)

        elif args.command == 'batch-decrypt':
            input_dir = Path(args.input_dir)
            output_dir = Path(args.output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)

            for config_file in input_dir.glob('*.json'):
                output_file = output_dir / f"decrypted_{config_file.name}"
                print(f"\nProcessing: {config_file}")
                decryptor.decrypt_config_file(str(config_file), str(output_file))

        elif args.command == 'batch-encrypt':
            input_dir = Path(args.input_dir)
            output_dir = Path(args.output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)

            for config_file in input_dir.glob('*.json'):
                output_file = output_dir / f"encrypted_{config_file.name}"
                print(f"\nProcessing: {config_file}")
                decryptor.encrypt_config_file(str(config_file), str(output_file), args.fields)
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
