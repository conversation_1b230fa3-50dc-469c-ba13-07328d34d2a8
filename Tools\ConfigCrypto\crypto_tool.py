#!/usr/bin/env python3
"""
CollapseLauncher Configuration Crypto Tool

This tool provides encryption and decryption capabilities for 
CollapseLauncher configuration files.
"""

import argparse
import base64
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Union, List

from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend


class CollapseDecryptor:
    """Decryptor for CollapseLauncher encrypted configuration fields."""
    
    def __init__(self, master_key_path: Optional[str] = None):
        """
        Initialize the decryptor.
        
        Args:
            master_key_path: Path to the master key configuration file
        """
        self.private_key = None
        self.master_key_config = None
        
        if master_key_path:
            self.load_master_key(master_key_path)
    
    def load_master_key(self, key_path: str) -> None:
        """
        Load master key from configuration file.
        
        Args:
            key_path: Path to the master key configuration file
        """
        try:
            with open(key_path, 'r', encoding='utf-8') as f:
                self.master_key_config = json.load(f)
            
            # Extract and decode the key
            key_data = self.master_key_config['Key']
            decoded_key = base64.b64decode(key_data)
            
            # Extract PEM data from Collapse format
            pem_start = decoded_key.find(b"-----BEGIN")
            if pem_start == -1:
                raise ValueError(f"Invalid key format: PEM data not found. {decoded_key}")
            
            pem_data = decoded_key[pem_start:]
            
            # Load the private key
            self.private_key = serialization.load_pem_private_key(
                pem_data,
                password=None,
                backend=default_backend()
            )
            
            print(f"✓ Master key loaded successfully from: {key_path}")
            
        except Exception as e:
            raise ValueError(f"Failed to load master key: {e}")
    
    def decrypt_field(self, encrypted_data: str) -> str:
        """
        Decrypt a single encrypted field.
        
        Args:
            encrypted_data: Base64 encoded encrypted data
            
        Returns:
            Decrypted plaintext string
        """
        if not self.private_key:
            raise ValueError("Master key not loaded. Call load_master_key() first.")
        
        try:
            # Decode base64
            encrypted_bytes = base64.b64decode(encrypted_data)
            
            # Check for Collapse prefix
            if not encrypted_bytes.startswith(b"Collapse"):
                raise ValueError("Invalid encrypted data format: missing Collapse prefix")
            
            # Extract the actual encrypted data (skip prefix and padding)
            # Format: "Collapse" + version + padding + encrypted_data
            collapse_prefix = b"Collapse"
            prefix_len = len(collapse_prefix)
            
            # Skip version and padding bytes
            version_byte = encrypted_bytes[prefix_len:prefix_len+1]
            padding_info = encrypted_bytes[prefix_len+1:prefix_len+17]  # 16 bytes padding info
            
            # Extract encrypted payload
            encrypted_payload = encrypted_bytes[prefix_len+17:]
            
            # Decrypt using RSA
            decrypted_bytes = self.private_key.decrypt(
                encrypted_payload,
                padding.PKCS1v15()
            )
            
            # Convert to string
            return decrypted_bytes.decode('utf-8')
            
        except Exception as e:
            raise ValueError(f"Decryption failed: {e}")
    
    def encrypt_field(self, plaintext: str) -> str:
        """
        Encrypt a plaintext string to Collapse format.
        
        Args:
            plaintext: Plain text to encrypt
            
        Returns:
            Base64 encoded encrypted data in Collapse format
        """
        if not self.private_key:
            raise ValueError("Master key not loaded. Call load_master_key() first.")
        
        try:
            # Get public key for encryption
            public_key = self.private_key.public_key()
            
            # Encrypt the plaintext
            encrypted_bytes = public_key.encrypt(
                plaintext.encode('utf-8'),
                padding.PKCS1v15()
            )
            
            # Create Collapse format
            collapse_prefix = b"Collapse"
            version_byte = b"\x01"  # Version 1
            padding_info = b"\x01" + b"\x00" * 15  # Padding info
            
            # Combine all parts
            full_encrypted = collapse_prefix + version_byte + padding_info + encrypted_bytes
            
            # Encode to base64
            return base64.b64encode(full_encrypted).decode('utf-8')
            
        except Exception as e:
            raise ValueError(f"Encryption failed: {e}")
    
    def decrypt_config_field(self, config: Dict[str, Any], field_name: str) -> Optional[str]:
        """
        Decrypt a specific field in a configuration dictionary.
        
        Args:
            config: Configuration dictionary
            field_name: Name of the field to decrypt
            
        Returns:
            Decrypted value or None if field doesn't exist
        """
        if field_name not in config:
            return None
        
        encrypted_value = config[field_name]
        if not isinstance(encrypted_value, str):
            return None
        
        try:
            return self.decrypt_field(encrypted_value)
        except Exception as e:
            print(f"Warning: Failed to decrypt field '{field_name}': {e}")
            return None
    
    def decrypt_config_file(self, input_path: str, output_path: str, 
                           fields_to_decrypt: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Decrypt specified fields in a configuration file.
        
        Args:
            input_path: Path to input configuration file
            output_path: Path to output decrypted file
            fields_to_decrypt: List of field names to decrypt (None for all known fields)
            
        Returns:
            Decrypted configuration dictionary
        """
        # Default fields that are typically encrypted
        if fields_to_decrypt is None:
            fields_to_decrypt = [
                'GameDispatchURL',
                'GameDispatchURLTemplate',
                'GameGatewayURLTemplate',
                'GameGatewayDefault',
                'LauncherSpriteURL',
                'LauncherResourceURL',
                'LauncherPluginURL',
                'LauncherNewsURL',
                'LauncherCPSType',
                'LauncherId',
                'LauncherGameId',
                'LauncherBizName',
                'DispatcherKey',
                'GameDispatchChannelName',
                'GameDispatchArrayURL'
            ]
        
        # Load configuration file
        with open(input_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        decrypted_config = config.copy()
        decryption_results = {}
        
        print(f"Decrypting configuration file: {input_path}")
        print("-" * 50)
        
        # Decrypt individual fields
        for field_name in fields_to_decrypt:
            if field_name in config:
                field_value = config[field_name]
                
                if isinstance(field_value, str):
                    # Single string field
                    try:
                        decrypted_value = self.decrypt_field(field_value)
                        decrypted_config[field_name] = decrypted_value
                        decryption_results[field_name] = "✓ Decrypted"
                        print(f"✓ {field_name}: {decrypted_value}")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt")
                
                elif isinstance(field_value, list):
                    # Array of strings
                    try:
                        decrypted_array = []
                        for item in field_value:
                            if isinstance(item, str):
                                decrypted_item = self.decrypt_field(item)
                                decrypted_array.append(decrypted_item)
                            else:
                                decrypted_array.append(item)
                        
                        decrypted_config[field_name] = decrypted_array
                        decryption_results[field_name] = "✓ Decrypted (array)"
                        print(f"✓ {field_name}: {len(decrypted_array)} items decrypted")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt array")
                
                elif isinstance(field_value, dict):
                    # Nested object - decrypt string values
                    try:
                        decrypted_object = {}
                        for key, value in field_value.items():
                            if isinstance(value, str):
                                try:
                                    decrypted_object[key] = self.decrypt_field(value)
                                except:
                                    decrypted_object[key] = value
                            else:
                                decrypted_object[key] = value
                        
                        decrypted_config[field_name] = decrypted_object
                        decryption_results[field_name] = "✓ Decrypted (object)"
                        print(f"✓ {field_name}: Object decrypted")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt object")
        
        # Save decrypted configuration
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(decrypted_config, f, indent=2, ensure_ascii=False)
        
        print("-" * 50)
        print(f"✓ Decrypted configuration saved to: {output_path}")
        
        return decrypted_config


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="CollapseLauncher Configuration Crypto Tool"
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Decrypt single field command
    decrypt_parser = subparsers.add_parser('decrypt', help='Decrypt a single field')
    decrypt_parser.add_argument('--field', required=True, help='Field name')
    decrypt_parser.add_argument('--data', required=True, help='Encrypted data')
    decrypt_parser.add_argument('--master-key', required=True, help='Master key file path')
    
    # Encrypt single field command
    encrypt_parser = subparsers.add_parser('encrypt', help='Encrypt a single field')
    encrypt_parser.add_argument('--data', required=True, help='Plain text data')
    encrypt_parser.add_argument('--master-key', required=True, help='Master key file path')
    
    # Decrypt config file command
    decrypt_config_parser = subparsers.add_parser('decrypt-config', help='Decrypt configuration file')
    decrypt_config_parser.add_argument('--input', required=True, help='Input config file')
    decrypt_config_parser.add_argument('--output', required=True, help='Output decrypted file')
    decrypt_config_parser.add_argument('--master-key', required=True, help='Master key file path')
    decrypt_config_parser.add_argument('--fields', nargs='*', help='Specific fields to decrypt')
    
    # Batch decrypt command
    batch_parser = subparsers.add_parser('batch-decrypt', help='Batch decrypt multiple files')
    batch_parser.add_argument('--input-dir', required=True, help='Input directory')
    batch_parser.add_argument('--output-dir', required=True, help='Output directory')
    batch_parser.add_argument('--master-key', required=True, help='Master key file path')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        decryptor = CollapseDecryptor(args.master_key)
        
        if args.command == 'decrypt':
            result = decryptor.decrypt_field(args.data)
            print(f"Field: {args.field}")
            print(f"Decrypted: {result}")
        
        elif args.command == 'encrypt':
            result = decryptor.encrypt_field(args.data)
            print(f"Plaintext: {args.data}")
            print(f"Encrypted: {result}")
        
        elif args.command == 'decrypt-config':
            decryptor.decrypt_config_file(args.input, args.output, args.fields)
        
        elif args.command == 'batch-decrypt':
            input_dir = Path(args.input_dir)
            output_dir = Path(args.output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            for config_file in input_dir.glob('*.json'):
                output_file = output_dir / f"decrypted_{config_file.name}"
                print(f"\nProcessing: {config_file}")
                decryptor.decrypt_config_file(str(config_file), str(output_file))
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
