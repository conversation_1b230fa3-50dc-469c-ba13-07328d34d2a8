# 使用指南

## 概述

Master Key Generator是为CollapseLauncher设计的工具，用于生成和管理master key配置文件。这些配置文件是launcher安全系统的核心组件。

## 工作原理

### 1. 密钥生成流程

```
1. 生成RSA密钥对 (私钥/公钥)
2. 将私钥转换为Collapse特定格式
3. 使用Base64编码密钥数据
4. 计算密钥哈希值
5. 创建JSON配置文件
6. 生成对应的stamp条目
```

### 2. 文件结构

```
output/
├── config_master.json     # 主配置文件
├── stamp_entry.json       # Stamp条目
├── private_key.pem        # 私钥备份
└── public_key.pem         # 公钥备份
```

## 详细使用步骤

### 步骤1: 环境准备

确保已安装Python 3.7+和所需依赖：

```bash
# 检查Python版本
python --version

# 安装依赖
pip install -r requirements.txt
```

### 步骤2: 生成密钥

#### 方法A: 使用批处理脚本 (推荐)

**Windows:**
```cmd
generate.bat
```

**Linux/macOS:**
```bash
chmod +x generate.sh
./generate.sh
```

#### 方法B: 直接使用Python脚本

```bash
# 基本用法
python generate_master_key.py

# 自定义参数
python generate_master_key.py \
    --key-size 2048 \
    --bit-size 256 \
    --output-dir ./my_keys \
    --config-version "3.2.0"
```

### 步骤3: 验证生成的文件

```bash
# 验证所有文件
python validate_config.py ./output

# 验证特定文件
python validate_config.py ./output/config_master.json --type config
```

### 步骤4: 部署配置

1. 将`config_master.json`复制到launcher的metadata目录
2. 更新`stamp.json`文件，添加生成的stamp条目
3. 重启launcher以加载新配置

## 参数说明

### 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--key-size` | 1024 | RSA密钥长度(位) |
| `--bit-size` | 128 | BitSize字段值 |
| `--output-dir` | ./output | 输出目录路径 |
| `--config-version` | 3.1.0 | 配置版本号 |

### 推荐配置

| 用途 | Key Size | Bit Size | 说明 |
|------|----------|----------|------|
| 开发测试 | 1024 | 128 | 快速生成，适合测试 |
| 生产环境 | 2048 | 256 | 更高安全性 |
| 高安全要求 | 4096 | 512 | 最高安全级别 |

## 配置文件详解

### config_master.json

```json
{
  "Key": "Base64编码的密钥数据",
  "BitSize": 128,
  "Hash": 4849480174666812961
}
```

- **Key**: Collapse格式的RSA私钥，Base64编码
- **BitSize**: 密钥位长度，用于加密算法配置
- **Hash**: 密钥内容的哈希值，用于完整性验证

### stamp_entry.json

```json
{
  "LastUpdated": 20240509063558,
  "MetadataPath": "config_master.json",
  "MetadataType": "MasterKey",
  "MetadataInclude": true,
  "PresetConfigVersion": "3.1.0"
}
```

- **LastUpdated**: 更新时间戳 (YYYYMMDDHHMMSS格式)
- **MetadataPath**: 配置文件路径
- **MetadataType**: 元数据类型 (固定为"MasterKey")
- **MetadataInclude**: 是否包含在metadata中
- **PresetConfigVersion**: 配置版本号

## 集成到CollapseLauncher

### 1. 文件部署

```
CollapseLauncher/
└── _metadatav3/
    ├── stamp.json              # 更新此文件
    └── config_master.json      # 新增此文件
```

### 2. 更新stamp.json

将生成的stamp条目添加到现有的stamp.json文件中：

```json
[
  {
    "LastUpdated": 20240509063558,
    "MetadataPath": "config_master.json",
    "MetadataType": "MasterKey",
    "MetadataInclude": true,
    "PresetConfigVersion": "3.1.0"
  },
  // ... 其他条目
]
```

### 3. 验证集成

启动launcher后，检查日志确认master key已正确加载：

```
✓ Master key loaded successfully
✓ RSA key size: 1024 bits
✓ Configuration version: 3.1.0
```

## 故障排除

### 常见错误

1. **"Private key not generated"**
   - 确保在调用其他方法前先调用`generate_rsa_keypair()`

2. **"Invalid base64"**
   - 检查Key字段是否被正确复制，避免换行符问题

3. **"Hash validation failed"**
   - 重新生成配置文件，确保哈希值正确计算

4. **"File not found"**
   - 检查文件路径和权限设置

### 调试技巧

1. 使用验证工具检查文件格式
2. 查看详细的错误输出
3. 比较生成的文件与示例文件
4. 检查文件编码 (应为UTF-8)

## 安全最佳实践

1. **私钥保护**
   - 不要将私钥文件提交到版本控制
   - 使用适当的文件权限保护私钥
   - 定期备份私钥文件

2. **密钥轮换**
   - 定期更新master key
   - 保留旧密钥用于向后兼容
   - 记录密钥更新历史

3. **环境隔离**
   - 开发和生产环境使用不同的密钥
   - 测试环境使用较短的密钥以提高性能

## 高级用法

### 批量生成

```bash
# 生成多个不同配置的密钥
for size in 1024 2048 4096; do
    python generate_master_key.py \
        --key-size $size \
        --output-dir "./keys_$size"
done
```

### 自动化脚本

```bash
#!/bin/bash
# 自动生成并部署密钥
python generate_master_key.py --key-size 2048
python validate_config.py ./output
cp ./output/config_master.json /path/to/launcher/_metadatav3/
echo "Master key deployed successfully"
```
