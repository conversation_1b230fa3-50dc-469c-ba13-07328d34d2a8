#!/bin/bash
# CollapseLauncher Master Key Generator Shell Script
# This script provides an easy way to generate master key configurations

set -e

echo "========================================"
echo "CollapseLauncher Master Key Generator"
echo "========================================"
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7+ and try again"
    exit 1
fi

# Check if required packages are installed
echo "Checking dependencies..."
if ! python3 -c "import cryptography" &> /dev/null; then
    echo "Installing required packages..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        exit 1
    fi
fi

echo "Dependencies OK"
echo

# Get user input for parameters
read -p "Enter RSA key size (default: 1024): " KEY_SIZE
KEY_SIZE=${KEY_SIZE:-1024}

read -p "Enter BitSize value (default: 128): " BIT_SIZE
BIT_SIZE=${BIT_SIZE:-128}

read -p "Enter output directory (default: ./output): " OUTPUT_DIR
OUTPUT_DIR=${OUTPUT_DIR:-./output}

read -p "Enter config version (default: 3.1.0): " CONFIG_VERSION
CONFIG_VERSION=${CONFIG_VERSION:-3.1.0}

echo
echo "Generating master key with the following parameters:"
echo "- RSA Key Size: $KEY_SIZE bits"
echo "- Bit Size: $BIT_SIZE"
echo "- Output Directory: $OUTPUT_DIR"
echo "- Config Version: $CONFIG_VERSION"
echo

# Generate the master key
python3 generate_master_key.py --key-size "$KEY_SIZE" --bit-size "$BIT_SIZE" --output-dir "$OUTPUT_DIR" --config-version "$CONFIG_VERSION"

if [ $? -ne 0 ]; then
    echo
    echo "Error: Failed to generate master key"
    exit 1
fi

echo
echo "Validating generated files..."
python3 validate_config.py "$OUTPUT_DIR"

if [ $? -ne 0 ]; then
    echo
    echo "Warning: Validation failed. Please check the generated files."
else
    echo
    echo "Success! Master key configuration generated and validated."
fi

echo
echo "Generated files are located in: $OUTPUT_DIR"
echo
