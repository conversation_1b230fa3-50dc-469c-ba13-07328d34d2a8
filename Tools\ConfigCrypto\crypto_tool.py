#!/usr/bin/env python3
"""
CollapseLauncher Configuration Crypto Tool

This tool provides encryption and decryption capabilities for 
CollapseLauncher configuration files.
"""

import argparse
import base64
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Union, List

from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend


class CollapseDecryptor:
    """Decryptor for CollapseLauncher encrypted configuration fields."""
    
    def __init__(self, master_key_path: Optional[str] = None):
        """
        Initialize the decryptor.
        
        Args:
            master_key_path: Path to the master key configuration file
        """
        self.private_key = None
        self.master_key_config = None
        
        if master_key_path:
            self.load_master_key(master_key_path)
    
    def load_master_key(self, key_path: str) -> None:
        """
        Load master key from configuration file.

        Args:
            key_path: Path to the master key configuration file
        """
        try:
            with open(key_path, 'r', encoding='utf-8') as f:
                self.master_key_config = json.load(f)

            # Key field is base64 encoded byte array (DER format RSA private key)
            key_base64 = self.master_key_config['Key']
            key_bytes = base64.b64decode(key_base64)

            # Check if it's ServeV3 format (compressed)
            if self._is_serve_v3_data(key_bytes):
                # Decompress ServeV3 data
                key_bytes = self._serve_v3_data(key_bytes)

            # Import DER format RSA private key
            self.private_key = serialization.load_der_private_key(
                key_bytes,
                password=None,
                backend=default_backend()
            )

            print(f"✓ Master key loaded successfully from: {key_path}")
            print(f"  Key size: {self.private_key.key_size} bits")

        except Exception as e:
            raise ValueError(f"Failed to load master key: {e}")

    def _is_serve_v3_data(self, data: bytes) -> bool:
        """
        Check if data is ServeV3 format (same as C# IsServeV3Data).

        Args:
            data: Data to check

        Returns:
            True if ServeV3 format, False otherwise
        """
        if len(data) < 8:
            return False

        # CollapseSignature = 7310310183885631299
        collapse_signature = 7310310183885631299
        signature = int.from_bytes(data[:8], byteorder='little')
        return signature == collapse_signature

    def _serve_v3_data(self, data: bytes) -> bytes:
        """
        Decompress ServeV3 data (simplified version of C# ServeV3Data).

        Args:
            data: ServeV3 format data

        Returns:
            Decompressed data
        """
        if len(data) < 32:
            raise ValueError("ServeV3 data format is corrupted!")

        # Read sizes
        compressed_size = int.from_bytes(data[16:24], byteorder='little')
        decompressed_size = int.from_bytes(data[24:32], byteorder='little')

        # Read attributes
        attrib_number = int.from_bytes(data[8:16], byteorder='little')
        compression_type = attrib_number & 0xFF
        is_use_encryption = (attrib_number >> 8) & 0xFF == 1

        # Extract raw data (skip 32 byte header)
        raw_data = data[32:]

        if is_use_encryption:
            raise NotImplementedError("Encrypted ServeV3 data is not supported yet")

        # Handle compression
        if compression_type == 0:  # None
            return raw_data[:decompressed_size]
        elif compression_type == 1:  # Brotli
            import brotli
            return brotli.decompress(raw_data)
        elif compression_type == 2:  # Zstd
            import zstandard as zstd
            dctx = zstd.ZstdDecompressor()
            return dctx.decompress(raw_data)
        else:
            raise ValueError(f"Unsupported compression type: {compression_type}")


    
    def decrypt_field(self, encrypted_data: str) -> str:
        """
        Decrypt a single encrypted field using ServeV3Data logic.

        Args:
            encrypted_data: Base64 encoded ServeV3 format data

        Returns:
            Decrypted plaintext string
        """
        if not self.private_key:
            raise ValueError("Master key not loaded. Call load_master_key() first.")

        try:
            # The encrypted_data should be base64 encoded ServeV3 format
            if not self._is_base64_valid(encrypted_data):
                return encrypted_data  # Return as-is if not base64

            # Decode base64
            data_bytes = base64.b64decode(encrypted_data)

            # Check if it's ServeV3 format
            if not self._is_serve_v3_data(data_bytes):
                return encrypted_data  # Return as-is if not ServeV3

            # Process ServeV3 data
            decrypted_data = self._serve_v3_data_with_decryption(data_bytes)

            # Convert to string
            return decrypted_data.decode('utf-8')

        except Exception as e:
            raise ValueError(f"Decryption failed: {e}")

    def _is_base64_valid(self, data: str) -> bool:
        """Check if string is valid base64."""
        try:
            base64.b64decode(data, validate=True)
            return True
        except Exception:
            return False

    def _serve_v3_data_with_decryption(self, data: bytes) -> bytes:
        """
        Process ServeV3 data with decryption support.

        Args:
            data: ServeV3 format data

        Returns:
            Processed data
        """
        if len(data) < 32:
            raise ValueError("ServeV3 data format is corrupted!")

        # Read sizes
        compressed_size = int.from_bytes(data[16:24], byteorder='little')
        decompressed_size = int.from_bytes(data[24:32], byteorder='little')

        # Read attributes
        attrib_number = int.from_bytes(data[8:16], byteorder='little')
        compression_type = attrib_number & 0xFF
        is_use_encryption = (attrib_number >> 8) & 0xFF == 1

        # Extract raw data (skip 32 byte header)
        raw_data = data[32:]

        # Handle encryption first
        if is_use_encryption:
            raw_data = self._decrypt_rsa_chunks(raw_data)

        # Then handle compression
        if compression_type == 0:  # None
            return raw_data[:decompressed_size]
        elif compression_type == 1:  # Brotli
            import brotli
            return brotli.decompress(raw_data)
        elif compression_type == 2:  # Zstd
            import zstandard as zstd
            dctx = zstd.ZstdDecompressor()
            return dctx.decompress(raw_data)
        else:
            raise ValueError(f"Unsupported compression type: {compression_type}")

    def _decrypt_rsa_chunks(self, encrypted_data: bytes) -> bytes:
        """
        Decrypt RSA encrypted data in chunks.

        Args:
            encrypted_data: RSA encrypted data

        Returns:
            Decrypted data
        """
        enc_bit_length = self.master_key_config['BitSize']
        decrypted_chunks = []

        offset = 0
        while offset < len(encrypted_data):
            # Extract chunk
            chunk = encrypted_data[offset:offset + enc_bit_length]

            # Decrypt chunk
            decrypted_chunk = self.private_key.decrypt(chunk, padding.PKCS1v15())
            decrypted_chunks.append(decrypted_chunk)

            offset += enc_bit_length

        return b''.join(decrypted_chunks)
    
    def encrypt_field(self, plaintext: str) -> str:
        """
        Encrypt a plaintext string using the reverse of C# DecryptStringWithMasterKey logic.

        Args:
            plaintext: Plain text to encrypt

        Returns:
            Hex-encoded encrypted data (same format as C# expects)
        """
        if not self.private_key:
            raise ValueError("Master key not loaded. Call load_master_key() first.")

        try:
            # Step 1: Encrypt plaintext using RSA (reverse of DecryptRSAContent)
            encrypted_base64 = self._encrypt_rsa_content(
                plaintext.encode('utf-8'),
                self.master_key_config['BitSize']
            )

            # Step 2: Convert to bytes (reverse of UTF-8 decode)
            encrypted_bytes = encrypted_base64.encode('utf-8')

            # Step 3: Encode using reverse of _f8j51 logic
            hex_encoded = self._f8j51_encode(encrypted_bytes)

            return hex_encoded

        except Exception as e:
            raise ValueError(f"Encryption failed: {e}")

    def _encrypt_rsa_content(self, content: bytes, enc_bit_length: int) -> str:
        """
        Python implementation of reverse C# DecryptRSAContent method.

        Args:
            content: Content to encrypt
            enc_bit_length: Encryption bit length (chunk size for output)

        Returns:
            Base64 encoded encrypted content
        """
        try:
            # Get public key for encryption
            public_key = self.private_key.public_key()

            # Calculate max chunk size for RSA encryption
            # RSA can encrypt data up to (key_size_in_bytes - padding_overhead)
            key_size_bytes = self.private_key.key_size // 8
            max_chunk_size = key_size_bytes - 11  # PKCS1v15 padding overhead

            # Encrypt in chunks
            encrypted_chunks = []
            i = 0

            while i < len(content):
                # Extract chunk
                chunk_size = min(max_chunk_size, len(content) - i)
                chunk = content[i:i + chunk_size]

                # Encrypt chunk using RSA with PKCS1v15 padding
                encrypted_chunk = public_key.encrypt(chunk, padding.PKCS1v15())
                encrypted_chunks.append(encrypted_chunk)

                i += chunk_size

            # Combine all encrypted chunks
            full_encrypted = b''.join(encrypted_chunks)

            # Encode to base64
            return base64.b64encode(full_encrypted).decode('utf-8')

        except Exception as e:
            raise ValueError(f"RSA encryption failed: {e}")

    def _f8j51_encode(self, data: bytes) -> str:
        """
        Python implementation of reverse C# _f8j51 method.
        Applies XOR with sKey and encodes to hex string.

        Args:
            data: Bytes to encode

        Returns:
            Hex-encoded string
        """
        # sKey from C# code
        s_key = bytes([
            232, 170, 135, 231,
            189, 170, 227, 130,
            134, 227, 129, 132
        ])

        result = []

        for i, byte_value in enumerate(data):
            # Apply XOR with sKey (reverse of decode operation)
            xor_value = byte_value ^ s_key[i % len(s_key)]

            # Convert to hex string (2 characters)
            hex_str = f"{xor_value:02x}"
            result.append(hex_str)

        return ''.join(result)
    
    def decrypt_config_field(self, config: Dict[str, Any], field_name: str) -> Optional[str]:
        """
        Decrypt a specific field in a configuration dictionary.
        
        Args:
            config: Configuration dictionary
            field_name: Name of the field to decrypt
            
        Returns:
            Decrypted value or None if field doesn't exist
        """
        if field_name not in config:
            return None
        
        encrypted_value = config[field_name]
        if not isinstance(encrypted_value, str):
            return None
        
        try:
            return self.decrypt_field(encrypted_value)
        except Exception as e:
            print(f"Warning: Failed to decrypt field '{field_name}': {e}")
            return None
    
    def decrypt_config_file(self, input_path: str, output_path: str, 
                           fields_to_decrypt: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Decrypt specified fields in a configuration file.
        
        Args:
            input_path: Path to input configuration file
            output_path: Path to output decrypted file
            fields_to_decrypt: List of field names to decrypt (None for all known fields)
            
        Returns:
            Decrypted configuration dictionary
        """
        # Default fields that are typically encrypted
        if fields_to_decrypt is None:
            fields_to_decrypt = [
                'GameDispatchURL',
                'GameDispatchURLTemplate',
                'GameGatewayURLTemplate',
                'GameGatewayDefault',
                'LauncherSpriteURL',
                'LauncherResourceURL',
                'LauncherPluginURL',
                'LauncherNewsURL',
                'LauncherCPSType',
                'LauncherId',
                'LauncherGameId',
                'LauncherBizName',
                'DispatcherKey',
                'GameDispatchChannelName',
                'GameDispatchArrayURL',
                'LauncherResourceChunksURL'  # This contains nested encrypted fields
            ]
        
        # Load configuration file
        with open(input_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        decrypted_config = config.copy()
        decryption_results = {}
        
        print(f"Decrypting configuration file: {input_path}")
        print("-" * 50)
        
        # Decrypt individual fields
        for field_name in fields_to_decrypt:
            if field_name in config:
                field_value = config[field_name]
                
                if isinstance(field_value, str):
                    # Single string field
                    try:
                        decrypted_value = self.decrypt_field(field_value)
                        decrypted_config[field_name] = decrypted_value
                        decryption_results[field_name] = "✓ Decrypted"
                        print(f"✓ {field_name}: {decrypted_value}")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt")
                
                elif isinstance(field_value, list):
                    # Array of strings
                    try:
                        decrypted_array = []
                        for item in field_value:
                            if isinstance(item, str):
                                decrypted_item = self.decrypt_field(item)
                                decrypted_array.append(decrypted_item)
                            else:
                                decrypted_array.append(item)
                        
                        decrypted_config[field_name] = decrypted_array
                        decryption_results[field_name] = "✓ Decrypted (array)"
                        print(f"✓ {field_name}: {len(decrypted_array)} items decrypted")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt array")
                
                elif isinstance(field_value, dict):
                    # Nested object - decrypt string values
                    try:
                        decrypted_object = {}
                        decrypted_count = 0

                        for key, value in field_value.items():
                            if isinstance(value, str):
                                try:
                                    decrypted_value = self.decrypt_field(value)
                                    decrypted_object[key] = decrypted_value
                                    if decrypted_value != value:  # Only count if actually decrypted
                                        decrypted_count += 1
                                        print(f"  ✓ {field_name}.{key}: {decrypted_value}")
                                    else:
                                        decrypted_object[key] = value
                                except Exception as e:
                                    print(f"  ⚠️ {field_name}.{key}: Failed to decrypt - {e}")
                                    decrypted_object[key] = value
                            else:
                                decrypted_object[key] = value

                        decrypted_config[field_name] = decrypted_object
                        if decrypted_count > 0:
                            decryption_results[field_name] = f"✓ Decrypted ({decrypted_count} fields)"
                            print(f"✓ {field_name}: {decrypted_count} nested fields decrypted")
                        else:
                            decryption_results[field_name] = "○ No encrypted fields found"
                            print(f"○ {field_name}: No encrypted fields found")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt object")
        
        # Save decrypted configuration
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(decrypted_config, f, indent=2, ensure_ascii=False)
        
        print("-" * 50)
        print(f"✓ Decrypted configuration saved to: {output_path}")
        
        return decrypted_config


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="CollapseLauncher Configuration Crypto Tool"
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Decrypt single field command
    decrypt_parser = subparsers.add_parser('decrypt', help='Decrypt a single field')
    decrypt_parser.add_argument('--field', required=True, help='Field name')
    decrypt_parser.add_argument('--data', required=True, help='Encrypted data')
    decrypt_parser.add_argument('--master-key', required=True, help='Master key file path')
    
    # Encrypt single field command
    encrypt_parser = subparsers.add_parser('encrypt', help='Encrypt a single field')
    encrypt_parser.add_argument('--data', required=True, help='Plain text data')
    encrypt_parser.add_argument('--master-key', required=True, help='Master key file path')
    
    # Decrypt config file command
    decrypt_config_parser = subparsers.add_parser('decrypt-config', help='Decrypt configuration file')
    decrypt_config_parser.add_argument('--input', required=True, help='Input config file')
    decrypt_config_parser.add_argument('--output', required=True, help='Output decrypted file')
    decrypt_config_parser.add_argument('--master-key', required=True, help='Master key file path')
    decrypt_config_parser.add_argument('--fields', nargs='*', help='Specific fields to decrypt')
    
    # Batch decrypt command
    batch_parser = subparsers.add_parser('batch-decrypt', help='Batch decrypt multiple files')
    batch_parser.add_argument('--input-dir', required=True, help='Input directory')
    batch_parser.add_argument('--output-dir', required=True, help='Output directory')
    batch_parser.add_argument('--master-key', required=True, help='Master key file path')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        decryptor = CollapseDecryptor(args.master_key)
        
        if args.command == 'decrypt':
            result = decryptor.decrypt_field(args.data)
            print(f"Field: {args.field}")
            print(f"Decrypted: {result}")
        
        elif args.command == 'encrypt':
            result = decryptor.encrypt_field(args.data)
            print(f"Plaintext: {args.data}")
            print(f"Encrypted: {result}")
        
        elif args.command == 'decrypt-config':
            decryptor.decrypt_config_file(args.input, args.output, args.fields)
        
        elif args.command == 'batch-decrypt':
            input_dir = Path(args.input_dir)
            output_dir = Path(args.output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            for config_file in input_dir.glob('*.json'):
                output_file = output_dir / f"decrypted_{config_file.name}"
                print(f"\nProcessing: {config_file}")
                decryptor.decrypt_config_file(str(config_file), str(output_file))
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
