﻿<Window x:Class="CollapseLauncher.UpdaterWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:helper="using:Hi3Helper"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d">
    <Grid>
        <Grid x:Name="DragArea"
              Height="320"
              HorizontalAlignment="Stretch"
              VerticalAlignment="Top" />
        <Page Background="{ThemeResource PageBackgroundAcrylicBrush}">
            <Grid Margin="24">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition Height="80" />
                </Grid.RowDefinitions>
                <Grid Grid.Row="0"
                      Margin="0,0,0,40"
                      HorizontalAlignment="Stretch"
                      VerticalAlignment="Stretch">
                    <StackPanel>
                        <TextBlock FontWeight="Normal"
                                   Style="{ThemeResource TitleTextBlockStyle}"
                                   Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateTitle1}" />
                        <TextBlock FontWeight="Bold"
                                   Style="{ThemeResource TitleLargeTextBlockStyle}"
                                   Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateTitle2}" />
                    </StackPanel>
                    <StackPanel VerticalAlignment="Bottom">
                        <Grid>
                            <StackPanel Margin="0,4,0,0">
                                <TextBlock Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateUpdateVersionTitle}" />
                                <TextBlock>
                                    <Run x:Name="CurrentVersionLabel"
                                         FontWeight="Bold"
                                         Text="-" />
                                    <Run Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateVersionSeparator}" />
                                    <Run x:Name="NewVersionLabel"
                                         FontWeight="Bold"
                                         Foreground="{ThemeResource AccentColor}"
                                         Text="-" />
                                </TextBlock>
                            </StackPanel>
                            <StackPanel HorizontalAlignment="Right">
                                <TextBlock HorizontalAlignment="Right"
                                           Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateUpdateChannelTitle}" />
                                <TextBlock>
                                    <Run x:Name="UpdateChannelLabel"
                                         FontWeight="Bold"
                                         Foreground="{ThemeResource AccentColor}"
                                         Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateUpdateChannelSubtitlePlaceholder}" />
                                </TextBlock>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Grid>
                <StackPanel Grid.Row="1"
                            VerticalAlignment="Bottom">
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.ColumnSpan="3">
                            <TextBlock x:Name="Status"
                                       Margin="0,0,0,4"
                                       Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateUpdateStatusTitle}" />
                            <TextBlock x:Name="ActivityStatus"
                                       Margin="0,0,0,4"
                                       Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateMiscIdle}"
                                       TextWrapping="Wrap" />
                            <TextBlock x:Name="ActivitySubStatus"
                                       FontWeight="Bold"
                                       Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateDownloadSizePlaceholder}" />
                        </StackPanel>
                        <StackPanel x:Name="ProgressStatus"
                                    Grid.Column="1"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Bottom"
                                    Orientation="Horizontal">
                            <TextBlock FontWeight="Bold"
                                       Text="{x:Bind progressBar.Value, Mode=OneWay}" />
                            <TextBlock Text="%" />
                        </StackPanel>
                        <StackPanel Grid.Column="2"
                                    HorizontalAlignment="Right">
                            <TextBlock />
                            <TextBlock x:Name="SpeedStatus"
                                       Margin="0,0,0,4"
                                       HorizontalAlignment="Right"
                                       Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateDownloadSpeedPlaceholder}" />
                            <TextBlock x:Name="TimeEstimation"
                                       HorizontalAlignment="Right"
                                       FontWeight="Bold"
                                       Text="{x:Bind helper:Locale.Lang._UpdatePage.ApplyUpdateDownloadTimeEstPlaceholder}" />
                        </StackPanel>
                    </Grid>
                    <ProgressBar x:Name="progressBar"
                                 IsIndeterminate="False"
                                 Maximum="100"
                                 Value="0" />
                </StackPanel>
            </Grid>
        </Page>
    </Grid>
</Window>
