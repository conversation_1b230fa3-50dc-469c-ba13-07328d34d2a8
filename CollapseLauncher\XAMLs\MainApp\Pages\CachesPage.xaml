﻿<Page x:Class="CollapseLauncher.Pages.CachesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:behaviors="using:CommunityToolkit.WinUI.Behaviors"
      xmlns:control="using:CommunityToolkit.WinUI.Controls"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:datatable="using:CommunityToolkit.WinUI.Controls.Labs.DataTable"
      xmlns:helper="using:Hi3Helper"
      xmlns:interactivity="using:Microsoft.Xaml.Interactivity"
      xmlns:local="using:CollapseLauncher"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      CacheMode="BitmapCache"
      Loaded="InitializeLoaded"
      NavigationCacheMode="Disabled"
      Unloaded="Page_Unloaded"
      mc:Ignorable="d">
    <Grid>
        <Grid x:Name="PageContent"
              Margin="32,40,32,18">
            <Grid.RowDefinitions>
                <RowDefinition Height="80" />
                <RowDefinition />
                <RowDefinition Height="124" />
            </Grid.RowDefinitions>
            <TextBlock Grid.Row="0"
                       Style="{ThemeResource TitleLargeTextBlockStyle}"
                       Text="{x:Bind helper:Locale.Lang._CachesPage.PageTitle}" />
            <Grid x:Name="CachesDataTableGrid"
                  Grid.Row="1"
                  VerticalAlignment="Stretch"
                  Visibility="Collapsed">
                <Grid.ChildrenTransitions>
                    <TransitionCollection>
                        <PopupThemeTransition />
                    </TransitionCollection>
                </Grid.ChildrenTransitions>
                <!-- ReSharper disable once Xaml.PossibleNullReferenceException -->
                <ListView ItemsSource="{x:Bind CurrentGameProperty.GameCache.AssetEntry}">
                    <ListView.Header>
                        <Border Padding="0,8,0,0"
                                Background="{ThemeResource AcrylicBackgroundFillColorBaseBrush}"
                                CornerRadius="8">
                            <interactivity:Interaction.Behaviors>
                                <behaviors:StickyHeaderBehavior />
                            </interactivity:Interaction.Behaviors>
                            <datatable:DataTable Margin="12,0,0,8">
                                <datatable:DataColumn MinWidth="128"
                                                      CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._CachesPage.ListCol1}"
                                                      DesiredWidth="5*"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="CacheName" />
                                <datatable:DataColumn CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._CachesPage.ListCol2}"
                                                      DesiredWidth="92"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="CacheType" />
                                <datatable:DataColumn MinWidth="128"
                                                      CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._CachesPage.ListCol3}"
                                                      DesiredWidth="7*"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="Source" />
                                <datatable:DataColumn CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._CachesPage.ListCol4}"
                                                      DesiredWidth="92"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="CacheSize" />
                                <datatable:DataColumn CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._CachesPage.ListCol5}"
                                                      DesiredWidth="92"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="CacheCurCRC" />
                                <datatable:DataColumn CanResize="True"
                                                      Content="{x:Bind helper:Locale.Lang._CachesPage.ListCol6}"
                                                      DesiredWidth="92"
                                                      FontSize="13"
                                                      FontWeight="SemiBold"
                                                      Tag="CacheExptCRC" />
                            </datatable:DataTable>
                        </Border>
                    </ListView.Header>
                    <ListView.ItemTemplate>
                        <DataTemplate x:DataType="local:IAssetProperty">
                            <datatable:DataRow HorizontalAlignment="Left">
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind Name}"
                                           TextTrimming="CharacterEllipsis" />
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind AssetTypeString}"
                                           TextTrimming="CharacterEllipsis" />
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind Source}"
                                           TextTrimming="CharacterEllipsis" />
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind SizeStr}"
                                           TextTrimming="CharacterEllipsis" />
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind LocalCRC}"
                                           TextTrimming="CharacterEllipsis" />
                                <TextBlock Margin="0,0,16,0"
                                           VerticalAlignment="Center"
                                           Text="{x:Bind RemoteCRC}"
                                           TextTrimming="CharacterEllipsis" />
                            </datatable:DataRow>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                    <ListView.ItemContainerStyle>
                        <Style BasedOn="{StaticResource DefaultListViewItemStyle}"
                               TargetType="ListViewItem">
                            <Setter Property="HorizontalContentAlignment" Value="Left" />
                        </Style>
                    </ListView.ItemContainerStyle>
                </ListView>
            </Grid>
            <StackPanel Grid.Row="2"
                        VerticalAlignment="Bottom">
                <TextBlock x:Name="CachesStatus"
                           Margin="0,0,0,16"
                           FontSize="18"
                           Text="{x:Bind helper:Locale.Lang._CachesPage.Status1}"
                           TextTrimming="CharacterEllipsis" />
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0"
                                Margin="0,0,32,0">
                        <TextBlock Margin="0,0,0,8"
                                   FontSize="16"
                                   FontWeight="Medium"
                                   Text="{x:Bind helper:Locale.Lang._CachesPage.CachesStatusHeader1}" />
                        <control:DockPanel HorizontalAlignment="Stretch"
                                           LastChildFill="False">
                            <TextBlock x:Name="CachesTotalStatus"
                                       control:DockPanel.Dock="Left"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._CachesPage.CachesTotalStatusNone}" />
                            <TextBlock control:DockPanel.Dock="Right"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="%" />
                            <TextBlock control:DockPanel.Dock="Right"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="{x:Bind CachesTotalProgressBar.Value, Mode=OneWay}" />
                        </control:DockPanel>
                        <ProgressBar x:Name="CachesTotalProgressBar"
                                     Height="25"
                                     Maximum="100"
                                     Value="0" />
                    </StackPanel>
                    <Grid Grid.Column="1"
                          Margin="0,0,0,10"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Bottom">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Button x:Name="UpdateCachesBtn"
                                HorizontalAlignment="Stretch"
                                Click="StartCachesUpdate"
                                CornerRadius="16"
                                Style="{ThemeResource AccentButtonStyle}"
                                Visibility="Collapsed">
                            <StackPanel Orientation="Horizontal">
                                <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                          FontSize="16"
                                          Glyph="&#xf019;" />
                                <TextBlock Margin="8,0"
                                           FontWeight="Medium"
                                           Text="{x:Bind helper:Locale.Lang._CachesPage.CachesBtn1}" />
                            </StackPanel>
                        </Button>
                        <SplitButton x:Name="CheckUpdateBtn"
                                     Grid.Column="0"
                                     HorizontalAlignment="Stretch"
                                     Click="StartCachesCheckSplitButton"
                                     CornerRadius="16"
                                     Tag="Fast"
                                     ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._CachesPage.CachesBtn2QuickDesc}">
                            <StackPanel Orientation="Horizontal"
                                        Spacing="8">
                                <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                          FontSize="16"
                                          Glyph="&#xf002;" />
                                <TextBlock FontWeight="SemiBold"
                                           Text="{x:Bind helper:Locale.Lang._CachesPage.CachesBtn2Quick}" />
                            </StackPanel>
                            <SplitButton.Flyout>
                                <Flyout Placement="Bottom">
                                    <StackPanel Margin="-8">
                                        <Button x:Name="CheckUpdateQuickBtn"
                                                Margin="0,0,0,8"
                                                HorizontalAlignment="Stretch"
                                                HorizontalContentAlignment="Stretch"
                                                Click="StartCachesCheck"
                                                Tag="Fast"
                                                ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._CachesPage.CachesBtn2QuickDesc}">
                                            <Grid ColumnSpacing="12">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                <FontIcon Grid.Column="1"
                                                          HorizontalAlignment="Right"
                                                          FontFamily="{ThemeResource FontAwesomeSolid}"
                                                          FontSize="16"
                                                          Glyph="&#xf101;" />
                                                <TextBlock Grid.Column="0"
                                                           HorizontalAlignment="Left"
                                                           FontWeight="SemiBold"
                                                           Text="{x:Bind helper:Locale.Lang._CachesPage.CachesBtn2Quick}" />
                                            </Grid>
                                        </Button>
                                        <Button x:Name="CheckUpdateFullBtn"
                                                HorizontalAlignment="Stretch"
                                                HorizontalContentAlignment="Stretch"
                                                Click="StartCachesCheck"
                                                Style="{ThemeResource AccentButtonStyle}"
                                                Tag="Full"
                                                ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._CachesPage.CachesBtn2FullDesc}">
                                            <Grid ColumnSpacing="12">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                <FontIcon Grid.Column="1"
                                                          HorizontalAlignment="Right"
                                                          FontFamily="{ThemeResource FontAwesomeSolid}"
                                                          FontSize="16"
                                                          Glyph="&#xf105;" />
                                                <TextBlock Grid.Column="0"
                                                           HorizontalAlignment="Left"
                                                           FontWeight="SemiBold"
                                                           Text="{x:Bind helper:Locale.Lang._CachesPage.CachesBtn2Full}" />
                                            </Grid>
                                        </Button>
                                    </StackPanel>
                                </Flyout>
                            </SplitButton.Flyout>
                        </SplitButton>
                        <Button x:Name="CancelBtn"
                                Grid.Column="1"
                                Margin="16,0,0,0"
                                HorizontalAlignment="Stretch"
                                Click="CancelOperation"
                                CornerRadius="16"
                                IsEnabled="False">
                            <TextBlock Margin="8,0"
                                       FontWeight="Medium"
                                       Text="{x:Bind helper:Locale.Lang._Misc.Cancel}" />
                        </Button>
                    </Grid>
                    <StackPanel Grid.Column="1"
                                Margin="0,0,0,10"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Bottom"
                                Orientation="Horizontal" />
                </Grid>
            </StackPanel>
        </Grid>
        <Grid x:Name="Overlay"
              Visibility="Collapsed">
            <StackPanel Margin="0,176,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Vertical">
                <ProgressRing x:Name="Ring"
                              Width="48"
                              Height="48"
                              Margin="32"
                              IsActive="True"
                              IsIndeterminate="false"
                              Maximum="100"
                              Value="100" />
                <TextBlock x:Name="OverlayTitle"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Style="{ThemeResource SubtitleTextBlockStyle}"
                           Text="Title" />
                <TextBlock x:Name="OverlaySubtitle"
                           Margin="0,8,0,192"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                           Text="Subtitle" />
            </StackPanel>
        </Grid>
    </Grid>
</Page>
