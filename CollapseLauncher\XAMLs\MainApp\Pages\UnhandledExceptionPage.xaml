﻿<Page x:Class="CollapseLauncher.Pages.UnhandledExceptionPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:helper="using:Hi3Helper"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      mc:Ignorable="d">
    <Grid Margin="32,32,32,32">
        <StackPanel Grid.Row="0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Orientation="Vertical">
            <TextBlock x:Name="Title"
                       Margin="0,0,0,24"
                       Style="{ThemeResource TitleLargeTextBlockStyle}" />
            <TextBlock x:Name="Subtitle"
                       Margin="0,0,0,8"
                       FontSize="18"
                       Style="{ThemeResource BodyStrongTextBlockStyle}" />
            <TextBox x:Name="ExceptionTextBox"
                     Height="450"
                     Margin="0,0,0,16"
                     HorizontalAlignment="Stretch"
                     VerticalAlignment="Stretch"
                     AcceptsReturn="True"
                     IsReadOnly="True"
                     ScrollViewer.HorizontalScrollBarVisibility="Auto"
                     ScrollViewer.VerticalScrollBarVisibility="Auto"
                     TextWrapping="Wrap" />
            <Button x:Name="CopyThrow"
                    HorizontalAlignment="Stretch"
                    Click="CopyTextToClipboard"
                    Content="{x:Bind helper:Locale.Lang._UnhandledExceptionPage.CopyClipboardBtn1}"
                    Style="{ThemeResource AccentButtonStyle}" />
            <Button x:Name="BackToPreviousPage"
                    Margin="0,16,0,0"
                    HorizontalAlignment="Stretch"
                    Click="GoBackPreviousPage"
                    Style="{ThemeResource AccentButtonStyle}"
                    Visibility="Collapsed">
                <StackPanel Orientation="Horizontal">
                    <FontIcon FontFamily="Segoe MDL2 Assets"
                              FontSize="14"
                              Glyph="&#xE112;" />
                    <TextBlock Margin="8,0,0,0"
                               Text="{x:Bind helper:Locale.Lang._UnhandledExceptionPage.GoBackPageBtn1}" />
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Page>
