﻿<!--  Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. See LICENSE in the project root for license information.  -->
<!--  <PERSON>S<PERSON>per disable IdentifierTypo  -->
<!--  ReSharper disable UnusedMember.Local  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<!--  ReSharper disable Xaml.InvalidResourceType  -->
<!--  ReSharper disable Xaml.StaticResourceNotResolved  -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.ThemeDictionaries>
        <ResourceDictionary x:Key="Default">
            <!--  Some resources need to be pointing to colors for animations to update correctly  -->
            <x:Double x:Key="RadioButtonBorderThemeThickness">1</x:Double>
            <StaticResource x:Key="RadioButtonForeground"
                            ResourceKey="TextFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonForegroundPointerOver"
                            ResourceKey="TextFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonForegroundPressed"
                            ResourceKey="TextFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonForegroundDisabled"
                            ResourceKey="TextFillColorDisabledBrush" />
            <StaticResource x:Key="RadioButtonBackground"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBackgroundPointerOver"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBackgroundPressed"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBackgroundDisabled"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrush"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrushPointerOver"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrushPressed"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrushDisabled"
                            ResourceKey="ControlFillColorTransparentBrush" />

            <StaticResource x:Key="RadioButtonOuterEllipseStroke"
                            ResourceKey="ControlStrongStrokeColorDefaultBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseStrokePointerOver"
                            ResourceKey="ControlStrongStrokeColorDefault" />
            <StaticResource x:Key="RadioButtonOuterEllipseStrokePressed"
                            ResourceKey="ControlStrongStrokeColorDisabled" />
            <StaticResource x:Key="RadioButtonOuterEllipseStrokeDisabled"
                            ResourceKey="ControlStrongStrokeColorDisabled" />
            <StaticResource x:Key="RadioButtonOuterEllipseFill"
                            ResourceKey="ControlAltFillColorSecondaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseFillPointerOver"
                            ResourceKey="ControlAltFillColorTertiaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseFillPressed"
                            ResourceKey="ControlAltFillColorQuarternaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseFillDisabled"
                            ResourceKey="ControlAltFillColorDisabledBrush" />

            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStroke"
                            ResourceKey="AccentFillColorDefaultBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStrokePointerOver"
                            ResourceKey="AccentFillColorSecondaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStrokePressed"
                            ResourceKey="AccentFillColorTertiaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStrokeDisabled"
                            ResourceKey="AccentFillColorDisabled" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFill"
                            ResourceKey="AccentFillColorDefaultBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFillPointerOver"
                            ResourceKey="AccentFillColorSecondaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFillPressed"
                            ResourceKey="AccentFillColorTertiaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFillDisabled"
                            ResourceKey="AccentFillColorDisabled" />

            <StaticResource x:Key="RadioButtonCheckGlyphFill"
                            ResourceKey="TextOnAccentFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphFillPointerOver"
                            ResourceKey="TextOnAccentFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphFillPressed"
                            ResourceKey="TextOnAccentFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphFillDisabled"
                            ResourceKey="TextOnAccentFillColorPrimary" />
            <StaticResource x:Key="RadioButtonCheckGlyphStroke"
                            ResourceKey="CircleElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokePointerOver"
                            ResourceKey="CircleElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokePressed"
                            ResourceKey="CircleElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeDisabled"
                            ResourceKey="CircleElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeChecked"
                            ResourceKey="AccentControlElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeCheckedPointerOver"
                            ResourceKey="AccentControlElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeCheckedPressed"
                            ResourceKey="AccentControlElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeCheckedDisabled"
                            ResourceKey="ControlElevationBorderBrush" />

            <!--  Legacy Brushes  -->
            <SolidColorBrush x:Key="RadioButtonBackgroundThemeBrush"
                             Color="#CCFFFFFF" />
            <SolidColorBrush x:Key="RadioButtonBorderThemeBrush"
                             Color="#CCFFFFFF" />
            <SolidColorBrush x:Key="RadioButtonGridBorderThemeBrush"
                             Color="#11FFFFFF" />
            <SolidColorBrush x:Key="RadioButtonContentDisabledForegroundThemeBrush"
                             Color="#66FFFFFF" />
            <SolidColorBrush x:Key="RadioButtonContentForegroundThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="RadioButtonDisabledBackgroundThemeBrush"
                             Color="#66FFFFFF" />
            <SolidColorBrush x:Key="RadioButtonDisabledBorderThemeBrush"
                             Color="#66FFFFFF" />
            <SolidColorBrush x:Key="RadioButtonDisabledForegroundThemeBrush"
                             Color="#66000000" />
            <SolidColorBrush x:Key="RadioButtonForegroundThemeBrush"
                             Color="#FF000000" />
            <SolidColorBrush x:Key="RadioButtonPointerOverBackgroundThemeBrush"
                             Color="#DEFFFFFF" />
            <SolidColorBrush x:Key="RadioButtonPointerOverBorderThemeBrush"
                             Color="#DEFFFFFF" />
            <SolidColorBrush x:Key="RadioButtonPointerOverForegroundThemeBrush"
                             Color="#FF000000" />
            <SolidColorBrush x:Key="RadioButtonPressedBackgroundThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="RadioButtonPressedBorderThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="RadioButtonPressedForegroundThemeBrush"
                             Color="#FF000000" />
            <SolidColorBrush x:Key="RadioButtonContentPointerOverForegroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightTextColor}" />
        </ResourceDictionary>

        <ResourceDictionary x:Key="HighContrast">
            <!--  Some resources need to be pointing to colors for animations to update correctly  -->
            <x:Double x:Key="RadioButtonBorderThemeThickness">1</x:Double>
            <StaticResource x:Key="RadioButtonForeground"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="RadioButtonForegroundPointerOver"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="RadioButtonForegroundPressed"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="RadioButtonForegroundDisabled"
                            ResourceKey="SystemColorGrayTextColorBrush" />
            <StaticResource x:Key="RadioButtonBackground"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonBackgroundPointerOver"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonBackgroundPressed"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonBackgroundDisabled"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrush"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrushPointerOver"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrushPressed"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrushDisabled"
                            ResourceKey="SystemControlTransparentBrush" />

            <StaticResource x:Key="RadioButtonOuterEllipseStroke"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseStrokePointerOver"
                            ResourceKey="SystemColorHighlightColor" />
            <StaticResource x:Key="RadioButtonOuterEllipseStrokePressed"
                            ResourceKey="SystemColorHighlightTextColor" />
            <StaticResource x:Key="RadioButtonOuterEllipseStrokeDisabled"
                            ResourceKey="SystemColorGrayTextColor" />
            <StaticResource x:Key="RadioButtonOuterEllipseFill"
                            ResourceKey="SystemColorButtonFaceColorBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseFillPointerOver"
                            ResourceKey="SystemColorHighlightTextColorBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseFillPressed"
                            ResourceKey="SystemColorHighlightTextColorBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseFillDisabled"
                            ResourceKey="SystemColorWindowColorBrush" />

            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStroke"
                            ResourceKey="SystemColorHighlightColorBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStrokePointerOver"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStrokePressed"
                            ResourceKey="SystemColorButtonFaceColorBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStrokeDisabled"
                            ResourceKey="SystemColorGrayTextColor" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFill"
                            ResourceKey="SystemColorHighlightTextColorBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFillPointerOver"
                            ResourceKey="SystemColorButtonFaceColorBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFillPressed"
                            ResourceKey="SystemColorButtonFaceColorBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFillDisabled"
                            ResourceKey="SystemColorWindowColorBrush" />

            <StaticResource x:Key="RadioButtonCheckGlyphFill"
                            ResourceKey="SystemColorHighlightColorBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphFillPointerOver"
                            ResourceKey="SystemColorButtonTextColor" />
            <StaticResource x:Key="RadioButtonCheckGlyphFillPressed"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphFillDisabled"
                            ResourceKey="SystemColorGrayTextColorBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStroke"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokePointerOver"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokePressed"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeDisabled"
                            ResourceKey="SystemColorGrayTextColorBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeChecked"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeCheckedPointerOver"
                            ResourceKey="SystemColorButtonTextColorBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeCheckedPressed"
                            ResourceKey="SystemColorHighlightTextColorBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeCheckedDisabled"
                            ResourceKey="SystemColorGrayTextColorBrush" />

            <!--  Legacy Brushes  -->
            <SolidColorBrush x:Key="RadioButtonBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonFaceColor}" />
            <SolidColorBrush x:Key="RadioButtonBorderThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="RadioButtonContentDisabledForegroundThemeBrush"
                             Color="{ThemeResource SystemColorGrayTextColor}" />
            <SolidColorBrush x:Key="RadioButtonContentForegroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="RadioButtonContentPointerOverForegroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightTextColor}" />
            <SolidColorBrush x:Key="RadioButtonDisabledBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonFaceColor}" />
            <SolidColorBrush x:Key="RadioButtonDisabledBorderThemeBrush"
                             Color="{ThemeResource SystemColorGrayTextColor}" />
            <SolidColorBrush x:Key="RadioButtonDisabledForegroundThemeBrush"
                             Color="{ThemeResource SystemColorGrayTextColor}" />
            <SolidColorBrush x:Key="RadioButtonForegroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="RadioButtonPointerOverBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightColor}" />
            <SolidColorBrush x:Key="RadioButtonPointerOverBorderThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="RadioButtonPointerOverForegroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightTextColor}" />
            <SolidColorBrush x:Key="RadioButtonPressedBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="RadioButtonPressedBorderThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="RadioButtonPressedForegroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonFaceColor}" />
        </ResourceDictionary>

        <ResourceDictionary x:Key="Light">
            <!--  Some resources need to be pointing to colors for animations to update correctly  -->
            <x:Double x:Key="RadioButtonBorderThemeThickness">1</x:Double>
            <StaticResource x:Key="RadioButtonForeground"
                            ResourceKey="TextFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonForegroundPointerOver"
                            ResourceKey="TextFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonForegroundPressed"
                            ResourceKey="TextFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonForegroundDisabled"
                            ResourceKey="TextFillColorDisabledBrush" />
            <StaticResource x:Key="RadioButtonBackground"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBackgroundPointerOver"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBackgroundPressed"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBackgroundDisabled"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrush"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrushPointerOver"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrushPressed"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="RadioButtonBorderBrushDisabled"
                            ResourceKey="ControlFillColorTransparentBrush" />

            <StaticResource x:Key="RadioButtonOuterEllipseStroke"
                            ResourceKey="ControlStrongStrokeColorDefaultBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseStrokePointerOver"
                            ResourceKey="ControlStrongStrokeColorDefault" />
            <StaticResource x:Key="RadioButtonOuterEllipseStrokePressed"
                            ResourceKey="ControlStrongStrokeColorDisabled" />
            <StaticResource x:Key="RadioButtonOuterEllipseStrokeDisabled"
                            ResourceKey="ControlStrongStrokeColorDisabled" />
            <StaticResource x:Key="RadioButtonOuterEllipseFill"
                            ResourceKey="ControlAltFillColorSecondaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseFillPointerOver"
                            ResourceKey="ControlAltFillColorTertiaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseFillPressed"
                            ResourceKey="ControlAltFillColorQuarternaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseFillDisabled"
                            ResourceKey="ControlAltFillColorDisabledBrush" />

            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStroke"
                            ResourceKey="AccentFillColorDefaultBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStrokePointerOver"
                            ResourceKey="AccentFillColorSecondaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStrokePressed"
                            ResourceKey="AccentFillColorTertiaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedStrokeDisabled"
                            ResourceKey="AccentFillColorDisabled" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFill"
                            ResourceKey="AccentFillColorDefaultBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFillPointerOver"
                            ResourceKey="AccentFillColorSecondaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFillPressed"
                            ResourceKey="AccentFillColorTertiaryBrush" />
            <StaticResource x:Key="RadioButtonOuterEllipseCheckedFillDisabled"
                            ResourceKey="AccentFillColorDisabled" />

            <StaticResource x:Key="RadioButtonCheckGlyphFill"
                            ResourceKey="TextOnAccentFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphFillPointerOver"
                            ResourceKey="TextOnAccentFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphFillPressed"
                            ResourceKey="TextOnAccentFillColorPrimaryBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphFillDisabled"
                            ResourceKey="TextOnAccentFillColorPrimary" />
            <StaticResource x:Key="RadioButtonCheckGlyphStroke"
                            ResourceKey="CircleElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokePointerOver"
                            ResourceKey="CircleElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokePressed"
                            ResourceKey="CircleElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeDisabled"
                            ResourceKey="CircleElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeChecked"
                            ResourceKey="AccentControlElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeCheckedPointerOver"
                            ResourceKey="AccentControlElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeCheckedPressed"
                            ResourceKey="AccentControlElevationBorderBrush" />
            <StaticResource x:Key="RadioButtonCheckGlyphStrokeCheckedDisabled"
                            ResourceKey="ControlElevationBorderBrush" />

            <!--  Legacy Brushes  -->
            <SolidColorBrush x:Key="RadioButtonBackgroundThemeBrush"
                             Color="#CCFFFFFF" />
            <SolidColorBrush x:Key="RadioButtonBorderThemeBrush"
                             Color="#45000000" />
            <SolidColorBrush x:Key="RadioButtonGridBorderThemeBrush"
                             Color="#11000000" />
            <SolidColorBrush x:Key="RadioButtonContentDisabledForegroundThemeBrush"
                             Color="#66000000" />
            <SolidColorBrush x:Key="RadioButtonContentForegroundThemeBrush"
                             Color="#FF000000" />
            <SolidColorBrush x:Key="RadioButtonDisabledBackgroundThemeBrush"
                             Color="#66CACACA" />
            <SolidColorBrush x:Key="RadioButtonDisabledBorderThemeBrush"
                             Color="#26000000" />
            <SolidColorBrush x:Key="RadioButtonDisabledForegroundThemeBrush"
                             Color="#66000000" />
            <SolidColorBrush x:Key="RadioButtonForegroundThemeBrush"
                             Color="#FF000000" />
            <SolidColorBrush x:Key="RadioButtonPointerOverBackgroundThemeBrush"
                             Color="#DEFFFFFF" />
            <SolidColorBrush x:Key="RadioButtonPointerOverBorderThemeBrush"
                             Color="#70000000" />
            <SolidColorBrush x:Key="RadioButtonPointerOverForegroundThemeBrush"
                             Color="#FF000000" />
            <SolidColorBrush x:Key="RadioButtonPressedBackgroundThemeBrush"
                             Color="#FF000000" />
            <SolidColorBrush x:Key="RadioButtonPressedBorderThemeBrush"
                             Color="#FF000000" />
            <SolidColorBrush x:Key="RadioButtonPressedForegroundThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="RadioButtonContentPointerOverForegroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightTextColor}" />
        </ResourceDictionary>
    </ResourceDictionary.ThemeDictionaries>

    <x:Double x:Key="RadioButtonCheckGlyphSize">12</x:Double>
    <x:Double x:Key="RadioButtonCheckGlyphPointerOverSize">14</x:Double>
    <x:Double x:Key="RadioButtonCheckGlyphPressedOverSize">10</x:Double>

    <Style BasedOn="{StaticResource DefaultRadioButtonStyle}"
           TargetType="RadioButton" />

    <Style x:Key="DefaultRadioButtonStyle"
           TargetType="RadioButton">
        <Setter Property="Background" Value="{ThemeResource RadioButtonBackground}" />
        <Setter Property="Foreground" Value="{ThemeResource RadioButtonForeground}" />
        <Setter Property="BorderBrush" Value="{ThemeResource RadioButtonBorderBrush}" />
        <Setter Property="Padding" Value="8,6,0,0" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
        <Setter Property="MinWidth" Value="120" />
        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
        <Setter Property="FocusVisualMargin" Value="-7,-3,-7,-3" />
        <Setter Property="CornerRadius" Value="{StaticResource ControlCornerRadius}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RadioButton">
                    <Grid x:Name="RootGrid"
                          Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          CornerRadius="{TemplateBinding CornerRadius}">

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseStroke}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseFill}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedStroke}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedFill}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphFill}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphStroke}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                       Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonForegroundPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="RootGrid"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonBackgroundPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="RootGrid"
                                                                       Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonBorderBrushPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{ThemeResource RadioButtonOuterEllipseStrokePointerOver}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{Binding Color, Source={ThemeResource RadioButtonOuterEllipseFillPointerOver}}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedStrokePointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedFillPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphFillPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphStrokePointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Width">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                  Value="14" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Height">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                  Value="14" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                       Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonForegroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="RootGrid"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="RootGrid"
                                                                       Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonBorderBrushPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{ThemeResource RadioButtonOuterEllipseStrokePressed}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{Binding Color, Source={ThemeResource RadioButtonOuterEllipseFillPressed}}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedStrokePressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedFillPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphFillPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphStrokePressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Width">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                  Value="10" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Height">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                  Value="10" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PressedCheckGlyph"
                                                                       Storyboard.TargetProperty="Opacity">
                                            <LinearDoubleKeyFrame KeyTime="0"
                                                                  Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="PressedCheckGlyph"
                                                                       Storyboard.TargetProperty="Width">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                  Value="10" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="PressedCheckGlyph"
                                                                       Storyboard.TargetProperty="Height">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                  Value="10" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                       Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonForegroundDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="RootGrid"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonBackgroundDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="RootGrid"
                                                                       Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonBorderBrushDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{ThemeResource RadioButtonOuterEllipseStrokeDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{Binding Color, Source={ThemeResource RadioButtonOuterEllipseFillDisabled}}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{ThemeResource RadioButtonOuterEllipseCheckedStrokeDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{ThemeResource RadioButtonOuterEllipseCheckedFillDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphFillDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphStrokeDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Width">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                  Value="14" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Height">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                  Value="14" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CheckStates">
                                <VisualState x:Name="Checked">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="CheckGlyph"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="1"
                                                         Duration="0" />
                                        <DoubleAnimation Storyboard.TargetName="OuterEllipse"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0"
                                                         Duration="0" />
                                        <DoubleAnimation Storyboard.TargetName="CheckOuterEllipse"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="1"
                                                         Duration="0" />
                                        <DoubleAnimation Storyboard.TargetName="PressedCheckGlyph"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0"
                                                         Duration="0" />
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphStrokeChecked}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PressedCheckGlyph"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphFillPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unchecked" />
                                <VisualState x:Name="Indeterminate" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>

                        <Grid Height="32"
                              VerticalAlignment="Top">
                            <Ellipse x:Name="OuterEllipse"
                                     Width="20"
                                     Height="20"
                                     Fill="{ThemeResource RadioButtonOuterEllipseFill}"
                                     Stroke="{ThemeResource RadioButtonOuterEllipseStroke}"
                                     StrokeThickness="{ThemeResource RadioButtonBorderThemeThickness}"
                                     UseLayoutRounding="False" />
                            <!--  A separate element is added since the two orthogonal state groups that cannot touch the same property  -->
                            <Ellipse x:Name="CheckOuterEllipse"
                                     Width="20"
                                     Height="20"
                                     Fill="{ThemeResource RadioButtonOuterEllipseCheckedFill}"
                                     Opacity="0"
                                     Stroke="{ThemeResource RadioButtonOuterEllipseCheckedStroke}"
                                     StrokeThickness="{ThemeResource RadioButtonBorderThemeThickness}"
                                     UseLayoutRounding="False" />
                            <Ellipse x:Name="CheckGlyph"
                                     Width="{ThemeResource RadioButtonCheckGlyphSize}"
                                     Height="{ThemeResource RadioButtonCheckGlyphSize}"
                                     Fill="{ThemeResource RadioButtonCheckGlyphFill}"
                                     Opacity="0"
                                     Stroke="{ThemeResource RadioButtonCheckGlyphStroke}"
                                     UseLayoutRounding="False" />
                            <!--  A separate element is added since the two orthogonal state groups that cannot touch the same property  -->
                            <Border x:Name="PressedCheckGlyph"
                                    Width="4"
                                    Height="4"
                                    Background="{ThemeResource RadioButtonCheckGlyphFill}"
                                    BackgroundSizing="OuterBorderEdge"
                                    BorderBrush="{ThemeResource RadioButtonCheckGlyphStroke}"
                                    CornerRadius="6"
                                    Opacity="0"
                                    UseLayoutRounding="False" />
                        </Grid>
                        <ContentPresenter x:Name="ContentPresenter"
                                          Grid.Column="1"
                                          Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          AutomationProperties.AccessibilityView="Raw"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"
                                          ContentTransitions="{TemplateBinding ContentTransitions}"
                                          Foreground="{TemplateBinding Foreground}"
                                          TextWrapping="Wrap" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="AudioLanguageSelectionRadioButtonStyle"
           TargetType="RadioButton">
        <Setter Property="Background" Value="{ThemeResource RadioButtonBackground}" />
        <Setter Property="Foreground" Value="{ThemeResource RadioButtonForeground}" />
        <Setter Property="BorderBrush" Value="{ThemeResource RadioButtonBorderBrush}" />
        <Setter Property="Padding" Value="8,0" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
        <Setter Property="MinWidth" Value="120" />
        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
        <Setter Property="FocusVisualMargin" Value="-7,-3,-7,-3" />
        <Setter Property="CornerRadius" Value="{StaticResource ControlCornerRadius}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RadioButton">
                    <Grid x:Name="RootGrid"
                          MinWidth="560"
                          Margin="0,-1"
                          Padding="8,8,16,8"
                          Background="{TemplateBinding Background}"
                          BorderBrush="{ThemeResource RadioButtonGridBorderThemeBrush}"
                          BorderThickness="1"
                          CornerRadius="8">

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="20" />
                        </Grid.ColumnDefinitions>

                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseStroke}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseFill}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedStroke}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedFill}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphFill}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphStroke}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                       Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonForegroundPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="RootGrid"
                                                                       Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonBorderBrushPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{ThemeResource RadioButtonOuterEllipseStrokePointerOver}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{Binding Color, Source={ThemeResource RadioButtonOuterEllipseFillPointerOver}}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedStrokePointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedFillPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphFillPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphStrokePointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Width">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                  Value="14" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Height">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                  Value="14" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                       Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonForegroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="RootGrid"
                                                                       Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonBorderBrushPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{ThemeResource RadioButtonOuterEllipseStrokePressed}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{Binding Color, Source={ThemeResource RadioButtonOuterEllipseFillPressed}}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedStrokePressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonOuterEllipseCheckedFillPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphFillPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphStrokePressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Width">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                  Value="10" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Height">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                  Value="10" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PressedCheckGlyph"
                                                                       Storyboard.TargetProperty="Opacity">
                                            <LinearDoubleKeyFrame KeyTime="0"
                                                                  Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="PressedCheckGlyph"
                                                                       Storyboard.TargetProperty="Width">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                  Value="10" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="PressedCheckGlyph"
                                                                       Storyboard.TargetProperty="Height">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                  Value="10" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                       Storyboard.TargetProperty="Foreground">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonForegroundDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="RootGrid"
                                                                       Storyboard.TargetProperty="BorderBrush">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonBorderBrushDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{ThemeResource RadioButtonOuterEllipseStrokeDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="OuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{Binding Color, Source={ThemeResource RadioButtonOuterEllipseFillDisabled}}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{ThemeResource RadioButtonOuterEllipseCheckedStrokeDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="CheckOuterEllipse"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                 Value="{ThemeResource RadioButtonOuterEllipseCheckedFillDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphFillDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphStrokeDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Width">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                  Value="14" />
                                        </DoubleAnimationUsingKeyFrames>
                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                       Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Height">
                                            <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                  KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                  Value="14" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CheckStates">
                                <VisualState x:Name="Checked">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="CheckGlyph"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="1"
                                                         Duration="0" />
                                        <DoubleAnimation Storyboard.TargetName="OuterEllipse"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0"
                                                         Duration="0" />
                                        <DoubleAnimation Storyboard.TargetName="CheckOuterEllipse"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="1"
                                                         Duration="0" />
                                        <DoubleAnimation Storyboard.TargetName="PressedCheckGlyph"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0"
                                                         Duration="0" />
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckGlyph"
                                                                       Storyboard.TargetProperty="Stroke">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphStrokeChecked}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PressedCheckGlyph"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource RadioButtonCheckGlyphFillPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unchecked" />
                                <VisualState x:Name="Indeterminate" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>

                        <Grid Grid.Column="1"
                              Height="32"
                              HorizontalAlignment="Right"
                              VerticalAlignment="Center">
                            <Ellipse x:Name="OuterEllipse"
                                     Width="20"
                                     Height="20"
                                     Fill="{ThemeResource RadioButtonOuterEllipseFill}"
                                     Stroke="{ThemeResource RadioButtonOuterEllipseStroke}"
                                     StrokeThickness="{ThemeResource RadioButtonBorderThemeThickness}"
                                     UseLayoutRounding="False" />
                            <!--  A separate element is added since the two orthogonal state groups that cannot touch the same property  -->
                            <Ellipse x:Name="CheckOuterEllipse"
                                     Width="20"
                                     Height="20"
                                     Fill="{ThemeResource RadioButtonOuterEllipseCheckedFill}"
                                     Opacity="0"
                                     Stroke="{ThemeResource RadioButtonOuterEllipseCheckedStroke}"
                                     StrokeThickness="{ThemeResource RadioButtonBorderThemeThickness}"
                                     UseLayoutRounding="False" />
                            <Ellipse x:Name="CheckGlyph"
                                     Width="{ThemeResource RadioButtonCheckGlyphSize}"
                                     Height="{ThemeResource RadioButtonCheckGlyphSize}"
                                     Fill="{ThemeResource RadioButtonCheckGlyphFill}"
                                     Opacity="0"
                                     Stroke="{ThemeResource RadioButtonCheckGlyphStroke}"
                                     UseLayoutRounding="False" />
                            <!--  A separate element is added since the two orthogonal state groups that cannot touch the same property  -->
                            <Border x:Name="PressedCheckGlyph"
                                    Width="4"
                                    Height="4"
                                    Background="{ThemeResource RadioButtonCheckGlyphFill}"
                                    BackgroundSizing="OuterBorderEdge"
                                    BorderBrush="{ThemeResource RadioButtonCheckGlyphStroke}"
                                    CornerRadius="6"
                                    Opacity="0"
                                    UseLayoutRounding="False" />
                        </Grid>
                        <ContentPresenter x:Name="ContentPresenter"
                                          Grid.Column="0"
                                          Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          AutomationProperties.AccessibilityView="Raw"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"
                                          ContentTransitions="{TemplateBinding ContentTransitions}"
                                          Foreground="{TemplateBinding Foreground}"
                                          TextWrapping="Wrap" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
