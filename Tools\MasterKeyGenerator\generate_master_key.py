#!/usr/bin/env python3
"""
CollapseLauncher Master Key Generator

This tool generates master key configuration files and stamp entries
for CollapseLauncher metadata system.
"""

import argparse
import base64
import json
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend


class MasterKeyGenerator:
    """Generator for CollapseLauncher master key configuration."""
    
    def __init__(self, key_size: int = 1024, bit_size: int = 128):
        """
        Initialize the generator.
        
        Args:
            key_size: RSA key size in bits
            bit_size: BitSize field value for the config
        """
        self.key_size = key_size
        self.bit_size = bit_size
        self.private_key = None
        self.public_key = None
        
    def generate_rsa_keypair(self) -> None:
        """Generate RSA key pair."""
        print(f"Generating RSA key pair with {self.key_size} bits...")
        
        # Generate private key
        self.private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=self.key_size,
            backend=default_backend()
        )
        
        # Get public key
        self.public_key = self.private_key.public_key()
        
        print("RSA key pair generated successfully.")
    
    def create_collapse_key_format(self) -> str:
        """
        Create the Collapse-specific key format using _f8j51 encoding.

        Returns:
            Hex-encoded key data (same format as C# expects)
        """
        if not self.private_key:
            raise ValueError("Private key not generated. Call generate_rsa_keypair() first.")

        # Create XML format RSA key (same as C# FromXmlString expects)
        xml_key_data = self._create_xml_rsa_key()

        # Convert XML to bytes
        xml_bytes = xml_key_data.encode('utf-8')

        # Encode using _f8j51 logic (same as C# _f8j51 reverse)
        hex_encoded = self._f8j51_encode(xml_bytes)

        return hex_encoded

    def _create_xml_rsa_key(self) -> str:
        """
        Create XML format RSA key (same format as C# RSA.ToXmlString).

        Returns:
            XML string containing RSA key parameters
        """
        if not self.private_key:
            raise ValueError("Private key not generated.")

        # Get RSA parameters
        private_numbers = self.private_key.private_numbers()
        public_numbers = private_numbers.public_numbers

        # Convert integers to base64 encoded bytes
        def int_to_base64(value):
            # Convert to bytes (big endian)
            byte_length = (value.bit_length() + 7) // 8
            if byte_length == 0:
                byte_length = 1
            bytes_value = value.to_bytes(byte_length, byteorder='big')
            return base64.b64encode(bytes_value).decode('utf-8')

        # Create XML structure
        xml_template = """<RSAKeyValue>
  <Modulus>{modulus}</Modulus>
  <Exponent>{exponent}</Exponent>
  <P>{p}</P>
  <Q>{q}</Q>
  <DP>{dp}</DP>
  <DQ>{dq}</DQ>
  <InverseQ>{inverse_q}</InverseQ>
  <D>{d}</D>
</RSAKeyValue>"""

        return xml_template.format(
            modulus=int_to_base64(public_numbers.n),
            exponent=int_to_base64(public_numbers.e),
            p=int_to_base64(private_numbers.p),
            q=int_to_base64(private_numbers.q),
            dp=int_to_base64(private_numbers.dmp1),
            dq=int_to_base64(private_numbers.dmq1),
            inverse_q=int_to_base64(private_numbers.iqmp),
            d=int_to_base64(private_numbers.d)
        )

    def _f8j51_encode(self, data: bytes) -> str:
        """
        Python implementation of C# _f8j51 encoding logic.

        Args:
            data: Bytes to encode

        Returns:
            Hex-encoded string
        """
        # sKey from C# code
        s_key = bytes([
            232, 170, 135, 231,
            189, 170, 227, 130,
            134, 227, 129, 132
        ])

        result = []

        for i, byte_value in enumerate(data):
            # Apply XOR with sKey
            xor_value = byte_value ^ s_key[i % len(s_key)]

            # Convert to hex string (2 characters, uppercase)
            hex_str = f"{xor_value:02X}"
            result.append(hex_str)

        return ''.join(result)
    
    def calculate_hash(self, key_data: str) -> int:
        """
        Calculate hash for the key data.
        
        Args:
            key_data: Base64 encoded key data
            
        Returns:
            Hash value as integer
        """
        # Simple hash calculation (mimicking CRC32-like behavior)
        key_bytes = key_data.encode('utf-8')
        hash_value = 0
        
        for byte in key_bytes:
            hash_value = ((hash_value << 5) + hash_value + byte) & 0xFFFFFFFFFFFFFFFF
        
        # Convert to signed 64-bit integer
        if hash_value >= 2**63:
            hash_value -= 2**64
            
        return hash_value
    
    def create_master_key_config(self) -> Dict[str, Any]:
        """
        Create the master key configuration dictionary.
        
        Returns:
            Dictionary representing the config_master.json content
        """
        if not self.private_key:
            raise ValueError("Private key not generated. Call generate_rsa_keypair() first.")
        
        # Create the key in Collapse format
        key_data = self.create_collapse_key_format()
        
        # Calculate hash
        hash_value = self.calculate_hash(key_data)
        
        return {
            "Key": key_data,
            "BitSize": self.bit_size,
            "Hash": hash_value
        }
    
    def create_stamp_entry(self, config_version: str = "3.1.0") -> Dict[str, Any]:
        """
        Create the stamp entry for the master key.
        
        Args:
            config_version: Version string for the configuration
            
        Returns:
            Dictionary representing the stamp entry
        """
        # Generate timestamp (current time in format: YYYYMMDDHHMMSS)
        timestamp = int(datetime.now().strftime("%Y%m%d%H%M%S"))
        
        return {
            "LastUpdated": timestamp,
            "MetadataPath": "config_master.json",
            "MetadataType": "MasterKey",
            "MetadataInclude": True,
            "PresetConfigVersion": config_version
        }
    
    def save_files(self, output_dir: str, config_version: str = "3.1.0") -> None:
        """
        Save all generated files to the output directory.
        
        Args:
            output_dir: Directory to save files
            config_version: Version string for the configuration
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"Saving files to: {output_path.absolute()}")
        
        # Generate configurations
        master_config = self.create_master_key_config()
        stamp_entry = self.create_stamp_entry(config_version)
        
        # Save config_master.json
        config_file = output_path / "config_master.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(master_config, f, indent=2, ensure_ascii=False)
        print(f"✓ Saved: {config_file}")
        
        # Save stamp entry
        stamp_file = output_path / "stamp_entry.json"
        with open(stamp_file, 'w', encoding='utf-8') as f:
            json.dump(stamp_entry, f, indent=2, ensure_ascii=False)
        print(f"✓ Saved: {stamp_file}")
        
        # Save private key (for backup)
        private_key_file = output_path / "private_key.pem"
        with open(private_key_file, 'wb') as f:
            f.write(self.private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        print(f"✓ Saved: {private_key_file}")
        
        # Save public key (for verification)
        public_key_file = output_path / "public_key.pem"
        with open(public_key_file, 'wb') as f:
            f.write(self.public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            ))
        print(f"✓ Saved: {public_key_file}")
        
        print("\n" + "="*50)
        print("Master Key Generation Complete!")
        print("="*50)
        print(f"Key Size: {self.key_size} bits")
        print(f"Bit Size: {self.bit_size}")
        print(f"Hash: {master_config['Hash']}")
        print(f"Timestamp: {stamp_entry['LastUpdated']}")
        print("="*50)


def main():
    """Main function to handle command line arguments and run the generator."""
    parser = argparse.ArgumentParser(
        description="Generate CollapseLauncher master key configuration files"
    )
    
    parser.add_argument(
        '--key-size',
        type=int,
        default=1024,
        help='RSA key size in bits (default: 1024)'
    )
    
    parser.add_argument(
        '--bit-size',
        type=int,
        default=128,
        help='BitSize field value (default: 128)'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='./output',
        help='Output directory (default: ./output)'
    )
    
    parser.add_argument(
        '--config-version',
        type=str,
        default='3.1.0',
        help='Configuration version (default: 3.1.0)'
    )
    
    args = parser.parse_args()
    
    try:
        # Create generator
        generator = MasterKeyGenerator(
            key_size=args.key_size,
            bit_size=args.bit_size
        )
        
        # Generate key pair
        generator.generate_rsa_keypair()
        
        # Save all files
        generator.save_files(
            output_dir=args.output_dir,
            config_version=args.config_version
        )
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
