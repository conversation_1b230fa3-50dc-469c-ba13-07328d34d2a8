name: Build, test and publish
on:
  push:
    branches:
      - master
    tags:
      - v**

jobs:
  build-test-publish:
    name: Build, test and publish library
    runs-on: windows-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install workloads
        run: dotnet workload install maui

      - name: Install Tizen workload on macOS / Linux
        if: runner.os != 'Windows'
        run: curl -sSL https://raw.githubusercontent.com/Samsung/Tizen.NET/main/workload/scripts/workload-install.sh | sudo bash

      - name: Install Tizen workload on Windows
        if: runner.os == 'Windows'
        run: |
          Invoke-WebRequest 'https://raw.githubusercontent.com/Samsung/Tizen.NET/main/workload/scripts/workload-install.ps1' -OutFile 'workload-install.ps1'
          ./workload-install.ps1

      - name: Build all projects in src/libs
        run: |
          Get-ChildItem -Path src/libs -Recurse -Filter *.csproj | ForEach-Object { dotnet build $_.FullName --configuration Release }
        shell: pwsh

      - name: Publish
        run: dotnet nuget push
          **.nupkg
          --skip-duplicate
          --source https://api.nuget.org/v3/index.json
          --api-key ${{ secrets.NUGET_KEY }}
