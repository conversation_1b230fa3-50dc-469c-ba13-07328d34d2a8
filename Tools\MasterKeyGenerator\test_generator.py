#!/usr/bin/env python3
"""
Test script for the Master Key Generator

This script runs basic tests to ensure the generator works correctly.
"""

import json
import os
import shutil
import tempfile
import unittest
from pathlib import Path

# Import our modules
from generate_master_key import Master<PERSON>eyGenerator
from validate_config import ConfigValidator


class TestMasterKeyGenerator(unittest.TestCase):
    """Test cases for the Master Key Generator."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        self.validator = ConfigValidator()
    
    def tearDown(self):
        """Clean up test environment."""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_key_generation(self):
        """Test RSA key pair generation."""
        self.generator.generate_rsa_keypair()
        
        # Check that keys are generated
        self.assertIsNotNone(self.generator.private_key)
        self.assertIsNotNone(self.generator.public_key)
        
        # Check key size
        self.assertEqual(self.generator.private_key.key_size, 1024)
    
    def test_collapse_key_format(self):
        """Test Collapse key format creation."""
        self.generator.generate_rsa_keypair()
        key_data = self.generator.create_collapse_key_format()
        
        # Check that it's a valid base64 string
        import base64
        try:
            decoded = base64.b64decode(key_data)
            self.assertTrue(decoded.startswith(b"Collapse"))
        except Exception as e:
            self.fail(f"Invalid base64 or format: {e}")
    
    def test_master_config_creation(self):
        """Test master config creation."""
        self.generator.generate_rsa_keypair()
        config = self.generator.create_master_key_config()
        
        # Check required fields
        self.assertIn('Key', config)
        self.assertIn('BitSize', config)
        self.assertIn('Hash', config)
        
        # Check field types
        self.assertIsInstance(config['Key'], str)
        self.assertIsInstance(config['BitSize'], int)
        self.assertIsInstance(config['Hash'], int)
        
        # Check values
        self.assertEqual(config['BitSize'], 128)
    
    def test_stamp_entry_creation(self):
        """Test stamp entry creation."""
        stamp = self.generator.create_stamp_entry("3.1.0")
        
        # Check required fields
        self.assertIn('LastUpdated', stamp)
        self.assertIn('MetadataPath', stamp)
        self.assertIn('MetadataType', stamp)
        self.assertIn('MetadataInclude', stamp)
        self.assertIn('PresetConfigVersion', stamp)
        
        # Check values
        self.assertEqual(stamp['MetadataPath'], 'config_master.json')
        self.assertEqual(stamp['MetadataType'], 'MasterKey')
        self.assertEqual(stamp['MetadataInclude'], True)
        self.assertEqual(stamp['PresetConfigVersion'], '3.1.0')
    
    def test_file_generation(self):
        """Test complete file generation process."""
        self.generator.generate_rsa_keypair()
        self.generator.save_files(self.test_dir, "3.1.0")
        
        # Check that all files are created
        expected_files = [
            'config_master.json',
            'stamp_entry.json',
            'private_key.pem',
            'public_key.pem'
        ]
        
        for filename in expected_files:
            file_path = Path(self.test_dir) / filename
            self.assertTrue(file_path.exists(), f"File {filename} was not created")
            self.assertGreater(file_path.stat().st_size, 0, f"File {filename} is empty")
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Generate files
        self.generator.generate_rsa_keypair()
        self.generator.save_files(self.test_dir, "3.1.0")
        
        # Validate config file
        config_path = Path(self.test_dir) / "config_master.json"
        self.assertTrue(self.validator.validate_master_config(str(config_path)))
        
        # Validate stamp file
        stamp_path = Path(self.test_dir) / "stamp_entry.json"
        self.assertTrue(self.validator.validate_stamp_entry(str(stamp_path)))
        
        # Validate directory
        self.assertTrue(self.validator.validate_directory(self.test_dir))
    
    def test_json_format(self):
        """Test that generated JSON files are valid."""
        self.generator.generate_rsa_keypair()
        self.generator.save_files(self.test_dir, "3.1.0")
        
        # Test config JSON
        config_path = Path(self.test_dir) / "config_master.json"
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        
        self.assertIsInstance(config_data, dict)
        
        # Test stamp JSON
        stamp_path = Path(self.test_dir) / "stamp_entry.json"
        with open(stamp_path, 'r') as f:
            stamp_data = json.load(f)
        
        self.assertIsInstance(stamp_data, dict)
    
    def test_different_key_sizes(self):
        """Test generation with different key sizes."""
        key_sizes = [1024, 2048]
        
        for key_size in key_sizes:
            with self.subTest(key_size=key_size):
                generator = MasterKeyGenerator(key_size=key_size, bit_size=128)
                generator.generate_rsa_keypair()
                
                self.assertEqual(generator.private_key.key_size, key_size)
                
                # Test that config can be created
                config = generator.create_master_key_config()
                self.assertIsInstance(config, dict)


class TestConfigValidator(unittest.TestCase):
    """Test cases for the Config Validator."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.validator = ConfigValidator()
    
    def tearDown(self):
        """Clean up test environment."""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_invalid_config_validation(self):
        """Test validation of invalid config files."""
        # Create invalid config file
        invalid_config = {"Key": "invalid", "BitSize": "not_int"}
        config_path = Path(self.test_dir) / "config_master.json"
        
        with open(config_path, 'w') as f:
            json.dump(invalid_config, f)
        
        # Should fail validation
        self.assertFalse(self.validator.validate_master_config(str(config_path)))
    
    def test_missing_files_validation(self):
        """Test validation when files are missing."""
        # Should fail when directory is empty
        self.assertFalse(self.validator.validate_directory(self.test_dir))


def run_tests():
    """Run all tests."""
    print("Running Master Key Generator Tests...")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestMasterKeyGenerator))
    suite.addTests(loader.loadTestsFromTestCase(TestConfigValidator))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("🎉 All tests passed!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
