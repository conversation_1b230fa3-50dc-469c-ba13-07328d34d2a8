#!/usr/bin/env python3
"""
Test script for batch encryption functionality.
"""

import json
import tempfile
from pathlib import Path

from crypto_tool import CollapseDecryptor
import sys
import os

# Add MasterKeyGenerator to path
sys.path.append(str(Path(__file__).parent.parent / "MasterKeyGenerator"))
from generate_master_key import Master<PERSON>eyGenerator


def test_single_file_encryption():
    """Test encryption of a single configuration file."""
    
    print("=" * 60)
    print("Testing Single File Encryption")
    print("=" * 60)
    
    # Create test configuration with plaintext fields
    test_config = {
        "ProfileName": "TestProfile",
        "LauncherResourceURL": "https://example.com/resources",
        "LauncherNewsURL": "https://news.example.com/api",
        "GameDispatchArrayURL": [
            "https://api1.example.com/dispatch",
            "https://api2.example.com/dispatch"
        ],
        "LauncherResourceChunksURL": {
            "MainBranchMatchingField": "game",
            "BranchUrl": "https://branch.example.com/resources",
            "MainUrl": "https://main.example.com/resources",
            "PreloadUrl": "https://preload.example.com/resources",
            "PatchUrl": "https://patch.example.com/resources"
        },
        "PlaintextField": "This should not be encrypted",
        "IsRepairEnabled": True
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Generate master key
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        master_config = generator.create_master_key_config()
        
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        # Save test config
        input_config_path = temp_path / "test_config.json"
        with open(input_config_path, 'w') as f:
            json.dump(test_config, f, indent=2)
        
        print(f"Created test config with plaintext fields:")
        print(f"  - LauncherResourceURL: {test_config['LauncherResourceURL']}")
        print(f"  - LauncherNewsURL: {test_config['LauncherNewsURL']}")
        print(f"  - GameDispatchArrayURL: {len(test_config['GameDispatchArrayURL'])} items")
        print(f"  - LauncherResourceChunksURL: {len(test_config['LauncherResourceChunksURL'])} fields")
        
        # Test encryption
        print("\n" + "-" * 40)
        print("Testing Encryption:")
        print("-" * 40)
        
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            
            output_config_path = temp_path / "encrypted_config.json"
            encrypted_config = decryptor.encrypt_config_file(
                str(input_config_path),
                str(output_config_path)
            )
            
            print("\n✓ Encryption completed successfully")
            
            # Verify encrypted config
            print("\nVerifying encrypted config:")
            print(f"  - LauncherResourceURL: {'Encrypted' if encrypted_config['LauncherResourceURL'] != test_config['LauncherResourceURL'] else 'Not encrypted'}")
            print(f"  - LauncherNewsURL: {'Encrypted' if encrypted_config['LauncherNewsURL'] != test_config['LauncherNewsURL'] else 'Not encrypted'}")
            print(f"  - PlaintextField: {'Unchanged' if encrypted_config['PlaintextField'] == test_config['PlaintextField'] else 'Changed'}")
            
            # Check nested fields
            original_chunks = test_config['LauncherResourceChunksURL']
            encrypted_chunks = encrypted_config['LauncherResourceChunksURL']
            
            for key in ['BranchUrl', 'MainUrl', 'PreloadUrl', 'PatchUrl']:
                is_encrypted = encrypted_chunks[key] != original_chunks[key]
                print(f"  - LauncherResourceChunksURL.{key}: {'Encrypted' if is_encrypted else 'Not encrypted'}")
            
            return True
            
        except Exception as e:
            print(f"❌ Encryption failed: {e}")
            return False


def test_encryption_decryption_cycle():
    """Test complete encryption -> decryption cycle."""
    
    print("\n" + "=" * 60)
    print("Testing Encryption -> Decryption Cycle")
    print("=" * 60)
    
    # Original test data
    original_data = {
        "LauncherResourceURL": "https://original.example.com/resources",
        "GameDispatchArrayURL": [
            "https://dispatch1.example.com",
            "https://dispatch2.example.com"
        ]
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Generate master key
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        master_config = generator.create_master_key_config()
        
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        # Save original config
        original_path = temp_path / "original.json"
        with open(original_path, 'w') as f:
            json.dump(original_data, f, indent=2)
        
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            
            # Step 1: Encrypt
            encrypted_path = temp_path / "encrypted.json"
            encrypted_config = decryptor.encrypt_config_file(
                str(original_path),
                str(encrypted_path)
            )
            
            print("✓ Step 1: Encryption completed")
            
            # Step 2: Decrypt
            decrypted_path = temp_path / "decrypted.json"
            decrypted_config = decryptor.decrypt_config_file(
                str(encrypted_path),
                str(decrypted_path)
            )
            
            print("✓ Step 2: Decryption completed")
            
            # Step 3: Verify data integrity
            print("\nVerifying data integrity:")
            
            for key, original_value in original_data.items():
                decrypted_value = decrypted_config.get(key)
                
                if isinstance(original_value, list):
                    matches = len(original_value) == len(decrypted_value) and all(
                        orig == decr for orig, decr in zip(original_value, decrypted_value)
                    )
                else:
                    matches = original_value == decrypted_value
                
                status = "✓" if matches else "❌"
                print(f"  {status} {key}: {'Match' if matches else 'Mismatch'}")
                
                if not matches:
                    print(f"    Original: {original_value}")
                    print(f"    Decrypted: {decrypted_value}")
            
            return True
            
        except Exception as e:
            print(f"❌ Encryption/Decryption cycle failed: {e}")
            return False


def test_batch_processing():
    """Test batch encryption of multiple files."""
    
    print("\n" + "=" * 60)
    print("Testing Batch Processing")
    print("=" * 60)
    
    # Create multiple test configs
    test_configs = {
        "config1.json": {
            "ProfileName": "Config1",
            "LauncherResourceURL": "https://config1.example.com/resources"
        },
        "config2.json": {
            "ProfileName": "Config2", 
            "LauncherNewsURL": "https://config2.example.com/news"
        },
        "config3.json": {
            "ProfileName": "Config3",
            "GameDispatchArrayURL": ["https://config3.example.com/dispatch"]
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        input_dir = temp_path / "input"
        output_dir = temp_path / "output"
        
        input_dir.mkdir()
        output_dir.mkdir()
        
        # Generate master key
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        master_config = generator.create_master_key_config()
        
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        # Create test config files
        for filename, config_data in test_configs.items():
            config_path = input_dir / filename
            with open(config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
        
        print(f"Created {len(test_configs)} test config files")
        
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            
            # Simulate batch encryption (process each file)
            for config_file in input_dir.glob('*.json'):
                output_file = output_dir / f"encrypted_{config_file.name}"
                decryptor.encrypt_config_file(str(config_file), str(output_file))
            
            print("✓ Batch encryption completed")
            
            # Verify all files were processed
            encrypted_files = list(output_dir.glob('encrypted_*.json'))
            print(f"✓ Generated {len(encrypted_files)} encrypted files")
            
            return len(encrypted_files) == len(test_configs)
            
        except Exception as e:
            print(f"❌ Batch processing failed: {e}")
            return False


def main():
    """Run all tests."""
    print("Testing Batch Encryption Functionality")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Single file encryption
    if test_single_file_encryption():
        success_count += 1
    
    # Test 2: Encryption/Decryption cycle
    if test_encryption_decryption_cycle():
        success_count += 1
    
    # Test 3: Batch processing
    if test_batch_processing():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 All tests passed! Batch encryption is working correctly.")
        print("\nUsage examples:")
        print("  # Encrypt single file")
        print("  python crypto_tool.py encrypt-config --input config.json --output encrypted.json --master-key key.json")
        print("  # Batch encrypt multiple files")
        print("  python crypto_tool.py batch-encrypt --input-dir ./configs --output-dir ./encrypted --master-key key.json")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
