﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable IdentifierTypo  -->
<!--  <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Local  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<Page x:Class="CollapseLauncher.Pages.HomePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:animatedvisuals="using:Microsoft.UI.Xaml.Controls.AnimatedVisuals"
      xmlns:customcontrol="using:CollapseLauncher.CustomControls"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:extension="using:CollapseLauncher.Extension"
      xmlns:helper="using:Hi3Helper"
      xmlns:imageex="using:ImageEx"
      xmlns:local="using:CollapseLauncher"
      xmlns:localStatic="using:CollapseLauncher.Statics"
      xmlns:localWindowSize="using:CollapseLauncher.WindowSize"
      xmlns:lottie="using:CollapseLauncher.AnimatedVisuals.Lottie"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:pages="using:CollapseLauncher.Pages"
      xmlns:s="using:CommunityToolkit.WinUI"
      xmlns:sophonTypes="using:CollapseLauncher.Helper.LauncherApiLoader.Legacy"
      x:Name="HomePage_Page"
      x:FieldModifier="public"
      CacheMode="BitmapCache"
      NavigationCacheMode="Enabled"
      Unloaded="Page_Unloaded"
      mc:Ignorable="d">
    <Page.Resources>
        <ThemeShadow x:Key="SharedShadow" />
        <s:AttachedDropShadow x:Key="SharedDropShadow"
                              BlurRadius="16"
                              CastTo="{x:Bind ImageEventImgShadow}"
                              Opacity="0.3"
                              Offset="0,4,0" />
    </Page.Resources>
    <Grid x:Name="FrameGrid"
          Margin="32,8,32,32"
          HorizontalAlignment="Stretch">
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="41" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.SidePanel1Width}" />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid x:Name="ImageEventImgGrid"
              Grid.Row="{x:Bind CurrentBannerIconRow}"
              Grid.RowSpan="{x:Bind pages:HomePage.CurrentBannerIconRowSpan}"
              Grid.Column="{x:Bind CurrentBannerIconColumn}"
              Grid.ColumnSpan="{x:Bind pages:HomePage.CurrentBannerIconColumnSpan}"
              Height="{x:Bind CurrentBannerIconHeight}"
              Margin="{x:Bind CurrentBannerIconMargin}"
              HorizontalAlignment="{x:Bind CurrentBannerIconHorizontalAlign}"
              VerticalAlignment="{x:Bind CurrentBannerIconVerticalAlign}"
              Visibility="Collapsed">
            <Grid x:Name="ImageEventImgShadow" />
            <Image x:Name="ImageEventImg"
                   s:Effects.Shadow="{ThemeResource SharedDropShadow}"
                   Loaded="SetHandCursor"
                   PointerPressed="ClickImageEventSpriteLink" />
        </Grid>
        <InfoBar x:Name="PreloadDialogBox"
                 Title=""
                 Grid.Row="0"
                 Grid.Column="0"
                 Grid.ColumnSpan="2"
                 MaxWidth="870"
                 Margin="0,16,48,-32"
                 HorizontalAlignment="Stretch"
                 VerticalAlignment="Top"
                 Background="{ThemeResource InfoBarAnnouncementBrush}"
                 CornerRadius="12"
                 IsClosable="True"
                 IsOpen="False"
                 Opacity="0"
                 Severity="Informational"
                 Shadow="{ThemeResource SharedShadow}">
            <InfoBar.Content>
                <StackPanel Margin="0,-4,38,0"
                            Orientation="Vertical">
                    <Grid x:Name="ProgressPreStatusGrid"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Top"
                          Visibility="Collapsed">
                        <Grid.ChildrenTransitions>
                            <PopupThemeTransition />
                        </Grid.ChildrenTransitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition Width="32" />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Grid Grid.Column="0"
                              HorizontalAlignment="Stretch">
                            <StackPanel HorizontalAlignment="Stretch"
                                        VerticalAlignment="Center"
                                        Orientation="Vertical">
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0"
                                                Grid.ColumnSpan="2">
                                        <StackPanel HorizontalAlignment="Left"
                                                    Orientation="Horizontal">
                                            <TextBlock x:Name="ProgressPrePerFileStatusFooter"
                                                       FontSize="12"
                                                       FontWeight="Bold"
                                                       Text="Downloading" />
                                        </StackPanel>
                                        <TextBlock x:Name="ProgressPreStatusFooter"
                                                   Margin="0,4,0,0"
                                                   FontSize="12"
                                                   FontWeight="Bold"
                                                   Text="{x:Bind helper:Locale.Lang._Misc.SpeedPlaceholder}" />
                                    </StackPanel>
                                    <StackPanel Grid.Column="1"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Bottom"
                                                Orientation="Horizontal">
                                        <TextBlock FontSize="12"
                                                   FontWeight="Bold"
                                                   Text="{x:Bind progressPrePerFileBar.Value, Mode=OneWay}" />
                                        <TextBlock FontSize="12"
                                                   FontWeight="Bold"
                                                   Text="%" />
                                    </StackPanel>
                                    <StackPanel Grid.Column="2"
                                                VerticalAlignment="Bottom">
                                        <TextBlock x:Name="ProgressPrePerFileStatusSubtitle"
                                                   HorizontalAlignment="Right"
                                                   FontSize="12"
                                                   Text="{x:Bind helper:Locale.Lang._Misc.PerFromToPlaceholder}" />
                                    </StackPanel>
                                </Grid>
                                <ProgressBar x:Name="progressPrePerFileBar"
                                             Margin="0,4,0,16"
                                             HorizontalAlignment="Stretch"
                                             VerticalAlignment="Top"
                                             IsIndeterminate="True"
                                             Maximum="100"
                                             Value="0" />
                            </StackPanel>
                        </Grid>
                        <Grid Grid.Column="2"
                              HorizontalAlignment="Stretch"
                              VerticalAlignment="Bottom">
                            <StackPanel HorizontalAlignment="Stretch"
                                        VerticalAlignment="Center"
                                        Orientation="Vertical">
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0"
                                                VerticalAlignment="Bottom">
                                        <TextBlock x:Name="ProgressPreStatusSubtitle"
                                                   HorizontalAlignment="Left"
                                                   FontSize="12"
                                                   Text="{x:Bind helper:Locale.Lang._Misc.PerFromToPlaceholder}" />
                                    </StackPanel>
                                    <StackPanel Grid.Column="1"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Bottom"
                                                Orientation="Horizontal">
                                        <TextBlock FontSize="12"
                                                   FontWeight="Bold"
                                                   Text="{x:Bind progressPreBar.Value, Mode=OneWay}" />
                                        <TextBlock FontSize="12"
                                                   FontWeight="Bold"
                                                   Text="%" />
                                    </StackPanel>
                                    <StackPanel Grid.Column="2">
                                        <TextBlock HorizontalAlignment="Right"
                                                   FontSize="12"
                                                   FontWeight="Bold"
                                                   Text="{x:Bind helper:Locale.Lang._Misc.TimeRemain}" />
                                        <TextBlock x:Name="ProgressPreTimeLeft"
                                                   Margin="0,4,0,0"
                                                   HorizontalAlignment="Right"
                                                   FontSize="12"
                                                   FontWeight="Bold"
                                                   Text="-" />
                                    </StackPanel>
                                </Grid>
                                <ProgressBar x:Name="progressPreBar"
                                             Margin="0,4,0,16"
                                             HorizontalAlignment="Stretch"
                                             VerticalAlignment="Top"
                                             IsIndeterminate="True"
                                             Maximum="100"
                                             Value="0" />
                            </StackPanel>
                        </Grid>
                    </Grid>
                    <Grid x:Name="ProgressPreSophonStatusGrid"
                          Visibility="Collapsed">
                        <Grid.ChildrenTransitions>
                            <PopupThemeTransition />
                        </Grid.ChildrenTransitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition Width="32" />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Grid Grid.Column="0"
                              HorizontalAlignment="Stretch">
                            <StackPanel HorizontalAlignment="Stretch"
                                        VerticalAlignment="Center"
                                        Orientation="Vertical">
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0"
                                                Grid.ColumnSpan="2">
                                        <StackPanel HorizontalAlignment="Left"
                                                    Orientation="Horizontal">
                                            <TextBlock FontSize="12"
                                                       FontWeight="Bold"
                                                       Text="{x:Bind ProgressPrePerFileStatusFooter.Text, Mode=OneWay}" />
                                        </StackPanel>
                                        <TextBlock Margin="0,4,0,0"
                                                   FontSize="12"
                                                   FontWeight="Bold"
                                                   Text="{x:Bind ProgressPreStatusFooter.Text, Mode=OneWay}" />
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Grid>
                        <Grid Grid.Row="0"
                              Grid.Column="0"
                              Grid.ColumnSpan="3"
                              Margin="0,0,0,8"
                              HorizontalAlignment="Stretch"
                              ColumnSpacing="16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Bottom">
                                <TextBlock HorizontalAlignment="Right"
                                           FontSize="12"
                                           Text="{x:Bind ProgressPreStatusSubtitle.Text, Mode=OneWay}" />
                            </StackPanel>
                            <StackPanel Grid.Column="1"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Bottom"
                                        Orientation="Horizontal">
                                <TextBlock FontSize="12"
                                           FontWeight="Bold"
                                           Text="{x:Bind progressPreBar.Value, Mode=OneWay}" />
                                <TextBlock FontSize="12"
                                           FontWeight="Bold"
                                           Text="%" />
                            </StackPanel>
                        </Grid>
                        <Grid Grid.Row="0"
                              Grid.Column="2"
                              HorizontalAlignment="Stretch"
                              VerticalAlignment="Bottom">
                            <StackPanel HorizontalAlignment="Stretch"
                                        VerticalAlignment="Center"
                                        Orientation="Vertical">
                                <Grid Margin="0,0,0,8">
                                    <StackPanel>
                                        <TextBlock HorizontalAlignment="Right"
                                                   FontSize="12"
                                                   FontWeight="Bold"
                                                   Text="{x:Bind helper:Locale.Lang._Misc.TimeRemain}" />
                                        <TextBlock Margin="0,4,0,0"
                                                   HorizontalAlignment="Right"
                                                   FontSize="12"
                                                   FontWeight="Bold"
                                                   Text="{x:Bind ProgressPreTimeLeft.Text, Mode=OneWay}" />
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Grid>
                        <ProgressBar Grid.Row="1"
                                     Grid.Column="0"
                                     Grid.ColumnSpan="3"
                                     Margin="0,8,0,24"
                                     HorizontalAlignment="Stretch"
                                     VerticalAlignment="Bottom"
                                     IsIndeterminate="{x:Bind progressPreBar.IsIndeterminate, Mode=OneWay}"
                                     Maximum="{x:Bind progressPreBar.Maximum, Mode=OneWay}"
                                     Value="{x:Bind progressPreBar.Value, Mode=OneWay}" />
                    </Grid>
                    <Grid x:Name="ProgressPreButtonGrid"
                          Margin="0,-8,0,16"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Top"
                          Visibility="Collapsed">
                        <Grid.ChildrenTransitions>
                            <PopupThemeTransition />
                        </Grid.ChildrenTransitions>
                        <Grid x:Name="DownloadModeLabelPreload"
                              Padding="8,2"
                              HorizontalAlignment="Left"
                              VerticalAlignment="Top"
                              Background="{ThemeResource AccentColorBrush}"
                              CornerRadius="8"
                              Shadow="{ThemeResource SharedShadow}"
                              Translation="0,0,16"
                              Visibility="Collapsed">
                            <TextBlock x:Name="DownloadModeLabelPreloadText"
                                       FontSize="10"
                                       FontWeight="Bold"
                                       Foreground="{ThemeResource ApplicationPageBackgroundThemeBrush}"
                                       HorizontalTextAlignment="Center"
                                       Text="" />
                        </Grid>
                        <Button x:Name="PauseDownloadPreBtn"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Stretch"
                                HorizontalContentAlignment="Stretch"
                                Click="CancelInstallationProcedure"
                                CornerRadius="16"
                                Shadow="{ThemeResource SharedShadow}"
                                Style="{ThemeResource AcrylicButtonStyle}"
                                Translation="0,0,8"
                                Visibility="Visible">
                            <Button.Content>
                                <Grid Margin="8,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Margin="0,-2,16,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               FontWeight="Medium"
                                               HorizontalTextAlignment="Left"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.PauseDownloadBtn}" />
                                    <FontIcon Grid.Column="1"
                                              HorizontalAlignment="Right"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              FontSize="14"
                                              Glyph="" />
                                </Grid>
                            </Button.Content>
                        </Button>
                        <Button x:Name="ResumeDownloadPreBtn"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Stretch"
                                HorizontalContentAlignment="Stretch"
                                Click="PredownloadDialog"
                                CornerRadius="16"
                                Shadow="{ThemeResource SharedShadow}"
                                Style="{ThemeResource AcrylicButtonStyle}"
                                Translation="0,0,8"
                                Visibility="Collapsed">
                            <Button.Content>
                                <Grid Margin="8,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Margin="0,-2,16,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               FontWeight="Medium"
                                               HorizontalTextAlignment="Left"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.ResumeDownloadBtn}" />
                                    <FontIcon Grid.Column="1"
                                              HorizontalAlignment="Right"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              FontSize="14"
                                              Glyph="" />
                                </Grid>
                            </Button.Content>
                        </Button>
                    </Grid>
                </StackPanel>
            </InfoBar.Content>
            <InfoBar.ActionButton>
                <Button x:Name="DownloadPreBtn"
                        Margin="0,0,-16,0"
                        HorizontalAlignment="Right"
                        HorizontalContentAlignment="Stretch"
                        Click="PredownloadDialog"
                        CornerRadius="16"
                        Shadow="{ThemeResource SharedShadow}"
                        Style="{ThemeResource AcrylicSemiButtonStyle}"
                        Translation="0,0,8"
                        Visibility="Visible">
                    <Button.Content>
                        <Grid Margin="8,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock Margin="0,-2,16,0"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"
                                       HorizontalTextAlignment="Left"
                                       Text="{x:Bind helper:Locale.Lang._HomePage.DownloadBtn}" />
                            <FontIcon Grid.Column="1"
                                      HorizontalAlignment="Right"
                                      FontFamily="{ThemeResource FontAwesomeSolid}"
                                      FontSize="14"
                                      Glyph="" />
                        </Grid>
                    </Button.Content>
                </Button>
            </InfoBar.ActionButton>
        </InfoBar>
        <StackPanel x:Name="SocMedPanel"
                    Grid.Row="0"
                    Grid.RowSpan="2"
                    Grid.Column="1"
                    Margin="0,18,-32,0"
                    Padding="0,4"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Background="{ThemeResource SocMedPanelAcrylicBrush}"
                    CornerRadius="12,0,0,12"
                    Orientation="Vertical"
                    Shadow="{ThemeResource SharedShadow}">
            <StackPanel.Transitions>
                <PopupThemeTransition />
            </StackPanel.Transitions>
            <ScrollViewer x:Name="SocMedPanelScrollView"
                          MaxHeight="336"
                          VerticalScrollBarVisibility="Auto">
                <ItemsControl IsTabStop="False"
                              ItemsSource="{x:Bind pages:HomePage.GameNewsData.SocialMedia}"
                              TabFocusNavigation="Once"
                              XYFocusKeyboardNavigation="Enabled">
                    <ItemsControl.Transitions>
                        <PopupThemeTransition />
                    </ItemsControl.Transitions>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate x:DataType="sophonTypes:LauncherGameNewsSocialMedia">
                            <Grid Loaded="SetHandCursor">
                                <Button x:Name="SocMedButton"
                                        Width="48px"
                                        Height="48px"
                                        Margin="8,4"
                                        Padding="8"
                                        Click="OpenSocMedLink"
                                        CornerRadius="8"
                                        FlyoutBase.AttachedFlyout="{Binding ElementName=SocMedFlyout}"
                                        Loaded="ApplyShadowToImageElement"
                                        PointerEntered="FadeInSocMedButton"
                                        PointerExited="FadeOutSocMedButton"
                                        Shadow="{ThemeResource SharedShadow}"
                                        Style="{ThemeResource TransparentDefaultButtonStyle}"
                                        Tag="{x:Bind SocialMediaUrl}">
                                    <ToolTipService.ToolTip>
                                        <ToolTip Opened="ShowSocMedFlyout"
                                                 Tag="{Binding ElementName=SocMedButton}"
                                                 Visibility="Collapsed" />
                                    </ToolTipService.ToolTip>
                                    <Button.Flyout>
                                        <Flyout x:Name="SocMedFlyout"
                                                OverlayInputPassThroughElement="{Binding ElementName=SocMedPanelScrollView}"
                                                Placement="Left">
                                            <StackPanel x:Name="DummySocMedStackPanel"
                                                        Padding="10,6"
                                                        Background="Transparent"
                                                        Loaded="OnLoadedSocMedFlyout"
                                                        PointerExited="HideSocMedFlyout"
                                                        Tag="{x:Bind}"
                                                        Visibility="Collapsed">
                                                <Grid Tag="{Binding ElementName=SocMedFlyout}"
                                                      Visibility="Collapsed" />

                                                <!--  START: SocialMediaParentPanel_QR  -->
                                                <StackPanel x:Name="SocialMediaParentPanel_QR"
                                                            Margin="4,8,4,0">
                                                    <Grid Margin="0,0,0,8"
                                                          Background="White"
                                                          CornerRadius="5"
                                                          Shadow="{ThemeResource SharedShadow}"
                                                          Translation="0,0,16">
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition />
                                                            <RowDefinition Height="Auto" />
                                                        </Grid.RowDefinitions>
                                                        <imageex:ImageEx Width="160"
                                                                         Margin="4"
                                                                         HorizontalAlignment="Center" />
                                                        <TextBlock Grid.Row="1"
                                                                   Margin="0,0,0,8"
                                                                   HorizontalAlignment="Center"
                                                                   FontWeight="Bold"
                                                                   Foreground="Black"
                                                                   TextAlignment="Center"
                                                                   TextWrapping="Wrap" />
                                                    </Grid>
                                                </StackPanel>

                                                <!--  START: SocialMediaParentPanel_Links  -->
                                                <StackPanel x:Name="SocialMediaParentPanel_Links"
                                                            Margin="0,2">
                                                    <ItemsControl>
                                                        <ItemsControl.ItemTemplate>
                                                            <DataTemplate x:DataType="sophonTypes:LauncherGameNewsSocialMediaQrLinks">
                                                                <Button MinWidth="160"
                                                                        Margin="0,2"
                                                                        HorizontalAlignment="Stretch"
                                                                        HorizontalContentAlignment="Stretch"
                                                                        Click="OpenSocMedLink"
                                                                        CornerRadius="5"
                                                                        Loaded="SetHandCursor"
                                                                        PointerEntered="ElementScaleOutHoveredPointerEntered"
                                                                        PointerExited="ElementScaleInHoveredPointerExited"
                                                                        Shadow="{ThemeResource SharedShadow}"
                                                                        Style="{ThemeResource AcrylicSemiButtonStyle}"
                                                                        Tag="{x:Bind Url}"
                                                                        Translation="0,0,8">
                                                                    <Grid Margin="0,0,0,0"
                                                                          HorizontalAlignment="Stretch">
                                                                        <Grid.ColumnDefinitions>
                                                                            <ColumnDefinition />
                                                                            <ColumnDefinition Width="Auto" />
                                                                        </Grid.ColumnDefinitions>
                                                                        <Grid HorizontalAlignment="Left">
                                                                            <TextBlock HorizontalAlignment="Left"
                                                                                       VerticalAlignment="Center"
                                                                                       FontWeight="Medium"
                                                                                       HorizontalTextAlignment="Left"
                                                                                       Text="{x:Bind Title}" />
                                                                        </Grid>
                                                                        <Grid Grid.Column="1"
                                                                              Margin="4,0,0,0"
                                                                              HorizontalAlignment="Center"
                                                                              VerticalAlignment="Center"
                                                                              RenderTransformOrigin="0.5, 0.5">
                                                                            <Grid.RenderTransform>
                                                                                <RotateTransform Angle="-90" />
                                                                            </Grid.RenderTransform>
                                                                            <AnimatedIcon Width="14"
                                                                                          HorizontalAlignment="Center"
                                                                                          VerticalAlignment="Center">
                                                                                <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                                                                <AnimatedIcon.FallbackIconSource>
                                                                                    <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                                                                    Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                                                                    Glyph="&#xE70D;" />
                                                                                </AnimatedIcon.FallbackIconSource>
                                                                            </AnimatedIcon>
                                                                        </Grid>
                                                                    </Grid>
                                                                </Button>
                                                            </DataTemplate>
                                                        </ItemsControl.ItemTemplate>
                                                    </ItemsControl>
                                                </StackPanel>

                                                <!--  START: SocialMediaParentPanel_Description  -->
                                                <TextBlock x:Name="SocialMediaParentPanel_Description"
                                                           Margin="0,4"
                                                           HorizontalAlignment="Center"
                                                           TextWrapping="Wrap" />
                                            </StackPanel>
                                            <Flyout.FlyoutPresenterStyle>
                                                <Style TargetType="FlyoutPresenter">
                                                    <Setter Property="Margin" Value="3,0,0,0" />
                                                    <Setter Property="CornerRadius" Value="5" />
                                                    <Setter Property="Padding" Value="0" />
                                                    <Setter Property="MinHeight" Value="0" />
                                                    <Setter Property="MinWidth" Value="0" />
                                                </Style>
                                            </Flyout.FlyoutPresenterStyle>
                                        </Flyout>
                                    </Button.Flyout>
                                    <Grid>
                                        <imageex:ImageEx x:Name="Icon"
                                                         Source="{x:Bind IconImg}" />
                                        <imageex:ImageEx x:Name="IconHover"
                                                         Opacity="0"
                                                         Source="{x:Bind IconImgHover}" />
                                    </Grid>
                                </Button>
                            </Grid>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </StackPanel>
        <Grid x:Name="SidePanel"
              Grid.Row="1"
              Grid.Column="0"
              Margin="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelBottomMargin}"
              HorizontalAlignment="Left"
              VerticalAlignment="Bottom"
              CornerRadius="8"
              PointerEntered="SidePanelScaleOutHoveredPointerEntered"
              PointerExited="SidePanelScaleInHoveredPointerExited"
              Shadow="{ThemeResource SharedShadow}"
              Translation="0,0,32">
            <Grid.RenderTransform>
                <CompositeTransform />
            </Grid.RenderTransform>
            <Grid x:Name="ImageCarouselAndPostPanel"
                  HorizontalAlignment="Left"
                  VerticalAlignment="Bottom"
                  Background="{ThemeResource PostAcrylicBrush}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <Grid PointerEntered="CarouselPointerEntered"
                      PointerExited="CarouselPointerExited">
                    <FlipView x:Name="ImageCarousel"
                              Height="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.EventPostCarouselBounds.Height}"
                              HorizontalAlignment="Stretch"
                              CornerRadius="4"
                              ItemsSource="{x:Bind pages:HomePage.GameNewsData.NewsCarousel}"
                              Shadow="{ThemeResource SharedShadow}"
                              Style="{ThemeResource CollapseFlipViewStyle}"
                              Translation="0,0,12">
                        <FlipView.ItemTemplate>
                            <DataTemplate x:DataType="sophonTypes:LauncherGameNewsCarousel">
                                <imageex:ImageEx EnableLazyLoading="True"
                                                 IsCacheEnabled="True"
                                                 Loaded="SetHandCursor"
                                                 PlaceholderSource="ms-appx:///Assets/Images/PageBackground/default.png"
                                                 PointerPressed="OpenImageLinkFromTag"
                                                 Source="{x:Bind CarouselImg}"
                                                 Stretch="Fill"
                                                 Tag="{x:Bind CarouselUrl}"
                                                 ToolTipService.ToolTip="{x:Bind CarouselTitle}" />
                            </DataTemplate>
                        </FlipView.ItemTemplate>
                    </FlipView>
                </Grid>
                <Grid x:Name="PostPanel"
                      Grid.Row="1"
                      Width="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelBounds.Width}"
                      Height="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelBounds.Height}"
                      Padding="8,6,8,0"
                      Visibility="{x:Bind pages:HomePage.IsPostInfoPanelAllEmpty}">
                    <Pivot HorizontalAlignment="Left"
                           SelectedIndex="{x:Bind pages:HomePage.DefaultPostPanelIndex}">
                        <Pivot.Resources>
                            <Style BasedOn="{StaticResource MinScrollBarStyle}"
                                   TargetType="ScrollBar" />
                        </Pivot.Resources>
                        <PivotItem Margin="12,0,-2,0">
                            <PivotItem.Header>
                                <TextBlock Margin="0,0,0,6"
                                           FontSize="13"
                                           FontWeight="Bold"
                                           Loaded="SetHandCursor"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.PostPanel_Events}" />
                            </PivotItem.Header>
                            <Grid>
                                <ScrollViewer Margin="0,4,0,0"
                                              VerticalScrollMode="Auto"
                                              Visibility="{x:Bind pages:HomePage.IsPostEventPanelVisible}">
                                    <StackPanel Margin="0,0,16,4">
                                        <ItemsControl IsTabStop="False"
                                                      ItemsSource="{x:Bind pages:HomePage.GameNewsData.NewsPostTypeActivity}"
                                                      TabFocusNavigation="Once"
                                                      XYFocusKeyboardNavigation="Enabled">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate x:DataType="sophonTypes:LauncherGameNewsPost">
                                                    <Button HorizontalAlignment="Stretch"
                                                            Click="OpenButtonLinkFromTag"
                                                            Loaded="SetHandCursor"
                                                            Style="{ThemeResource DraftButtonStyle}"
                                                            Tag="{x:Bind PostUrl}">
                                                        <Grid Margin="0,2"
                                                              HorizontalAlignment="Stretch"
                                                              Background="Transparent"
                                                              PointerEntered="HyperLink_OnPointerEntered"
                                                              PointerExited="HyperLink_OnPointerExited"
                                                              ToolTipService.ToolTip="{x:Bind Title}">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition />
                                                                <ColumnDefinition Width="Auto" />
                                                            </Grid.ColumnDefinitions>
                                                            <customcontrol:CompressedTextBlock Margin="0,0,8,0"
                                                                                               HorizontalAlignment="Left"
                                                                                               VerticalAlignment="Center"
                                                                                               FontSize="12"
                                                                                               Foreground="{ThemeResource TextFillColorPrimary}"
                                                                                               Text="{x:Bind Title}"
                                                                                               TextTrimming="CharacterEllipsis" />
                                                            <TextBlock Grid.Column="1"
                                                                       HorizontalAlignment="Right"
                                                                       VerticalAlignment="Center"
                                                                       FontSize="12"
                                                                       FontWeight="Bold"
                                                                       Foreground="{ThemeResource AccentColor}"
                                                                       Text="{x:Bind PostDate}" />
                                                        </Grid>
                                                    </Button>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </StackPanel>
                                </ScrollViewer>
                                <StackPanel Margin="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonMargin}"
                                            HorizontalAlignment="Right"
                                            Visibility="{x:Bind pages:HomePage.IsPostEventPanelEmpty}">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Width="{x:Bind pages:HomePage.PostEmptyMascotTextWidth}"
                                                   Margin="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonTextMargin}"
                                                   HorizontalAlignment="Right"
                                                   VerticalAlignment="Bottom"
                                                   FontSize="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonTextSize}"
                                                   FontWeight="Black"
                                                   Opacity="0.8"
                                                   Text="{x:Bind helper:Locale.Lang._HomePage.PostPanel_NoNews}"
                                                   TextAlignment="Right"
                                                   TextWrapping="Wrap" />
                                        <Image Height="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonHeight}"
                                               Margin="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonInnerMargin}"
                                               HorizontalAlignment="Right"
                                               VerticalAlignment="Center"
                                               Source="{x:Bind NoNewsSplashMascot}" />
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </PivotItem>
                        <PivotItem Margin="12,0,-2,0">
                            <PivotItem.Header>
                                <TextBlock Margin="0,0,0,6"
                                           FontSize="13"
                                           FontWeight="Bold"
                                           Loaded="SetHandCursor"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.PostPanel_Notices}" />
                            </PivotItem.Header>
                            <Grid>
                                <ScrollViewer Margin="0,4,0,0"
                                              VerticalScrollMode="Auto"
                                              Visibility="{x:Bind pages:HomePage.IsPostNoticePanelVisible}">
                                    <StackPanel Margin="0,0,16,4">
                                        <ItemsControl IsTabStop="False"
                                                      ItemsSource="{x:Bind pages:HomePage.GameNewsData.NewsPostTypeAnnouncement}"
                                                      TabFocusNavigation="Once"
                                                      XYFocusKeyboardNavigation="Enabled">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate x:DataType="sophonTypes:LauncherGameNewsPost">
                                                    <Button HorizontalAlignment="Stretch"
                                                            Click="OpenButtonLinkFromTag"
                                                            Loaded="SetHandCursor"
                                                            Style="{ThemeResource DraftButtonStyle}"
                                                            Tag="{x:Bind PostUrl}">
                                                        <Grid Margin="0,2"
                                                              HorizontalAlignment="Stretch"
                                                              Background="Transparent"
                                                              PointerEntered="HyperLink_OnPointerEntered"
                                                              PointerExited="HyperLink_OnPointerExited"
                                                              ToolTipService.ToolTip="{x:Bind Title}">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition />
                                                                <ColumnDefinition Width="Auto" />
                                                            </Grid.ColumnDefinitions>
                                                            <customcontrol:CompressedTextBlock Margin="0,0,8,0"
                                                                                               HorizontalAlignment="Left"
                                                                                               VerticalAlignment="Center"
                                                                                               FontSize="12"
                                                                                               Foreground="{ThemeResource TextFillColorPrimary}"
                                                                                               Text="{x:Bind Title}"
                                                                                               TextTrimming="CharacterEllipsis" />
                                                            <TextBlock Grid.Column="1"
                                                                       HorizontalAlignment="Right"
                                                                       VerticalAlignment="Center"
                                                                       FontSize="12"
                                                                       FontWeight="Bold"
                                                                       Foreground="{ThemeResource AccentColor}"
                                                                       Text="{x:Bind PostDate}" />
                                                        </Grid>
                                                    </Button>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </StackPanel>
                                </ScrollViewer>
                                <StackPanel Margin="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonMargin}"
                                            HorizontalAlignment="Right"
                                            Visibility="{x:Bind pages:HomePage.IsPostNoticePanelEmpty}">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Width="{x:Bind pages:HomePage.PostEmptyMascotTextWidth}"
                                                   Margin="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonTextMargin}"
                                                   HorizontalAlignment="Right"
                                                   VerticalAlignment="Bottom"
                                                   FontSize="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonTextSize}"
                                                   FontWeight="Black"
                                                   Opacity="0.8"
                                                   Text="{x:Bind helper:Locale.Lang._HomePage.PostPanel_NoNews}"
                                                   TextAlignment="Right"
                                                   TextWrapping="Wrap" />
                                        <Image Height="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonHeight}"
                                               Margin="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonInnerMargin}"
                                               HorizontalAlignment="Right"
                                               VerticalAlignment="Center"
                                               Source="{x:Bind NoNewsSplashMascot}" />
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </PivotItem>
                        <PivotItem Margin="12,0,-2,0">
                            <PivotItem.Header>
                                <TextBlock Margin="0,0,0,6"
                                           FontSize="13"
                                           FontWeight="Bold"
                                           Loaded="SetHandCursor"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.PostPanel_Info}" />
                            </PivotItem.Header>
                            <Grid>
                                <ScrollViewer Margin="0,4,0,0"
                                              VerticalScrollMode="Auto"
                                              Visibility="{x:Bind pages:HomePage.IsPostInfoPanelVisible}">
                                    <StackPanel Margin="0,0,16,4">
                                        <ItemsControl IsTabStop="False"
                                                      ItemsSource="{x:Bind pages:HomePage.GameNewsData.NewsPostTypeInfo}"
                                                      TabFocusNavigation="Once"
                                                      XYFocusKeyboardNavigation="Enabled">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate x:DataType="sophonTypes:LauncherGameNewsPost">
                                                    <Button HorizontalAlignment="Stretch"
                                                            Click="OpenButtonLinkFromTag"
                                                            Loaded="SetHandCursor"
                                                            Style="{ThemeResource DraftButtonStyle}"
                                                            Tag="{x:Bind PostUrl}">
                                                        <Grid Margin="0,2"
                                                              HorizontalAlignment="Stretch"
                                                              Background="Transparent"
                                                              PointerEntered="HyperLink_OnPointerEntered"
                                                              PointerExited="HyperLink_OnPointerExited"
                                                              ToolTipService.ToolTip="{x:Bind Title}">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition />
                                                                <ColumnDefinition Width="Auto" />
                                                            </Grid.ColumnDefinitions>
                                                            <customcontrol:CompressedTextBlock Margin="0,0,8,0"
                                                                                               HorizontalAlignment="Left"
                                                                                               VerticalAlignment="Center"
                                                                                               FontSize="12"
                                                                                               Foreground="{ThemeResource TextFillColorPrimary}"
                                                                                               Text="{x:Bind Title}"
                                                                                               TextTrimming="CharacterEllipsis" />
                                                            <TextBlock Grid.Column="1"
                                                                       HorizontalAlignment="Right"
                                                                       VerticalAlignment="Center"
                                                                       FontSize="12"
                                                                       FontWeight="Bold"
                                                                       Foreground="{ThemeResource AccentColor}"
                                                                       Text="{x:Bind PostDate}" />
                                                        </Grid>
                                                    </Button>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </StackPanel>
                                </ScrollViewer>
                                <StackPanel Margin="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonMargin}"
                                            HorizontalAlignment="Right"
                                            Visibility="{x:Bind pages:HomePage.IsPostInfoPanelEmpty}">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Width="{x:Bind pages:HomePage.PostEmptyMascotTextWidth}"
                                                   Margin="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonTextMargin}"
                                                   HorizontalAlignment="Right"
                                                   VerticalAlignment="Bottom"
                                                   FontSize="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonTextSize}"
                                                   FontWeight="Black"
                                                   Opacity="0.8"
                                                   Text="{x:Bind helper:Locale.Lang._HomePage.PostPanel_NoNews}"
                                                   TextAlignment="Right"
                                                   TextWrapping="Wrap" />
                                        <Image Height="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonHeight}"
                                               Margin="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelPaimonInnerMargin}"
                                               HorizontalAlignment="Right"
                                               VerticalAlignment="Center"
                                               Source="{x:Bind NoNewsSplashMascot}" />
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </PivotItem>
                    </Pivot>
                </Grid>
                <Grid x:Name="ImageCarouselPipsPager"
                      Grid.Row="0"
                      Padding="8,0"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Bottom"
                      Background="{ThemeResource CarouselPipsAcrylicBrush}"
                      CornerRadius="8,8,0,0"
                      Shadow="{ThemeResource SharedShadow}"
                      Translation="0,0,16">
                    <PipsPager VerticalAlignment="Center"
                               NextButtonVisibility="Collapsed"
                               NumberOfPages="{x:Bind pages:HomePage.GameNewsData.NewsCarousel.Count}"
                               Orientation="Horizontal"
                               PreviousButtonVisibility="Collapsed"
                               SelectedPageIndex="{x:Bind Path=ImageCarousel.SelectedIndex, Mode=TwoWay}" />
                </Grid>
            </Grid>
        </Grid>
        <Grid x:Name="SophonProgressStatusGrid"
              Grid.Row="1"
              Grid.Column="0"
              Margin="0,0,0,48"
              HorizontalAlignment="Stretch"
              VerticalAlignment="Center"
              Shadow="{ThemeResource SharedShadow}"
              Visibility="Collapsed">
            <Grid.ChildrenTransitions>
                <PopupThemeTransition />
            </Grid.ChildrenTransitions>
            <Grid>
                <Grid.ChildrenTransitions>
                    <PopupThemeTransition />
                </Grid.ChildrenTransitions>
                <Grid x:Name="SophonProgressRingShadow"
                      CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(SophonProgressRingShadow)}"
                      Shadow="{ThemeResource SharedShadow}"
                      Translation="0,0,48" />
                <ProgressRing Width="Auto"
                              Height="Auto"
                              HorizontalAlignment="Stretch"
                              IsIndeterminate="{x:Bind SophonProgressRing.IsIndeterminate, Mode=OneWay}"
                              Maximum="{x:Bind SophonProgressRing.Maximum, Mode=OneWay}"
                              Value="{x:Bind SophonProgressRing.Value, Mode=OneWay}" />
                <Ellipse x:Name="SophonProgressRingBG"
                         HorizontalAlignment="Stretch"
                         Fill="{ThemeResource ProgressBackgroundAcrylicBrush}" />
                <ProgressRing x:Name="SophonProgressRing"
                              Width="Auto"
                              Height="Auto"
                              Margin="-6"
                              HorizontalAlignment="Stretch"
                              IsIndeterminate="True"
                              Maximum="100"
                              Value="0" />
                <Grid x:Name="SophonProgressPerFile"
                      Margin="16"
                      Visibility="Visible">
                    <ProgressRing Width="Auto"
                                  Height="Auto"
                                  HorizontalAlignment="Stretch"
                                  IsIndeterminate="{x:Bind SophonProgressRingPerFile.IsIndeterminate, Mode=OneWay}"
                                  Maximum="{x:Bind SophonProgressRingPerFile.Maximum, Mode=OneWay}"
                                  Value="{x:Bind SophonProgressRingPerFile.Value, Mode=OneWay}" />
                    <Ellipse x:Name="SophonProgressRingBG2"
                             Margin="4"
                             HorizontalAlignment="Stretch"
                             VerticalAlignment="Stretch"
                             Fill="{ThemeResource ProgressBackgroundAcrylicBrush}" />
                    <ProgressRing x:Name="SophonProgressRingPerFile"
                                  Width="Auto"
                                  Height="Auto"
                                  Margin="8"
                                  HorizontalAlignment="Stretch"
                                  IsIndeterminate="True"
                                  Maximum="100"
                                  Value="0" />
                </Grid>
            </Grid>
            <StackPanel HorizontalAlignment="Stretch"
                        VerticalAlignment="Center">
                <Grid Margin="0,0,0,8"
                      Padding="8,4"
                      HorizontalAlignment="Center"
                      Background="{ThemeResource AccentColorBrush}"
                      ColumnSpacing="6"
                      CornerRadius="4"
                      Shadow="{ThemeResource SharedShadow}"
                      Translation="0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <FontIcon x:Name="SophonProgressStatusTitleIcon"
                              HorizontalAlignment="Center"
                              FontFamily="{ThemeResource FontAwesome}"
                              FontSize="12"
                              FontWeight="Bold"
                              Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                              Glyph="" />
                    <TextBlock x:Name="SophonProgressStatusTitleText"
                               Grid.Column="1"
                               HorizontalAlignment="Center"
                               FontSize="11"
                               FontWeight="Medium"
                               Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                               Text="-" />
                </Grid>
                <Grid x:Name="SophonProgressStatusSizeTotalGrid"
                      Margin="0,2"
                      HorizontalAlignment="Center"
                      ColumnSpacing="6">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="14"
                              Glyph="" />
                    <TextBlock x:Name="SophonProgressStatusSizeTotalText"
                               Grid.Column="1"
                               FontSize="12"
                               FontWeight="Medium"
                               Text="{x:Bind helper:Locale.Lang._Misc.PerFromToPlaceholder}"
                               TextAlignment="Left" />
                </Grid>
                <Grid x:Name="SophonProgressStatusSizeDownloadedGrid"
                      Margin="0,2"
                      HorizontalAlignment="Center"
                      ColumnSpacing="6">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <FontIcon FontFamily="{ThemeResource FontAwesome}"
                              FontSize="14"
                              Glyph="" />
                    <TextBlock x:Name="SophonProgressStatusSizeDownloadedText"
                               Grid.Column="1"
                               FontSize="12"
                               FontWeight="Medium"
                               Text="{x:Bind helper:Locale.Lang._Misc.PerFromToPlaceholder}"
                               TextAlignment="Left" />
                </Grid>
                <StackPanel HorizontalAlignment="Center"
                            Orientation="Horizontal">
                    <TextBlock FontWeight="Bold"
                               Text="{x:Bind SophonProgressRing.Value, Mode=OneWay}" />
                    <TextBlock Margin="-1,0,0,0"
                               FontWeight="Bold"
                               Text="%" />
                </StackPanel>
                <StackPanel x:Name="SophonProgressStatusFooter"
                            Margin="0,4"
                            Padding="4,2"
                            HorizontalAlignment="Center"
                            Background="{ThemeResource AccentColorBrush}"
                            CornerRadius="4"
                            Orientation="Horizontal"
                            Shadow="{ThemeResource SharedShadow}"
                            Spacing="4"
                            Translation="0,0,16"
                            Visibility="Visible">
                    <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="10"
                              Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                              Glyph="" />
                    <TextBlock FontSize="9"
                               FontWeight="Bold"
                               Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                               Text="{x:Bind helper:Locale.Lang._Misc.SpeedTextOnly}"
                               TextAlignment="Center" />
                </StackPanel>
                <Grid Margin="0,2"
                      HorizontalAlignment="Center"
                      ColumnSpacing="6">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition MaxWidth="72" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition MaxWidth="72" />
                    </Grid.ColumnDefinitions>
                    <FontIcon FontFamily="{ThemeResource FontAwesome}"
                              FontSize="12"
                              Glyph="" />
                    <TextBlock x:Name="SophonProgressStatusSpeedTotalText"
                               Grid.Column="1"
                               FontSize="10"
                               FontWeight="Medium"
                               Text="-"
                               TextAlignment="Left" />
                    <FontIcon Grid.Column="2"
                              FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="12"
                              Glyph="" />
                    <TextBlock x:Name="SophonProgressStatusSpeedDownloadedText"
                               Grid.Column="3"
                               FontSize="10"
                               FontWeight="Medium"
                               Text="-"
                               TextAlignment="Left" />
                </Grid>
                <TextBlock x:Name="SophonProgressTimeLeft"
                           HorizontalAlignment="Center"
                           FontSize="10"
                           FontWeight="Bold"
                           Text="{x:Bind helper:Locale.Lang._Misc.TimeRemainHMSFormatPlaceholder}" />
            </StackPanel>
            <Button x:Name="SophonProgressInformationButton"
                    Width="32"
                    Height="32"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Bottom"
                    CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(SophonProgressInformationButton)}"
                    Shadow="{ThemeResource SharedShadow}"
                    Translation="0,0,16">
                <Button.Content>
                    <Grid Margin="-16"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Stretch"
                          Background="{ThemeResource GameSettingsBtnBrush}">
                        <FontIcon FontFamily="{ThemeResource FontAwesome}"
                                  Glyph=""
                                  Opacity="0.6" />
                    </Grid>
                </Button.Content>
                <Button.Flyout>
                    <Flyout Placement="LeftEdgeAlignedBottom">
                        <StackPanel MaxWidth="370">
                            <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_Title}" />
                            <TextBlock Margin="0,8,0,0"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_1}"
                                       TextWrapping="Wrap" />
                            <TextBlock Margin="0,8,0,0"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_2}"
                                       TextWrapping="Wrap" />
                            <MenuFlyoutSeparator Margin="0,8" />
                            <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_IndicatorTitle}" />
                            <Grid Margin="0,8,0,0"
                                  HorizontalAlignment="Stretch"
                                  ColumnSpacing="8"
                                  RowSpacing="8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition />
                                </Grid.RowDefinitions>
                                <FontIcon VerticalAlignment="Center"
                                          FontFamily="{ThemeResource FontAwesomeSolid}"
                                          FontSize="14"
                                          Glyph="" />
                                <TextBlock Grid.Row="0"
                                           Grid.Column="1"
                                           Margin="0,0,8,0"
                                           VerticalAlignment="Center"
                                           FontSize="12"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_Indicator1}"
                                           TextWrapping="WrapWholeWords" />
                                <FontIcon Grid.Row="1"
                                          Grid.Column="0"
                                          VerticalAlignment="Center"
                                          FontFamily="{ThemeResource FontAwesome}"
                                          FontSize="14"
                                          Glyph="" />
                                <TextBlock Grid.Row="1"
                                           Grid.Column="1"
                                           Margin="0,0,8,0"
                                           VerticalAlignment="Center"
                                           FontSize="12"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_Indicator2}"
                                           TextWrapping="WrapWholeWords" />
                                <FontIcon Grid.Row="0"
                                          Grid.Column="2"
                                          VerticalAlignment="Center"
                                          FontFamily="{ThemeResource FontAwesome}"
                                          FontSize="14"
                                          Glyph="" />
                                <TextBlock Grid.Row="0"
                                           Grid.Column="3"
                                           VerticalAlignment="Center"
                                           FontSize="12"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_Indicator3}"
                                           TextWrapping="WrapWholeWords" />
                                <FontIcon Grid.Row="1"
                                          Grid.Column="2"
                                          VerticalAlignment="Center"
                                          FontFamily="{ThemeResource FontAwesomeSolid}"
                                          FontSize="14"
                                          Glyph="" />
                                <TextBlock Grid.Row="1"
                                           Grid.Column="3"
                                           VerticalAlignment="Center"
                                           FontSize="12"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_Indicator4}"
                                           TextWrapping="WrapWholeWords" />
                            </Grid>
                        </StackPanel>
                    </Flyout>
                </Button.Flyout>
            </Button>
            <Button x:Name="SophonProgressSettingsButton"
                    Width="32"
                    Height="32"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Bottom"
                    Click="ProgressSettingsButton_OnClick"
                    CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(SophonProgressSettingsButton)}"
                    Shadow="{ThemeResource SharedShadow}"
                    Translation="0,0,16">
                <Button.Content>
                    <Grid Margin="-16"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Stretch"
                          Background="{ThemeResource GameSettingsBtnBrush}">
                        <AnimatedIcon Width="20">
                            <AnimatedIcon.Source>
                                <animatedvisuals:AnimatedSettingsVisualSource />
                            </AnimatedIcon.Source>
                            <AnimatedIcon.FallbackIconSource>
                                <SymbolIconSource Symbol="Setting" />
                            </AnimatedIcon.FallbackIconSource>
                        </AnimatedIcon>
                    </Grid>
                </Button.Content>
            </Button>
        </Grid>
        <Grid x:Name="ProgressStatusGrid"
              Grid.Row="1"
              Grid.Column="0"
              MaxWidth="300"
              Margin="0,0,0,48"
              HorizontalAlignment="Center"
              VerticalAlignment="Center"
              Shadow="{ThemeResource SharedShadow}"
              Visibility="Collapsed">
            <Grid.ChildrenTransitions>
                <PopupThemeTransition />
            </Grid.ChildrenTransitions>
            <Grid>
                <Grid.ChildrenTransitions>
                    <PopupThemeTransition />
                </Grid.ChildrenTransitions>
                <Grid x:Name="progressRingShadow"
                      CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(progressRingShadow)}"
                      Shadow="{ThemeResource SharedShadow}"
                      Translation="0,0,48" />
                <ProgressRing Width="Auto"
                              Height="Auto"
                              HorizontalAlignment="Stretch"
                              IsIndeterminate="{x:Bind progressRing.IsIndeterminate, Mode=OneWay}"
                              Maximum="{x:Bind progressRing.Maximum, Mode=OneWay}"
                              Value="{x:Bind progressRing.Value, Mode=OneWay}" />
                <Ellipse x:Name="progressRingBG"
                         HorizontalAlignment="Stretch"
                         Fill="{ThemeResource ProgressBackgroundAcrylicBrush}" />
                <ProgressRing x:Name="progressRing"
                              Width="Auto"
                              Height="Auto"
                              Margin="-6"
                              HorizontalAlignment="Stretch"
                              IsIndeterminate="True"
                              Maximum="100"
                              Value="0" />
                <Grid x:Name="progressPerFile"
                      Margin="16"
                      Visibility="Visible">
                    <ProgressRing Width="Auto"
                                  Height="Auto"
                                  HorizontalAlignment="Stretch"
                                  IsIndeterminate="{x:Bind progressRingPerFile.IsIndeterminate, Mode=OneWay}"
                                  Maximum="{x:Bind progressRingPerFile.Maximum, Mode=OneWay}"
                                  Value="{x:Bind progressRingPerFile.Value, Mode=OneWay}" />
                    <Grid x:Name="ProgressPerFileCircleShadowCaster"
                          Margin="4"
                          CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(ProgressPerFileCircleShadowCaster)}"
                          Shadow="{ThemeResource SharedShadow}"
                          Translation="0,0,32" />
                    <Ellipse x:Name="progressRingBG2"
                             Margin="4"
                             HorizontalAlignment="Stretch"
                             VerticalAlignment="Stretch"
                             Fill="{ThemeResource ProgressBackgroundAcrylicBrush}" />
                    <ProgressRing x:Name="progressRingPerFile"
                                  Width="Auto"
                                  Height="Auto"
                                  Margin="8"
                                  HorizontalAlignment="Stretch"
                                  IsIndeterminate="True"
                                  Maximum="100"
                                  Value="0" />
                </Grid>
            </Grid>
            <StackPanel HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                <Grid Margin="0,0,0,8"
                      Padding="8,4"
                      HorizontalAlignment="Center"
                      Background="{ThemeResource AccentColorBrush}"
                      ColumnSpacing="6"
                      CornerRadius="4"
                      Shadow="{ThemeResource SharedShadow}"
                      Translation="0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <FontIcon x:Name="ProgressStatusTitleIcon"
                              HorizontalAlignment="Center"
                              FontFamily="{ThemeResource FontAwesome}"
                              FontSize="12"
                              FontWeight="Bold"
                              Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                              Glyph="" />
                    <TextBlock x:Name="ProgressStatusTitle"
                               Grid.Column="1"
                               HorizontalAlignment="Center"
                               FontSize="11"
                               FontWeight="Medium"
                               Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                               Text="-" />
                </Grid>
                <TextBlock x:Name="ProgressStatusSubtitle"
                           HorizontalAlignment="Center"
                           Text="{x:Bind helper:Locale.Lang._Misc.PerFromToPlaceholder}" />
                <StackPanel HorizontalAlignment="Center"
                            Orientation="Horizontal">
                    <TextBlock FontWeight="Bold"
                               Text="{x:Bind progressRing.Value, Mode=OneWay}" />
                    <TextBlock Margin="-1,0,0,0"
                               FontWeight="Bold"
                               Text="%" />
                </StackPanel>
                <StackPanel Margin="0,4"
                            Padding="4,2"
                            HorizontalAlignment="Center"
                            Background="{ThemeResource AccentColorBrush}"
                            CornerRadius="4"
                            Orientation="Horizontal"
                            Shadow="{ThemeResource SharedShadow}"
                            Spacing="4"
                            Translation="0,0,16"
                            Visibility="Visible">
                    <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="10"
                              Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                              Glyph="" />
                    <TextBlock FontSize="9"
                               FontWeight="Bold"
                               Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                               Text="{x:Bind helper:Locale.Lang._Misc.SpeedTextOnly}"
                               TextAlignment="Center" />
                </StackPanel>
                <Grid Margin="0,2"
                      HorizontalAlignment="Center"
                      ColumnSpacing="6">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition MaxWidth="160" />
                    </Grid.ColumnDefinitions>
                    <FontIcon x:Name="ProgressStatusIconDisk"
                              Grid.Column="0"
                              FontFamily="{ThemeResource FontAwesome}"
                              FontSize="12"
                              Glyph="" />
                    <FontIcon x:Name="ProgressStatusIconInternet"
                              Grid.Column="0"
                              Visibility="Collapsed"
                              FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="12"
                              Glyph="" />
                    <TextBlock x:Name="ProgressStatusFooter"
                               Grid.Column="1"
                               FontSize="10"
                               FontWeight="Medium"
                               Text="-"
                               TextAlignment="Left" />
                </Grid>
                <TextBlock x:Name="ProgressTimeLeft"
                           HorizontalAlignment="Center"
                           FontSize="10"
                           FontWeight="Bold"
                           Text="{x:Bind helper:Locale.Lang._Misc.TimeRemainHMSFormatPlaceholder}" />
                <Grid x:Name="DownloadModeLabel"
                      Margin="0,8,0,0"
                      Padding="8,2"
                      HorizontalAlignment="Center"
                      Background="{ThemeResource AccentColorBrush}"
                      CornerRadius="8"
                      Shadow="{ThemeResource SharedShadow}"
                      Translation="0,0,16"
                      Visibility="Collapsed">
                    <TextBlock x:Name="DownloadModeLabelText"
                               FontSize="10"
                               FontWeight="Bold"
                               Foreground="{ThemeResource ApplicationPageBackgroundThemeBrush}"
                               HorizontalTextAlignment="Center"
                               Text="" />
                </Grid>
            </StackPanel>
            <Button x:Name="ProgressSettingsButton"
                    Width="32"
                    Height="32"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Bottom"
                    Click="ProgressSettingsButton_OnClick"
                    CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(ProgressSettingsButton)}"
                    Shadow="{ThemeResource SharedShadow}"
                    Translation="0,0,16">
                <Button.Content>
                    <Grid Margin="-16"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Stretch"
                          Background="{ThemeResource GameSettingsBtnBrush}">
                        <AnimatedIcon Width="20">
                            <AnimatedIcon.Source>
                                <animatedvisuals:AnimatedSettingsVisualSource />
                            </AnimatedIcon.Source>
                            <AnimatedIcon.FallbackIconSource>
                                <SymbolIconSource Symbol="Setting" />
                            </AnimatedIcon.FallbackIconSource>
                        </AnimatedIcon>
                    </Grid>
                </Button.Content>
            </Button>
        </Grid>
        <StackPanel x:Name="RightBottomButtons"
                    Grid.Row="2"
                    Grid.Column="1"
                    HorizontalAlignment="Right"
                    Orientation="Horizontal"
                    Spacing="8">
            <Button x:Name="CommunityToolsBtn"
                    Padding="0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    HorizontalContentAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(CommunityToolsBtn)}"
                    Shadow="{ThemeResource SharedShadow}">
                <Button.Resources>
                    <DataTemplate x:Key="CommunityToolsInnerBtn"
                                  x:DataType="local:CommunityToolsEntry">
                        <Button HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Click="OpenCommunityButtonLink"
                                Loaded="SetHandCursor"
                                Tag="{x:Bind URL}">
                            <Grid Margin="-8,0,0,0"
                                  HorizontalAlignment="Left">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="28" />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <FontIcon Grid.Column="0"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          FontFamily="{x:Bind IconFontFamily}"
                                          FontSize="12"
                                          Glyph="{x:Bind IconGlyph}" />
                                <TextBlock Grid.Column="1"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           Text="{x:Bind Text}"
                                           TextTrimming="CharacterEllipsis" />
                            </Grid>
                        </Button>
                    </DataTemplate>
                    <DataTemplate x:Key="CommunityToolsInnerAccentBtn"
                                  x:DataType="local:CommunityToolsEntry">
                        <Button HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Click="OpenCommunityButtonLink"
                                Loaded="SetHandCursor"
                                Style="{ThemeResource AccentButtonStyle}"
                                Tag="{x:Bind URL}">
                            <Grid Margin="-8,0,0,0"
                                  HorizontalAlignment="Left">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="28" />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <FontIcon HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          FontFamily="{x:Bind IconFontFamily}"
                                          FontSize="12"
                                          Glyph="{x:Bind IconGlyph}" />
                                <TextBlock Grid.Column="1"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           Text="{x:Bind Text}"
                                           TextTrimming="CharacterEllipsis" />
                            </Grid>
                        </Button>
                    </DataTemplate>
                </Button.Resources>
                <Button.Content>
                    <Grid HorizontalAlignment="Stretch"
                          VerticalAlignment="Stretch">
                        <Grid Margin="-8"
                              HorizontalAlignment="Stretch"
                              VerticalAlignment="Stretch"
                              Background="{ThemeResource GameSettingsBtnBrush}" />
                        <Grid Margin="16,0,16,2"
                              ColumnSpacing="8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <FontIcon HorizontalAlignment="Stretch"
                                      VerticalAlignment="Center"
                                      FontSize="18"
                                      Glyph="&#xE71B;" />
                            <TextBlock Grid.Column="1"
                                       HorizontalAlignment="Stretch"
                                       VerticalAlignment="Center"
                                       FontWeight="SemiBold"
                                       Text="{x:Bind helper:Locale.Lang._HomePage.CommunityToolsBtn}" />
                        </Grid>
                    </Grid>
                </Button.Content>
                <Button.Flyout>
                    <Flyout>
                        <ScrollViewer MaxWidth="436"
                                      Margin="-16,-4,-16,-16"
                                      HorizontalScrollBarVisibility="Visible"
                                      HorizontalScrollMode="Enabled"
                                      VerticalScrollBarVisibility="Disabled"
                                      VerticalScrollMode="Disabled">
                            <Grid Margin="16,0,8,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Margin="0,0,0,8"
                                               FontSize="16"
                                               Style="{ThemeResource BodyStrongTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.CommunityToolsBtn_OfficialText}" />
                                    <GridView x:Name="OfficialToolsStackPanel"
                                              ItemTemplate="{StaticResource CommunityToolsInnerAccentBtn}"
                                              ItemsSource="{x:Bind localStatic:PageStatics.CommunityToolsProperty.OfficialToolsList}">
                                        <GridView.ItemContainerStyle>
                                            <Style TargetType="GridViewItem">
                                                <Setter Property="Margin" Value="0,4,8,4" />
                                                <Setter Property="Width" Value="200" />
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                                <Setter Property="MinHeight" Value="0" />
                                                <Setter Property="Padding" Value="0,0,0,0" />
                                            </Style>
                                        </GridView.ItemContainerStyle>
                                        <GridView.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <ItemsWrapGrid x:Name="CommunityToolsMaxItemsWrapGrid1"
                                                               MaximumRowsOrColumns="1"
                                                               Orientation="Horizontal" />
                                            </ItemsPanelTemplate>
                                        </GridView.ItemsPanel>
                                    </GridView>
                                </StackPanel>
                                <StackPanel Grid.Column="1"
                                            Margin="16,0,0,0">
                                    <TextBlock Margin="0,0,0,8"
                                               FontSize="16"
                                               Style="{ThemeResource BodyStrongTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.CommunityToolsBtn_CommunityText}" />
                                    <GridView x:Name="CommunityToolsStackPanel"
                                              ItemTemplate="{StaticResource CommunityToolsInnerBtn}"
                                              ItemsSource="{x:Bind localStatic:PageStatics.CommunityToolsProperty.CommunityToolsList}">
                                        <GridView.ItemContainerStyle>
                                            <Style TargetType="GridViewItem">
                                                <Setter Property="Margin" Value="0,4,8,4" />
                                                <Setter Property="Width" Value="200" />
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                                <Setter Property="MinHeight" Value="0" />
                                                <Setter Property="Padding" Value="0,0,0,0" />
                                            </Style>
                                        </GridView.ItemContainerStyle>
                                        <GridView.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <ItemsWrapGrid x:Name="CommunityToolsMaxItemsWrapGrid2"
                                                               MaximumRowsOrColumns="2"
                                                               Orientation="Horizontal" />
                                            </ItemsPanelTemplate>
                                        </GridView.ItemsPanel>
                                    </GridView>
                                </StackPanel>
                            </Grid>
                        </ScrollViewer>
                    </Flyout>
                </Button.Flyout>
            </Button>
            <Button x:Name="GameStartupSetting"
                    Padding="0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    HorizontalContentAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(GameStartupSetting)}"
                    Shadow="{ThemeResource SharedShadow}">
                <Button.Content>
                    <Grid HorizontalAlignment="Stretch"
                          VerticalAlignment="Stretch">
                        <Grid Margin="-8"
                              HorizontalAlignment="Stretch"
                              VerticalAlignment="Stretch"
                              Background="{ThemeResource GameSettingsBtnBrush}" />
                        <Grid Margin="16,0,16,2"
                              ColumnSpacing="8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <AnimatedIcon Width="18">
                                <AnimatedIcon.Source>
                                    <animatedvisuals:AnimatedSettingsVisualSource />
                                </AnimatedIcon.Source>
                                <AnimatedIcon.FallbackIconSource>
                                    <SymbolIconSource Symbol="Setting" />
                                </AnimatedIcon.FallbackIconSource>
                            </AnimatedIcon>
                            <TextBlock Grid.Column="1"
                                       HorizontalAlignment="Stretch"
                                       VerticalAlignment="Center"
                                       FontWeight="SemiBold"
                                       Text="{x:Bind helper:Locale.Lang._HomePage.GameSettingsBtn}" />
                        </Grid>
                    </Grid>
                </Button.Content>
                <Button.Flyout>
                    <Flyout x:Name="GameStartupSettingFlyout"
                            Placement="TopEdgeAlignedRight">
                        <ScrollViewer MaxHeight="474"
                                      Margin="-16,-16"
                                      VerticalScrollBarVisibility="Auto"
                                      VerticalScrollMode="Auto">
                            <StackPanel x:Name="GameStartupSettingFlyoutContainer"
                                        MaxWidth="280"
                                        Margin="16,16">
                                <TextBlock Margin="0,0,0,8"
                                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel1}" />
                                <Button x:Name="OpenGameFolderButton"
                                        Margin="0,8"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Click="OpenGameFolderButton_Click"
                                        CornerRadius="14"
                                        Style="{ThemeResource AccentButtonStyle}">
                                    <Button.Content>
                                        <Grid Margin="4,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="18" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <FontIcon VerticalAlignment="Center"
                                                      FontFamily="{ThemeResource FontAwesomeSolid}"
                                                      FontSize="16"
                                                      Glyph="" />
                                            <TextBlock Grid.Column="1"
                                                       Margin="8,0,0,0"
                                                       FontWeight="Medium"
                                                       Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel1OpenGameFolder}"
                                                       TextWrapping="Wrap" />
                                        </Grid>
                                    </Button.Content>
                                </Button>
                                <Button x:Name="OpenCacheFolderButton"
                                        Margin="0,8"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Click="OpenCacheFolderButton_Click"
                                        CornerRadius="14">
                                    <Button.Content>
                                        <Grid Margin="4,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="18" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                      FontSize="16"
                                                      Glyph="" />
                                            <TextBlock Grid.Column="1"
                                                       Margin="8,0,0,0"
                                                       FontWeight="Medium"
                                                       Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel1OpenCacheFolder}"
                                                       TextWrapping="Wrap" />
                                        </Grid>
                                    </Button.Content>
                                </Button>
                                <Button x:Name="OpenScreenshotFolderButton"
                                        Margin="0,8"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Click="OpenScreenshotFolderButton_Click"
                                        CornerRadius="14">
                                    <Button.Content>
                                        <Grid Margin="4,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="18" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                      FontSize="16"
                                                      Glyph="" />
                                            <TextBlock Grid.Column="1"
                                                       Margin="8,0,0,0"
                                                       FontWeight="Medium"
                                                       Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel1OpenScreenshotFolder}"
                                                       TextWrapping="Wrap" />
                                        </Grid>
                                    </Button.Content>
                                </Button>
                                <Button x:Name="CleanupFilesButton"
                                        Margin="0,8"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Click="CleanupFilesButton_Click"
                                        CornerRadius="14">
                                    <Button.Content>
                                        <Grid Margin="4,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="18" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                      FontSize="16"
                                                      Glyph="" />
                                            <TextBlock Grid.Column="1"
                                                       Margin="8,0,0,0"
                                                       FontWeight="Medium"
                                                       Text="{x:Bind helper:Locale.Lang._FileCleanupPage.Title}"
                                                       TextWrapping="Wrap" />
                                        </Grid>
                                    </Button.Content>
                                </Button>
                                <TextBlock Margin="0,8,0,8"
                                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel2}" />
                                <Button x:Name="RepairGameButton"
                                        Margin="0,8"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Click="RepairGameButton_Click"
                                        CornerRadius="14"
                                        Style="{ThemeResource AccentButtonStyle}">
                                    <Button.Content>
                                        <Grid Margin="4,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="18" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                      FontSize="16"
                                                      Glyph="" />
                                            <TextBlock Grid.Column="1"
                                                       Margin="8,0,0,0"
                                                       Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel2RepairGame}"
                                                       TextWrapping="Wrap" />
                                        </Grid>
                                    </Button.Content>
                                </Button>
                                <Button x:Name="UninstallGameButton"
                                        Margin="0,8"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Click="UninstallGameButton_Click"
                                        CornerRadius="14">
                                    <Button.Content>
                                        <Grid Margin="4,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="18" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                      FontSize="16"
                                                      Glyph="" />
                                            <TextBlock Grid.Column="1"
                                                       Margin="8,0,0,0"
                                                       Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel2UninstallGame}"
                                                       TextWrapping="Wrap" />
                                        </Grid>
                                    </Button.Content>
                                </Button>
                                <Button x:Name="ConvertVersionButton"
                                        Margin="0,8"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Click="ConvertVersionButton_Click"
                                        CornerRadius="14">
                                    <Button.Content>
                                        <Grid Margin="4,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="18" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                      FontSize="16"
                                                      Glyph="" />
                                            <TextBlock Grid.Column="1"
                                                       Margin="8,0,0,0"
                                                       Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel2ConvertVersion}"
                                                       TextWrapping="Wrap" />
                                        </Grid>
                                    </Button.Content>
                                </Button>
                                <Button x:Name="MoveGameLocationButton"
                                        Margin="0,8"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Click="MoveGameLocationButton_Click"
                                        CornerRadius="14">
                                    <Button.Content>
                                        <Grid Margin="4,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="18" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                      FontSize="16"
                                                      Glyph="" />
                                            <TextBlock Grid.Column="1"
                                                       Margin="8,0,0,0"
                                                       Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel2MoveGameLocationGame}"
                                                       TextWrapping="Wrap" />
                                        </Grid>
                                    </Button.Content>
                                </Button>
                                <Button x:Name="StopGameButton"
                                        Margin="0,8"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Click="StopGameButton_Click"
                                        CornerRadius="14"
                                        IsEnabled="False">
                                    <Button.Content>
                                        <Grid Margin="4,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="18" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                      FontSize="16"
                                                      Glyph="" />
                                            <TextBlock Grid.Column="1"
                                                       Margin="8,0,0,0"
                                                       Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel2StopGame}"
                                                       TextWrapping="Wrap" />
                                        </Grid>
                                    </Button.Content>
                                </Button>
                                <Rectangle Height="1"
                                           HorizontalAlignment="Stretch"
                                           Fill="Gray" />
                                <TextBlock MinWidth="200"
                                           Margin="0,8,0,0"
                                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel3RegionalSettings}" />
                                <StackPanel>
                                    <TextBlock MinWidth="200"
                                               Margin="0,8,0,0"
                                               Style="{ThemeResource BodyStrongTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel3RegionRpc}" />
                                    <ToggleSwitch x:Name="RegionRpcToggle"
                                                  IsOn="{x:Bind ToggleRegionPlayingRpc, Mode=TwoWay}"
                                                  OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                                  OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                </StackPanel>
                                <StackPanel x:Name="CustomChangeBGParams"
                                            x:FieldModifier="internal"
                                            Visibility="Visible">
                                    <TextBlock MinWidth="200"
                                               Margin="0,8,0,0"
                                               Style="{ThemeResource BodyStrongTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel3CustomBGRegionSectionTitle}" />
                                    <ToggleSwitch x:Name="UseCustomBGParamsSwitch"
                                                  IsOn="{x:Bind UseCustomBGRegion, Mode=TwoWay}"
                                                  OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                                  OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                    <Button x:Name="ChangeGameBGButton"
                                            Margin="0,8,0,8"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Left"
                                            Click="ChangeGameBGButton_Click"
                                            CornerRadius="14">
                                        <Button.Content>
                                            <Grid Margin="4,0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="18" />
                                                    <ColumnDefinition />
                                                </Grid.ColumnDefinitions>
                                                <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                          FontSize="16"
                                                          Glyph="" />
                                                <TextBlock Grid.Column="1"
                                                           Margin="8,0,0,0"
                                                           Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel3CustomBGRegion}"
                                                           TextWrapping="Wrap" />
                                            </Grid>
                                        </Button.Content>
                                    </Button>
                                    <TextBlock x:Name="BGPathDisplay"
                                               Width="{x:Bind CustomChangeBGParams.Width}"
                                               Margin="0,4"
                                               HorizontalAlignment="Stretch"
                                               VerticalAlignment="Center"
                                               Text=""
                                               TextTrimming="CharacterEllipsis" />
                                </StackPanel>
                                <StackPanel x:Name="CustomStartupArgs"
                                            Visibility="Collapsed">
                                    <TextBlock MinWidth="200"
                                               Margin="0,8,0,0"
                                               Style="{ThemeResource BodyStrongTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel3}" />
                                    <ToggleSwitch x:Name="CustomStartupArgsSwitch"
                                                  Margin="0,0,0,8"
                                                  IsOn="{x:Bind UseCustomArgs, Mode=TwoWay}"
                                                  OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                                  OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                    <TextBox x:Name="CustomArgsTextBox"
                                             Text="{x:Bind CustomArgsValue, Mode=TwoWay}"
                                             TextWrapping="Wrap" />
                                </StackPanel>
                                <Rectangle Height="1"
                                           Margin="0,8,0,0"
                                           HorizontalAlignment="Stretch"
                                           Fill="Gray" />
                                <StackPanel x:Name="MiscPanel">
                                    <TextBlock Margin="0,8,0,8"
                                               Style="{ThemeResource BodyStrongTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel4}" />
                                    <TextBlock Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel4ShowEventsPanel}"
                                               TextWrapping="Wrap" />
                                    <ToggleSwitch x:Name="ShowEventsPanelToggle"
                                                  IsEnabled="False"
                                                  IsOn="{x:Bind IsEventsPanelShow, Mode=TwoWay}"
                                                  OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                                  OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                    <TextBlock Margin="0,8,0,0"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel4ScaleUpEventsPanel}"
                                               TextWrapping="Wrap" />
                                    <ToggleSwitch x:Name="ScaleUpEventsPanelToggle"
                                                  IsEnabled="False"
                                                  IsOn="{x:Bind pages:HomePage.IsEventsPanelScaleUp, Mode=TwoWay}"
                                                  OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                                  OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                    <TextBlock Margin="0,8,0,0"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel4ShowSocialMediaPanel}"
                                               TextWrapping="Wrap" />
                                    <ToggleSwitch x:Name="ShowSocialMediaPanelToggle"
                                                  IsEnabled="True"
                                                  IsOn="{x:Bind IsSocMedPanelShow, Mode=TwoWay}"
                                                  OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                                  OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                    <TextBlock Margin="0,8,0,0"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel4ShowPlaytimeButton}"
                                               TextWrapping="Wrap" />
                                    <ToggleSwitch x:Name="PlaytimeVisibilityToggle"
                                                  IsEnabled="True"
                                                  IsOn="{x:Bind IsPlaytimeBtnVisible, Mode=TwoWay}"
                                                  OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                                  OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                    <TextBlock Margin="0,8,0,0"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel4SyncPlaytimeDatabase}"
                                               TextWrapping="Wrap" />
                                    <ToggleSwitch x:Name="PlaytimeDbSyncToggle"
                                                  IsEnabled="True"
                                                  IsOn="{x:Bind IsPlaytimeSyncDb, Mode=TwoWay}"
                                                  OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                                  OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                    <Rectangle Height="1"
                                               HorizontalAlignment="Stretch"
                                               Fill="Gray" />
                                    <Button x:Name="ShortcutButton"
                                            Margin="0,8"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Left"
                                            Click="ShortcutButton_Click"
                                            CornerRadius="14">
                                        <Button.Content>
                                            <Grid Margin="4,0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="18" />
                                                    <ColumnDefinition />
                                                </Grid.ColumnDefinitions>
                                                <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                                          FontSize="16"
                                                          Glyph="" />
                                                <TextBlock Grid.Column="1"
                                                           Margin="8,0,0,0"
                                                           FontWeight="Medium"
                                                           Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel4CreateShortcutBtn}"
                                                           TextWrapping="Wrap" />
                                            </Grid>
                                        </Button.Content>
                                    </Button>
                                    <Button x:Name="AddToSteamButton"
                                            Margin="0,8"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Left"
                                            Click="AddToSteamButton_Click"
                                            CornerRadius="14">
                                        <Button.Content>
                                            <Grid Margin="4,0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="18" />
                                                    <ColumnDefinition />
                                                </Grid.ColumnDefinitions>
                                                <FontIcon FontFamily="{ThemeResource FontAwesomeBrand}"
                                                          FontSize="18"
                                                          Glyph="" />
                                                <TextBlock Grid.Column="1"
                                                           Margin="8,0,0,0"
                                                           FontWeight="Medium"
                                                           Text="{x:Bind helper:Locale.Lang._HomePage.GameSettings_Panel4AddToSteamBtn}"
                                                           TextWrapping="Wrap" />
                                            </Grid>
                                        </Button.Content>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </ScrollViewer>
                    </Flyout>
                </Button.Flyout>
            </Button>
        </StackPanel>
        <StackPanel x:Name="LeftBottomButtons"
                    Grid.Row="2"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    HorizontalAlignment="Left"
                    Orientation="Horizontal"
                    Spacing="8">
            <Grid Width="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.PostPanelBounds.Width}"
                  HorizontalAlignment="Left"
                  VerticalAlignment="Stretch">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Grid x:Name="LauncherGameStatusPlaceholderBtn"
                      Grid.Row="0"
                      Grid.Column="0"
                      HorizontalAlignment="Stretch"
                      CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(LauncherGameStatusPlaceholderBtn)}"
                      PointerEntered="ElementScaleOutHoveredPointerEntered"
                      PointerExited="ElementScaleInHoveredPointerExited"
                      Shadow="{ThemeResource SharedShadow}"
                      Translation="0,0,32"
                      Visibility="Collapsed">
                    <Button x:Name="GamePlaceholderBtnComingSoon"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(InstallGameBtn)}"
                            IsEnabled="False"
                            Style="{ThemeResource NewAccentButtonStyle}"
                            Visibility="Collapsed">
                        <Button.Content>
                            <Grid Margin="8,0">
                                <TextBlock HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Center"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GameStatusPlaceholderComingSoonBtn}"
                                           TextTrimming="CharacterEllipsis" />
                            </Grid>
                        </Button.Content>
                    </Button>
                    <Button x:Name="GamePlaceholderBtnPreRegister"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="OpenButtonLinkFromTag"
                            CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(InstallGameBtn)}"
                            Style="{ThemeResource AccentButtonStyle}"
                            Tag="{x:Bind pages:HomePage.GamePreRegisterLink}"
                            Visibility="Collapsed">
                        <Button.Content>
                            <Grid Margin="8,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Grid Width="64"
                                      Margin="-20,-12"
                                      Padding="8,0"
                                      RenderTransformOrigin="0.5, 0.5">
                                    <Grid.RenderTransform>
                                        <RotateTransform Angle="180" />
                                    </Grid.RenderTransform>
                                    <AnimatedVisualPlayer Height="28"
                                                          HorizontalAlignment="Right">
                                        <lottie:StartGameIcon />
                                    </AnimatedVisualPlayer>
                                </Grid>
                                <TextBlock Grid.Column="1"
                                           Margin="0,-2,0,0"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Center"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GameStatusPlaceholderPreRegisterBtn}"
                                           TextTrimming="CharacterEllipsis" />
                                <Grid Grid.Column="2"
                                      Width="64"
                                      Margin="-20,-12"
                                      Padding="8,0">
                                    <AnimatedVisualPlayer Height="28"
                                                          HorizontalAlignment="Right">
                                        <lottie:StartGameIcon />
                                    </AnimatedVisualPlayer>
                                </Grid>
                            </Grid>
                        </Button.Content>
                    </Button>
                </Grid>
                <Grid x:Name="LauncherBtn"
                      Grid.Row="0"
                      Grid.Column="0"
                      HorizontalAlignment="Stretch"
                      CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(LauncherBtn)}"
                      Loaded="SetHandCursor"
                      PointerEntered="ElementScaleOutHoveredPointerEntered"
                      PointerExited="ElementScaleInHoveredPointerExited"
                      Shadow="{ThemeResource SharedShadow}"
                      Translation="0,0,32">
                    <Button x:Name="InstallGameBtn"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="InstallGameDialog"
                            CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(InstallGameBtn)}"
                            Style="{ThemeResource NewAccentButtonStyle}">
                        <Button.Content>
                            <Grid Margin="8,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.InstallBtn}"
                                           TextTrimming="CharacterEllipsis" />
                                <Grid x:Name="InstallGameBtnAnimBackground"
                                      Grid.Column="1"
                                      Margin="-20,-12"
                                      Padding="8,0"
                                      CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(InstallGameBtnAnimBackground)}">
                                    <AnimatedVisualPlayer Height="26"
                                                          HorizontalAlignment="Right">
                                        <lottie:DownloadIcon />
                                    </AnimatedVisualPlayer>
                                </Grid>
                            </Grid>
                        </Button.Content>
                    </Button>
                    <Button x:Name="UpdateGameBtn"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="UpdateGameDialog"
                            CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(UpdateGameBtn)}"
                            Loaded="SetHandCursor"
                            Style="{ThemeResource NewAccentButtonStyle}"
                            Visibility="Collapsed">
                        <Button.Content>
                            <Grid Margin="8,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.UpdateBtn}"
                                           TextTrimming="CharacterEllipsis" />
                                <Grid x:Name="UpdateGameBtnAnimBackground"
                                      Grid.Column="1"
                                      Width="64"
                                      Margin="-20,-12"
                                      Padding="12,0"
                                      CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(UpdateGameBtnAnimBackground)}">
                                    <AnimatedVisualPlayer Height="28"
                                                          HorizontalAlignment="Right">
                                        <lottie:UpdateIcon />
                                    </AnimatedVisualPlayer>
                                </Grid>
                            </Grid>
                        </Button.Content>
                    </Button>
                    <Button x:Name="StartGameBtn"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="StartGame"
                            CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(StartGameBtn)}"
                            Loaded="SetHandCursor"
                            Style="{ThemeResource NewAccentButtonStyle}"
                            Visibility="Collapsed">
                        <Grid Margin="8,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock HorizontalAlignment="Left"
                                       VerticalAlignment="Center"
                                       FontWeight="SemiBold"
                                       HorizontalTextAlignment="Left"
                                       Text="{x:Bind helper:Locale.Lang._HomePage.StartBtn}"
                                       TextTrimming="CharacterEllipsis" />
                            <Grid x:Name="StartGameBtnAnimBackground"
                                  Grid.Column="1"
                                  Width="64"
                                  Margin="-20,-12"
                                  Padding="12,0"
                                  CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(StartGameBtnAnimBackground)}">
                                <AnimatedVisualPlayer Height="28"
                                                      HorizontalAlignment="Right">
                                    <lottie:StartGameIcon />
                                </AnimatedVisualPlayer>
                            </Grid>
                            <FontIcon Grid.Column="1"
                                      HorizontalAlignment="Right"
                                      FontFamily="{ThemeResource FontAwesomeSolid}"
                                      FontSize="14"
                                      Glyph=""
                                      Opacity="0" />
                        </Grid>
                    </Button>
                    <Button x:Name="CancelDownloadBtn"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="CancelInstallationProcedure"
                            CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(CancelDownloadBtn)}"
                            Loaded="SetHandCursor"
                            Style="{ThemeResource NewAccentButtonStyle}"
                            Visibility="Collapsed">
                        <Grid Margin="8,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock HorizontalAlignment="Left"
                                       VerticalAlignment="Center"
                                       FontWeight="SemiBold"
                                       HorizontalTextAlignment="Left"
                                       Text="{x:Bind helper:Locale.Lang._HomePage.PauseCancelDownloadBtn}"
                                       TextTrimming="CharacterEllipsis" />
                            <FontIcon Grid.Column="1"
                                      HorizontalAlignment="Right"
                                      FontFamily="{ThemeResource FontAwesomeSolid}"
                                      FontSize="14"
                                      Glyph="" />
                        </Grid>
                    </Button>
                    <Button x:Name="ResumeDownloadBtn"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(ResumeDownloadBtn)}"
                            Loaded="SetHandCursor"
                            Style="{ThemeResource NewAccentButtonStyle}"
                            Visibility="Collapsed">
                        <Grid Margin="8,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock HorizontalAlignment="Left"
                                       VerticalAlignment="Center"
                                       FontWeight="SemiBold"
                                       HorizontalTextAlignment="Left"
                                       Text="{x:Bind helper:Locale.Lang._HomePage.ResumeDownloadBtn}"
                                       TextTrimming="CharacterEllipsis" />
                            <FontIcon Grid.Column="1"
                                      HorizontalAlignment="Right"
                                      FontFamily="{ThemeResource FontAwesomeSolid}"
                                      FontSize="14"
                                      Glyph="" />
                        </Grid>
                    </Button>
                </Grid>
            </Grid>
            <Button x:Name="PlaytimeBtn"
                    Padding="0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    HorizontalContentAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    Click="ForceUpdatePlaytimeButton_Click"
                    CornerRadius="{x:Bind extension:UIElementExtensions.AttachRoundedKindCornerRadius(PlaytimeBtn)}"
                    PointerExited="HidePlaytimeStatsFlyout"
                    Shadow="{ThemeResource SharedShadow}"
                    Tag="{x:Bind PlaytimeStatsFlyout}"
                    Translation="0,0,32">
                <ToolTipService.ToolTip>
                    <ToolTip x:Name="PlaytimeStatsToolTip"
                             Opened="ShowPlaytimeStatsFlyout"
                             Visibility="Collapsed" />
                </ToolTipService.ToolTip>
                <FlyoutBase.AttachedFlyout>
                    <Flyout x:Name="PlaytimeStatsFlyout"
                            LightDismissOverlayMode="Off"
                            Opened="PlaytimeStatsFlyout_OnOpened"
                            OverlayInputPassThroughElement="{x:Bind PlaytimeBtn}"
                            Placement="Top">
                        <Grid ColumnSpacing="8"
                              Tag="PlaytimeStatsFlyoutGrid">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition />
                                <RowDefinition Height="Auto" />
                                <RowDefinition />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Row="0"
                                       Grid.Column="0"
                                       Grid.ColumnSpan="3"
                                       Margin="0,0,0,4"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Stats_Title}" />
                            <StackPanel Grid.Row="1"
                                        Grid.Column="0"
                                        HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Stats_Daily}" />
                                <TextBlock x:Name="PlaytimeStatsDaily"
                                           HorizontalAlignment="Left"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource AccentColor}"
                                           Text="-" />
                            </StackPanel>
                            <StackPanel Grid.Row="1"
                                        Grid.Column="1"
                                        HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Stats_Weekly}" />
                                <TextBlock x:Name="PlaytimeStatsWeekly"
                                           HorizontalAlignment="Left"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource AccentColor}"
                                           Text="-" />
                            </StackPanel>
                            <StackPanel Grid.Row="1"
                                        Grid.Column="2"
                                        HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Stats_Monthly}" />
                                <TextBlock x:Name="PlaytimeStatsMonthly"
                                           HorizontalAlignment="Left"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource AccentColor}"
                                           Text="-" />
                            </StackPanel>
                            <TextBlock Grid.Row="2"
                                       Grid.Column="0"
                                       Grid.ColumnSpan="3"
                                       Margin="0,12,0,4"
                                       Style="{ThemeResource BodyStrongTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Stats_LastSession}" />
                            <StackPanel Grid.Row="3"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="2"
                                        HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Stats_LastSession_StartTime}" />
                                <TextBlock x:Name="PlaytimeStatsLastPlayed"
                                           HorizontalAlignment="Left"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource AccentColor}"
                                           Text="-" />
                            </StackPanel>
                            <StackPanel Grid.Row="3"
                                        Grid.Column="2"
                                        HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Stats_LastSession_Duration}" />
                                <TextBlock x:Name="PlaytimeStatsLastSession"
                                           HorizontalAlignment="Left"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource AccentColor}"
                                           Text="-" />
                            </StackPanel>
                        </Grid>
                    </Flyout>
                </FlyoutBase.AttachedFlyout>
                <Button.Content>
                    <Grid HorizontalAlignment="Stretch"
                          VerticalAlignment="Stretch">
                        <Grid Margin="-8"
                              HorizontalAlignment="Stretch"
                              VerticalAlignment="Stretch"
                              Background="{ThemeResource GameSettingsBtnBrush}" />
                        <Grid x:Name="PlaytimeStatsGrid"
                              Margin="16,0,16,2"
                              ColumnSpacing="8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <FontIcon HorizontalAlignment="Stretch"
                                      VerticalAlignment="Center"
                                      FontSize="18"
                                      Glyph="&#xE916;" />
                            <TextBlock x:Name="PlaytimeMainBtn"
                                       Grid.Column="1"
                                       HorizontalAlignment="Stretch"
                                       VerticalAlignment="Center"
                                       FontWeight="SemiBold"
                                       Text="-" />
                            <TextBlock x:Name="PlaytimeToolTipIcon"
                                       Grid.Column="2"
                                       Margin="0,0,-4,0"
                                       VerticalAlignment="Center"
                                       FontFamily="{ThemeResource FontAwesomeSolid}"
                                       FontSize="18"
                                       Opacity="0.25"
                                       Text="&#xf05a;" />
                        </Grid>
                    </Grid>
                </Button.Content>
                <Button.Flyout>
                    <Flyout x:Name="PlaytimeFlyout">
                        <StackPanel MaxWidth="250">
                            <StackPanel x:Name="PlaytimeIdleStack"
                                        Spacing="8">
                                <TextBlock Style="{ThemeResource BodyStrongTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Panel1}" />
                                <Grid ColumnSpacing="12">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <TextBox x:Name="HourPlaytimeTextBox"
                                             HorizontalContentAlignment="Center"
                                             BeforeTextChanging="NumberValidationTextBox"
                                             Text="" />
                                    <TextBox x:Name="MinutePlaytimeTextBox"
                                             Grid.Column="1"
                                             HorizontalContentAlignment="Center"
                                             BeforeTextChanging="NumberValidationTextBox"
                                             Text="" />
                                </Grid>
                                <StackPanel Margin="0,-4,0,4"
                                            Orientation="Horizontal">
                                    <TextBlock x:Name="PlaytimeHours"
                                               Width="55"
                                               Margin="5,1,0,0"
                                               HorizontalAlignment="Center"
                                               FontSize="11"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Idle_Panel1Hours}" />
                                    <TextBlock x:Name="PlaytimeMinutes"
                                               Width="55"
                                               Margin="19,1,0,0"
                                               HorizontalAlignment="Center"
                                               FontSize="11"
                                               Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Idle_Panel1Minutes}" />
                                </StackPanel>
                                <Button x:Name="ChangePlaytimeButton"
                                        Margin="-4,0"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Stretch"
                                        Click="ChangePlaytimeButton_Click"
                                        CornerRadius="14"
                                        Style="{ThemeResource AccentButtonStyle}">
                                    <Grid ColumnSpacing="12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <FontIcon Grid.Column="1"
                                                  HorizontalAlignment="Right"
                                                  VerticalAlignment="Center"
                                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                                  FontSize="12"
                                                  Glyph="" />
                                        <TextBlock Grid.Column="0"
                                                   HorizontalAlignment="Left"
                                                   VerticalAlignment="Center"
                                                   FontWeight="SemiBold"
                                                   Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Idle_ChangeBtn}" />
                                    </Grid>
                                </Button>
                                <Button x:Name="ResetPlaytimeButton"
                                        Margin="-4,0"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Stretch"
                                        Click="ResetPlaytimeButton_Click"
                                        CornerRadius="14">
                                    <Grid ColumnSpacing="12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <FontIcon Grid.Column="1"
                                                  HorizontalAlignment="Right"
                                                  VerticalAlignment="Center"
                                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                                  FontSize="12"
                                                  Glyph="" />
                                        <TextBlock Grid.Column="0"
                                                   HorizontalAlignment="Left"
                                                   VerticalAlignment="Center"
                                                   FontWeight="SemiBold"
                                                   Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Idle_ResetBtn}" />
                                    </Grid>
                                </Button>
                                <Button x:Name="SyncDbPlaytimeBtn"
                                        Margin="-4,0,-4,-4"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Stretch"
                                        Click="SyncDbPlaytimeButton_Click"
                                        CornerRadius="14">
                                    <Grid ColumnSpacing="12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <FontIcon x:Name="SyncDbPlaytimeBtnGlyph"
                                                  Grid.Column="1"
                                                  HorizontalAlignment="Right"
                                                  VerticalAlignment="Center"
                                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                                  FontSize="12"
                                                  Glyph="" />
                                        <TextBlock x:Name="SyncDbPlaytimeBtnText"
                                                   Grid.Column="0"
                                                   HorizontalAlignment="Left"
                                                   VerticalAlignment="Center"
                                                   FontWeight="SemiBold"
                                                   Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Idle_SyncDb}" />
                                    </Grid>
                                </Button>
                            </StackPanel>
                            <StackPanel x:Name="PlaytimeRunningStack"
                                        Margin="0,0"
                                        Visibility="Collapsed">
                                <TextBlock Margin="0,0,0,8"
                                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Panel1}" />
                                <TextBlock Margin="0,0,0,8"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Running_Info1}"
                                           TextWrapping="Wrap" />
                                <TextBlock Margin="0,0,0,0"
                                           Text="{x:Bind helper:Locale.Lang._HomePage.GamePlaytime_Running_Info2}"
                                           TextWrapping="Wrap" />
                            </StackPanel>
                        </StackPanel>
                    </Flyout>
                </Button.Flyout>
            </Button>
        </StackPanel>
    </Grid>
</Page>
