# 使用示例

## 基本操作示例

### 1. 解密单个配置文件

```bash
# 使用快速脚本
decrypt.bat config_Hi3CN.json

# 或使用Python命令
python crypto_tool.py decrypt-config \
    --input config_Hi3CN.json \
    --output config_Hi3CN_decrypted.json \
    --master-key config_master.json
```

**输出示例：**
```
✓ GameDispatchArrayURL: 2 items decrypted
✓ LauncherResourceURL: https://example.com/launcher/resources
✓ LauncherResourceChunksURL: 4 nested fields decrypted
  ✓ LauncherResourceChunksURL.BranchUrl: https://branch.example.com
  ✓ LauncherResourceChunksURL.MainUrl: https://main.example.com
  ✓ LauncherResourceChunksURL.PreloadUrl: https://preload.example.com
  ✓ LauncherResourceChunksURL.PatchUrl: https://patch.example.com
```

### 2. 加密单个配置文件

```bash
# 使用快速脚本
encrypt.bat plaintext_config.json

# 或使用Python命令
python crypto_tool.py encrypt-config \
    --input plaintext_config.json \
    --output encrypted_config.json \
    --master-key config_master.json
```

**输出示例：**
```
✓ LauncherResourceURL: Encrypted (45 chars -> 324 chars)
✓ LauncherNewsURL: Encrypted (38 chars -> 324 chars)
✓ LauncherResourceChunksURL: 4 nested fields encrypted
  ✓ LauncherResourceChunksURL.BranchUrl: Encrypted (42 chars -> 324 chars)
  ✓ LauncherResourceChunksURL.MainUrl: Encrypted (40 chars -> 324 chars)
```

### 3. 批量处理多个文件

```bash
# 使用交互式脚本
batch_process.bat  # Windows
batch_process.sh   # Linux/macOS

# 或直接使用Python命令
# 批量解密
python crypto_tool.py batch-decrypt \
    --input-dir ./encrypted_configs \
    --output-dir ./decrypted_configs \
    --master-key config_master.json

# 批量加密
python crypto_tool.py batch-encrypt \
    --input-dir ./plaintext_configs \
    --output-dir ./encrypted_configs \
    --master-key config_master.json
```

## 高级用法示例

### 1. 分析配置文件结构

```bash
# 详细分析
python field_analyzer.py config_Hi3CN.json

# 简要分析
python field_analyzer.py config_Hi3CN.json --brief

# 生成分析报告
python field_analyzer.py config_Hi3CN.json --output-report analysis.json

# 生成自动解密脚本
python field_analyzer.py config_Hi3CN.json --generate-script auto_decrypt.py
```

**分析输出示例：**
```
======================================================================
Configuration File Analysis: config_Hi3CN.json
======================================================================
📊 Summary:
   Total Fields: 45
   Encrypted Fields: 12
   Plaintext Fields: 33
   Total Encrypted Strings: 15

🔒 Encrypted Fields:
   🔐 GameDispatchArrayURL (list): 2 encrypted items
   🔐 LauncherResourceURL (str): 324 chars, 256 bytes encrypted
   🔐 LauncherResourceChunksURL (dict): 4 encrypted values
```

### 2. 指定特定字段进行处理

```bash
# 只解密特定字段
python crypto_tool.py decrypt-config \
    --input config.json \
    --output decrypted.json \
    --master-key key.json \
    --fields LauncherResourceURL GameDispatchArrayURL

# 只加密特定字段
python crypto_tool.py encrypt-config \
    --input plaintext.json \
    --output encrypted.json \
    --master-key key.json \
    --fields LauncherNewsURL LauncherSpriteURL
```

### 3. 单个字段操作

```bash
# 解密单个字段
python crypto_tool.py decrypt \
    --field "LauncherResourceURL" \
    --data "Q29sbGFwc2UBAAAAAAAAAGICAAAAAAAAXgIA..." \
    --master-key config_master.json

# 加密单个字段
python crypto_tool.py encrypt \
    --data "https://example.com/api/resources" \
    --master-key config_master.json
```

## 工作流程示例

### 场景1：修改现有配置

```bash
# 1. 解密现有配置
python crypto_tool.py decrypt-config \
    --input config_Hi3CN.json \
    --output config_Hi3CN_edit.json \
    --master-key config_master.json

# 2. 编辑解密后的配置文件
# (使用文本编辑器修改 config_Hi3CN_edit.json)

# 3. 重新加密配置
python crypto_tool.py encrypt-config \
    --input config_Hi3CN_edit.json \
    --output config_Hi3CN_new.json \
    --master-key config_master.json
```

### 场景2：创建新的游戏配置

```bash
# 1. 基于模板创建配置
python config_generator.py \
    --master-key config_master.json \
    --template templates/template_basic.json \
    --config-name config_MyGame.json \
    --game-name "My Game" \
    --game-region "Global"

# 2. 验证生成的配置
python field_analyzer.py generated_configs/config_MyGame.json

# 3. 测试解密验证
python crypto_tool.py decrypt-config \
    --input generated_configs/config_MyGame.json \
    --output test_decrypted.json \
    --master-key config_master.json
```

### 场景3：批量迁移配置

```bash
# 1. 批量解密旧配置
python crypto_tool.py batch-decrypt \
    --input-dir ./old_configs \
    --output-dir ./plaintext_configs \
    --master-key old_master_key.json

# 2. 生成新的master key
cd ../MasterKeyGenerator
python generate_master_key.py --key-size 2048 --output-dir ./new_keys

# 3. 批量加密为新格式
cd ../ConfigCrypto
python crypto_tool.py batch-encrypt \
    --input-dir ./plaintext_configs \
    --output-dir ./new_configs \
    --master-key ../MasterKeyGenerator/new_keys/config_master.json
```

## 故障排除示例

### 问题1：解密失败

```bash
# 检查master key是否正确
python crypto_tool.py decrypt \
    --field "test" \
    --data "Q29sbGFwc2U..." \
    --master-key config_master.json

# 分析配置文件格式
python field_analyzer.py problematic_config.json --brief
```

### 问题2：批量处理中断

```bash
# 检查单个文件
python crypto_tool.py decrypt-config \
    --input problematic_file.json \
    --output test_output.json \
    --master-key config_master.json

# 验证master key格式
python test_corrected_implementation.py
```

### 问题3：嵌套字段未处理

```bash
# 测试嵌套字段处理
python test_nested_fields.py

# 手动指定嵌套字段
python crypto_tool.py decrypt-config \
    --input config.json \
    --output decrypted.json \
    --master-key key.json \
    --fields LauncherResourceChunksURL
```

## 性能优化建议

1. **批量处理大文件时**：
   ```bash
   # 使用较小的密钥长度进行测试
   python generate_master_key.py --key-size 1024
   
   # 分批处理大量文件
   # 将文件分组到不同目录中分别处理
   ```

2. **网络环境较差时**：
   ```bash
   # 本地处理，避免依赖网络下载
   pip install --no-deps -r requirements.txt
   ```

3. **内存限制环境**：
   ```bash
   # 逐个处理文件而不是批量加载
   for file in *.json; do
       python crypto_tool.py decrypt-config --input "$file" --output "dec_$file" --master-key key.json
   done
   ```
