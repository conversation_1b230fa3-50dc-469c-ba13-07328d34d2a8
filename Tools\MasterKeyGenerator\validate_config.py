#!/usr/bin/env python3
"""
CollapseLauncher Master Key Configuration Validator

This tool validates the generated master key configuration files
and verifies their structure and integrity.
"""

import argparse
import base64
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional

from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend


class ConfigValidator:
    """Validator for CollapseLauncher master key configuration."""
    
    def __init__(self):
        """Initialize the validator."""
        pass
    
    def validate_master_config(self, config_path: str) -> bool:
        """
        Validate the config_master.json file.
        
        Args:
            config_path: Path to the config_master.json file
            
        Returns:
            True if valid, False otherwise
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"Validating: {config_path}")
            print("-" * 40)
            
            # Check required fields
            required_fields = ['Key', 'BitSize', 'Hash']
            for field in required_fields:
                if field not in config:
                    print(f"❌ Missing required field: {field}")
                    return False
                print(f"✓ Found field: {field}")
            
            # Validate Key field
            if not self._validate_key_field(config['Key']):
                return False
            
            # Validate BitSize field
            if not self._validate_bitsize_field(config['BitSize']):
                return False
            
            # Validate Hash field
            if not self._validate_hash_field(config['Hash']):
                return False
            
            print("✓ All validations passed!")
            return True
            
        except FileNotFoundError:
            print(f"❌ File not found: {config_path}")
            return False
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON format: {e}")
            return False
        except Exception as e:
            print(f"❌ Validation error: {e}")
            return False
    
    def _validate_key_field(self, key_data: str) -> bool:
        """Validate the Key field."""
        try:
            # Check if it's valid base64
            decoded = base64.b64decode(key_data)
            print(f"✓ Key is valid base64 ({len(decoded)} bytes)")
            
            # Check for Collapse prefix
            if not decoded.startswith(b"Collapse"):
                print("⚠️  Warning: Key doesn't start with 'Collapse' prefix")
            else:
                print("✓ Key has correct 'Collapse' prefix")
            
            # Try to extract and validate RSA key
            if self._extract_and_validate_rsa_key(decoded):
                print("✓ RSA key structure is valid")
            else:
                print("⚠️  Warning: Could not validate RSA key structure")
            
            return True
            
        except Exception as e:
            print(f"❌ Invalid Key field: {e}")
            return False
    
    def _extract_and_validate_rsa_key(self, key_data: bytes) -> bool:
        """Extract and validate RSA key from the Collapse format."""
        try:
            # Skip the Collapse prefix and padding
            # Format: "Collapse" + padding + structure + PEM data
            collapse_prefix = b"Collapse"
            if not key_data.startswith(collapse_prefix):
                return False
            
            # Find the PEM start marker
            pem_start = key_data.find(b"-----BEGIN")
            if pem_start == -1:
                return False
            
            # Extract PEM data
            pem_data = key_data[pem_start:]
            
            # Try to load the private key
            private_key = serialization.load_pem_private_key(
                pem_data,
                password=None,
                backend=default_backend()
            )
            
            # Get key size
            key_size = private_key.key_size
            print(f"✓ RSA key size: {key_size} bits")
            
            return True
            
        except Exception:
            return False
    
    def _validate_bitsize_field(self, bit_size: Any) -> bool:
        """Validate the BitSize field."""
        if not isinstance(bit_size, int):
            print(f"❌ BitSize must be an integer, got: {type(bit_size)}")
            return False
        
        if bit_size <= 0:
            print(f"❌ BitSize must be positive, got: {bit_size}")
            return False
        
        print(f"✓ BitSize is valid: {bit_size}")
        return True
    
    def _validate_hash_field(self, hash_value: Any) -> bool:
        """Validate the Hash field."""
        if not isinstance(hash_value, int):
            print(f"❌ Hash must be an integer, got: {type(hash_value)}")
            return False
        
        # Check if it's within 64-bit signed integer range
        if not (-2**63 <= hash_value < 2**63):
            print(f"❌ Hash value out of 64-bit signed integer range: {hash_value}")
            return False
        
        print(f"✓ Hash is valid: {hash_value}")
        return True
    
    def validate_stamp_entry(self, stamp_path: str) -> bool:
        """
        Validate the stamp entry file.
        
        Args:
            stamp_path: Path to the stamp_entry.json file
            
        Returns:
            True if valid, False otherwise
        """
        try:
            with open(stamp_path, 'r', encoding='utf-8') as f:
                stamp = json.load(f)
            
            print(f"\nValidating: {stamp_path}")
            print("-" * 40)
            
            # Check required fields
            required_fields = ['LastUpdated', 'MetadataPath', 'MetadataType', 'MetadataInclude']
            for field in required_fields:
                if field not in stamp:
                    print(f"❌ Missing required field: {field}")
                    return False
                print(f"✓ Found field: {field}")
            
            # Validate specific field values
            if stamp['MetadataPath'] != 'config_master.json':
                print(f"❌ MetadataPath should be 'config_master.json', got: {stamp['MetadataPath']}")
                return False
            print("✓ MetadataPath is correct")
            
            if stamp['MetadataType'] != 'MasterKey':
                print(f"❌ MetadataType should be 'MasterKey', got: {stamp['MetadataType']}")
                return False
            print("✓ MetadataType is correct")
            
            if not isinstance(stamp['MetadataInclude'], bool):
                print(f"❌ MetadataInclude should be boolean, got: {type(stamp['MetadataInclude'])}")
                return False
            print("✓ MetadataInclude is valid")
            
            if not isinstance(stamp['LastUpdated'], int):
                print(f"❌ LastUpdated should be integer, got: {type(stamp['LastUpdated'])}")
                return False
            print(f"✓ LastUpdated is valid: {stamp['LastUpdated']}")
            
            print("✓ All stamp validations passed!")
            return True
            
        except FileNotFoundError:
            print(f"❌ File not found: {stamp_path}")
            return False
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON format: {e}")
            return False
        except Exception as e:
            print(f"❌ Validation error: {e}")
            return False
    
    def validate_directory(self, directory: str) -> bool:
        """
        Validate all configuration files in a directory.
        
        Args:
            directory: Directory containing the configuration files
            
        Returns:
            True if all files are valid, False otherwise
        """
        dir_path = Path(directory)
        
        if not dir_path.exists():
            print(f"❌ Directory not found: {directory}")
            return False
        
        print(f"Validating directory: {dir_path.absolute()}")
        print("=" * 50)
        
        # Check for required files
        config_file = dir_path / "config_master.json"
        stamp_file = dir_path / "stamp_entry.json"
        
        all_valid = True
        
        # Validate config file
        if config_file.exists():
            if not self.validate_master_config(str(config_file)):
                all_valid = False
        else:
            print(f"❌ Missing file: {config_file}")
            all_valid = False
        
        # Validate stamp file
        if stamp_file.exists():
            if not self.validate_stamp_entry(str(stamp_file)):
                all_valid = False
        else:
            print(f"❌ Missing file: {stamp_file}")
            all_valid = False
        
        print("\n" + "=" * 50)
        if all_valid:
            print("🎉 All validations passed! Configuration is ready to use.")
        else:
            print("❌ Some validations failed. Please check the errors above.")
        print("=" * 50)
        
        return all_valid


def main():
    """Main function to handle command line arguments and run the validator."""
    parser = argparse.ArgumentParser(
        description="Validate CollapseLauncher master key configuration files"
    )
    
    parser.add_argument(
        'path',
        help='Path to config file or directory containing configuration files'
    )
    
    parser.add_argument(
        '--type',
        choices=['config', 'stamp', 'directory'],
        default='directory',
        help='Type of validation to perform (default: directory)'
    )
    
    args = parser.parse_args()
    
    validator = ConfigValidator()
    
    try:
        if args.type == 'config':
            success = validator.validate_master_config(args.path)
        elif args.type == 'stamp':
            success = validator.validate_stamp_entry(args.path)
        else:  # directory
            success = validator.validate_directory(args.path)
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
