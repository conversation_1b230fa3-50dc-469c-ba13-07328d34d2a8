# rename this file to appveyor.yml and move it to the repo's root directory to use it

version: '#{build}'
pull_requests:
  do_not_increment_build_number: true
skip_tags: true
skip_branch_with_pr: true
image: Visual Studio 2022
branches:
  only:
  - preview
  - stable
  - nativeaot-test
for:
  -
    branches:
      only:
      - preview
    configuration: Release
    platform: x64
    environment:
      SENTRY_AUTH_TOKEN:
        secure: 0+V+hKjxzGHSx/feeS7l9UFqD989glkg69Z2a/XW1PAPOrSu4NXB25k1X42bAbIH4mHrZtdQgV+qYPi2IrMfJVgd+I5VBI/befw4T7utkhFEvxN9AEvJLQesSacXRY23YV27L5OUzkUtbubg1sM+Dbt/LVJw9y3QYN4gIhQku+x9K0kuAQwUYmYbaQFNbrPj9n2E24up+NlmCBaT9Xc6f4Js6I7KRiE5hLgqR6ENpydKR6HVF0+HCEOB4+xIiW9<PERSON>
    clone_script:
    - cmd: >-
        git clone -q --branch=preview https://github.com/CollapseLauncher/Collapse.git C:\projects\collapse

        git checkout origin/preview
    install:
    - cmd: >-
        echo Init submodules
        
        git submodule update --init --force --depth=20 --recursive --jobs=4
          
        echo Install dotnet sdk
        
        choco install dotnet-9.0-sdk-2xx --version=9.0.202
        
        dotnet --version
        
        echo.



    build_script:
    - cmd: >-
        @echo on
        
        setlocal enabledelayedexpansion
        
        echo Setting environments...
        
        set name=Collapse
        
        set channel=preview
        
        echo.
        
        echo Build app...
        
        dotnet restore CollapseLauncher
        
        dotnet publish CollapseLauncher -c Release -p:PublishProfile=Publish-PreviewRelease -p:PublishDir=".\preview-build\"
        
        
        echo Getting app version...
        
        for /f "tokens=2 delims==" %%I in ('wmic datafile where "name='C:\\projects\\collapse\\CollapseLauncher\\preview-build\\CollapseLauncher.exe'" get Version /value ^| find "Version"') do set "version_untrim=%%I"
        
        set "version=%version_untrim:~0,-2%"
        
        echo Got version %version%
        
        echo.
        
        
        echo Setting up Deployable Resources...
        
        mkdir DeployResource
        
        mkdir DeployResource\current
        
        CollapseLauncher\%channel%-build\CollapseLauncher.exe generatevelopackmetadata
        
        xcopy "CollapseLauncher\%channel%-build\*" "DeployResource\current\" /E /K /Y /I
        
        curl "https://github.com/CollapseLauncher/CollapseLauncher-ReleaseRepo/raw/refs/heads/main/Update.exe" --output DeployResource\update.exe
        
        
        echo Moving all the files into artifact to be pushed to SignPath
        
        mkdir SignArtifact
        
        mkdir SignArtifact\BuildArtifact-%version%
        
        
        xcopy "CollapseLauncher\%channel%-build\*" "SignArtifact\BuildArtifact-%version%\" /E /K /Y /I
    
    
    test: off
    artifacts:
    - path: SignArtifact\
      name: SignArtifact-Preview

    deploy:
    - provider: Webhook
      url: https://app.signpath.io/API/v1/6988fc60-19f4-4710-8eb7-e837c60c83b4/Integrations/AppVeyor?ProjectSlug=Collapse&SigningPolicySlug=release-signing&ArtifactConfigurationSlug=preview
      authorization:
        secure: B8zpDU6wkKuCBRz65VfTFxUCxY7HniWmRbJP/E3tE40kGmHKdaFnMCDSURQFWwR1pCcXHqbkJd3YX76tnTXQow==
      on:
        branch: preview
    notifications:
    - provider: Email
      to:
      - <EMAIL>
      on_build_success: true
      on_build_failure: true
      on_build_status_changed: true

  -
    branches:
      only:
      - stable
    configuration: Publish
    platform: x64
    environment:
      SENTRY_AUTH_TOKEN:
        secure: 0+V+hKjxzGHSx/feeS7l9UFqD989glkg69Z2a/XW1PAPOrSu4NXB25k1X42bAbIH4mHrZtdQgV+qYPi2IrMfJVgd+I5VBI/befw4T7utkhFEvxN9AEvJLQesSacXRY23YV27L5OUzkUtbubg1sM+Dbt/LVJw9y3QYN4gIhQku+x9K0kuAQwUYmYbaQFNbrPj9n2E24up+NlmCBaT9Xc6f4Js6I7KRiE5hLgqR6ENpydKR6HVF0+HCEOB4+xIiW9A
    clone_script:
    - cmd: >-
        git clone -q --branch=stable https://github.com/CollapseLauncher/Collapse.git C:\projects\collapse

        git checkout origin/stable
    install:
    - cmd: >-
        echo Init submodules

        git submodule update --init --force --depth=20 --recursive --jobs=4

        echo.

        echo Install dotnet sdk

        choco install dotnet-9.0-sdk-2xx --version=9.0.202

        dotnet --version

        echo.



    build_script:
    - cmd: >-
        @echo on

        setlocal enabledelayedexpansion

        echo Setting environments...

        set name=Collapse

        set channel=stable

        echo.

        echo Build app...

        dotnet restore CollapseLauncher

        dotnet publish CollapseLauncher -c Publish -p:PublishProfile=Publish-StableRelease -p:PublishDir=".\stable-build\"


        echo Getting app version...

        for /f "tokens=2 delims==" %%I in ('wmic datafile where "name='C:\\projects\\collapse\\CollapseLauncher\\stable-build\\CollapseLauncher.exe'" get Version /value ^| find "Version"') do set "version_untrim=%%I"

        set "version=%version_untrim:~0,-2%"

        echo Got version %version%

        echo.


        echo Setting up Deployable Resources...

        mkdir DeployResource

        mkdir DeployResource\current

        CollapseLauncher\%channel%-build\CollapseLauncher.exe generatevelopackmetadata

        xcopy "CollapseLauncher\%channel%-build\*" "DeployResource\current\" /E /K /Y /I

        curl "https://github.com/CollapseLauncher/CollapseLauncher-ReleaseRepo/raw/refs/heads/main/Update.exe" --output DeployResource\update.exe

        echo.


        echo Moving all the files into artifact to be pushed to SignPath

        mkdir SignArtifact

        mkdir SignArtifact\BuildArtifact-%version%

        xcopy "CollapseLauncher\%channel%-build\*" "SignArtifact\BuildArtifact-%version%\" /E /K /Y /I

        
    test: off
    artifacts:
    - path: SignArtifact\
      name: SignArtifact-Stable

    deploy:
    - provider: Webhook
      url: https://app.signpath.io/API/v1/6988fc60-19f4-4710-8eb7-e837c60c83b4/Integrations/AppVeyor?ProjectSlug=Collapse&SigningPolicySlug=release-signing&ArtifactConfigurationSlug=initial
      authorization:
        secure: B8zpDU6wkKuCBRz65VfTFxUCxY7HniWmRbJP/E3tE40kGmHKdaFnMCDSURQFWwR1pCcXHqbkJd3YX76tnTXQow==
      on:
        branch: stable
    notifications:
    - provider: Email
      to:
      - <EMAIL>
      on_build_success: true
      on_build_failure: true
      on_build_status_changed: true


  -
    branches:
      only:
        - nativeaot-test
    configuration: Release
    platform: x64
    clone_script:
      - cmd: >-
          git clone -q --branch=nativeaot-test https://github.com/CollapseLauncher/Collapse.git C:\projects\collapse
          
          git checkout origin/nativeaot-test
    install:
      - cmd: >-
          echo Init submodules
          
          git submodule update --init --force --depth=20 --recursive
          
          echo.
          
          echo.
          
          echo Install dotnet sdk
          
          choco install dotnet-9.0-sdk-2xx --version=9.0.202
          
          dotnet --version
          
          echo.



    build_script:
      - cmd: >-
          @echo on
          
          setlocal enabledelayedexpansion
          
          echo Setting environments...
          
          set name=Collapse
          
          set channel=preview
          
          echo.
          
          echo Build app...
          
          dotnet restore CollapseLauncher
          
          dotnet publish CollapseLauncher -c Release -p:PublishProfile=Publish-PreviewReleaseAOT -p:PublishDir=".\%channel%-build\"
          
          
          echo Getting app version...
          
          for /f "tokens=2 delims==" %%I in ('wmic datafile where "name='C:\\projects\\collapse\\CollapseLauncher\\%channel%-build\\CollapseLauncher.exe'" get Version /value ^| find "Version"') do set "version_untrim=%%I"
          
          set "version=%version_untrim:~0,-2%"
          
          echo Got version %version%
          
          echo.
          
          
          echo Setting up Deployable Resources...
          
          CollapseLauncher\%channel%-build\CollapseLauncher.exe generatevelopackmetadata
          
          echo.
          
          echo Archiving PDB files
          
          7z a debug-sym.zip CollapseLauncher\%channel%-build\*.pdb
          
          echo Moving all the files into artifact to be pushed to SignPath
          
          mkdir SignArtifact
          
          mkdir SignArtifact\BuildArtifact-%version%
          
          xcopy "CollapseLauncher\%channel%-build\*" "SignArtifact\BuildArtifact-%version%\" /E /K /Y /I
    
    test: off
    artifacts:
      - path: SignArtifact\
        name: SignArtifact-Preview
      - path: debug-sym.zip
        name: DebugSymbols

    deploy:
      - provider: Webhook
        url: https://app.signpath.io/API/v1/6988fc60-19f4-4710-8eb7-e837c60c83b4/Integrations/AppVeyor?ProjectSlug=Collapse&SigningPolicySlug=test-signing&ArtifactConfigurationSlug=preview-aot
        authorization:
          secure: B8zpDU6wkKuCBRz65VfTFxUCxY7HniWmRbJP/E3tE40kGmHKdaFnMCDSURQFWwR1pCcXHqbkJd3YX76tnTXQow==
        on:
          branch: nativeaot-test
          
      - provider: environment
        name: GH_AppVeyorTestDeployTarget_Releases-Files

    notifications:
      - provider: Email
        to:
          - <EMAIL>
        on_build_success: true
        on_build_failure: true
        on_build_status_changed: true