﻿<!--  <PERSON><PERSON><PERSON>per disable IdentifierTypo  -->
<!--  <PERSON><PERSON><PERSON>per disable UnusedMember.Local  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<Page x:Class="CollapseLauncher.Pages.OOBE.OOBESelectGameBG"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:oobe="using:CollapseLauncher.Pages.OOBE"
      xmlns:s="using:CommunityToolkit.WinUI"
      Background="Transparent"
      mc:Ignorable="d">
    <Page.Resources>
        <s:AttachedDropShadow x:Key="DetailsLogoShadowController"
                              BlurRadius="32"
                              CastTo="{x:Bind GameDetailsLogoShadow}"
                              Opacity="1" />
    </Page.Resources>
    <Grid HorizontalAlignment="Stretch"
          VerticalAlignment="Stretch">
        <Image VerticalAlignment="Center"
               Opacity="0.50"
               Source="ms-appx:///Assets/Images/PageBackground/StartupBackground2.png"
               Stretch="UniformToFill" />
        <Grid x:Name="GameDetails">
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Image x:Name="GameDetailsPoster"
                   Grid.ColumnSpan="2"
                   HorizontalAlignment="Right"
                   VerticalAlignment="Stretch"
                   Stretch="UniformToFill" />
            <StackPanel Grid.Column="1"
                        Margin="32,0,40,110"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Bottom">
                <Grid x:Name="GameDetailsLogoShadow" />
                <Image x:Name="GameDetailsLogo"
                       Width="220"
                       Margin="0,0,0,24"
                       HorizontalAlignment="Right"
                       s:Effects.Shadow="{ThemeResource DetailsLogoShadowController}" />
                <Grid x:Name="GameDetailsDescriptionContainer"
                      CornerRadius="8,8,0,0">
                    <Grid x:Name="GameDetailsTextBackground"
                          Background="{ThemeResource AcrylicBackgroundFillColorDefaultBrush}" />
                    <ScrollViewer MaxHeight="192">
                        <TextBlock x:Name="GameDetailsDescription"
                                   Margin="16,12"
                                   FontSize="16"
                                   FontWeight="Medium"
                                   TextAlignment="Center"
                                   TextWrapping="Wrap" />
                    </ScrollViewer>
                </Grid>
                <Button x:Name="GameDetailsHomepage"
                        Padding="16,4,16,8"
                        HorizontalAlignment="Stretch"
                        HorizontalContentAlignment="Stretch"
                        Click="GameDetailsHomepage_Click"
                        CornerRadius="0,0,8,8"
                        Style="{ThemeResource AccentButtonStyle}"
                        Tag="{x:Bind oobe:OOBESelectGameBG.GameDetailsHomepageLink}"
                        ToolTipService.ToolTip="{x:Bind oobe:OOBESelectGameBG.GameDetailsHomepageLink}">
                    <Grid HorizontalAlignment="Stretch">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <TextBlock x:Name="GameDetailsHomepageText"
                                   HorizontalAlignment="Left"
                                   FontWeight="SemiBold"
                                   Text="{x:Bind oobe:OOBESelectGameBG.GameDetailsHomepageLink}"
                                   TextTrimming="CharacterEllipsis" />
                        <FontIcon Grid.Column="1"
                                  Margin="10,0,0,-3"
                                  HorizontalAlignment="Right"
                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                  FontSize="14"
                                  Glyph="&#xf0c1;" />
                    </Grid>
                </Button>
            </StackPanel>
        </Grid>
        <Grid x:Name="GameDetailsDummy"
              Margin="0,0,96,0"
              HorizontalAlignment="Right"
              Visibility="Collapsed">
            <Image Width="400"
                   Height="400"
                   Source="ms-appx:///Assets/Images/GameMascot/AponiaFly.png" />
        </Grid>
    </Grid>
</Page>
