# ✨ Contributing to the Windows Community Toolkit Labs

Thank you for exhibiting interest in contributing to the Windows Community Toolkit. The team is delighted to welcome you onboard to our exciting and growing project. Any contribution or value added go a long way to enhance the project!

Please bear with us as we onboard to this new process using Windows Community Toolkit Labs.

**Note:** _In Preview we're currently not accepting new experiments beyond our trial list to ensure our infrastructure is stabilized, if you'd like to contribute [please see where you can lend a hand with Labs itself here](https://github.com/CommunityToolkit/Labs-Windows/issues?q=is%3Aopen+is%3Aissue+label%3A%22help+wanted%22)_ [If you have an idea for a component though, please feel free to open a discussion here.](https://github.com/CommunityToolkit/Labs-Windows/discussions?discussions_q=category%3AExperiments+category%3A%22Ideas%22+)

## ❔ Questions <a name="question"></a>

Due to the high volume of incoming issues please keep our GitHub issues for bug reports about the Labs infrastructure itself. For general questions and discussions (including new features), [please open up a discussion (if one doesn't exist already)](https://github.com/CommunityToolkit/Labs-Windows/discussions). You can [also reach out in the `#community-toolkit` channel on Discord here](https://aka.ms/wct/discord)

## 🙋 Help Wanted <a name="help"></a>

We have a list of issues that are labeled as [help wanted](https://github.com/CommunityToolkit/Labs-Windows/labels/help%20wanted). Note, the level of complexity in the list can vary, but feel free to jump in to help solve these issues. Be sure to comment on issues first where you'd like to help out to coordinate with others in the community.

## 💙 Thank You

**Thank you so much for contributing to this amazing project. We hope you will continue to add value and find yourself as a highly reliable source to the Windows Community Toolkit.**
