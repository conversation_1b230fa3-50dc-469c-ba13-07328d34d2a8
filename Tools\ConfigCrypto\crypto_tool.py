#!/usr/bin/env python3
"""
CollapseLauncher Configuration Crypto Tool

This tool provides encryption and decryption capabilities for 
CollapseLauncher configuration files.
"""

import argparse
import base64
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Union, List

from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend


class CollapseDecryptor:
    """Decryptor for CollapseLauncher encrypted configuration fields."""
    
    def __init__(self, master_key_path: Optional[str] = None):
        """
        Initialize the decryptor.
        
        Args:
            master_key_path: Path to the master key configuration file
        """
        self.private_key = None
        self.master_key_config = None
        
        if master_key_path:
            self.load_master_key(master_key_path)
    
    def load_master_key(self, key_path: str) -> None:
        """
        Load master key from configuration file.

        Args:
            key_path: Path to the master key configuration file
        """
        try:
            with open(key_path, 'r', encoding='utf-8') as f:
                self.master_key_config = json.load(f)

            # Extract and decode the key using the same logic as C# _f8j51 method
            key_data = self.master_key_config['Key']

            # Decode using the same method as C# _f8j51
            decoded_key_bytes = self._f8j51_decode(key_data)
            print(f"✓ Master key loaded successfully from: {decoded_key_bytes}")

            # Convert to UTF-8 string (should be XML format RSA key)
            xml_key_data = decoded_key_bytes.decode('utf-8')
            print(f"✓ Master key loaded successfully from: {xml_key_data}")

            # Parse XML RSA key format and convert to cryptography RSA key
            self.private_key = self._parse_xml_rsa_key(xml_key_data)

            print(f"✓ Master key loaded successfully from: {key_path}")

        except Exception as e:
            raise ValueError(f"Failed to load master key: {e}")

    def _f8j51_decode(self, hex_string: str) -> bytes:
        """
        Python implementation of C# _f8j51 method.
        Decodes hex string and applies XOR with sKey.

        Args:
            hex_string: Hexadecimal string to decode

        Returns:
            Decoded bytes
        """
        # sKey from C# code
        s_key = bytes([
            232, 170, 135, 231,
            189, 170, 227, 130,
            134, 227, 129, 132
        ])

        # Hex character mapping (same as C# __951 dictionary)
        hex_map = {
            'a': 0xA, 'b': 0xB, 'c': 0xC, 'd': 0xD, 'e': 0xE, 'f': 0xF,
            'A': 0xA, 'B': 0xB, 'C': 0xC, 'D': 0xD, 'E': 0xE, 'F': 0xF,
            '0': 0x0, '1': 0x1, '2': 0x2, '3': 0x3, '4': 0x4,
            '5': 0x5, '6': 0x6, '7': 0x7, '8': 0x8, '9': 0x9
        }

        result = bytearray(len(hex_string) // 2)

        for i in range(0, len(hex_string), 2):
            # Get two hex characters
            high_char = hex_string[i]
            low_char = hex_string[i + 1]

            # Convert to byte value
            byte_value = (hex_map[high_char] << 4) | hex_map[low_char]

            # Apply XOR with sKey (same as C# logic)
            result_index = i // 2
            result[result_index] = byte_value ^ s_key[result_index % len(s_key)]

        return bytes(result)

    def _parse_xml_rsa_key(self, xml_data: str) -> rsa.RSAPrivateKey:
        """
        Parse XML format RSA key and convert to cryptography RSA key.

        Args:
            xml_data: XML format RSA key data

        Returns:
            RSA private key object
        """
        import xml.etree.ElementTree as ET

        try:
            # Parse XML
            root = ET.fromstring(xml_data)

            # Extract RSA parameters from XML
            modulus = base64.b64decode(root.find('Modulus').text)
            exponent = base64.b64decode(root.find('Exponent').text)
            d = base64.b64decode(root.find('D').text)
            p = base64.b64decode(root.find('P').text)
            q = base64.b64decode(root.find('Q').text)
            dp = base64.b64decode(root.find('DP').text)
            dq = base64.b64decode(root.find('DQ').text)
            iqmp = base64.b64decode(root.find('InverseQ').text)

            # Convert bytes to integers
            def bytes_to_int(data):
                return int.from_bytes(data, byteorder='big')

            # Create RSA private key
            private_key = rsa.RSAPrivateNumbers(
                p=bytes_to_int(p),
                q=bytes_to_int(q),
                d=bytes_to_int(d),
                dmp1=bytes_to_int(dp),
                dmq1=bytes_to_int(dq),
                iqmp=bytes_to_int(iqmp),
                public_numbers=rsa.RSAPublicNumbers(
                    e=bytes_to_int(exponent),
                    n=bytes_to_int(modulus)
                )
            ).private_key(backend=default_backend())

            return private_key

        except Exception as e:
            raise ValueError(f"Failed to parse XML RSA key: {e}")
    
    def decrypt_field(self, encrypted_data: str) -> str:
        """
        Decrypt a single encrypted field using the same logic as C# DecryptStringWithMasterKey.

        Args:
            encrypted_data: Hex-encoded encrypted data (same format as C# input)

        Returns:
            Decrypted plaintext string
        """
        if not self.private_key:
            raise ValueError("Master key not loaded. Call load_master_key() first.")

        try:
            # Step 1: Decode hex string using _f8j51 logic (same as C# line 86)
            decoded_bytes = self._f8j51_decode(encrypted_data)

            # Step 2: Convert to UTF-8 string (base64 encoded RSA encrypted data)
            base64_encrypted_data = decoded_bytes.decode('utf-8')

            # Step 3: Decrypt RSA content using the same logic as C# DecryptRSAContent
            decrypted_bytes = self._decrypt_rsa_content(
                base64_encrypted_data,
                self.master_key_config['BitSize']
            )

            # Step 4: Convert result to UTF-8 string
            return decrypted_bytes.decode('utf-8')

        except Exception as e:
            raise ValueError(f"Decryption failed: {e}")

    def _decrypt_rsa_content(self, content_base64: str, enc_bit_length: int) -> bytes:
        """
        Python implementation of C# DecryptRSAContent method.

        Args:
            content_base64: Base64 encoded encrypted content
            enc_bit_length: Encryption bit length (chunk size)

        Returns:
            Decrypted bytes
        """
        try:
            # Decode base64 content
            enc_content = base64.b64decode(content_base64)

            # Decrypt in chunks (same logic as C# method)
            dec_content = bytearray()
            j = 0

            while j < len(enc_content):
                # Extract chunk
                chunk_size = min(enc_bit_length, len(enc_content) - j)
                chunk = enc_content[j:j + chunk_size]

                # Decrypt chunk using RSA with PKCS1v15 padding
                chunk_dec = self.private_key.decrypt(chunk, padding.PKCS1v15())
                dec_content.extend(chunk_dec)

                j += enc_bit_length

            return bytes(dec_content)

        except Exception as e:
            raise ValueError(f"RSA decryption failed: {e}")
    
    def encrypt_field(self, plaintext: str) -> str:
        """
        Encrypt a plaintext string using the reverse of C# DecryptStringWithMasterKey logic.

        Args:
            plaintext: Plain text to encrypt

        Returns:
            Hex-encoded encrypted data (same format as C# expects)
        """
        if not self.private_key:
            raise ValueError("Master key not loaded. Call load_master_key() first.")

        try:
            # Step 1: Encrypt plaintext using RSA (reverse of DecryptRSAContent)
            encrypted_base64 = self._encrypt_rsa_content(
                plaintext.encode('utf-8'),
                self.master_key_config['BitSize']
            )

            # Step 2: Convert to bytes (reverse of UTF-8 decode)
            encrypted_bytes = encrypted_base64.encode('utf-8')

            # Step 3: Encode using reverse of _f8j51 logic
            hex_encoded = self._f8j51_encode(encrypted_bytes)

            return hex_encoded

        except Exception as e:
            raise ValueError(f"Encryption failed: {e}")

    def _encrypt_rsa_content(self, content: bytes, enc_bit_length: int) -> str:
        """
        Python implementation of reverse C# DecryptRSAContent method.

        Args:
            content: Content to encrypt
            enc_bit_length: Encryption bit length (chunk size for output)

        Returns:
            Base64 encoded encrypted content
        """
        try:
            # Get public key for encryption
            public_key = self.private_key.public_key()

            # Calculate max chunk size for RSA encryption
            # RSA can encrypt data up to (key_size_in_bytes - padding_overhead)
            key_size_bytes = self.private_key.key_size // 8
            max_chunk_size = key_size_bytes - 11  # PKCS1v15 padding overhead

            # Encrypt in chunks
            encrypted_chunks = []
            i = 0

            while i < len(content):
                # Extract chunk
                chunk_size = min(max_chunk_size, len(content) - i)
                chunk = content[i:i + chunk_size]

                # Encrypt chunk using RSA with PKCS1v15 padding
                encrypted_chunk = public_key.encrypt(chunk, padding.PKCS1v15())
                encrypted_chunks.append(encrypted_chunk)

                i += chunk_size

            # Combine all encrypted chunks
            full_encrypted = b''.join(encrypted_chunks)

            # Encode to base64
            return base64.b64encode(full_encrypted).decode('utf-8')

        except Exception as e:
            raise ValueError(f"RSA encryption failed: {e}")

    def _f8j51_encode(self, data: bytes) -> str:
        """
        Python implementation of reverse C# _f8j51 method.
        Applies XOR with sKey and encodes to hex string.

        Args:
            data: Bytes to encode

        Returns:
            Hex-encoded string
        """
        # sKey from C# code
        s_key = bytes([
            232, 170, 135, 231,
            189, 170, 227, 130,
            134, 227, 129, 132
        ])

        result = []

        for i, byte_value in enumerate(data):
            # Apply XOR with sKey (reverse of decode operation)
            xor_value = byte_value ^ s_key[i % len(s_key)]

            # Convert to hex string (2 characters)
            hex_str = f"{xor_value:02x}"
            result.append(hex_str)

        return ''.join(result)
    
    def decrypt_config_field(self, config: Dict[str, Any], field_name: str) -> Optional[str]:
        """
        Decrypt a specific field in a configuration dictionary.
        
        Args:
            config: Configuration dictionary
            field_name: Name of the field to decrypt
            
        Returns:
            Decrypted value or None if field doesn't exist
        """
        if field_name not in config:
            return None
        
        encrypted_value = config[field_name]
        if not isinstance(encrypted_value, str):
            return None
        
        try:
            return self.decrypt_field(encrypted_value)
        except Exception as e:
            print(f"Warning: Failed to decrypt field '{field_name}': {e}")
            return None
    
    def decrypt_config_file(self, input_path: str, output_path: str, 
                           fields_to_decrypt: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Decrypt specified fields in a configuration file.
        
        Args:
            input_path: Path to input configuration file
            output_path: Path to output decrypted file
            fields_to_decrypt: List of field names to decrypt (None for all known fields)
            
        Returns:
            Decrypted configuration dictionary
        """
        # Default fields that are typically encrypted
        if fields_to_decrypt is None:
            fields_to_decrypt = [
                'GameDispatchURL',
                'GameDispatchURLTemplate',
                'GameGatewayURLTemplate',
                'GameGatewayDefault',
                'LauncherSpriteURL',
                'LauncherResourceURL',
                'LauncherPluginURL',
                'LauncherNewsURL',
                'LauncherCPSType',
                'LauncherId',
                'LauncherGameId',
                'LauncherBizName',
                'DispatcherKey',
                'GameDispatchChannelName',
                'GameDispatchArrayURL'
            ]
        
        # Load configuration file
        with open(input_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        decrypted_config = config.copy()
        decryption_results = {}
        
        print(f"Decrypting configuration file: {input_path}")
        print("-" * 50)
        
        # Decrypt individual fields
        for field_name in fields_to_decrypt:
            if field_name in config:
                field_value = config[field_name]
                
                if isinstance(field_value, str):
                    # Single string field
                    try:
                        decrypted_value = self.decrypt_field(field_value)
                        decrypted_config[field_name] = decrypted_value
                        decryption_results[field_name] = "✓ Decrypted"
                        print(f"✓ {field_name}: {decrypted_value}")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt")
                
                elif isinstance(field_value, list):
                    # Array of strings
                    try:
                        decrypted_array = []
                        for item in field_value:
                            if isinstance(item, str):
                                decrypted_item = self.decrypt_field(item)
                                decrypted_array.append(decrypted_item)
                            else:
                                decrypted_array.append(item)
                        
                        decrypted_config[field_name] = decrypted_array
                        decryption_results[field_name] = "✓ Decrypted (array)"
                        print(f"✓ {field_name}: {len(decrypted_array)} items decrypted")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt array")
                
                elif isinstance(field_value, dict):
                    # Nested object - decrypt string values
                    try:
                        decrypted_object = {}
                        for key, value in field_value.items():
                            if isinstance(value, str):
                                try:
                                    decrypted_object[key] = self.decrypt_field(value)
                                except:
                                    decrypted_object[key] = value
                            else:
                                decrypted_object[key] = value
                        
                        decrypted_config[field_name] = decrypted_object
                        decryption_results[field_name] = "✓ Decrypted (object)"
                        print(f"✓ {field_name}: Object decrypted")
                    except Exception as e:
                        decryption_results[field_name] = f"❌ Failed: {e}"
                        print(f"❌ {field_name}: Failed to decrypt object")
        
        # Save decrypted configuration
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(decrypted_config, f, indent=2, ensure_ascii=False)
        
        print("-" * 50)
        print(f"✓ Decrypted configuration saved to: {output_path}")
        
        return decrypted_config


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="CollapseLauncher Configuration Crypto Tool"
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Decrypt single field command
    decrypt_parser = subparsers.add_parser('decrypt', help='Decrypt a single field')
    decrypt_parser.add_argument('--field', required=True, help='Field name')
    decrypt_parser.add_argument('--data', required=True, help='Encrypted data')
    decrypt_parser.add_argument('--master-key', required=True, help='Master key file path')
    
    # Encrypt single field command
    encrypt_parser = subparsers.add_parser('encrypt', help='Encrypt a single field')
    encrypt_parser.add_argument('--data', required=True, help='Plain text data')
    encrypt_parser.add_argument('--master-key', required=True, help='Master key file path')
    
    # Decrypt config file command
    decrypt_config_parser = subparsers.add_parser('decrypt-config', help='Decrypt configuration file')
    decrypt_config_parser.add_argument('--input', required=True, help='Input config file')
    decrypt_config_parser.add_argument('--output', required=True, help='Output decrypted file')
    decrypt_config_parser.add_argument('--master-key', required=True, help='Master key file path')
    decrypt_config_parser.add_argument('--fields', nargs='*', help='Specific fields to decrypt')
    
    # Batch decrypt command
    batch_parser = subparsers.add_parser('batch-decrypt', help='Batch decrypt multiple files')
    batch_parser.add_argument('--input-dir', required=True, help='Input directory')
    batch_parser.add_argument('--output-dir', required=True, help='Output directory')
    batch_parser.add_argument('--master-key', required=True, help='Master key file path')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        decryptor = CollapseDecryptor(args.master_key)
        
        if args.command == 'decrypt':
            result = decryptor.decrypt_field(args.data)
            print(f"Field: {args.field}")
            print(f"Decrypted: {result}")
        
        elif args.command == 'encrypt':
            result = decryptor.encrypt_field(args.data)
            print(f"Plaintext: {args.data}")
            print(f"Encrypted: {result}")
        
        elif args.command == 'decrypt-config':
            decryptor.decrypt_config_file(args.input, args.output, args.fields)
        
        elif args.command == 'batch-decrypt':
            input_dir = Path(args.input_dir)
            output_dir = Path(args.output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            for config_file in input_dir.glob('*.json'):
                output_file = output_dir / f"decrypted_{config_file.name}"
                print(f"\nProcessing: {config_file}")
                decryptor.decrypt_config_file(str(config_file), str(output_file))
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
