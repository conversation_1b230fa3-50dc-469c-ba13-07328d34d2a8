#!/usr/bin/env python3
"""
Test suite for the Configuration Crypto Tool

This script runs comprehensive tests to ensure the crypto tools work correctly.
"""

import json
import os
import shutil
import tempfile
import unittest
from pathlib import Path

# Import our modules
from crypto_tool import <PERSON>lap<PERSON>D<PERSON>ryptor
from field_analyzer import Config<PERSON><PERSON><PERSON><PERSON>yzer
from config_generator import ConfigGenerator


class TestCryptoTool(unittest.TestCase):
    """Test cases for the Crypto Tool."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        
        # Create a mock master key for testing
        self.master_key_config = {
            "Key": "Q29sbGFwc2UBAAAAAAAAAGICAAAAAAAAXgIAAAAAAACPLoEwggJaAgEAAoGBAK16ClpvNiUl+FGIoZLdhUo66d6Wak8begMidGXsR7LErWHrAhblDpgwa5NJ8T02Nq+6AeGQvbQ4R85gOujHuesJA+O2d3whAXyxcZhFXVArzNUSBXUMUAgewjGhVlx6FXwYTXA+tDrVw+9bxbrAS4qGuhwx2ypPC3YFnVddDlmhAgERAoGBAKNFr2QsbzIFnmra8mwbyL5VkNF+gixWGHtruNhl6SC5G6dzxbsx755LsIqf8ht+UZZUtnnxo3xxNIYAN3Gs6zYDOoAQ3BWu4s574iIkE8ksRbs8t5l6Y0kBWhoHb5rlEC4IOamoXHRoE+OeRc6ORkrviMk6Q/nTaCsu/TS0274xAkEA16GiwrgCGvcYbgSZOBJRx4G9kioGgexLSyW62iK4EuT0Xu9xyflBDaC4yooFkxrflqEAIiEfTqNGlYeJks+5qwJBAM30GOHVovw5aN/Musb1+KVBAI9YW3haP0Isux9ND9PPVtCgujHYvhmOFLkRibYF+1GXxkHR0Y/JgY4oxcoVJeMCQHIoZTno8g5GlHZ657RF7w9Er6e75VPXcyfIrjc/jqCXVDJCh4kLfMr6vC79xrdKdl7NtMbGPcA4Uotl32vXYksCQBg61cA3QFnoou0nJQhZLE+tS1wogztV6VMUUj/q8s2f7BiLYTMKcLe2XMp6iKwAtCe3gLxzCZhyDz3mrduKBHUCQD+P74KtVxe+zfCvzsU23I1hQgtXfr91ucVkCW5lNyn1RdsDkrWXiC09l9drOwBw/rlHzfTv8XRASHqnEi1cVo8D",
            "BitSize": 128,
            "Hash": 4849480174666812961
        }
        
        self.master_key_path = Path(self.test_dir) / "test_master_key.json"
        with open(self.master_key_path, 'w') as f:
            json.dump(self.master_key_config, f)
    
    def tearDown(self):
        """Clean up test environment."""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_master_key_loading(self):
        """Test master key loading."""
        decryptor = CollapseDecryptor(str(self.master_key_path))
        self.assertIsNotNone(decryptor.private_key)
        self.assertIsNotNone(decryptor.master_key_config)
    
    def test_encryption_decryption_cycle(self):
        """Test encryption and decryption cycle."""
        decryptor = CollapseDecryptor(str(self.master_key_path))
        
        test_data = "https://example.com/api/test"
        
        # Encrypt
        encrypted = decryptor.encrypt_field(test_data)
        self.assertIsInstance(encrypted, str)
        self.assertTrue(len(encrypted) > 0)
        
        # Decrypt
        decrypted = decryptor.decrypt_field(encrypted)
        self.assertEqual(decrypted, test_data)
    
    def test_config_file_processing(self):
        """Test configuration file processing."""
        decryptor = CollapseDecryptor(str(self.master_key_path))
        
        # Create test config
        test_config = {
            "ProfileName": "TestProfile",
            "LauncherResourceURL": "https://example.com/resources",
            "GameDispatchArrayURL": [
                "https://api1.example.com",
                "https://api2.example.com"
            ],
            "PlaintextField": "This should not be encrypted"
        }
        
        # Save test config
        config_path = Path(self.test_dir) / "test_config.json"
        with open(config_path, 'w') as f:
            json.dump(test_config, f)
        
        # Process config
        output_path = Path(self.test_dir) / "decrypted_config.json"
        result = decryptor.decrypt_config_file(
            str(config_path), 
            str(output_path),
            ['LauncherResourceURL', 'GameDispatchArrayURL']
        )
        
        # Verify results
        self.assertIn('ProfileName', result)
        self.assertEqual(result['ProfileName'], 'TestProfile')
        self.assertEqual(result['PlaintextField'], 'This should not be encrypted')


class TestFieldAnalyzer(unittest.TestCase):
    """Test cases for the Field Analyzer."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.analyzer = ConfigFieldAnalyzer()
    
    def tearDown(self):
        """Clean up test environment."""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_base64_detection(self):
        """Test base64 detection."""
        valid_base64 = "SGVsbG8gV29ybGQ="  # "Hello World"
        invalid_base64 = "This is not base64!"
        
        self.assertTrue(self.analyzer.is_base64_encoded(valid_base64))
        self.assertFalse(self.analyzer.is_base64_encoded(invalid_base64))
    
    def test_collapse_encryption_detection(self):
        """Test Collapse encryption detection."""
        # This is a mock Collapse-encrypted string
        collapse_encrypted = "Q29sbGFwc2UBAAAAAAAAAGICAAAAAAAAXgIAAAAAAACPLoE="
        not_collapse = "SGVsbG8gV29ybGQ="
        
        self.assertTrue(self.analyzer.is_collapse_encrypted(collapse_encrypted))
        self.assertFalse(self.analyzer.is_collapse_encrypted(not_collapse))
    
    def test_field_analysis(self):
        """Test field analysis."""
        # Test string field
        string_analysis = self.analyzer.analyze_field_value(
            "LauncherResourceURL", 
            "https://example.com"
        )
        self.assertEqual(string_analysis['type'], 'str')
        self.assertEqual(string_analysis['encryption_status'], 'plaintext')
        
        # Test array field
        array_analysis = self.analyzer.analyze_field_value(
            "GameSupportedLanguages",
            ["English", "Chinese"]
        )
        self.assertEqual(array_analysis['type'], 'list')
        self.assertEqual(array_analysis['length'], 2)
    
    def test_config_analysis(self):
        """Test complete configuration analysis."""
        test_config = {
            "ProfileName": "TestProfile",
            "LauncherResourceURL": "https://example.com",
            "GameSupportedLanguages": ["English", "Chinese"],
            "IsRepairEnabled": True
        }
        
        config_path = Path(self.test_dir) / "test_config.json"
        with open(config_path, 'w') as f:
            json.dump(test_config, f)
        
        analysis = self.analyzer.analyze_config_file(str(config_path))
        
        self.assertEqual(analysis['total_fields'], 4)
        self.assertIn('ProfileName', analysis['fields'])
        self.assertIn('summary', analysis)


class TestConfigGenerator(unittest.TestCase):
    """Test cases for the Config Generator."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        
        # Create a mock master key
        self.master_key_config = {
            "Key": "Q29sbGFwc2UBAAAAAAAAAGICAAAAAAAAXgIAAAAAAACPLoEwggJaAgEAAoGBAK16ClpvNiUl+FGIoZLdhUo66d6Wak8begMidGXsR7LErWHrAhblDpgwa5NJ8T02Nq+6AeGQvbQ4R85gOujHuesJA+O2d3whAXyxcZhFXVArzNUSBXUMUAgewjGhVlx6FXwYTXA+tDrVw+9bxbrAS4qGuhwx2ypPC3YFnVddDlmhAgERAoGBAKNFr2QsbzIFnmra8mwbyL5VkNF+gixWGHtruNhl6SC5G6dzxbsx755LsIqf8ht+UZZUtnnxo3xxNIYAN3Gs6zYDOoAQ3BWu4s574iIkE8ksRbs8t5l6Y0kBWhoHb5rlEC4IOamoXHRoE+OeRc6ORkrviMk6Q/nTaCsu/TS0274xAkEA16GiwrgCGvcYbgSZOBJRx4G9kioGgexLSyW62iK4EuT0Xu9xyflBDaC4yooFkxrflqEAIiEfTqNGlYeJks+5qwJBAM30GOHVovw5aN/Musb1+KVBAI9YW3haP0Isux9ND9PPVtCgujHYvhmOFLkRibYF+1GXxkHR0Y/JgY4oxcoVJeMCQHIoZTno8g5GlHZ657RF7w9Er6e75VPXcyfIrjc/jqCXVDJCh4kLfMr6vC79xrdKdl7NtMbGPcA4Uotl32vXYksCQBg61cA3QFnoou0nJQhZLE+tS1wogztV6VMUUj/q8s2f7BiLYTMKcLe2XMp6iKwAtCe3gLxzCZhyDz3mrduKBHUCQD+P74KtVxe+zfCvzsU23I1hQgtXfr91ucVkCW5lNyn1RdsDkrWXiC09l9drOwBw/rlHzfTv8XRASHqnEi1cVo8D",
            "BitSize": 128,
            "Hash": 4849480174666812961
        }
        
        self.master_key_path = Path(self.test_dir) / "test_master_key.json"
        with open(self.master_key_path, 'w') as f:
            json.dump(self.master_key_config, f)
    
    def tearDown(self):
        """Clean up test environment."""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_config_generation(self):
        """Test configuration generation."""
        generator = ConfigGenerator(str(self.master_key_path))
        
        test_data = {
            "ProfileName": "TestGame",
            "LauncherResourceURL": "https://example.com/resources",
            "GameDispatchArrayURL": ["https://api1.example.com", "https://api2.example.com"]
        }
        
        config = generator.create_config_from_template(test_data)
        
        # Check that basic fields are preserved
        self.assertEqual(config['ProfileName'], 'TestGame')
        
        # Check that encrypted fields are actually encrypted
        self.assertNotEqual(config['LauncherResourceURL'], 'https://example.com/resources')
        self.assertTrue(len(config['LauncherResourceURL']) > 0)
        
        # Check that hash is generated
        self.assertIsInstance(config['Hash'], int)
        self.assertGreater(config['Hash'], 0)
    
    def test_stamp_generation(self):
        """Test stamp entry generation."""
        generator = ConfigGenerator(str(self.master_key_path))
        
        stamp = generator.create_stamp_entry(
            "config_test.json",
            "Test Game",
            "Test Region"
        )
        
        self.assertEqual(stamp['MetadataPath'], 'config_test.json')
        self.assertEqual(stamp['MetadataType'], 'PresetConfigV2')
        self.assertEqual(stamp['GameName'], 'Test Game')
        self.assertEqual(stamp['GameRegion'], 'Test Region')
        self.assertTrue(stamp['MetadataInclude'])


def run_tests():
    """Run all tests."""
    print("Running Configuration Crypto Tool Tests...")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestCryptoTool))
    suite.addTests(loader.loadTestsFromTestCase(TestFieldAnalyzer))
    suite.addTests(loader.loadTestsFromTestCase(TestConfigGenerator))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 All tests passed!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
        if result.failures:
            print("\nFailures:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\nErrors:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
        
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
