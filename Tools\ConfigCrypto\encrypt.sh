#!/bin/bash
# CollapseLauncher Configuration Encryption Tool (Linux/macOS)
# Quick encryption script for configuration files

set -e

echo "========================================"
echo "CollapseLauncher Config Encryption Tool"
echo "========================================"
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7+ and try again"
    exit 1
fi

# Check if config file is provided as argument
if [ -z "$1" ]; then
    read -p "Enter configuration file path (plaintext): " CONFIG_FILE
else
    CONFIG_FILE="$1"
fi

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Configuration file not found: $CONFIG_FILE"
    exit 1
fi

# Check if master key file exists
MASTER_KEY_FILE="../MasterKeyGenerator/output/config_master.json"
if [ ! -f "$MASTER_KEY_FILE" ]; then
    read -p "Enter master key file path: " MASTER_KEY_FILE
    if [ ! -f "$MASTER_KEY_FILE" ]; then
        echo "Error: Master key file not found: $MASTER_KEY_FILE"
        exit 1
    fi
fi

# Generate output filename
CONFIG_DIR=$(dirname "$CONFIG_FILE")
CONFIG_NAME=$(basename "$CONFIG_FILE" .json)
OUTPUT_FILE="$CONFIG_DIR/${CONFIG_NAME}_encrypted.json"

echo
echo "Configuration: $CONFIG_FILE"
echo "Master Key: $MASTER_KEY_FILE"
echo "Output: $OUTPUT_FILE"
echo

# Check dependencies
echo "Checking dependencies..."
if ! python3 -c "import cryptography, json" &> /dev/null; then
    echo "Installing required packages..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        exit 1
    fi
fi

echo "Dependencies OK"
echo

# Analyze the configuration first
echo "Analyzing configuration file..."
python3 field_analyzer.py "$CONFIG_FILE" --brief

echo
echo "Starting encryption..."
python3 crypto_tool.py encrypt-config \
    --input "$CONFIG_FILE" \
    --output "$OUTPUT_FILE" \
    --master-key "$MASTER_KEY_FILE"

if [ $? -ne 0 ]; then
    echo
    echo "Error: Encryption failed"
    exit 1
fi

echo
echo "========================================"
echo "Encryption completed successfully!"
echo "========================================"
echo "Output file: $OUTPUT_FILE"
echo

# Ask if user wants to open the encrypted file
read -p "Open encrypted file? (y/n): " OPEN_FILE
if [[ "$OPEN_FILE" =~ ^[Yy]$ ]]; then
    if command -v code &> /dev/null; then
        code "$OUTPUT_FILE"
    elif command -v nano &> /dev/null; then
        nano "$OUTPUT_FILE"
    elif command -v vim &> /dev/null; then
        vim "$OUTPUT_FILE"
    else
        echo "No suitable editor found. File saved at: $OUTPUT_FILE"
    fi
fi
