{"version": 1, "dependencies": {"net9.0": {"Microsoft.NET.ILLink.Tasks": {"type": "Direct", "requested": "[9.0.6, )", "resolved": "9.0.6", "contentHash": "TXy3SbJzGXQbxxIxCjdrp8bwEyTDImyYNpTpd6v7P3JL2Y7dno8EYG7dPezfYTa5SoWKdhbH9cbnwHHs3BR5gA=="}, "System.Drawing.Common": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "T/6nqx0B7/uTe5JjBwrKZilLuwfhHLOVmNKlT/wr4A9Dna94mgTdz3lTfrdJ72QRx7IHCv/LzoJPmFSfK/N6WA==", "dependencies": {"Microsoft.Win32.SystemEvents": "9.0.5"}}, "Microsoft.Win32.SystemEvents": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "D4OYNpmvAsF9MkaY2W8Jue2XuNHDhygvwzo019hs+lP85KaVnOlXmqsjDKr1dHb1DPxDnOKpe6mAgJN7S6ttwg=="}}, "net9.0/win-x64": {"Microsoft.Win32.SystemEvents": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "D4OYNpmvAsF9MkaY2W8Jue2XuNHDhygvwzo019hs+lP85KaVnOlXmqsjDKr1dHb1DPxDnOKpe6mAgJN7S6ttwg=="}}}}