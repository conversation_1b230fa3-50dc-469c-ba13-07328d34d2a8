name: <PERSON>odana for Pull Request
on:
 pull_request:
    branches:
      - main
      - '*staging'
    paths-ignore:
      - '**.md'
      - 'Hi3Helper.Core/Lang/**.json'
      - 'Docs/**'
      - '**/packages.lock.json'

jobs:
  qodana:
    runs-on: windows-latest
    
    permissions:
        actions: read
        contents: write
        pull-requests: write
        checks: write
        security-events: write
    strategy:
      matrix:
        configuration: [Release] # No need to distribute Debug builds
        platform: [x64]
        framework: [net9.0-windows10.0.26100.0]
    env:
      Configuration: ${{ matrix.configuration }}
      Platform: ${{ matrix.platform }}
      DOTNET_INSTALL_DIR: '.\.dotnet' 
      DOTNET_VERSION: '9.0.3xx'
      DOTNET_QUALITY: 'ga'
      NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages
    
    steps:
    # skip all the steps if the head repo is not the same as the main repo
    # https://github.com/orgs/community/discussions/26829#discussioncomment-3253575
      - uses: actions/checkout@v4
        if: ${{ github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name }}
        with:
            ref: ${{ github.event.pull_request.head.sha }}  # to check out the actual pull request commit, not the merge commit
            fetch-depth: 0  # a full history is required for pull request analysis
            submodules: recursive # many many submodules  
      - name: Install .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}
          dotnet-quality: ${{ env.DOTNET_QUALITY }}
          cache: true
          cache-dependency-path: CollapseLauncher/packages.lock.json
          
      - name: 'Qodana Scan'
        if: ${{ github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name }} 
        uses: JetBrains/qodana-action@main
        with:
          args: --ide,QDNET
          pr-mode: true
        env:
            QODANA_TOKEN: ${{ secrets.QODANA_TOKEN }} # read the steps about it below
            _JAVA_OPTIONS: -Xmx2g
            JAVA_OPTIONS: -Xmx2g
      - uses: github/codeql-action/upload-sarif@v3
        if: ${{ github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name }}
        with:
            sarif_file: ${{ runner.temp }}/qodana/results/qodana.sarif.json
