#!/usr/bin/env python3
"""
Test script to verify the corrected implementation based on DataCooker.cs analysis.
"""

import json
import tempfile
from pathlib import Path

# Import our corrected modules
from crypto_tool import CollapseDecryptor
import sys
import os

# Add MasterKeyGenerator to path
sys.path.append(str(Path(__file__).parent.parent / "MasterKeyGenerator"))
from generate_master_key import Master<PERSON>eyGenerator


def test_master_key_generation_and_loading():
    """Test master key generation and loading with correct DER format."""
    
    print("=" * 60)
    print("Testing Corrected Master Key Implementation")
    print("=" * 60)
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Step 1: Generate master key using corrected generator
        print("Step 1: Generating master key...")
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        
        # Create master key config
        master_config = generator.create_master_key_config()
        
        # Save master key
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        print(f"✓ Master key generated and saved")
        print(f"  Key format: DER encoded, Base64: {master_config['Key'][:50]}...")
        print(f"  BitSize: {master_config['BitSize']}")
        print(f"  Hash: {master_config['Hash']}")
        
        # Step 2: Load master key using corrected decryptor
        print("\nStep 2: Loading master key...")
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            print("✓ Master key loaded successfully")
            print(f"  Loaded key size: {decryptor.private_key.key_size} bits")
        except Exception as e:
            print(f"❌ Failed to load master key: {e}")
            return False
        
        return True


def test_serve_v3_detection():
    """Test ServeV3 format detection."""
    
    print("\n" + "=" * 60)
    print("Testing ServeV3 Format Detection")
    print("=" * 60)
    
    # Create a dummy decryptor for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Generate a test master key
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        master_config = generator.create_master_key_config()
        
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        decryptor = CollapseDecryptor(str(master_key_path))
        
        # Test ServeV3 signature detection
        collapse_signature = 7310310183885631299
        test_data = collapse_signature.to_bytes(8, byteorder='little') + b'\x00' * 24
        
        is_serve_v3 = decryptor._is_serve_v3_data(test_data)
        print(f"✓ ServeV3 detection test: {is_serve_v3}")
        
        # Test non-ServeV3 data
        non_serve_v3 = b'Hello World'
        is_not_serve_v3 = decryptor._is_serve_v3_data(non_serve_v3)
        print(f"✓ Non-ServeV3 detection test: {not is_not_serve_v3}")
        
        return is_serve_v3 and not is_not_serve_v3


def test_field_decryption():
    """Test field decryption with various formats."""
    
    print("\n" + "=" * 60)
    print("Testing Field Decryption")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Generate a test master key
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        master_config = generator.create_master_key_config()
        
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        decryptor = CollapseDecryptor(str(master_key_path))
        
        # Test 1: Plain text (should return as-is)
        plain_text = "https://example.com/api"
        result1 = decryptor.decrypt_field(plain_text)
        print(f"✓ Plain text test: '{plain_text}' -> '{result1}'")
        
        # Test 2: Invalid base64 (should return as-is)
        invalid_b64 = "This is not base64!"
        result2 = decryptor.decrypt_field(invalid_b64)
        print(f"✓ Invalid base64 test: '{invalid_b64}' -> '{result2}'")
        
        # Test 3: Valid base64 but not ServeV3 (should return as-is)
        valid_b64_not_serve_v3 = "SGVsbG8gV29ybGQ="  # "Hello World"
        result3 = decryptor.decrypt_field(valid_b64_not_serve_v3)
        print(f"✓ Valid base64 (non-ServeV3) test: '{valid_b64_not_serve_v3}' -> '{result3}'")
        
        return True


def test_with_known_master_key():
    """Test with the known master key from user's config."""
    
    print("\n" + "=" * 60)
    print("Testing with Known Master Key")
    print("=" * 60)
    
    # The known master key from user's config
    known_config = {
        "Key": "Q29sbGFwc2UBAAAAAAAAAGICAAAAAAAAXgIAAAAAAACPLoEwggJaAgEAAoGBAK16ClpvNiUl+FGIoZLdhUo66d6Wak8begMidGXsR7LErWHrAhblDpgwa5NJ8T02Nq+6AeGQvbQ4R85gOujHuesJA+O2d3whAXyxcZhFXVArzNUSBXUMUAgewjGhVlx6FXwYTXA+tDrVw+9bxbrAS4qGuhwx2ypPC3YFnVddDlmhAgERAoGBAKNFr2QsbzIFnmra8mwbyL5VkNF+gixWGHtruNhl6SC5G6dzxbsx755LsIqf8ht+UZZUtnnxo3xxNIYAN3Gs6zYDOoAQ3BWu4s574iIkE8ksRbs8t5l6Y0kBWhoHb5rlEC4IOamoXHRoE+OeRc6ORkrviMk6Q/nTaCsu/TS0274xAkEA16GiwrgCGvcYbgSZOBJRx4G9kioGgexLSyW62iK4EuT0Xu9xyflBDaC4yooFkxrflqEAIiEfTqNGlYeJks+5qwJBAM30GOHVovw5aN/Musb1+KVBAI9YW3haP0Isux9ND9PPVtCgujHYvhmOFLkRibYF+1GXxkHR0Y/JgY4oxcoVJeMCQHIoZTno8g5GlHZ657RF7w9Er6e75VPXcyfIrjc/jqCXVDJCh4kLfMr6vC79xrdKdl7NtMbGPcA4Uotl32vXYksCQBg61cA3QFnoou0nJQhZLE+tS1wogztV6VMUUj/q8s2f7BiLYTMKcLe2XMp6iKwAtCe3gLxzCZhyDz3mrduKBHUCQD+P74KtVxe+zfCvzsU23I1hQgtXfr91ucVkCW5lNyn1RdsDkrWXiC09l9drOwBw/rlHzfTv8XRASHqnEi1cVo8D",
        "BitSize": 128,
        "Hash": 4849480174666812961
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        master_key_path = temp_path / "known_master_key.json"
        
        with open(master_key_path, 'w') as f:
            json.dump(known_config, f, indent=2)
        
        print(f"Testing with known master key...")
        print(f"Key (first 50 chars): {known_config['Key'][:50]}...")
        
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            print("✓ Known master key loaded successfully")
            print(f"  Key size: {decryptor.private_key.key_size} bits")
            print("✓ Ready to decrypt real configuration data")
            return True
            
        except Exception as e:
            print(f"❌ Failed to load known master key: {e}")
            print("This indicates the Key field format is different than expected")
            
            # Let's analyze the key data
            import base64
            try:
                key_bytes = base64.b64decode(known_config['Key'])
                print(f"  Decoded key length: {len(key_bytes)} bytes")
                print(f"  First 16 bytes: {key_bytes[:16].hex()}")
                
                # Check if it starts with "Collapse"
                if key_bytes.startswith(b"Collapse"):
                    print("  ✓ Key starts with 'Collapse' prefix")
                else:
                    print("  ❌ Key does not start with 'Collapse' prefix")
                    
            except Exception as decode_error:
                print(f"  ❌ Failed to decode base64: {decode_error}")
            
            return False


def main():
    """Run all tests."""
    print("Testing Corrected CollapseLauncher Crypto Implementation")
    print("Based on DataCooker.cs analysis")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: Generate and load new master key
    if test_master_key_generation_and_loading():
        success_count += 1
    
    # Test 2: ServeV3 format detection
    if test_serve_v3_detection():
        success_count += 1
    
    # Test 3: Field decryption
    if test_field_decryption():
        success_count += 1
    
    # Test 4: Known master key
    if test_with_known_master_key():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 All tests passed! Implementation appears correct.")
        return True
    else:
        print("❌ Some tests failed. Implementation needs further adjustment.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
