#!/usr/bin/env python3
"""
Configuration Generator

This tool helps generate new CollapseLauncher configuration files
with properly encrypted fields.
"""

import argparse
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from crypto_tool import CollapseDecryptor


class ConfigGenerator:
    """Generator for CollapseLauncher configuration files."""
    
    def __init__(self, master_key_path: str):
        """
        Initialize the generator.
        
        Args:
            master_key_path: Path to the master key configuration file
        """
        self.decryptor = CollapseDecryptor(master_key_path)
        
        # Template for a basic configuration
        self.config_template = {
            "ProfileName": "",
            "LauncherType": "HoYoPlay",
            "GameChannel": "Stable",
            "IsExperimental": False,
            "ZoneName": "",
            "ZoneFullname": "",
            "ZoneDescription": "",
            "ZoneURL": "",
            "ZoneLogoURL": "",
            "ZonePosterURL": "",
            "InstallRegistryLocation": "",
            "DefaultGameLocation": "",
            "ConfigRegistryLocation": "",
            "FallbackLanguage": "en",
            "GameDirectoryName": "Games",
            "GameExecutableName": "",
            "GameType": "",
            "VendorType": "",
            "LauncherID": 0,
            "ChannelID": 0,
            "SubChannelID": 0,
            "GameSupportedLanguages": [],
            "GameDispatchArrayURL": [],
            "GameDispatchChannelName": "",
            "GameDispatchURLTemplate": "",
            "GameGatewayURLTemplate": "",
            "GameGatewayDefault": "",
            "GameDefaultCVLanguage": "",
            "IsHideSocMedDesc": False,
            "LauncherSpriteURLMultiLang": False,
            "LauncherSpriteURLMultiLangFallback": "en-us",
            "LauncherSpriteURL": "",
            "LauncherCPSType": "",
            "LauncherNewsURL": "",
            "LauncherResourceURL": "",
            "LauncherPluginURL": "",
            "LauncherBizName": "",
            "LauncherId": "",
            "LauncherGameId": "",
            "DispatcherKey": "",
            "IsPluginUpdateEnabled": False,
            "IsRepairEnabled": True,
            "IsCacheUpdateEnabled": True,
            "InternalGameNameFolder": "",
            "InternalGameNameInConfig": "",
            "GameDataTemplates": {},
            "ZoneSteamAssets": {},
            "ApiGeneralUserAgent": "Mozilla/5.0",
            "Hash": 0
        }
        
        # Fields that should be encrypted
        self.encrypted_fields = {
            'GameDispatchArrayURL',
            'GameDispatchChannelName', 
            'GameDispatchURLTemplate',
            'GameGatewayURLTemplate',
            'GameGatewayDefault',
            'LauncherSpriteURL',
            'LauncherCPSType',
            'LauncherNewsURL',
            'LauncherResourceURL',
            'LauncherPluginURL',
            'LauncherBizName',
            'LauncherId',
            'LauncherGameId',
            'DispatcherKey'
        }
    
    def create_config_from_template(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a configuration from template with user data.
        
        Args:
            config_data: User-provided configuration data
            
        Returns:
            Complete configuration dictionary
        """
        # Start with template
        config = self.config_template.copy()
        
        # Update with user data
        config.update(config_data)
        
        # Encrypt specified fields
        for field_name in self.encrypted_fields:
            if field_name in config and config[field_name]:
                value = config[field_name]
                
                if isinstance(value, str) and value:
                    # Encrypt single string
                    config[field_name] = self.decryptor.encrypt_field(value)
                elif isinstance(value, list) and value:
                    # Encrypt array of strings
                    encrypted_array = []
                    for item in value:
                        if isinstance(item, str) and item:
                            encrypted_array.append(self.decryptor.encrypt_field(item))
                        else:
                            encrypted_array.append(item)
                    config[field_name] = encrypted_array
        
        # Calculate hash (simple implementation)
        config_str = json.dumps(config, sort_keys=True)
        config['Hash'] = hash(config_str) & 0x7FFFFFFFFFFFFFFF  # Ensure positive
        
        return config
    
    def create_stamp_entry(self, config_name: str, game_name: str, game_region: str, 
                          config_version: str = "3.2.0") -> Dict[str, Any]:
        """
        Create a stamp entry for the configuration.
        
        Args:
            config_name: Name of the configuration file
            game_name: Game name
            game_region: Game region
            config_version: Configuration version
            
        Returns:
            Stamp entry dictionary
        """
        timestamp = int(datetime.now().strftime("%Y%m%d%H%M%S"))
        
        return {
            "LastUpdated": timestamp,
            "MetadataPath": config_name,
            "MetadataType": "PresetConfigV2",
            "MetadataInclude": True,
            "GameName": game_name,
            "GameRegion": game_region,
            "PresetConfigVersion": config_version
        }
    
    def generate_interactive_config(self) -> Dict[str, Any]:
        """
        Generate configuration interactively by asking user for input.
        
        Returns:
            Configuration dictionary
        """
        print("=== Interactive Configuration Generator ===")
        print("Enter the following information (press Enter for default):")
        print()
        
        config_data = {}
        
        # Basic information
        config_data['ProfileName'] = input("Profile Name (e.g., Hi3CN): ") or "CustomProfile"
        config_data['ZoneName'] = input("Zone Name (e.g., Mainland China): ") or "Custom Zone"
        config_data['ZoneFullname'] = input("Zone Full Name: ") or config_data['ZoneName']
        config_data['ZoneDescription'] = input("Zone Description: ") or "Custom game configuration"
        config_data['ZoneURL'] = input("Zone URL (e.g., https://example.com): ") or ""
        
        print()
        
        # Game information
        config_data['GameType'] = input("Game Type (e.g., Honkai, Genshin): ") or "Custom"
        config_data['VendorType'] = input("Vendor Type (e.g., miHoYo): ") or "Custom"
        config_data['GameExecutableName'] = input("Game Executable (e.g., Game.exe): ") or "Game.exe"
        config_data['InternalGameNameFolder'] = input("Game Folder Name: ") or "CustomGame"
        config_data['InternalGameNameInConfig'] = input("Game Config Name: ") or config_data['InternalGameNameFolder']
        
        print()
        
        # Launcher information
        launcher_id = input("Launcher ID (number): ")
        if launcher_id.isdigit():
            config_data['LauncherID'] = int(launcher_id)
        
        channel_id = input("Channel ID (number): ")
        if channel_id.isdigit():
            config_data['ChannelID'] = int(channel_id)
        
        sub_channel_id = input("Sub Channel ID (number): ")
        if sub_channel_id.isdigit():
            config_data['SubChannelID'] = int(sub_channel_id)
        
        print()
        
        # URLs (will be encrypted)
        print("=== URLs (will be encrypted) ===")
        config_data['LauncherResourceURL'] = input("Launcher Resource URL: ") or ""
        config_data['LauncherNewsURL'] = input("Launcher News URL: ") or ""
        config_data['LauncherSpriteURL'] = input("Launcher Sprite URL: ") or ""
        
        # Dispatch URLs
        dispatch_urls = []
        print("Game Dispatch URLs (enter one per line, empty line to finish):")
        while True:
            url = input("  URL: ")
            if not url:
                break
            dispatch_urls.append(url)
        
        if dispatch_urls:
            config_data['GameDispatchArrayURL'] = dispatch_urls
        
        print()
        
        # Languages
        languages = []
        print("Supported Languages (enter one per line, empty line to finish):")
        while True:
            lang = input("  Language: ")
            if not lang:
                break
            languages.append(lang)
        
        if languages:
            config_data['GameSupportedLanguages'] = languages
            config_data['GameDefaultCVLanguage'] = languages[0]
        
        return config_data
    
    def save_config_and_stamp(self, config: Dict[str, Any], output_dir: str, 
                             config_name: str, game_name: str, game_region: str) -> None:
        """
        Save configuration and stamp files.
        
        Args:
            config: Configuration dictionary
            output_dir: Output directory
            config_name: Configuration file name
            game_name: Game name for stamp
            game_region: Game region for stamp
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Save configuration file
        config_file = output_path / config_name
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✓ Configuration saved: {config_file}")
        
        # Save stamp entry
        stamp_entry = self.create_stamp_entry(config_name, game_name, game_region)
        stamp_file = output_path / f"stamp_{config_name}"
        with open(stamp_file, 'w', encoding='utf-8') as f:
            json.dump(stamp_entry, f, indent=2, ensure_ascii=False)
        
        print(f"✓ Stamp entry saved: {stamp_file}")
        
        # Save decrypted version for reference
        decrypted_file = output_path / f"decrypted_{config_name}"
        with open(decrypted_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✓ Reference copy saved: {decrypted_file}")


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="Generate CollapseLauncher configuration files"
    )
    
    parser.add_argument(
        '--master-key',
        required=True,
        help='Master key file path'
    )
    
    parser.add_argument(
        '--template',
        help='JSON template file to use as base'
    )
    
    parser.add_argument(
        '--output-dir',
        default='./generated_configs',
        help='Output directory (default: ./generated_configs)'
    )
    
    parser.add_argument(
        '--config-name',
        help='Configuration file name (e.g., config_MyGame.json)'
    )
    
    parser.add_argument(
        '--game-name',
        help='Game name for stamp entry'
    )
    
    parser.add_argument(
        '--game-region',
        help='Game region for stamp entry'
    )
    
    parser.add_argument(
        '--interactive',
        action='store_true',
        help='Use interactive mode to generate configuration'
    )
    
    args = parser.parse_args()
    
    try:
        generator = ConfigGenerator(args.master_key)
        
        if args.interactive:
            # Interactive mode
            config_data = generator.generate_interactive_config()
            config_name = args.config_name or f"config_{config_data['ProfileName']}.json"
            game_name = args.game_name or config_data.get('GameType', 'Custom Game')
            game_region = args.game_region or config_data.get('ZoneName', 'Custom Region')
        
        elif args.template:
            # Template mode
            with open(args.template, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            config_name = args.config_name or f"config_{config_data.get('ProfileName', 'Custom')}.json"
            game_name = args.game_name or config_data.get('GameType', 'Custom Game')
            game_region = args.game_region or config_data.get('ZoneName', 'Custom Region')
        
        else:
            print("Error: Either --interactive or --template must be specified")
            return 1
        
        # Generate configuration
        config = generator.create_config_from_template(config_data)
        
        # Save files
        generator.save_config_and_stamp(
            config, args.output_dir, config_name, game_name, game_region
        )
        
        print("\n" + "=" * 50)
        print("Configuration generation completed!")
        print("=" * 50)
        print(f"Output directory: {args.output_dir}")
        print(f"Configuration: {config_name}")
        print(f"Game: {game_name} - {game_region}")
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
