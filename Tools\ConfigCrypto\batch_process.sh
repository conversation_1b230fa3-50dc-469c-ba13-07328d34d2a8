#!/bin/bash
# CollapseLauncher Batch Configuration Processing Tool (Linux/macOS)
# Batch encrypt or decrypt multiple configuration files

set -e

echo "=========================================="
echo "CollapseLauncher Batch Processing Tool"
echo "=========================================="
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7+ and try again"
    exit 1
fi

# Get operation type
echo "Select operation:"
echo "1. Batch Decrypt"
echo "2. Batch Encrypt"
echo
read -p "Enter choice (1 or 2): " OPERATION

if [ "$OPERATION" = "1" ]; then
    COMMAND="batch-decrypt"
    PREFIX="decrypted_"
    echo "Selected: Batch Decrypt"
elif [ "$OPERATION" = "2" ]; then
    COMMAND="batch-encrypt"
    PREFIX="encrypted_"
    echo "Selected: Batch Encrypt"
else
    echo "Invalid choice. Please enter 1 or 2."
    exit 1
fi

echo

# Get input directory
read -p "Enter input directory path: " INPUT_DIR
if [ ! -d "$INPUT_DIR" ]; then
    echo "Error: Input directory not found: $INPUT_DIR"
    exit 1
fi

# Get output directory
read -p "Enter output directory path: " OUTPUT_DIR
if [ ! -d "$OUTPUT_DIR" ]; then
    mkdir -p "$OUTPUT_DIR"
    echo "Created output directory: $OUTPUT_DIR"
fi

# Check if master key file exists
MASTER_KEY_FILE="../MasterKeyGenerator/output/config_master.json"
if [ ! -f "$MASTER_KEY_FILE" ]; then
    read -p "Enter master key file path: " MASTER_KEY_FILE
    if [ ! -f "$MASTER_KEY_FILE" ]; then
        echo "Error: Master key file not found: $MASTER_KEY_FILE"
        exit 1
    fi
fi

echo
echo "Input Directory: $INPUT_DIR"
echo "Output Directory: $OUTPUT_DIR"
echo "Master Key: $MASTER_KEY_FILE"
echo "Operation: $COMMAND"
echo

# Check dependencies
echo "Checking dependencies..."
if ! python3 -c "import cryptography, json" &> /dev/null; then
    echo "Installing required packages..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        exit 1
    fi
fi

echo "Dependencies OK"
echo

# Count JSON files
FILE_COUNT=$(find "$INPUT_DIR" -name "*.json" -type f | wc -l)

if [ "$FILE_COUNT" -eq 0 ]; then
    echo "No JSON files found in input directory."
    exit 1
fi

echo "Found $FILE_COUNT JSON file(s) to process."
echo

# Confirm operation
read -p "Proceed with $COMMAND? (y/n): " CONFIRM
if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 1
fi

echo
echo "Starting batch processing..."
echo "=========================================="

# Execute batch operation
python3 crypto_tool.py "$COMMAND" \
    --input-dir "$INPUT_DIR" \
    --output-dir "$OUTPUT_DIR" \
    --master-key "$MASTER_KEY_FILE"

if [ $? -ne 0 ]; then
    echo
    echo "Error: Batch processing failed"
    exit 1
fi

echo
echo "=========================================="
echo "Batch processing completed successfully!"
echo "=========================================="
echo "Input: $INPUT_DIR"
echo "Output: $OUTPUT_DIR"
echo "Processed: $FILE_COUNT file(s)"
echo

# Ask if user wants to open output directory
read -p "Open output directory? (y/n): " OPEN_DIR
if [[ "$OPEN_DIR" =~ ^[Yy]$ ]]; then
    if command -v xdg-open &> /dev/null; then
        xdg-open "$OUTPUT_DIR"
    elif command -v open &> /dev/null; then
        open "$OUTPUT_DIR"
    else
        echo "Output directory: $OUTPUT_DIR"
    fi
fi
