<Project>
  <Import Project="$([MSBuild]::GetPathOfFileAbove(Directory.Build.props))" Condition="Exists('$([MSBuild]::GetPathOfFileAbove(Directory.Build.props))')" />
  
  <PropertyGroup>
    <ToolkitComponentName>ImageCropper</ToolkitComponentName>
    <Description>The ImageCropper control allows the user to freely crop an image.</Description>
    
    <!-- Rns suffix is required for namespaces shared across projects. See https://github.com/CommunityToolkit/Labs-Windows/issues/152 -->
    <RootNamespace>CommunityToolkit.WinUI.Controls.ImageCropperRns</RootNamespace>
    <PackageReadmeFile>ReadMe.md</PackageReadmeFile>
  </PropertyGroup>

  <ItemGroup>
      <PackageReference Include="Microsoft.Graphics.Win2D" Version="********"/>
  </ItemGroup>

  <!-- Sets this up as a toolkit component's source project -->
  <Import Project="$(ToolingDirectory)\ToolkitComponent.SourceProject.props" />

  <ItemGroup>
    <None Include="ReadMe.md">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
  </ItemGroup>

  <PropertyGroup>
    <PackageId>$(PackageIdPrefix).$(PackageIdVariant).Controls.$(ToolkitComponentName)</PackageId>
  </PropertyGroup>
</Project>
