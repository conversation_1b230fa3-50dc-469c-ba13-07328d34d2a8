{"profiles": {"Collapse Launcher": {"commandName": "Project", "nativeDebugging": false}, "OOBE Setup": {"commandName": "Project", "commandLineArgs": "oobesetup", "nativeDebugging": false}, "Updater": {"commandName": "Project", "commandLineArgs": "update --input \"C:\\Program Files\\Collapse Launcher\" --channel Preview", "nativeDebugging": false}, "Elevate Updater": {"commandName": "Project", "commandLineArgs": "elevateupdate --input \"H:\\Data\\test\" --channel Preview", "nativeDebugging": false}, "Reindexer": {"commandName": "Project", "commandLineArgs": "reindex --input \"\\myGit\\CollapseLauncher-ReleaseRepo\\preview\" --upver ********", "nativeDebugging": false}, "Take Ownership": {"commandName": "Project", "commandLineArgs": "takeownership --input \"H:\\Data\\Games\\miHoYoGames\"", "nativeDebugging": false}, "Migrate": {"commandName": "Project", "commandLineArgs": "migrate --input \"C:\\Program Files\\Honkai Impact 3 sea\" --output \"H:\\Games\\miHoYoGames\\Hi3SEA\"", "nativeDebugging": false}, "Hi3 Cache Updater": {"commandName": "Project", "commandLineArgs": "hi3cacheupdate", "nativeDebugging": true}, "Start on Tray": {"commandName": "Project", "commandLineArgs": "tray", "nativeDebugging": false}, "Generate Velopack Metadata": {"commandName": "Project", "commandLineArgs": "generatevelopackmetadata", "nativeDebugging": false}}}