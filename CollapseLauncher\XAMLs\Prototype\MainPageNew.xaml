﻿<Page x:Class="CollapseLauncher.Prototype.MainPageNew"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:helper="using:Hi3Helper"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      mc:Ignorable="d">
    <Page.Resources>
        <ThemeShadow x:Name="SharedShadow" />
        <!-- ReSharper disable once Xaml.RedundantResource -->
        <Thickness x:Key="NavigationViewBorderThickness">0</Thickness>
    </Page.Resources>
    <Grid x:Name="MainPageGrid">
        <!--<Grid.ChildrenTransitions>
            <PopupThemeTransition/>
        </Grid.ChildrenTransitions>-->
        <Image x:Name="BackgroundBackBuffer"
               HorizontalAlignment="Center"
               VerticalAlignment="Center"
               Opacity="1"
               Source="ms-appx:///F:\CollapseData\_img\bg\aa3a710cdd9dc3ffaeaab26d40f2cb28_3604085419409320596.png"
               Stretch="UniformToFill" />
        <NavigationView x:Name="nvSample"
                        Background="{ThemeResource NavigationBarBrush}"
                        ExpandedModeThresholdWidth="500"
                        IsBackButtonVisible="Auto"
                        IsBackEnabled="True"
                        IsSettingsVisible="True"
                        IsTabStop="False"
                        PaneClosing="nvSample_PaneClosing"
                        PaneDisplayMode="LeftCompact"
                        PaneOpening="nvSample_PaneOpening"
                        PaneTitle="Menu"
                        SelectionFollowsFocus="Disabled">
            <NavigationView.MenuItems>
                <NavigationViewItem x:Name="SamplePage1Item"
                                    Content="Menu Item1"
                                    Tag="SamplePage1">
                    <NavigationViewItem.Icon>
                        <SymbolIcon Symbol="Play" />
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
                <NavigationViewItemHeader Content="Actions" />
                <NavigationViewItem x:Name="SamplePage2Item"
                                    Content="Menu Item2"
                                    SelectsOnInvoked="True"
                                    Tag="SamplePage2">
                    <NavigationViewItem.Icon>
                        <SymbolIcon Symbol="Save" />
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
                <NavigationViewItem x:Name="SamplePage3Item"
                                    Content="Menu Item3"
                                    Tag="SamplePage3">
                    <NavigationViewItem.Icon>
                        <SymbolIcon Symbol="Refresh" />
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
            </NavigationView.MenuItems>
            <NavigationView.PaneCustomContent>
                <HyperlinkButton x:Name="PaneHyperlink"
                                 Margin="12,0"
                                 Content="More info"
                                 Visibility="Collapsed" />
            </NavigationView.PaneCustomContent>
            <NavigationView.PaneFooter>
                <StackPanel x:Name="FooterStackPanel"
                            Orientation="Vertical"
                            Visibility="Collapsed">
                    <NavigationViewItem AutomationProperties.Name="download"
                                        Icon="Download" />
                    <NavigationViewItem AutomationProperties.Name="favorite"
                                        Icon="Favorite" />
                </StackPanel>
            </NavigationView.PaneFooter>
            <Grid HorizontalAlignment="Left">
                <!-- ReSharper disable once Xaml.PathError -->
                <Image x:Name="BackgroundBack"
                       Margin="-2,0,0,0"
                       HorizontalAlignment="Left"
                       VerticalAlignment="Center"
                       Opacity="1"
                       Source="ms-appx:///F:/CollapseData/_img/bg/aa3a710cdd9dc3ffaeaab26d40f2cb28_3604085419409320596.png"
                       Stretch="UniformToFill" />
                <Frame x:Name="contentFrame" />
            </Grid>
        </NavigationView>
        <Grid Height="48"
              VerticalAlignment="Top">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition />
                <ColumnDefinition Width="32" />
                <ColumnDefinition Width="104" />
            </Grid.ColumnDefinitions>
            <Button Name="GridBG_Icon"
                    Grid.Column="0"
                    Height="33"
                    Margin="58,8,0,0"
                    VerticalAlignment="Top"
                    Style="{ThemeResource TransparentDefaultButtonStyle}">
                <!--<Button.Transitions>
                    <RepositionThemeTransition/>
                </Button.Transitions>-->
                <!--<Button.ContentTransitions>
                    <RepositionThemeTransition/>
                </Button.ContentTransitions>-->
                <StackPanel Margin="0,0,0,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Orientation="Horizontal">
                    <!--<StackPanel.ChildrenTransitions>
                        <RepositionThemeTransition/>
                    </StackPanel.ChildrenTransitions>-->
                    <Image x:Name="GridBG_IconImg"
                           Width="24"
                           Height="24"
                           Margin="0,-1,0,0"
                           Opacity="1"
                           Source="ms-appx:///Assets/CollapseLauncherLogo.png" />
                    <StackPanel Name="GridBG_IconTitle"
                                Width="0"
                                Margin="8,-8,0,0"
                                VerticalAlignment="Center">
                        <StackPanel.OpacityTransition>
                            <ScalarTransition />
                        </StackPanel.OpacityTransition>
                        <TextBlock Margin="-6,0,0,0"
                                   HorizontalAlignment="Left"
                                   FontSize="10"
                                   FontWeight="Medium"
                                   Opacity="0.8"
                                   Text="Collapse"
                                   Visibility="Visible" />
                        <TextBlock Margin="0,-3,0,-8"
                                   HorizontalAlignment="Left"
                                   FontSize="12"
                                   FontWeight="Bold"
                                   Foreground="{ThemeResource AccentColor}"
                                   Opacity="0.8"
                                   Text="Launcher"
                                   Visibility="Visible" />
                    </StackPanel>
                    <StackPanel x:Name="PreviewBuildIndicator"
                                Margin="-16,-12,0,0"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Background="{ThemeResource AccentColor}"
                                CornerRadius="3"
                                Visibility="Visible">
                        <TextBlock Padding="3,0,3,0"
                                   VerticalAlignment="Center"
                                   FontSize="8"
                                   FontWeight="Bold"
                                   Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                                   Text="PRE" />
                    </StackPanel>
                </StackPanel>
            </Button>
        </Grid>
        <Grid x:Name="NotificationLostFocusBackground"
              VerticalAlignment="Stretch"
              Opacity="0"
              PointerPressed="NotificationContainerBackground_PointerPressed"
              Visibility="Collapsed">
            <Grid.OpacityTransition>
                <ScalarTransition />
            </Grid.OpacityTransition>
            <Grid Background="{ThemeResource NotificationLostFocusBackgroundGradientBrush}"
                  Opacity="0.00000001" />
            <Grid Margin="0,48,0,0"
                  Background="{ThemeResource NotificationLostFocusBackgroundGradientBrush}" />
        </Grid>
        <ScrollViewer x:Name="NotificationPanel"
                      Margin="0,48,0,0"
                      HorizontalAlignment="Right"
                      Background="{ThemeResource NotificationPanelBrush}"
                      CornerRadius="0,0,0,16"
                      HorizontalScrollBarVisibility="Disabled"
                      Shadow="{ThemeResource SharedShadow}">
            <!--<ScrollViewer.Transitions>
                <RepositionThemeTransition/>
            </ScrollViewer.Transitions>-->
            <Grid MinWidth="608"
                  MinHeight="200">
                <StackPanel x:Name="NoNotificationIndicator"
                            Margin="0,-47,0,0"
                            VerticalAlignment="Center">
                    <StackPanel.OpacityTransition>
                        <ScalarTransition />
                    </StackPanel.OpacityTransition>
                    <Image Width="200"
                           Margin="0,0,0,16"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Opacity="0.5"
                           Source="ms-appx:///Assets/Images/GameMascot/PaimonSleep-MonoTransparent.png" />
                    <TextBlock FontSize="20"
                               FontWeight="Medium"
                               Opacity="0.3"
                               Text="{x:Bind helper: Locale.Lang._MainPage.NotifNoNewNotifs}"
                               TextAlignment="Center" />
                </StackPanel>
                <StackPanel x:Name="NotificationContainer"
                            Margin="0,0,0,7">
                    <StackPanel.ChildrenTransitions>
                        <TransitionCollection>
                            <PopupThemeTransition />
                            <RepositionThemeTransition />
                        </TransitionCollection>
                    </StackPanel.ChildrenTransitions>
                </StackPanel>
            </Grid>
        </ScrollViewer>
    </Grid>
</Page>
