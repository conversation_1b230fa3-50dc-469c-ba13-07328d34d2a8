﻿<Application x:Class="CollapseLauncher.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:animatedvisuals="using:Microsoft.UI.Xaml.Controls.AnimatedVisuals"
             xmlns:controls="using:Microsoft.UI.Xaml.Controls"
             xmlns:conv="using:CollapseLauncher.Pages"
             xmlns:interactivity="using:Microsoft.Xaml.Interactivity"
             xmlns:primitives="using:Microsoft.UI.Xaml.Controls.Primitives"
             xmlns:ui="using:Windows.UI">
    <!--  ReSharper disable Xaml.InvalidResourceType  -->
    <!--  ReSharper disable Xaml.ConstructorWarning  -->
    <!--  ReSharper disable Xaml.StaticResourceNotResolved  -->
    <!--  ReSharper disable Xaml.BindingWithContextNotResolved  -->
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
                <!--  Custom controls styles  -->
                <ResourceDictionary Source="ms-appx:///Hi3Helper.CommunityToolkit.WinUI.Controls.ImageCropper/Themes/Generic.xaml" />
                <ResourceDictionary Source="ms-appx:///Hi3Helper.CommunityToolkit.WinUI.Controls.SettingsControls/Themes/Generic.xaml" />

                <ResourceDictionary Source="ms-appx:///XAMLs/Theme/ContentDialogCollapse.xaml" />
                <ResourceDictionary Source="ms-appx:///XAMLs/Theme/ContentDialogOverlay.xaml" />
                <ResourceDictionary Source="ms-appx:///XAMLs/Theme/FlipView.xaml" />
                <ResourceDictionary Source="ms-appx:///XAMLs/Theme/RadioButton.xaml" />
                <ResourceDictionary Source="ms-appx:///XAMLs/Theme/Slider.xaml" />
                <ResourceDictionary Source="ms-appx:///XAMLs/Theme/NavigationView_ThemeResource.xaml" />
                <ResourceDictionary Source="ms-appx:///XAMLs/Theme/CustomControls/ImageEx/ImageEx.xaml" />
                <ResourceDictionary Source="ms-appx:///XAMLs/Theme/CustomControls/CommunityToolkit.Labs/DataTable/DataColumn.xaml" />
                <ResourceDictionary Source="ms-appx:///XAMLs/Theme/CustomControls/CommunityToolkit.Labs/MarkdownTextBlock/MarkdownTextBlock.xaml" />
                <ResourceDictionary Source="ms-appx:///XAMLs/Theme/CustomControls/UserFeedbackDialog/UserFeedbackDialog.xaml" />
                <ResourceDictionary>
                    <ResourceDictionary.ThemeDictionaries>
                        <ResourceDictionary x:Key="Default">
                            <!--  For dark theme  -->
                            <x:String x:Key="AppLogo">ms-appx:///Assets/CollapseLauncherLogo.png</x:String>
                            <x:String x:Key="AppLogoOutline">ms-appx:///Assets/CollapseLauncherLogoOutlineLight.png</x:String>
                            <!--  Base accent color  -->
                            <ui:Color x:Key="TemplateAccentColor">#ffd52a</ui:Color>
                            <ui:Color x:Key="SystemAccentColor">#ffd52a</ui:Color>
                            <ui:Color x:Key="SystemAccentColorLight1">#ffd52a</ui:Color>
                            <ui:Color x:Key="SystemAccentColorLight2">#ffd52a</ui:Color>
                            <ui:Color x:Key="SystemAccentColorLight3">#ffd52a</ui:Color>
                            <SolidColorBrush x:Key="SolidColorBrushForegroundTransparent"
                                             Color="#00FFFFFF" />
                            <SolidColorBrush x:Key="SolidColorBrushBackgroundTransparent"
                                             Color="#00222222" />
                            <SolidColorBrush x:Key="SolidColorBrushBackgroundHover"
                                             Color="#55222222" />
                            <SolidColorBrush x:Key="SolidColorBrushBackgroundPressed"
                                             Color="#77222222" />
                            <SolidColorBrush x:Key="SolidColorBrushBackgroundDisabled"
                                             Color="#11222222" />
                            <AcrylicBrush x:Key="AccentColorBrush"
                                          FallbackColor="{ThemeResource SystemAccentColor}"
                                          Opacity="0.8"
                                          TintColor="{ThemeResource SystemAccentColor}"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0.2" />
                            <SolidColorBrush x:Key="AccentColor"
                                             Color="{ThemeResource SystemAccentColorLight2}" />
                            <SolidColorBrush x:Key="DefaultBGColorAccentBrush"
                                             Color="#FFFFFF" />
                            <SolidColorBrush x:Key="DefaultFGColorAccentBrush"
                                             Color="#000000" />
                            <SolidColorBrush x:Key="WindowBackground"
                                             Color="#242424" />
                            <!--  TitleBar brushes  -->
                            <LinearGradientBrush x:Key="BackgroundOverlayTitleBarBrush" StartPoint="0,1" EndPoint="0,0">
                                <GradientStop Offset="0" Color="#00000000" />
                                <GradientStop Offset="1" Color="#66000000" />
                            </LinearGradientBrush>
                            <!--  Dialog brushes  -->
                            <ui:Color x:Key="DialogTitleColor">#ffd52a</ui:Color>
                            <SolidColorBrush x:Key="DialogTitleBrush"
                                             Color="{ThemeResource DialogTitleColor}" />
                            <!--  Notification brushes  -->
                            <AcrylicBrush x:Key="NotificationPanelBrush"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.5"
                                          TintOpacity="0.0" />
                            <LinearGradientBrush x:Key="NotificationLostFocusBackgroundGradientBrush" StartPoint="0,0" EndPoint="1,0">
                                <GradientStop Offset="0" Color="#7F000000" />
                                <GradientStop Offset="0.6" Color="#FF000000" />
                            </LinearGradientBrush>
                            <AcrylicBrush x:Key="InfoBarInformationalSeverityBackgroundBrush"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.5"
                                          TintOpacity="0.0" />
                            <!--  Page brushes  -->
                            <AcrylicBrush x:Key="UnhandledErrorPageBrush"
                                          TintColor="#000000"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="GameSettingsApplyGridBrush"
                                          FallbackColor="#EE222222"
                                          TintColor="#666666"
                                          TintLuminosityOpacity="0.1"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="PostAcrylicBrush"
                                          FallbackColor="#EE303030"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="BackgroundImageMaskAcrylicBrush"
                                          FallbackColor="#EE222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.7"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="PageBackgroundAcrylicBrush"
                                          FallbackColor="#E1222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.00"
                                          TintOpacity="1" />
                            <AcrylicBrush x:Key="UpdatePageAcrylicBrush"
                                          FallbackColor="#FF1F1F1F"
                                          TintColor="#111111"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0.6" />
                            <AcrylicBrush x:Key="SocMedPanelAcrylicBrush"
                                          FallbackColor="#E1222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.25"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="WebView2GridBackground"
                                          FallbackColor="#EE1D1D1D"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0.75" />
                            <!--  Controls element brush  -->
                            <AcrylicBrush x:Key="AudioLanguageSelectionRadioButtonBrush"
                                          FallbackColor="#EE303030"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="ProgressBackgroundAcrylicBrush"
                                          FallbackColor="#AA222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.2"
                                          TintOpacity="0.2" />
                            <AcrylicBrush x:Key="DialogAcrylicBrush"
                                          FallbackColor="#FF222222"
                                          TintColor="#FF222222"
                                          TintLuminosityOpacity="1"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="InfoBarAnnouncementBrush"
                                          FallbackColor="#333333"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="CarouselPipsAcrylicBrush"
                                          FallbackColor="#EE333333"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="WindowTrayBrush"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.9"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="GameSettingsBtnBrush"
                                          FallbackColor="#EE222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.65"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="ButtonDisabledBrush"
                                          FallbackColor="#EE222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.65"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="LoadingPopupGridBrush"
                                          FallbackColor="{ThemeResource SystemAccentColorLight1}"
                                          TintColor="{ThemeResource SystemAccentColorLight1}"
                                          TintOpacity="1" />

                            <!--  For Navigation View  -->
                            <AcrylicBrush x:Key="NavigationViewContentBackground"
                                          FallbackColor="Transparent"
                                          Opacity="0"
                                          TintColor="Transparent"
                                          TintLuminosityOpacity="0"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="NavigationViewDefaultPaneBackground"
                                          FallbackColor="#AA222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.5"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="NavigationViewUnfoldedPaneBackground"
                                          FallbackColor="#AA222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.5"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="NavigationViewUnfoldedTransparentPaneBackground"
                                          FallbackColor="#AA222222"
                                          Opacity="0"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.5"
                                          TintOpacity="0.0" />
                            <LinearGradientBrush x:Key="NavigationViewExpandedPaneBackground" StartPoint="0,0" EndPoint="0.15,0">
                                <GradientStop Offset="0" Color="#40000000" />
                                <GradientStop Offset="1" Color="#00000000" />
                            </LinearGradientBrush>
                            <SolidColorBrush x:Key="NavigationViewButtonBackground"
                                             Color="#00000000" />
                            <SolidColorBrush x:Key="NavigationViewButtonBackgroundPointerOver"
                                             Color="#22000000" />
                            <SolidColorBrush x:Key="NavigationViewButtonBackgroundPressed"
                                             Color="#11000000" />
                            <SolidColorBrush x:Key="NavigationViewButtonBackgroundDisabled"
                                             Color="#0A000000" />
                            <SolidColorBrush x:Key="NavigationViewItemBackground"
                                             Color="#00000000" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundPointerOver"
                                             Color="#22000000" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundPressed"
                                             Color="#11000000" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundDisabled"
                                             Color="#0A000000" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundSelected"
                                             Color="#44000000" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundSelectedPointerOver"
                                             Color="#55000000" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundSelectedPressed"
                                             Color="#33000000" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundSelectedDisabled"
                                             Color="#22000000" />


                            <LinearGradientBrush x:Key="LoadingGradientBG" StartPoint="0.5,0" EndPoint="0.5,1">
                                <GradientStop Offset="0.0" Color="#22000000" />
                                <GradientStop Offset="1" Color="#60000000" />
                            </LinearGradientBrush>
                            <!--  For Acrylic Toggle Button  -->
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackground"
                                          FallbackColor="#AA222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.4"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundPointerOver"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.6"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundPressed"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundDisabled"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.20"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundChecked"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundCheckedPointerOver"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundCheckedPressed"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundCheckedDisabled"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundIndeterminate"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundIndeterminatePointerOver"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundIndeterminatePressed"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundIndeterminateDisabled"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <!--  For Acrylic ComboBox  -->
                            <AcrylicBrush x:Key="AcrylicComboBoxBackground"
                                          FallbackColor="#88222222"
                                          TintColor="#AA222222"
                                          TintLuminosityOpacity="0.60"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicComboBoxBackgroundFocused"
                                          FallbackColor="#BB222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.50"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicComboBoxBackgroundPointerOver"
                                          FallbackColor="#BB222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.50"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicComboBoxBackgroundPressed"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.40"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicComboBoxBackgroundDisabled"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.30"
                                          TintOpacity="0.0" />
                            <!--  For Acrylic Button  -->
                            <AcrylicBrush x:Key="NewAccentButtonBackgroundDisabled"
                                          FallbackColor="#77222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.3"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="NewAccentButtonBackgroundPointerOver"
                                          FallbackColor="{ThemeResource SystemAccentColor}"
                                          Opacity="0.9"
                                          TintColor="{ThemeResource SystemAccentColor}" />
                            <AcrylicBrush x:Key="NewAccentButtonBackgroundPressed"
                                          FallbackColor="{ThemeResource SystemAccentColor}"
                                          Opacity="0.75"
                                          TintColor="{ThemeResource SystemAccentColor}" />
                            <AcrylicBrush x:Key="NewAccentButtonBackground"
                                          FallbackColor="{ThemeResource SystemAccentColor}"
                                          TintColor="{ThemeResource SystemAccentColor}" />
                            <SolidColorBrush x:Key="NewAccentButtonForegroundDisabled"
                                             Opacity="0.75"
                                             Color="#DDFFFFFF" />
                            <SolidColorBrush x:Key="NewAccentButtonForegroundPointerOver"
                                             Color="#EE000000" />
                            <SolidColorBrush x:Key="NewAccentButtonForegroundPressed"
                                             Color="#CC000000" />
                            <SolidColorBrush x:Key="NewAccentButtonForeground"
                                             Color="#FF111111" />
                            <AcrylicBrush x:Key="AcrylicButtonBackground"
                                          FallbackColor="#88222222"
                                          TintColor="#AA222222"
                                          TintLuminosityOpacity="0.3"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="AcrylicSemiButtonBackground"
                                          FallbackColor="#88222222"
                                          TintColor="#AA222222"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="AcrylicButtonBackgroundFocused"
                                          FallbackColor="#BB222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.50"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicButtonBackgroundPointerOver"
                                          FallbackColor="#BB222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.50"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicButtonBackgroundPressed"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.40"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicButtonBackgroundDisabled"
                                          FallbackColor="#DD222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.30"
                                          TintOpacity="0.0" />
                            <!--  Window Caption Button Brush  -->
                            <AcrylicBrush x:Key="WindowCaptionBackgroundAcrylicLight"
                                          FallbackColor="#C0222222"
                                          TintColor="#222222"
                                          TintLuminosityOpacity="0.5"
                                          TintOpacity="0.0" />
                            <SolidColorBrush x:Key="WindowCaptionBackground"
                                             Color="#00000000" />
                            <SolidColorBrush x:Key="WindowCaptionBackgroundDisabled"
                                             Color="#00000000" />
                            <SolidColorBrush x:Key="WindowCaptionButtonBackgroundPointerOver"
                                             Color="#60000000" />
                            <SolidColorBrush x:Key="WindowCaptionButtonBackgroundPressed"
                                             Color="#22222222" />
                            <SolidColorBrush x:Key="WindowCaptionBackgroundClose"
                                             Color="#00E00000" />
                            <SolidColorBrush x:Key="WindowCaptionBackgroundClosePointerOver"
                                             Color="#FFE00000" />
                            <SolidColorBrush x:Key="WindowCaptionBackgroundClosePressed"
                                             Color="#A0B00000" />
                        </ResourceDictionary>
                        <ResourceDictionary x:Key="Light">
                            <!--  For light theme  -->
                            <x:String x:Key="AppLogo">ms-appx:///Assets/CollapseLauncherLogo.png</x:String>
                            <x:String x:Key="AppLogoOutline">ms-appx:///Assets/CollapseLauncherLogoOutlineDark.png</x:String>
                            <!--  Base accent color  -->
                            <ui:Color x:Key="TemplateAccentColor">#693758</ui:Color>
                            <ui:Color x:Key="SystemAccentColor">#693758</ui:Color>
                            <ui:Color x:Key="SystemAccentColorDark1">#693758</ui:Color>
                            <ui:Color x:Key="SystemAccentColorDark2">#693758</ui:Color>
                            <ui:Color x:Key="SystemAccentColorDark3">#693758</ui:Color>
                            <SolidColorBrush x:Key="SolidColorBrushForegroundTransparent"
                                             Color="#00000000" />
                            <SolidColorBrush x:Key="SolidColorBrushBackgroundTransparent"
                                             Color="#00FFFFFF" />
                            <SolidColorBrush x:Key="SolidColorBrushBackgroundHover"
                                             Color="#55FFFFFF" />
                            <SolidColorBrush x:Key="SolidColorBrushBackgroundPressed"
                                             Color="#77FFFFFF" />
                            <SolidColorBrush x:Key="SolidColorBrushBackgroundDisabled"
                                             Color="#11FFFFFF" />
                            <AcrylicBrush x:Key="AccentColorBrush"
                                          FallbackColor="{ThemeResource SystemAccentColor}"
                                          Opacity="0.8"
                                          TintColor="{ThemeResource SystemAccentColor}"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0.2" />
                            <SolidColorBrush x:Key="AccentColor"
                                             Color="{ThemeResource SystemAccentColor}" />
                            <SolidColorBrush x:Key="DefaultBGColorAccentBrush"
                                             Color="#000000" />
                            <SolidColorBrush x:Key="DefaultFGColorAccentBrush"
                                             Color="#FFFFFF" />
                            <SolidColorBrush x:Key="WindowBackground"
                                             Color="#ECECEC" />
                            <!--  TitleBar brushes  -->
                            <LinearGradientBrush x:Key="BackgroundOverlayTitleBarBrush" StartPoint="0,1" EndPoint="0,0">
                                <GradientStop Offset="0" Color="#00FFFFFF" />
                                <GradientStop Offset="1" Color="#44FFFFFF" />
                            </LinearGradientBrush>
                            <!--  Dialog brushes  -->
                            <ui:Color x:Key="DialogTitleColor">#693758</ui:Color>
                            <SolidColorBrush x:Key="DialogTitleBrush"
                                             Color="{ThemeResource DialogTitleColor}" />
                            <!--  Notification brushes  -->
                            <AcrylicBrush x:Key="NotificationPanelBrush"
                                          FallbackColor="#E0FFFFFF"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0.0" />
                            <LinearGradientBrush x:Key="NotificationLostFocusBackgroundGradientBrush" StartPoint="0,0" EndPoint="1,0">
                                <GradientStop Offset="0" Color="#7FFFFFFF" />
                                <GradientStop Offset="0.6" Color="#FFFFFFFF" />
                            </LinearGradientBrush>
                            <AcrylicBrush x:Key="InfoBarInformationalSeverityBackgroundBrush"
                                          FallbackColor="#E0FFFFFF"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0.0" />
                            <!--  Page brushes  -->
                            <AcrylicBrush x:Key="UnhandledErrorPageBrush"
                                          TintColor="#FFFFFF"
                                          TintOpacity="1" />
                            <AcrylicBrush x:Key="GameSettingsApplyGridBrush"
                                          FallbackColor="#EEFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.0"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="PostAcrylicBrush"
                                          FallbackColor="#CCFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="BackgroundImageMaskAcrylicBrush"
                                          FallbackColor="#E0EEEEEE"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.9"
                                          TintOpacity="0.4" />
                            <AcrylicBrush x:Key="PageBackgroundAcrylicBrush"
                                          FallbackColor="#FFEEEEEE"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.0"
                                          TintOpacity="0.25" />
                            <AcrylicBrush x:Key="UpdatePageAcrylicBrush"
                                          FallbackColor="#FFEAEAEA"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0.25" />
                            <AcrylicBrush x:Key="SocMedPanelAcrylicBrush"
                                          FallbackColor="#CCEEEEEE"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.6"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="WebView2GridBackground"
                                          FallbackColor="#EEEAEAEA"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.5"
                                          TintOpacity="0" />
                            <!--  Controls element brush  -->
                            <AcrylicBrush x:Key="AudioLanguageSelectionRadioButtonBrush"
                                          FallbackColor="#EEEEEEEE"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="ProgressBackgroundAcrylicBrush"
                                          FallbackColor="#CCFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintOpacity="0.75" />
                            <AcrylicBrush x:Key="DialogAcrylicBrush"
                                          FallbackColor="#FFEAEAEA"
                                          TintColor="#FFFFFF"
                                          TintOpacity="0.7" />
                            <AcrylicBrush x:Key="InfoBarAnnouncementBrush"
                                          FallbackColor="#FFFFFF"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="CarouselPipsAcrylicBrush"
                                          FallbackColor="#DDFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="WindowTrayBrush"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.2"
                                          TintOpacity="0" />
                            <AcrylicBrush x:Key="GameSettingsBtnBrush"
                                          FallbackColor="#AAFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="ButtonDisabledBrush"
                                          FallbackColor="#AAFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.75"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="LoadingPopupGridBrush"
                                          FallbackColor="{ThemeResource SystemAccentColorDark1}"
                                          TintColor="{ThemeResource SystemAccentColorDark1}"
                                          TintOpacity="1" />
                            <!--  Navigation View Styles  -->
                            <AcrylicBrush x:Key="NavigationViewContentBackground"
                                          FallbackColor="Transparent"
                                          Opacity="0"
                                          TintColor="Transparent"
                                          TintLuminosityOpacity="0"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="NavigationViewDefaultPaneBackground"
                                          FallbackColor="#FFEEEEEE"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.9"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="NavigationViewUnfoldedPaneBackground"
                                          FallbackColor="#D0EEEEEE"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.9"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="NavigationViewUnfoldedTransparentPaneBackground"
                                          FallbackColor="#D0EEEEEE"
                                          Opacity="0"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.6"
                                          TintOpacity="0.0" />
                            <LinearGradientBrush x:Key="NavigationViewExpandedPaneBackground" StartPoint="0,0" EndPoint="0.15,0">
                                <GradientStop Offset="0" Color="#44FFFFFF" />
                                <GradientStop Offset="1" Color="#00FFFFFF" />
                            </LinearGradientBrush>
                            <SolidColorBrush x:Key="NavigationViewButtonBackground"
                                             Color="#00FFFFFF" />
                            <SolidColorBrush x:Key="NavigationViewButtonBackgroundPointerOver"
                                             Color="#66FFFFFF" />
                            <SolidColorBrush x:Key="NavigationViewButtonBackgroundPressed"
                                             Color="#44FFFFFF" />
                            <SolidColorBrush x:Key="NavigationViewButtonBackgroundDisabled"
                                             Color="#22FFFFFF" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundPointerOver"
                                             Color="#66FFFFFF" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundPressed"
                                             Color="#44FFFFFF" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundDisabled"
                                             Color="#22FFFFFF" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundSelected"
                                             Color="#88FFFFFF" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundSelectedPointerOver"
                                             Color="#55FFFFFF" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundSelectedPressed"
                                             Color="#44FFFFFF" />
                            <SolidColorBrush x:Key="NavigationViewItemBackgroundSelectedDisabled"
                                             Color="#22FFFFFF" />

                            <LinearGradientBrush x:Key="LoadingGradientBG" StartPoint="0.5,0" EndPoint="0.5,1">
                                <GradientStop Offset="0.50" Color="#00FFFFFF" />
                                <GradientStop Offset="1" Color="#A0FFFFFF" />
                            </LinearGradientBrush>
                            <!--  For Acrylic Toggle Button  -->
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackground"
                                          FallbackColor="#A0FFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundPointerOver"
                                          FallbackColor="#B0FFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.60"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundPressed"
                                          FallbackColor="#D0FFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.40"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundDisabled"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.40"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundChecked"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.60"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundCheckedPointerOver"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.40"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundCheckedPressed"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.40"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundCheckedDisabled"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.40"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundIndeterminate"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundIndeterminatePointerOver"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundIndeterminatePressed"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicToggleButtonBackgroundIndeterminateDisabled"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <!--  For Acrylic ComboBox  -->
                            <AcrylicBrush x:Key="AcrylicComboBoxBackground"
                                          FallbackColor="#E0FFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicComboBoxBackgroundFocused"
                                          FallbackColor="#D0FFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.6"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicComboBoxBackgroundPointerOver"
                                          FallbackColor="#D0FFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.6"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicComboBoxBackgroundPressed"
                                          FallbackColor="#C0FFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.50"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicComboBoxBackgroundDisabled"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.40"
                                          TintOpacity="0.0" />
                            <!--  For Acrylic Button  -->
                            <AcrylicBrush x:Key="NewAccentButtonBackgroundDisabled"
                                          FallbackColor="#88FFFFFF"
                                          TintColor="#88FFFFFF" />
                            <AcrylicBrush x:Key="NewAccentButtonBackgroundPointerOver"
                                          FallbackColor="{ThemeResource SystemAccentColor}"
                                          Opacity="0.9"
                                          TintColor="{ThemeResource SystemAccentColor}" />
                            <AcrylicBrush x:Key="NewAccentButtonBackgroundPressed"
                                          FallbackColor="{ThemeResource SystemAccentColor}"
                                          Opacity="0.8"
                                          TintColor="{ThemeResource SystemAccentColor}" />
                            <AcrylicBrush x:Key="NewAccentButtonBackground"
                                          FallbackColor="{ThemeResource SystemAccentColor}"
                                          TintColor="{ThemeResource SystemAccentColor}" />
                            <SolidColorBrush x:Key="NewAccentButtonForegroundDisabled"
                                             Color="#88000000" />
                            <SolidColorBrush x:Key="NewAccentButtonForegroundPointerOver"
                                             Color="#DDFFFFFF" />
                            <SolidColorBrush x:Key="NewAccentButtonForegroundPressed"
                                             Color="#CCFFFFFF" />
                            <SolidColorBrush x:Key="NewAccentButtonForeground"
                                             Color="#FFFFFF" />
                            <AcrylicBrush x:Key="AcrylicButtonBackground"
                                          FallbackColor="#D0EEEEEE"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicSemiButtonBackground"
                                          FallbackColor="#D0EEEEEE"
                                          TintColor="#EEEEEE"
                                          TintLuminosityOpacity="0.8"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicButtonBackgroundFocused"
                                          FallbackColor="#EEEEEE"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.70"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicButtonBackgroundPointerOver"
                                          FallbackColor="#FFEEEEEE"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.9"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicButtonBackgroundPressed"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.60"
                                          TintOpacity="0.0" />
                            <AcrylicBrush x:Key="AcrylicButtonBackgroundDisabled"
                                          FallbackColor="#FFFFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.80"
                                          TintOpacity="0.0" />
                            <!--  Window Caption Button Brush  -->
                            <AcrylicBrush x:Key="WindowCaptionBackgroundAcrylicLight"
                                          FallbackColor="#88FFFFFF"
                                          TintColor="#FFFFFF"
                                          TintLuminosityOpacity="0.5"
                                          TintOpacity="0.0" />
                            <SolidColorBrush x:Key="WindowCaptionBackground"
                                             Color="#00FFFFFF" />
                            <SolidColorBrush x:Key="WindowCaptionBackgroundDisabled"
                                             Color="#00FFFFFF" />
                            <SolidColorBrush x:Key="WindowCaptionButtonBackgroundPointerOver"
                                             Color="#A0FFFFFF" />
                            <SolidColorBrush x:Key="WindowCaptionButtonBackgroundPressed"
                                             Color="#40FFFFFF" />
                            <SolidColorBrush x:Key="WindowCaptionBackgroundClose"
                                             Color="#00FF6666" />
                            <SolidColorBrush x:Key="WindowCaptionBackgroundClosePointerOver"
                                             Color="#FFFF0000" />
                            <SolidColorBrush x:Key="WindowCaptionBackgroundClosePressed"
                                             Color="#90FF2222" />
                        </ResourceDictionary>
                    </ResourceDictionary.ThemeDictionaries>
                    <!--  Navigation title-bar border thickness  -->
                    <Thickness x:Key="NavigationViewContentMargin">0,47,0,0</Thickness>

                    <!--  START: ToggleButton styles  -->
                    <Style x:Key="AcrylicToggleButtonStyle"
                           TargetType="ToggleButton">
                        <Setter Property="Background" Value="{ThemeResource AcrylicToggleButtonBackground}" />
                        <Setter Property="BackgroundSizing" Value="InnerBorderEdge" />
                        <Setter Property="Foreground" Value="{ThemeResource ToggleButtonForeground}" />
                        <Setter Property="BorderBrush" Value="{ThemeResource ToggleButtonBorderBrush}" />
                        <Setter Property="BorderThickness" Value="{ThemeResource ToggleButtonBorderThemeThickness}" />
                        <Setter Property="Padding" Value="{StaticResource ButtonPadding}" />
                        <Setter Property="HorizontalAlignment" Value="Left" />
                        <Setter Property="VerticalAlignment" Value="Center" />
                        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
                        <Setter Property="FontWeight" Value="Normal" />
                        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
                        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
                        <Setter Property="FocusVisualMargin" Value="-3" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ToggleButton">
                                    <ContentPresenter x:Name="ContentPresenter"
                                                      Padding="{TemplateBinding Padding}"
                                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      AutomationProperties.AccessibilityView="Raw"
                                                      Background="{TemplateBinding Background}"
                                                      BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                                      BorderBrush="{TemplateBinding BorderBrush}"
                                                      BorderThickness="{TemplateBinding BorderThickness}"
                                                      Content="{TemplateBinding Content}"
                                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                                      ContentTransitions="{TemplateBinding ContentTransitions}"
                                                      CornerRadius="{TemplateBinding CornerRadius}">
                                        <ContentPresenter.BackgroundTransition>
                                            <BrushTransition Duration="0:0:0.083" />
                                        </ContentPresenter.BackgroundTransition>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicToggleButtonBackgroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicToggleButtonBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Disabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicToggleButtonBackgroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Checked">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBackgroundChecked}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundChecked}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushChecked}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BackgroundSizing">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="CheckedPointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBackgroundCheckedPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushCheckedPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundCheckedPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BackgroundSizing">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="CheckedPressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicToggleButtonBackgroundCheckedPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundCheckedPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushCheckedPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BackgroundSizing">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonCheckedStateBackgroundSizing}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="CheckedDisabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicToggleButtonBackgroundCheckedDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundCheckedDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushCheckedDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Indeterminate">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicToggleButtonBackgroundIndeterminate}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundIndeterminate}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushIndeterminate}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="IndeterminatePointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicToggleButtonBackgroundIndeterminatePointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushIndeterminatePointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundIndeterminatePointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="IndeterminatePressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicToggleButtonBackgroundIndeterminatePressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushIndeterminatePressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundIndeterminatePressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="IndeterminateDisabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicToggleButtonBackgroundIndeterminateDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonForegroundIndeterminateDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ToggleButtonBorderBrushIndeterminateDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                    </ContentPresenter>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <!--  START: Button styles  -->
                    <Style x:Key="DefaultButtonStyle"
                           TargetType="Button">
                        <Setter Property="Background" Value="{ThemeResource ButtonBackground}" />
                        <Setter Property="BackgroundSizing" Value="InnerBorderEdge" />
                        <Setter Property="Foreground" Value="{ThemeResource ButtonForeground}" />
                        <Setter Property="BorderBrush" Value="{ThemeResource ButtonBorderBrush}" />
                        <Setter Property="BorderThickness" Value="{ThemeResource ButtonBorderThemeThickness}" />
                        <Setter Property="Padding" Value="{StaticResource ButtonPadding}" />
                        <Setter Property="HorizontalAlignment" Value="Left" />
                        <Setter Property="VerticalAlignment" Value="Center" />
                        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
                        <Setter Property="FontWeight" Value="Normal" />
                        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
                        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
                        <Setter Property="FocusVisualMargin" Value="-3" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <ContentPresenter x:Name="ContentPresenter"
                                                      Padding="{TemplateBinding Padding}"
                                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      controls:AnimatedIcon.State="Normal"
                                                      AutomationProperties.AccessibilityView="Raw"
                                                      Background="{TemplateBinding Background}"
                                                      BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                                      BorderBrush="{TemplateBinding BorderBrush}"
                                                      BorderThickness="{TemplateBinding BorderThickness}"
                                                      Content="{TemplateBinding Content}"
                                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                                      ContentTransitions="{TemplateBinding ContentTransitions}"
                                                      CornerRadius="{TemplateBinding CornerRadius}">
                                        <ContentPresenter.BackgroundTransition>
                                            <BrushTransition Duration="0:0:0.083" />
                                        </ContentPresenter.BackgroundTransition>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBackgroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="PointerOver" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Pressed" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Disabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonDisabledBrush}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <!--  DisabledVisual Should be handled by the control, not the animated icon.  -->
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Normal" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                    </ContentPresenter>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <Style x:Key="AccentButtonStyle"
                           TargetType="Button">
                        <Setter Property="Foreground" Value="{ThemeResource AccentButtonForeground}" />
                        <Setter Property="Background" Value="{ThemeResource AccentButtonBackground}" />
                        <Setter Property="BackgroundSizing" Value="OuterBorderEdge" />
                        <Setter Property="BorderBrush" Value="{ThemeResource AccentButtonBorderBrush}" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <ContentPresenter x:Name="ContentPresenter"
                                                      Padding="{TemplateBinding Padding}"
                                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      controls:AnimatedIcon.State="Normal"
                                                      AutomationProperties.AccessibilityView="Raw"
                                                      Background="{TemplateBinding Background}"
                                                      BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                                      BorderBrush="{TemplateBinding BorderBrush}"
                                                      BorderThickness="{TemplateBinding BorderThickness}"
                                                      Content="{TemplateBinding Content}"
                                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                                      ContentTransitions="{TemplateBinding ContentTransitions}"
                                                      CornerRadius="{TemplateBinding CornerRadius}"
                                                      Foreground="{TemplateBinding Foreground}">
                                        <ContentPresenter.BackgroundTransition>
                                            <BrushTransition Duration="0:0:0.083" />
                                        </ContentPresenter.BackgroundTransition>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AccentButtonBackgroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AccentButtonBorderBrushPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AccentButtonForegroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="PointerOver" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AccentButtonBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AccentButtonBorderBrushPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AccentButtonForegroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Pressed" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Disabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AccentButtonBackgroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AccentButtonBorderBrushDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AccentButtonForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Disabled" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                    </ContentPresenter>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <Style x:Key="AcrylicButtonStyle"
                           TargetType="Button">
                        <Setter Property="Background" Value="{ThemeResource AcrylicButtonBackground}" />
                        <Setter Property="BackgroundSizing" Value="InnerBorderEdge" />
                        <Setter Property="Foreground" Value="{ThemeResource ButtonForeground}" />
                        <Setter Property="BorderBrush" Value="{ThemeResource ButtonBorderBrush}" />
                        <Setter Property="BorderThickness" Value="{ThemeResource ButtonBorderThemeThickness}" />
                        <Setter Property="Padding" Value="{StaticResource ButtonPadding}" />
                        <Setter Property="HorizontalAlignment" Value="Left" />
                        <Setter Property="VerticalAlignment" Value="Center" />
                        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
                        <Setter Property="FontWeight" Value="Normal" />
                        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
                        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
                        <Setter Property="FocusVisualMargin" Value="-3" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <ContentPresenter x:Name="ContentPresenter"
                                                      Padding="{TemplateBinding Padding}"
                                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      controls:AnimatedIcon.State="Normal"
                                                      AutomationProperties.AccessibilityView="Raw"
                                                      BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                                      BorderThickness="{TemplateBinding BorderThickness}"
                                                      Content="{TemplateBinding Content}"
                                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                                      ContentTransitions="{TemplateBinding ContentTransitions}"
                                                      CornerRadius="{TemplateBinding CornerRadius}">
                                        <ContentPresenter.BackgroundTransition>
                                            <BrushTransition Duration="0:0:0.083" />
                                        </ContentPresenter.BackgroundTransition>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicButtonBackground}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicButtonBackgroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="PointerOver" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicButtonBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Pressed" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Disabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicButtonBackgroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <!--  DisabledVisual Should be handled by the control, not the animated icon.  -->
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Normal" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                    </ContentPresenter>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <Style x:Key="AcrylicSemiButtonStyle"
                           TargetType="Button">
                        <Setter Property="Background" Value="{ThemeResource AcrylicSemiButtonBackground}" />
                        <Setter Property="BackgroundSizing" Value="InnerBorderEdge" />
                        <Setter Property="Foreground" Value="{ThemeResource ButtonForeground}" />
                        <Setter Property="BorderBrush" Value="{ThemeResource ButtonBorderBrush}" />
                        <Setter Property="BorderThickness" Value="{ThemeResource ButtonBorderThemeThickness}" />
                        <Setter Property="Padding" Value="{StaticResource ButtonPadding}" />
                        <Setter Property="HorizontalAlignment" Value="Left" />
                        <Setter Property="VerticalAlignment" Value="Center" />
                        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
                        <Setter Property="FontWeight" Value="Normal" />
                        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
                        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
                        <Setter Property="FocusVisualMargin" Value="-3" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <ContentPresenter x:Name="ContentPresenter"
                                                      Padding="{TemplateBinding Padding}"
                                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      controls:AnimatedIcon.State="Normal"
                                                      AutomationProperties.AccessibilityView="Raw"
                                                      BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                                      BorderThickness="{TemplateBinding BorderThickness}"
                                                      Content="{TemplateBinding Content}"
                                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                                      ContentTransitions="{TemplateBinding ContentTransitions}"
                                                      CornerRadius="{TemplateBinding CornerRadius}">
                                        <ContentPresenter.BackgroundTransition>
                                            <BrushTransition Duration="0:0:0.083" />
                                        </ContentPresenter.BackgroundTransition>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicSemiButtonBackground}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicButtonBackgroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="PointerOver" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicButtonBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Pressed" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Disabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicButtonBackgroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <!--  DisabledVisual Should be handled by the control, not the animated icon.  -->
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Normal" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                    </ContentPresenter>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <Style x:Key="TransparentDefaultButtonStyle"
                           TargetType="Button">
                        <Setter Property="Background" Value="{ThemeResource ButtonBackground}" />
                        <Setter Property="BackgroundSizing" Value="InnerBorderEdge" />
                        <Setter Property="Foreground" Value="{ThemeResource ButtonForeground}" />
                        <Setter Property="BorderBrush" Value="{ThemeResource ButtonBorderBrush}" />
                        <Setter Property="BorderThickness" Value="{ThemeResource ButtonBorderThemeThickness}" />
                        <Setter Property="Padding" Value="{StaticResource ButtonPadding}" />
                        <Setter Property="HorizontalAlignment" Value="Left" />
                        <Setter Property="VerticalAlignment" Value="Center" />
                        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
                        <Setter Property="FontWeight" Value="Normal" />
                        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
                        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
                        <Setter Property="FocusVisualMargin" Value="-3" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <ContentPresenter x:Name="ContentPresenter"
                                                      Padding="{TemplateBinding Padding}"
                                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      controls:AnimatedIcon.State="Normal"
                                                      AutomationProperties.AccessibilityView="Raw"
                                                      BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                                      BorderThickness="{TemplateBinding BorderThickness}"
                                                      Content="{TemplateBinding Content}"
                                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                                      ContentTransitions="{TemplateBinding ContentTransitions}"
                                                      CornerRadius="{TemplateBinding CornerRadius}">
                                        <ContentPresenter.BackgroundTransition>
                                            <BrushTransition Duration="0:0:0.083" />
                                        </ContentPresenter.BackgroundTransition>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource SolidColorBrushBackgroundTransparent}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource SolidColorBrushBackgroundHover}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="PointerOver" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource SolidColorBrushBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Pressed" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Disabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource SolidColorBrushBackgroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <!--  DisabledVisual Should be handled by the control, not the animated icon.  -->
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Normal" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                    </ContentPresenter>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <Style x:Key="NewAccentButtonStyle"
                           TargetType="Button">
                        <Setter Property="Background" Value="{ThemeResource NewAccentButtonBackground}" />
                        <Setter Property="BackgroundSizing" Value="InnerBorderEdge" />
                        <Setter Property="Foreground" Value="{ThemeResource NewAccentButtonForeground}" />
                        <Setter Property="BorderBrush" Value="{ThemeResource ButtonBorderBrush}" />
                        <Setter Property="BorderThickness" Value="{ThemeResource ButtonBorderThemeThickness}" />
                        <Setter Property="Padding" Value="{StaticResource ButtonPadding}" />
                        <Setter Property="HorizontalAlignment" Value="Left" />
                        <Setter Property="VerticalAlignment" Value="Center" />
                        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
                        <Setter Property="FontWeight" Value="Normal" />
                        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
                        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
                        <Setter Property="FocusVisualMargin" Value="-3" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <ContentPresenter x:Name="ContentPresenter"
                                                      Padding="{TemplateBinding Padding}"
                                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      controls:AnimatedIcon.State="Normal"
                                                      AutomationProperties.AccessibilityView="Raw"
                                                      Background="{TemplateBinding Background}"
                                                      BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                                      BorderBrush="{TemplateBinding BorderBrush}"
                                                      BorderThickness="{TemplateBinding BorderThickness}"
                                                      Content="{TemplateBinding Content}"
                                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                                      ContentTransitions="{TemplateBinding ContentTransitions}"
                                                      CornerRadius="{TemplateBinding CornerRadius}">
                                        <ContentPresenter.BackgroundTransition>
                                            <BrushTransition Duration="0:0:0.083" />
                                        </ContentPresenter.BackgroundTransition>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource NewAccentButtonBackgroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource NewAccentButtonForegroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="PointerOver" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource NewAccentButtonBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource NewAccentButtonForegroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Pressed" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Disabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource NewAccentButtonBackgroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderThickness">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="0" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource NewAccentButtonForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <!--  DisabledVisual Should be handled by the control, not the animated icon.  -->
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Normal" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                    </ContentPresenter>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <Style x:Key="DraftButtonStyle"
                           TargetType="Button">
                        <Setter Property="Background" Value="Transparent" />
                        <Setter Property="BackgroundSizing" Value="InnerBorderEdge" />
                        <Setter Property="BorderBrush" Value="Transparent" />
                        <Setter Property="BorderThickness" Value="0" />
                        <Setter Property="Padding" Value="0" />
                        <Setter Property="HorizontalAlignment" Value="Left" />
                        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                        <Setter Property="VerticalAlignment" Value="Center" />
                        <Setter Property="VerticalContentAlignment" Value="Stretch" />
                        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
                        <Setter Property="FontWeight" Value="Normal" />
                        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
                        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
                        <Setter Property="FocusVisualMargin" Value="-3" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <ContentPresenter x:Name="ContentPresenter"
                                                      Padding="{TemplateBinding Padding}"
                                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                                      controls:AnimatedIcon.State="Normal"
                                                      AutomationProperties.AccessibilityView="Raw"
                                                      Background="{TemplateBinding Background}"
                                                      BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                                      BorderBrush="{TemplateBinding BorderBrush}"
                                                      BorderThickness="{TemplateBinding BorderThickness}"
                                                      Content="{TemplateBinding Content}"
                                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                                      ContentTransitions="{TemplateBinding ContentTransitions}"
                                                      CornerRadius="{TemplateBinding CornerRadius}">
                                        <ContentPresenter.BackgroundTransition>
                                            <BrushTransition Duration="0:0:0.083" />
                                        </ContentPresenter.BackgroundTransition>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />
                                                <VisualState x:Name="Disabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource NewAccentButtonBackgroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ButtonBorderBrushDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="BorderThickness">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="0" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource NewAccentButtonForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <!--  DisabledVisual Should be handled by the control, not the animated icon.  -->
                                                        <Setter Target="ContentPresenter.(controls:AnimatedIcon.State)" Value="Normal" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                    </ContentPresenter>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <!--  END: Button styles  -->
                    <!--  ComboBox styles  -->
                    <Style x:Key="AcrylicComboBoxStyle"
                           TargetType="ComboBox">
                        <Setter Property="Padding" Value="{ThemeResource ComboBoxPadding}" />
                        <Setter Property="MaxDropDownHeight" Value="504" />
                        <Setter Property="Foreground" Value="{ThemeResource ComboBoxForeground}" />
                        <Setter Property="Background" Value="{ThemeResource AcrylicComboBoxBackground}" />
                        <Setter Property="BorderBrush" Value="{ThemeResource ComboBoxBorderBrush}" />
                        <Setter Property="BorderThickness" Value="0" />
                        <Setter Property="TabNavigation" Value="Once" />
                        <Setter Property="TextBoxStyle" Value="{StaticResource ComboBoxTextBoxStyle}" />
                        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
                        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
                        <Setter Property="ScrollViewer.HorizontalScrollMode" Value="Disabled" />
                        <Setter Property="ScrollViewer.VerticalScrollMode" Value="Auto" />
                        <Setter Property="ScrollViewer.IsVerticalRailEnabled" Value="True" />
                        <Setter Property="ScrollViewer.IsDeferredScrollingEnabled" Value="False" />
                        <Setter Property="ScrollViewer.BringIntoViewOnFocusChange" Value="True" />
                        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                        <Setter Property="HorizontalAlignment" Value="Left" />
                        <Setter Property="VerticalAlignment" Value="Top" />
                        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
                        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
                        <Setter Property="UseSystemFocusVisuals" Value="{ThemeResource IsApplicationFocusVisualKindReveal}" />
                        <Setter Property="primitives:ComboBoxHelper.KeepInteriorCornersSquare" Value="true" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                        <Setter Property="ItemsPanel">
                            <Setter.Value>
                                <ItemsPanelTemplate>
                                    <CarouselPanel />
                                </ItemsPanelTemplate>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ComboBox">
                                    <Grid x:Name="LayoutRoot">
                                        <Grid.Resources>
                                            <Storyboard x:Key="OverlayOpeningAnimation">
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Opacity">
                                                    <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                            Value="0.0" />
                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                          KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                          Value="1.0" />
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <Storyboard x:Key="OverlayClosingAnimation">
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Opacity">
                                                    <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                            Value="1.0" />
                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                          KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                          Value="0.0" />
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </Grid.Resources>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="38" />
                                        </Grid.ColumnDefinitions>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicComboBoxBackgroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxBorderBrushPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxForegroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextBlock"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForegroundPointerOver}}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownGlyph.(controls:AnimatedIcon.State)" Value="PointerOver" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicComboBoxBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxBorderBrushPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxForegroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextBlock"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForegroundPressed}}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownGlyph.(controls:AnimatedIcon.State)" Value="Pressed" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Disabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource AcrylicComboBoxBackgroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxBorderBrushDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HeaderContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxHeaderForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextBlock"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForegroundDisabled}}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="DropDownGlyph"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxDropDownGlyphForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="FocusStates">
                                                <VisualState x:Name="Focused">
                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="HighlightBackground"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation Storyboard.TargetName="Pill"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxForegroundFocused}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextBlock"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForegroundFocused}}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="DropDownGlyph"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxDropDownGlyphForegroundFocused}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="FocusedPressed">
                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="HighlightBackground"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation Storyboard.TargetName="Pill"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxForegroundFocusedPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextBlock"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForegroundFocusedPressed}}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="DropDownGlyph"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxDropDownGlyphForegroundFocusedPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Unfocused" />
                                                <VisualState x:Name="PointerFocused" />
                                                <VisualState x:Name="FocusedDropDown">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PopupBorder"
                                                                                       Storyboard.TargetProperty="Visibility"
                                                                                       Duration="0">
                                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                                <DiscreteObjectKeyFrame.Value>
                                                                    <Visibility>Visible</Visibility>
                                                                </DiscreteObjectKeyFrame.Value>
                                                            </DiscreteObjectKeyFrame>
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="DropDownStates">
                                                <VisualState x:Name="Opened">
                                                    <Storyboard>
                                                        <SplitOpenThemeAnimation ClosedTargetName="ContentPresenter"
                                                                                 OffsetFromCenter="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.DropDownOffset}"
                                                                                 OpenedLength="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.DropDownOpenedHeight}"
                                                                                 OpenedTargetName="PopupBorder" />
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Closed">
                                                    <Storyboard>
                                                        <SplitCloseThemeAnimation ClosedTargetName="ContentPresenter"
                                                                                  OffsetFromCenter="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.DropDownOffset}"
                                                                                  OpenedLength="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.DropDownOpenedHeight}"
                                                                                  OpenedTargetName="PopupBorder" />
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="EditableModeStates">
                                                <VisualState x:Name="TextBoxFocused">
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownGlyph.Foreground" Value="{ThemeResource ComboBoxEditableDropDownGlyphForeground}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TextBoxFocusedOverlayPointerOver">
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownGlyph.Foreground" Value="{ThemeResource ComboBoxEditableDropDownGlyphForeground}" />
                                                        <Setter Target="DropDownOverlay.Background" Value="{ThemeResource ComboBoxDropDownBackgroundPointerOver}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TextBoxFocusedOverlayPressed">
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownGlyph.Foreground" Value="{ThemeResource ComboBoxEditableDropDownGlyphForeground}" />
                                                        <Setter Target="DropDownOverlay.Background" Value="{ThemeResource ComboBoxFocusedDropDownBackgroundPointerPressed}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TextBoxOverlayPointerOver">
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownOverlay.Background" Value="{ThemeResource ComboBoxDropDownBackgroundPointerOver}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TextBoxOverlayPressed">
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownOverlay.Background" Value="{ThemeResource ComboBoxDropDownBackgroundPointerPressed}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TextBoxUnfocused" />
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                        <ContentPresenter x:Name="HeaderContentPresenter"
                                                          Grid.Row="0"
                                                          Grid.Column="0"
                                                          Grid.ColumnSpan="2"
                                                          Margin="{ThemeResource ComboBoxTopHeaderMargin}"
                                                          VerticalAlignment="Top"
                                                          x:DeferLoadStrategy="Lazy"
                                                          Content="{TemplateBinding Header}"
                                                          ContentTemplate="{TemplateBinding HeaderTemplate}"
                                                          FlowDirection="{TemplateBinding FlowDirection}"
                                                          FontWeight="{ThemeResource ComboBoxHeaderThemeFontWeight}"
                                                          Foreground="{ThemeResource ComboBoxHeaderForeground}"
                                                          LineHeight="20"
                                                          TextWrapping="Wrap"
                                                          Visibility="Collapsed" />
                                        <Border x:Name="HighlightBackground"
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="2"
                                                Margin="-4"
                                                Background="{ThemeResource AcrylicComboBoxBackgroundFocused}"
                                                BorderBrush="{ThemeResource ComboBoxBackgroundBorderBrushFocused}"
                                                BorderThickness="{StaticResource ComboBoxBackgroundBorderThicknessFocused}"
                                                CornerRadius="{StaticResource ComboBoxHiglightBorderCornerRadius}"
                                                Opacity="0" />
                                        <Border x:Name="Background"
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="2"
                                                MinWidth="{ThemeResource ComboBoxThemeMinWidth}"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                Control.IsTemplateFocusTarget="True"
                                                CornerRadius="{TemplateBinding CornerRadius}"
                                                Translation="0,0,1" />
                                        <Rectangle x:Name="Pill"
                                                   Grid.Row="1"
                                                   Grid.Column="0"
                                                   Margin="1,0,0,0"
                                                   Opacity="0"
                                                   Style="{StaticResource ComboBoxItemPill}">
                                            <Rectangle.RenderTransform>
                                                <!--  PillTransform  -->
                                                <CompositeTransform ScaleY="1" />
                                            </Rectangle.RenderTransform>
                                        </Rectangle>
                                        <ContentPresenter x:Name="ContentPresenter"
                                                          Grid.Row="1"
                                                          Grid.Column="0"
                                                          Margin="{TemplateBinding Padding}"
                                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                            <TextBlock x:Name="PlaceholderTextBlock"
                                                       Foreground="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForeground}}"
                                                       Text="{TemplateBinding PlaceholderText}" />
                                        </ContentPresenter>
                                        <TextBox x:Name="EditableText"
                                                 Grid.Row="1"
                                                 Grid.Column="0"
                                                 Grid.ColumnSpan="2"
                                                 Margin="0,0,0,0"
                                                 Padding="{ThemeResource ComboBoxEditableTextPadding}"
                                                 HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                 VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                 x:Load="False"
                                                 AutomationProperties.Name="{TemplateBinding AutomationProperties.Name}"
                                                 BorderBrush="Transparent"
                                                 CornerRadius="{TemplateBinding CornerRadius}"
                                                 Foreground="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForeground}}"
                                                 Header="{TemplateBinding Header}"
                                                 PlaceholderText="{TemplateBinding PlaceholderText}"
                                                 Style="{TemplateBinding TextBoxStyle}"
                                                 Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Text, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                 Visibility="Collapsed" />
                                        <Border x:Name="DropDownOverlay"
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                Width="30"
                                                Margin="4,4,4,4"
                                                HorizontalAlignment="Right"
                                                x:Load="False"
                                                Background="Transparent"
                                                CornerRadius="{StaticResource ComboBoxDropDownButtonBackgroundCornerRadius}"
                                                Visibility="Collapsed" />
                                        <AnimatedIcon x:Name="DropDownGlyph"
                                                      Grid.Row="1"
                                                      Grid.Column="1"
                                                      Width="12"
                                                      Height="12"
                                                      MinHeight="{ThemeResource ComboBoxMinHeight}"
                                                      Margin="0,0,14,0"
                                                      HorizontalAlignment="Right"
                                                      VerticalAlignment="Center"
                                                      AnimatedIcon.State="Normal"
                                                      AutomationProperties.AccessibilityView="Raw"
                                                      Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                      IsHitTestVisible="False">
                                            <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                            <AnimatedIcon.FallbackIconSource>
                                                <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                                FontSize="12"
                                                                Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                                Glyph="&#xE70D;" />
                                            </AnimatedIcon.FallbackIconSource>
                                        </AnimatedIcon>
                                        <ContentPresenter x:Name="DescriptionPresenter"
                                                          Grid.Row="2"
                                                          Grid.Column="0"
                                                          Grid.ColumnSpan="2"
                                                          x:Load="False"
                                                          Content="{TemplateBinding Description}"
                                                          Foreground="{ThemeResource SystemControlDescriptionTextForegroundBrush}" />
                                        <Popup x:Name="Popup"
                                               Grid.Row="0"
                                               Grid.Column="0">
                                            <!--  Force ShouldConstrainToRootBounds to True  -->
                                            <!--  Fix https://github.com/microsoft/microsoft-ui-xaml/issues/8657  -->
                                            <interactivity:Interaction.Behaviors>
                                                <interactivity:DataTriggerBehavior Binding="{Binding ShouldConstrainToRootBounds, ElementName=Popup}"
                                                                                   ComparisonCondition="Equal"
                                                                                   Value="False">
                                                    <interactivity:ChangePropertyAction PropertyName="ShouldConstrainToRootBounds"
                                                                                        TargetObject="{Binding ElementName=Popup}"
                                                                                        Value="True" />
                                                </interactivity:DataTriggerBehavior>
                                            </interactivity:Interaction.Behaviors>
                                            <Border x:Name="PopupBorder"
                                                    Margin="0,-0.5,0,-1"
                                                    Padding="{ThemeResource ComboBoxDropdownBorderPadding}"
                                                    HorizontalAlignment="Stretch"
                                                    Background="{ThemeResource ComboBoxDropDownBackground}"
                                                    BackgroundSizing="InnerBorderEdge"
                                                    BorderBrush="{ThemeResource ComboBoxDropDownBorderBrush}"
                                                    BorderThickness="{ThemeResource ComboBoxDropdownBorderThickness}"
                                                    CornerRadius="{ThemeResource OverlayCornerRadius}">
                                                <ScrollViewer x:Name="ScrollViewer"
                                                              MinWidth="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.DropDownContentMinWidth}"
                                                              AutomationProperties.AccessibilityView="Raw"
                                                              BringIntoViewOnFocusChange="{TemplateBinding ScrollViewer.BringIntoViewOnFocusChange}"
                                                              Foreground="{ThemeResource ComboBoxDropDownForeground}"
                                                              HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                                              HorizontalScrollMode="{TemplateBinding ScrollViewer.HorizontalScrollMode}"
                                                              IsDeferredScrollingEnabled="{TemplateBinding ScrollViewer.IsDeferredScrollingEnabled}"
                                                              IsHorizontalRailEnabled="{TemplateBinding ScrollViewer.IsHorizontalRailEnabled}"
                                                              IsVerticalRailEnabled="{TemplateBinding ScrollViewer.IsVerticalRailEnabled}"
                                                              VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                                                              VerticalScrollMode="{TemplateBinding ScrollViewer.VerticalScrollMode}"
                                                              VerticalSnapPointsAlignment="Near"
                                                              VerticalSnapPointsType="OptionalSingle"
                                                              ZoomMode="Disabled">
                                                    <ItemsPresenter Margin="{ThemeResource ComboBoxDropdownContentMargin}" />
                                                </ScrollViewer>
                                            </Border>
                                        </Popup>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <Style x:Key="DefaultComboBoxStyle"
                           TargetType="ComboBox">
                        <Setter Property="Padding" Value="{ThemeResource ComboBoxPadding}" />
                        <Setter Property="MaxDropDownHeight" Value="504" />
                        <Setter Property="Foreground" Value="{ThemeResource ComboBoxForeground}" />
                        <Setter Property="Background" Value="{ThemeResource ComboBoxBackground}" />
                        <Setter Property="BorderBrush" Value="{ThemeResource ComboBoxBorderBrush}" />
                        <Setter Property="BorderThickness" Value="{ThemeResource ComboBoxBorderThemeThickness}" />
                        <Setter Property="TabNavigation" Value="Once" />
                        <Setter Property="TextBoxStyle" Value="{StaticResource ComboBoxTextBoxStyle}" />
                        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
                        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
                        <Setter Property="ScrollViewer.HorizontalScrollMode" Value="Disabled" />
                        <Setter Property="ScrollViewer.VerticalScrollMode" Value="Auto" />
                        <Setter Property="ScrollViewer.IsVerticalRailEnabled" Value="True" />
                        <Setter Property="ScrollViewer.IsDeferredScrollingEnabled" Value="False" />
                        <Setter Property="ScrollViewer.BringIntoViewOnFocusChange" Value="True" />
                        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                        <Setter Property="HorizontalAlignment" Value="Left" />
                        <Setter Property="VerticalAlignment" Value="Top" />
                        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
                        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
                        <Setter Property="UseSystemFocusVisuals" Value="{ThemeResource IsApplicationFocusVisualKindReveal}" />
                        <Setter Property="primitives:ComboBoxHelper.KeepInteriorCornersSquare" Value="true" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                        <Setter Property="ItemsPanel">
                            <Setter.Value>
                                <ItemsPanelTemplate>
                                    <CarouselPanel />
                                </ItemsPanelTemplate>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ComboBox">
                                    <Grid x:Name="LayoutRoot">
                                        <Grid.Resources>
                                            <Storyboard x:Key="OverlayOpeningAnimation">
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Opacity">
                                                    <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                            Value="0.0" />
                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                          KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                          Value="1.0" />
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                            <Storyboard x:Key="OverlayClosingAnimation">
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Opacity">
                                                    <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                            Value="1.0" />
                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                          KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                          Value="0.0" />
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </Grid.Resources>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="38" />
                                        </Grid.ColumnDefinitions>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxBackgroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxBorderBrushPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxForegroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextBlock"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForegroundPointerOver}}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownGlyph.(controls:AnimatedIcon.State)" Value="PointerOver" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxBorderBrushPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxForegroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextBlock"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForegroundPressed}}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownGlyph.(controls:AnimatedIcon.State)" Value="Pressed" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Disabled">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxBackgroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Background"
                                                                                       Storyboard.TargetProperty="BorderBrush">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxBorderBrushDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HeaderContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxHeaderForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextBlock"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForegroundDisabled}}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="DropDownGlyph"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxDropDownGlyphForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="FocusStates">
                                                <VisualState x:Name="Focused">
                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="HighlightBackground"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation Storyboard.TargetName="Pill"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxForegroundFocused}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextBlock"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForegroundFocused}}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="DropDownGlyph"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxDropDownGlyphForegroundFocused}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="FocusedPressed">
                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="HighlightBackground"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation Storyboard.TargetName="Pill"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentPresenter"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxForegroundFocusedPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderTextBlock"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForegroundFocusedPressed}}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="DropDownGlyph"
                                                                                       Storyboard.TargetProperty="Foreground">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource ComboBoxDropDownGlyphForegroundFocusedPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Unfocused" />
                                                <VisualState x:Name="PointerFocused" />
                                                <VisualState x:Name="FocusedDropDown">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PopupBorder"
                                                                                       Storyboard.TargetProperty="Visibility"
                                                                                       Duration="0">
                                                            <DiscreteObjectKeyFrame KeyTime="0">
                                                                <DiscreteObjectKeyFrame.Value>
                                                                    <Visibility>Visible</Visibility>
                                                                </DiscreteObjectKeyFrame.Value>
                                                            </DiscreteObjectKeyFrame>
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="DropDownStates">
                                                <VisualState x:Name="Opened">
                                                    <Storyboard>
                                                        <SplitOpenThemeAnimation ClosedTargetName="ContentPresenter"
                                                                                 OffsetFromCenter="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.DropDownOffset}"
                                                                                 OpenedLength="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.DropDownOpenedHeight}"
                                                                                 OpenedTargetName="PopupBorder" />
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Closed">
                                                    <Storyboard>
                                                        <SplitCloseThemeAnimation ClosedTargetName="ContentPresenter"
                                                                                  OffsetFromCenter="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.DropDownOffset}"
                                                                                  OpenedLength="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.DropDownOpenedHeight}"
                                                                                  OpenedTargetName="PopupBorder" />
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="EditableModeStates">
                                                <VisualState x:Name="TextBoxFocused">
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownGlyph.Foreground" Value="{ThemeResource ComboBoxEditableDropDownGlyphForeground}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TextBoxFocusedOverlayPointerOver">
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownGlyph.Foreground" Value="{ThemeResource ComboBoxEditableDropDownGlyphForeground}" />
                                                        <Setter Target="DropDownOverlay.Background" Value="{ThemeResource ComboBoxDropDownBackgroundPointerOver}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TextBoxFocusedOverlayPressed">
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownGlyph.Foreground" Value="{ThemeResource ComboBoxEditableDropDownGlyphForeground}" />
                                                        <Setter Target="DropDownOverlay.Background" Value="{ThemeResource ComboBoxFocusedDropDownBackgroundPointerPressed}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TextBoxOverlayPointerOver">
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownOverlay.Background" Value="{ThemeResource ComboBoxDropDownBackgroundPointerOver}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TextBoxOverlayPressed">
                                                    <VisualState.Setters>
                                                        <Setter Target="DropDownOverlay.Background" Value="{ThemeResource ComboBoxDropDownBackgroundPointerPressed}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TextBoxUnfocused" />
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                        <ContentPresenter x:Name="HeaderContentPresenter"
                                                          Grid.Row="0"
                                                          Grid.Column="0"
                                                          Grid.ColumnSpan="2"
                                                          Margin="{ThemeResource ComboBoxTopHeaderMargin}"
                                                          VerticalAlignment="Top"
                                                          x:DeferLoadStrategy="Lazy"
                                                          Content="{TemplateBinding Header}"
                                                          ContentTemplate="{TemplateBinding HeaderTemplate}"
                                                          FlowDirection="{TemplateBinding FlowDirection}"
                                                          FontWeight="{ThemeResource ComboBoxHeaderThemeFontWeight}"
                                                          Foreground="{ThemeResource ComboBoxHeaderForeground}"
                                                          LineHeight="20"
                                                          TextWrapping="Wrap"
                                                          Visibility="Collapsed" />
                                        <Border x:Name="HighlightBackground"
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="2"
                                                Margin="-4"
                                                Background="{ThemeResource ComboBoxBackgroundFocused}"
                                                BorderBrush="{ThemeResource ComboBoxBackgroundBorderBrushFocused}"
                                                BorderThickness="{StaticResource ComboBoxBackgroundBorderThicknessFocused}"
                                                CornerRadius="{StaticResource ComboBoxHiglightBorderCornerRadius}"
                                                Opacity="0" />
                                        <Border x:Name="Background"
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="2"
                                                MinWidth="{ThemeResource ComboBoxThemeMinWidth}"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                Control.IsTemplateFocusTarget="True"
                                                CornerRadius="{TemplateBinding CornerRadius}"
                                                Translation="0,0,1" />
                                        <Rectangle x:Name="Pill"
                                                   Grid.Row="1"
                                                   Grid.Column="0"
                                                   Margin="1,0,0,0"
                                                   Opacity="0"
                                                   Style="{StaticResource ComboBoxItemPill}">
                                            <Rectangle.RenderTransform>
                                                <!--  PillTransform  -->
                                                <CompositeTransform ScaleY="1" />
                                            </Rectangle.RenderTransform>
                                        </Rectangle>
                                        <ContentPresenter x:Name="ContentPresenter"
                                                          Grid.Row="1"
                                                          Grid.Column="0"
                                                          Margin="{TemplateBinding Padding}"
                                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                            <TextBlock x:Name="PlaceholderTextBlock"
                                                       Foreground="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForeground}}"
                                                       Text="{TemplateBinding PlaceholderText}" />
                                        </ContentPresenter>
                                        <TextBox x:Name="EditableText"
                                                 Grid.Row="1"
                                                 Grid.Column="0"
                                                 Grid.ColumnSpan="2"
                                                 Margin="0,0,0,0"
                                                 Padding="{ThemeResource ComboBoxEditableTextPadding}"
                                                 HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                 VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                 x:Load="False"
                                                 AutomationProperties.Name="{TemplateBinding AutomationProperties.Name}"
                                                 BorderBrush="Transparent"
                                                 CornerRadius="{TemplateBinding CornerRadius}"
                                                 Foreground="{Binding PlaceholderForeground, RelativeSource={RelativeSource TemplatedParent}, TargetNullValue={ThemeResource ComboBoxPlaceHolderForeground}}"
                                                 Header="{TemplateBinding Header}"
                                                 PlaceholderText="{TemplateBinding PlaceholderText}"
                                                 Style="{TemplateBinding TextBoxStyle}"
                                                 Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Text, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                 Visibility="Collapsed" />
                                        <Border x:Name="DropDownOverlay"
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                Width="30"
                                                Margin="4,4,4,4"
                                                HorizontalAlignment="Right"
                                                x:Load="False"
                                                Background="Transparent"
                                                CornerRadius="{StaticResource ComboBoxDropDownButtonBackgroundCornerRadius}"
                                                Visibility="Collapsed" />
                                        <AnimatedIcon x:Name="DropDownGlyph"
                                                      Grid.Row="1"
                                                      Grid.Column="1"
                                                      Width="12"
                                                      Height="12"
                                                      MinHeight="{ThemeResource ComboBoxMinHeight}"
                                                      Margin="0,0,14,0"
                                                      HorizontalAlignment="Right"
                                                      VerticalAlignment="Center"
                                                      AnimatedIcon.State="Normal"
                                                      AutomationProperties.AccessibilityView="Raw"
                                                      Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                      IsHitTestVisible="False">
                                            <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                            <AnimatedIcon.FallbackIconSource>
                                                <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                                FontSize="12"
                                                                Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                                Glyph="&#xE70D;" />
                                            </AnimatedIcon.FallbackIconSource>
                                        </AnimatedIcon>
                                        <ContentPresenter x:Name="DescriptionPresenter"
                                                          Grid.Row="2"
                                                          Grid.Column="0"
                                                          Grid.ColumnSpan="2"
                                                          x:Load="False"
                                                          Content="{TemplateBinding Description}"
                                                          Foreground="{ThemeResource SystemControlDescriptionTextForegroundBrush}" />
                                        <Popup x:Name="Popup"
                                               Grid.Row="0"
                                               Grid.Column="0">
                                            <!--  Force ShouldConstrainToRootBounds to True  -->
                                            <!--  Fix https://github.com/microsoft/microsoft-ui-xaml/issues/8657  -->
                                            <interactivity:Interaction.Behaviors>
                                                <interactivity:DataTriggerBehavior Binding="{Binding ShouldConstrainToRootBounds, ElementName=Popup}"
                                                                                   ComparisonCondition="Equal"
                                                                                   Value="False">
                                                    <interactivity:ChangePropertyAction PropertyName="ShouldConstrainToRootBounds"
                                                                                        TargetObject="{Binding ElementName=Popup}"
                                                                                        Value="True" />
                                                </interactivity:DataTriggerBehavior>
                                            </interactivity:Interaction.Behaviors>
                                            <Border x:Name="PopupBorder"
                                                    Margin="0,-0.5,0,-1"
                                                    Padding="{ThemeResource ComboBoxDropdownBorderPadding}"
                                                    HorizontalAlignment="Stretch"
                                                    Background="{ThemeResource ComboBoxDropDownBackground}"
                                                    BackgroundSizing="InnerBorderEdge"
                                                    BorderBrush="{ThemeResource ComboBoxDropDownBorderBrush}"
                                                    BorderThickness="{ThemeResource ComboBoxDropdownBorderThickness}"
                                                    CornerRadius="{ThemeResource OverlayCornerRadius}">
                                                <ScrollViewer x:Name="ScrollViewer"
                                                              MinWidth="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.DropDownContentMinWidth}"
                                                              AutomationProperties.AccessibilityView="Raw"
                                                              BringIntoViewOnFocusChange="{TemplateBinding ScrollViewer.BringIntoViewOnFocusChange}"
                                                              Foreground="{ThemeResource ComboBoxDropDownForeground}"
                                                              HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                                              HorizontalScrollMode="{TemplateBinding ScrollViewer.HorizontalScrollMode}"
                                                              IsDeferredScrollingEnabled="{TemplateBinding ScrollViewer.IsDeferredScrollingEnabled}"
                                                              IsHorizontalRailEnabled="{TemplateBinding ScrollViewer.IsHorizontalRailEnabled}"
                                                              IsVerticalRailEnabled="{TemplateBinding ScrollViewer.IsVerticalRailEnabled}"
                                                              VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                                                              VerticalScrollMode="{TemplateBinding ScrollViewer.VerticalScrollMode}"
                                                              VerticalSnapPointsAlignment="Near"
                                                              VerticalSnapPointsType="OptionalSingle"
                                                              ZoomMode="Disabled">
                                                    <ItemsPresenter Margin="{ThemeResource ComboBoxDropdownContentMargin}" />
                                                </ScrollViewer>
                                            </Border>
                                        </Popup>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <Style BasedOn="{StaticResource DefaultComboBoxStyle}"
                           TargetType="ComboBox" />
                    <!--  WindowChrome min max close styles  -->
                    <x:Double x:Key="WindowCaptionButtonStrokeWidth">2</x:Double>

                    <Style x:Key="WindowCaptionButton"
                           TargetType="Button">
                        <Setter Property="BorderThickness" Value="0" />
                        <Setter Property="Background" Value="{ThemeResource WindowCaptionBackground}" />
                        <Setter Property="IsTabStop" Value="False" />
                        <Setter Property="VerticalAlignment" Value="Top" />
                        <Setter Property="Width" Value="32" />
                        <Setter Property="Height" Value="32" />

                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border x:Name="LayoutRoot"
                                            Padding="{TemplateBinding Padding}"
                                            Background="{TemplateBinding Background}"
                                            BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="{TemplateBinding CornerRadius}">
                                        <Border.BackgroundTransition>
                                            <BrushTransition Duration="0:0:0.050" />
                                        </Border.BackgroundTransition>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />
                                                <VisualState x:Name="PointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource WindowCaptionButtonBackgroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Path"
                                                                                       Storyboard.TargetProperty="Stroke">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource WindowCaptionForeground}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>

                                                <VisualState x:Name="Pressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource WindowCaptionButtonBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Path"
                                                                                       Storyboard.TargetProperty="Stroke">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource WindowCaptionForegroundDisabled}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <!--  these 2 states are only for Close button, needed because they use fixed colors for a theme and cannot be changed by user  -->
                                                <VisualState x:Name="CloseButtonPointerOver">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource WindowCaptionButtonBackgroundPointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Path"
                                                                                       Storyboard.TargetProperty="Stroke">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource WindowCaptionButtonStrokePointerOver}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>

                                                <VisualState x:Name="CloseButtonPressed">
                                                    <Storyboard>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                                       Storyboard.TargetProperty="Background">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource WindowCaptionButtonBackgroundPressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Path"
                                                                                       Storyboard.TargetProperty="Stroke">
                                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                                    Value="{ThemeResource WindowCaptionButtonStrokePressed}" />
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>

                                            </VisualStateGroup>

                                            <VisualStateGroup x:Name="MinMaxStates">
                                                <VisualState x:Name="WindowStateNormal">
                                                    <VisualState.Setters>
                                                        <Setter Target="Path.Data" Value="M 1.516 -0.001 L 7.451 0.009 C 8.751 0.019 9 1 8.981 1.477 L 9.002 7.558 M 9.002 7.547 C 8.929 8.669 8 9 7.43 9.015 L 1.464 9.005 C 0.374 8.973 0 8 -0.004 7.484 L -0.004 1.477 C 0 1 0.415 0.009 1.527 -0.001" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="WindowStateMaximized">
                                                    <VisualState.Setters>
                                                        <Setter Target="Path.Data" Value="M 1.516 -0.001 L 7.451 0.009 C 8.751 0.019 9 1 8.981 1.477 L 9.002 7.558 M 11 6 L 11 2 C 11 0 10 -2 8.011 -1.946 L 7.06 -1.969 L 3 -2 M 9.002 7.547 C 8.929 8.669 8 9 7.43 9.015 L 1.464 9.005 C 0.374 8.973 0 8 -0.004 7.484 L -0.004 1.477 C 0 1 0.415 0.009 1.527 -0.001" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                            </VisualStateGroup>

                                        </VisualStateManager.VisualStateGroups>

                                        <Path x:Name="Path"
                                              Width="10"
                                              Height="10"
                                              Data="{TemplateBinding Content}"
                                              Stretch="Fill"
                                              Stroke="{TemplateBinding Foreground}"
                                              StrokeEndLineCap="Round"
                                              StrokeStartLineCap="Round"
                                              StrokeThickness="{ThemeResource WindowCaptionButtonStrokeWidth}"
                                              UseLayoutRounding="True" />
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>

                    <!--  ScrollBar styles  -->
                    <conv:CapsuleCornerRadiusConverter x:Key="CapsuleCornerRadiusConverter" />
                    <Style x:Key="DefaultScrollBarStyle"
                           TargetType="ScrollBar">
                        <Setter Property="MinWidth" Value="{StaticResource ScrollBarSize}" />
                        <Setter Property="MinHeight" Value="{StaticResource ScrollBarSize}" />
                        <Setter Property="Background" Value="{ThemeResource ScrollBarBackground}" />
                        <Setter Property="Foreground" Value="{ThemeResource ScrollBarForeground}" />
                        <Setter Property="BorderBrush" Value="{ThemeResource ScrollBarBorderBrush}" />
                        <Setter Property="IsTabStop" Value="False" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ScrollBarCornerRadius}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ScrollBar">
                                    <Grid x:Name="Root">

                                        <Grid.Resources>
                                            <ControlTemplate x:Key="RepeatButtonTemplate"
                                                             TargetType="RepeatButton">
                                                <Grid x:Name="Root"
                                                      Background="Transparent">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>

                                                </Grid>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="HorizontalIncrementTemplate"
                                                             TargetType="RepeatButton">
                                                <Grid x:Name="Root"
                                                      Padding="{ThemeResource ScrollBarHorizontalIncreaseMargin}"
                                                      Background="{ThemeResource ScrollBarButtonBackground}"
                                                      BorderBrush="{ThemeResource ScrollBarButtonBorderBrush}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="PointerOver">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPointerOver}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Pressed">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPressed}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                    <FontIcon x:Name="Arrow"
                                                              FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                              FontSize="{StaticResource ScrollBarButtonArrowIconFontSize}"
                                                              Foreground="{ThemeResource ScrollBarButtonArrowForeground}"
                                                              Glyph="&#xEDDA;"
                                                              MirroredWhenRightToLeft="True"
                                                              RenderTransformOrigin="0.5, 0.5">
                                                        <FontIcon.RenderTransform>
                                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                                        </FontIcon.RenderTransform>
                                                    </FontIcon>

                                                </Grid>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="HorizontalDecrementTemplate"
                                                             TargetType="RepeatButton">
                                                <Grid x:Name="Root"
                                                      Padding="{ThemeResource ScrollBarHorizontalDecreaseMargin}"
                                                      Background="{ThemeResource ScrollBarButtonBackground}"
                                                      BorderBrush="{ThemeResource ScrollBarButtonBorderBrush}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="PointerOver">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPointerOver}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Pressed">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPressed}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                    <FontIcon x:Name="Arrow"
                                                              FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                              FontSize="{StaticResource ScrollBarButtonArrowIconFontSize}"
                                                              Foreground="{ThemeResource ScrollBarButtonArrowForeground}"
                                                              Glyph="&#xEDD9;"
                                                              MirroredWhenRightToLeft="True"
                                                              RenderTransformOrigin="0.5, 0.5">
                                                        <FontIcon.RenderTransform>
                                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                                        </FontIcon.RenderTransform>
                                                    </FontIcon>

                                                </Grid>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="VerticalIncrementTemplate"
                                                             TargetType="RepeatButton">
                                                <Grid x:Name="Root"
                                                      Padding="{ThemeResource ScrollBarVerticalIncreaseMargin}"
                                                      Background="{ThemeResource ScrollBarButtonBackground}"
                                                      BorderBrush="{ThemeResource ScrollBarButtonBorderBrush}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="PointerOver">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPointerOver}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Pressed">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPressed}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                    <FontIcon x:Name="Arrow"
                                                              FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                              FontSize="{StaticResource ScrollBarButtonArrowIconFontSize}"
                                                              Foreground="{ThemeResource ScrollBarButtonArrowForeground}"
                                                              Glyph="&#xEDDC;"
                                                              RenderTransformOrigin="0.5, 0.5">
                                                        <FontIcon.RenderTransform>
                                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                                        </FontIcon.RenderTransform>
                                                    </FontIcon>

                                                </Grid>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="VerticalDecrementTemplate"
                                                             TargetType="RepeatButton">
                                                <Grid x:Name="Root"
                                                      Padding="{ThemeResource ScrollBarVerticalDecreaseMargin}"
                                                      Background="{ThemeResource ScrollBarButtonBackground}"
                                                      BorderBrush="{ThemeResource ScrollBarButtonBorderBrush}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="PointerOver">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPointerOver}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Pressed">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPressed}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                    <FontIcon x:Name="Arrow"
                                                              FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                              FontSize="{StaticResource ScrollBarButtonArrowIconFontSize}"
                                                              Foreground="{ThemeResource ScrollBarButtonArrowForeground}"
                                                              Glyph="&#xEDDB;"
                                                              RenderTransformOrigin="0.5, 0.5">
                                                        <FontIcon.RenderTransform>
                                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                                        </FontIcon.RenderTransform>
                                                    </FontIcon>

                                                </Grid>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="VerticalThumbTemplate"
                                                             TargetType="Thumb">
                                                <Rectangle x:Name="ThumbVisual"
                                                           Fill="{TemplateBinding Background}"
                                                           RadiusX="{Binding Width, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource CapsuleCornerRadiusConverter}, ConverterParameter={ThemeResource ScrollBarThumbStrokeThickness}}"
                                                           RadiusY="{Binding Width, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource CapsuleCornerRadiusConverter}, ConverterParameter={ThemeResource ScrollBarThumbStrokeThickness}}"
                                                           Stroke="{TemplateBinding BorderBrush}"
                                                           StrokeThickness="{ThemeResource ScrollBarThumbStrokeThickness}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ThumbVisual"
                                                                                                   Storyboard.TargetProperty="Fill">
                                                                        <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarColorChangeDuration}"
                                                                                                Value="{ThemeResource ScrollBarThumbFillDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimation Storyboard.TargetName="ThumbVisual"
                                                                                     Storyboard.TargetProperty="Opacity"
                                                                                     To="0"
                                                                                     Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                </Rectangle>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="HorizontalThumbTemplate"
                                                             TargetType="Thumb">
                                                <Rectangle x:Name="ThumbVisual"
                                                           Fill="{TemplateBinding Background}"
                                                           RadiusX="{Binding Height, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource CapsuleCornerRadiusConverter}, ConverterParameter={ThemeResource ScrollBarThumbStrokeThickness}}"
                                                           RadiusY="{Binding Height, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource CapsuleCornerRadiusConverter}, ConverterParameter={ThemeResource ScrollBarThumbStrokeThickness}}"
                                                           Stroke="{TemplateBinding BorderBrush}"
                                                           StrokeThickness="{ThemeResource ScrollBarThumbStrokeThickness}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ThumbVisual"
                                                                                                   Storyboard.TargetProperty="Fill">
                                                                        <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarColorChangeDuration}"
                                                                                                Value="{ThemeResource ScrollBarThumbFillDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimation Storyboard.TargetName="ThumbVisual"
                                                                                     Storyboard.TargetProperty="Opacity"
                                                                                     To="0"
                                                                                     Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                </Rectangle>
                                            </ControlTemplate>
                                        </Grid.Resources>

                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />

                                                <VisualState x:Name="Disabled">
                                                    <VisualState.Setters>
                                                        <Setter Target="Root.Background" Value="{ThemeResource ScrollBarBackgroundDisabled}" />
                                                        <Setter Target="Root.BorderBrush" Value="{ThemeResource ScrollBarBorderBrushDisabled}" />
                                                        <Setter Target="Root.Opacity" Value="0.5" />
                                                        <Setter Target="HorizontalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokeDisabled}" />
                                                        <Setter Target="VerticalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokeDisabled}" />
                                                        <Setter Target="HorizontalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillDisabled}" />
                                                        <Setter Target="VerticalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillDisabled}" />
                                                        <Setter Target="HorizontalPanningThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackgroundDisabled}" />
                                                        <Setter Target="VerticalPanningThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackgroundDisabled}" />

                                                    </VisualState.Setters>
                                                </VisualState>

                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="ScrollingIndicatorStates">
                                                <VisualState x:Name="TouchIndicator">
                                                    <VisualState.Setters>
                                                        <Setter Target="HorizontalRoot.Visibility" Value="Collapsed" />
                                                        <Setter Target="VerticalRoot.Visibility" Value="Collapsed" />
                                                        <Setter Target="HorizontalPanningRoot.Opacity" Value="1" />
                                                        <Setter Target="VerticalPanningRoot.Opacity" Value="1" />

                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="MouseIndicator">
                                                    <VisualState.Setters>
                                                        <Setter Target="HorizontalPanningRoot.Visibility" Value="Collapsed" />
                                                        <Setter Target="VerticalPanningRoot.Visibility" Value="Collapsed" />
                                                        <Setter Target="HorizontalRoot.IsHitTestVisible" Value="True" />
                                                        <Setter Target="VerticalRoot.IsHitTestVisible" Value="True" />
                                                        <Setter Target="HorizontalThumb.Opacity" Value="1" />
                                                        <Setter Target="VerticalThumb.Opacity" Value="1" />

                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="NoIndicator">
                                                    <VisualState.Setters>
                                                        <Setter Target="HorizontalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />
                                                        <Setter Target="VerticalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />

                                                    </VisualState.Setters>

                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="VerticalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalThumb"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalThumb"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="VerticalThumb"
                                                                                       Storyboard.TargetProperty="Width">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                  Value="{StaticResource ScrollBarVerticalThumbMinWidth}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="VerticalThumbTransform"
                                                                                       Storyboard.TargetProperty="X">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                  Value="{StaticResource ScrollBarThumbOffset}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="HorizontalThumb"
                                                                                       Storyboard.TargetProperty="Height">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                  Value="{StaticResource ScrollBarHorizontalThumbMinHeight}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="HorizontalThumbTransform"
                                                                                       Storyboard.TargetProperty="Y">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                  Value="{StaticResource ScrollBarThumbOffset}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalRoot"
                                                                                       Storyboard.TargetProperty="Visibility">
                                                            <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarOpacityChangeDuration}">
                                                                <DiscreteObjectKeyFrame.Value>
                                                                    <Visibility>Collapsed</Visibility>
                                                                </DiscreteObjectKeyFrame.Value>
                                                            </DiscreteObjectKeyFrame>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalRoot"
                                                                                       Storyboard.TargetProperty="Visibility">
                                                            <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarOpacityChangeDuration}">
                                                                <DiscreteObjectKeyFrame.Value>
                                                                    <Visibility>Collapsed</Visibility>
                                                                </DiscreteObjectKeyFrame.Value>
                                                            </DiscreteObjectKeyFrame>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalPanningRoot"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalPanningRoot"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalPanningRoot"
                                                                                       Storyboard.TargetProperty="Visibility">
                                                            <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarContractDuration}">
                                                                <DiscreteObjectKeyFrame.Value>
                                                                    <Visibility>Collapsed</Visibility>
                                                                </DiscreteObjectKeyFrame.Value>
                                                            </DiscreteObjectKeyFrame>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalPanningRoot"
                                                                                       Storyboard.TargetProperty="Visibility">
                                                            <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarContractDuration}">
                                                                <DiscreteObjectKeyFrame.Value>
                                                                    <Visibility>Collapsed</Visibility>
                                                                </DiscreteObjectKeyFrame.Value>
                                                            </DiscreteObjectKeyFrame>
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>

                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="ConsciousStates">

                                                <VisualStateGroup.Transitions>
                                                    <VisualTransition From="Expanded"
                                                                      To="Collapsed">

                                                        <Storyboard>
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="VerticalSmallIncrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="VerticalLargeIncrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="VerticalLargeDecrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="VerticalSmallDecrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="VerticalTrackRect"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="HorizontalSmallIncrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="HorizontalLargeIncrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="HorizontalLargeDecrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="HorizontalSmallDecrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="HorizontalTrackRect"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                           EnableDependentAnimation="True"
                                                                                           Storyboard.TargetName="VerticalThumb"
                                                                                           Storyboard.TargetProperty="Width">
                                                                <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                      KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                      Value="{StaticResource ScrollBarVerticalThumbMinWidth}" />
                                                            </DoubleAnimationUsingKeyFrames>
                                                            <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                           Storyboard.TargetName="VerticalThumbTransform"
                                                                                           Storyboard.TargetProperty="X">
                                                                <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                      KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                      Value="{StaticResource ScrollBarThumbOffset}" />
                                                            </DoubleAnimationUsingKeyFrames>
                                                            <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                           EnableDependentAnimation="True"
                                                                                           Storyboard.TargetName="HorizontalThumb"
                                                                                           Storyboard.TargetProperty="Height">
                                                                <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                      KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                      Value="{StaticResource ScrollBarHorizontalThumbMinHeight}" />
                                                            </DoubleAnimationUsingKeyFrames>
                                                            <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                           Storyboard.TargetName="HorizontalThumbTransform"
                                                                                           Storyboard.TargetProperty="Y">
                                                                <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                      KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                      Value="{StaticResource ScrollBarThumbOffset}" />
                                                            </DoubleAnimationUsingKeyFrames>
                                                        </Storyboard>
                                                    </VisualTransition>
                                                </VisualStateGroup.Transitions>
                                                <VisualState x:Name="Collapsed">
                                                    <VisualState.Setters>
                                                        <Setter Target="HorizontalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />
                                                        <Setter Target="VerticalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />

                                                    </VisualState.Setters>
                                                </VisualState>

                                                <VisualState x:Name="Expanded">
                                                    <VisualState.Setters>
                                                        <Setter Target="Root.Background" Value="{ThemeResource ScrollBarBackgroundPointerOver}" />
                                                        <Setter Target="Root.BorderBrush" Value="{ThemeResource ScrollBarBorderBrushPointerOver}" />
                                                        <Setter Target="HorizontalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokePointerOver}" />
                                                        <Setter Target="VerticalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokePointerOver}" />
                                                        <Setter Target="HorizontalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillPointerOver}" />
                                                        <Setter Target="VerticalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillPointerOver}" />
                                                        <Setter Target="HorizontalThumb.Background" Value="{ThemeResource ScrollBarThumbBackground}" />
                                                        <Setter Target="VerticalThumb.Background" Value="{ThemeResource ScrollBarThumbBackground}" />

                                                    </VisualState.Setters>

                                                    <Storyboard>
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <!--  Because of the blurriness caused by SCALE animation performed on the object with rounded corners, we have to use dependent animation on width to rerasterize the mask on every tick of the animation.  -->
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="VerticalThumb"
                                                                                       Storyboard.TargetProperty="Width">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarExpandDuration}"
                                                                                  Value="{StaticResource ScrollBarSize}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       Storyboard.TargetName="VerticalThumbTransform"
                                                                                       Storyboard.TargetProperty="X">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarExpandDuration}"
                                                                                  Value="0" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="HorizontalThumb"
                                                                                       Storyboard.TargetProperty="Height">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarExpandDuration}"
                                                                                  Value="{StaticResource ScrollBarSize}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       Storyboard.TargetName="HorizontalThumbTransform"
                                                                                       Storyboard.TargetProperty="Y">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarExpandDuration}"
                                                                                  Value="0" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="ExpandedWithoutAnimation">
                                                    <VisualState.Setters>
                                                        <Setter Target="Root.Background" Value="{ThemeResource ScrollBarBackgroundPointerOver}" />
                                                        <Setter Target="Root.BorderBrush" Value="{ThemeResource ScrollBarBorderBrushPointerOver}" />
                                                        <Setter Target="HorizontalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokePointerOver}" />
                                                        <Setter Target="VerticalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokePointerOver}" />
                                                        <Setter Target="HorizontalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillPointerOver}" />
                                                        <Setter Target="VerticalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillPointerOver}" />
                                                        <Setter Target="HorizontalThumb.Background" Value="{ThemeResource ScrollBarThumbBackground}" />
                                                        <Setter Target="VerticalThumb.Background" Value="{ThemeResource ScrollBarThumbBackground}" />

                                                    </VisualState.Setters>
                                                    <!--
                                                        The storyboard below cannot be moved to a transition since transitions
                                                        will not be run by the framework when animations are disabled in the system
                                                    -->

                                                    <Storyboard>
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="VerticalThumb"
                                                                                       Storyboard.TargetProperty="Width">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarSize}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       Storyboard.TargetName="VerticalThumbTransform"
                                                                                       Storyboard.TargetProperty="X">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="0" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="HorizontalThumb"
                                                                                       Storyboard.TargetProperty="Height">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarSize}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       Storyboard.TargetName="HorizontalThumbTransform"
                                                                                       Storyboard.TargetProperty="Y">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="0" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="CollapsedWithoutAnimation">
                                                    <VisualState.Setters>
                                                        <Setter Target="HorizontalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />
                                                        <Setter Target="VerticalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />

                                                    </VisualState.Setters>
                                                    <!--
                                                        The storyboard below cannot be moved to a transition since transitions
                                                        will not be run by the framework when animations are disabled in the system
                                                    -->

                                                    <Storyboard>
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="VerticalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="HorizontalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="VerticalThumb"
                                                                                       Storyboard.TargetProperty="Width">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarVerticalThumbMinWidth}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                       Storyboard.TargetName="VerticalThumbTransform"
                                                                                       Storyboard.TargetProperty="X">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarThumbOffset}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="HorizontalThumb"
                                                                                       Storyboard.TargetProperty="Height">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarHorizontalThumbMinHeight}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                       Storyboard.TargetName="HorizontalThumbTransform"
                                                                                       Storyboard.TargetProperty="Y">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarThumbOffset}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>

                                            </VisualStateGroup>

                                        </VisualStateManager.VisualStateGroups>
                                        <Grid x:Name="HorizontalRoot"
                                              Background="{TemplateBinding Background}"
                                              BorderBrush="{TemplateBinding BorderBrush}"
                                              CornerRadius="{TemplateBinding CornerRadius}"
                                              IsHitTestVisible="False">

                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <Rectangle x:Name="HorizontalTrackRect"
                                                       Grid.ColumnSpan="5"
                                                       Margin="0"
                                                       Fill="{ThemeResource ScrollBarTrackFill}"
                                                       Opacity="0"
                                                       RadiusX="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TopLeftCornerRadiusDoubleValueConverter2x}}"
                                                       RadiusY="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BottomRightCornerRadiusDoubleValueConverter2x}}"
                                                       Stroke="{ThemeResource ScrollBarTrackStroke}"
                                                       StrokeThickness="{ThemeResource ScrollBarTrackBorderThemeThickness}" />
                                            <RepeatButton x:Name="HorizontalSmallDecrease"
                                                          Grid.Column="0"
                                                          Width="{StaticResource ScrollBarSize}"
                                                          MinHeight="{StaticResource ScrollBarSize}"
                                                          Padding="{ThemeResource ScrollBarHorizontalDecreaseMargin}"
                                                          VerticalAlignment="Center"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource HorizontalDecrementTemplate}" />
                                            <RepeatButton x:Name="HorizontalLargeDecrease"
                                                          Grid.Column="1"
                                                          Width="0"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource RepeatButtonTemplate}" />
                                            <Thumb x:Name="HorizontalThumb"
                                                   Grid.Column="2"
                                                   Height="{ThemeResource ScrollBarHorizontalThumbMinHeight}"
                                                   MinWidth="{StaticResource ScrollBarHorizontalThumbMinWidth}"
                                                   AutomationProperties.AccessibilityView="Raw"
                                                   Background="{ThemeResource ScrollBarPanningThumbBackground}"
                                                   BorderBrush="{ThemeResource ScrollBarThumbBorderBrush}"
                                                   CornerRadius="{TemplateBinding CornerRadius}"
                                                   Opacity="0"
                                                   RenderTransformOrigin="0.5,1"
                                                   Template="{StaticResource HorizontalThumbTemplate}">
                                                <Thumb.RenderTransform>
                                                    <TranslateTransform x:Name="HorizontalThumbTransform" Y="{StaticResource ScrollBarThumbOffset}" />
                                                </Thumb.RenderTransform>
                                            </Thumb>
                                            <RepeatButton x:Name="HorizontalLargeIncrease"
                                                          Grid.Column="3"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource RepeatButtonTemplate}" />
                                            <RepeatButton x:Name="HorizontalSmallIncrease"
                                                          Grid.Column="4"
                                                          Width="{StaticResource ScrollBarSize}"
                                                          MinHeight="{StaticResource ScrollBarSize}"
                                                          Padding="{ThemeResource ScrollBarHorizontalIncreaseMargin}"
                                                          VerticalAlignment="Center"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource HorizontalIncrementTemplate}" />

                                        </Grid>
                                        <Grid x:Name="HorizontalPanningRoot"
                                              MinWidth="24"
                                              CornerRadius="{TemplateBinding CornerRadius}"
                                              Opacity="0"
                                              Visibility="Collapsed">
                                            <Border x:Name="HorizontalPanningThumb"
                                                    Height="2"
                                                    MinWidth="32"
                                                    Margin="0,2,0,2"
                                                    HorizontalAlignment="Left"
                                                    VerticalAlignment="Bottom"
                                                    Background="{ThemeResource ScrollBarPanningThumbBackground}"
                                                    BorderThickness="0" />

                                        </Grid>
                                        <Grid x:Name="VerticalRoot"
                                              Background="{TemplateBinding Background}"
                                              BorderBrush="{TemplateBinding BorderBrush}"
                                              IsHitTestVisible="False">

                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>
                                            <Rectangle x:Name="VerticalTrackRect"
                                                       Grid.RowSpan="5"
                                                       Margin="0"
                                                       Fill="{ThemeResource ScrollBarTrackFill}"
                                                       Opacity="0"
                                                       RadiusX="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TopLeftCornerRadiusDoubleValueConverter2x}}"
                                                       RadiusY="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BottomRightCornerRadiusDoubleValueConverter2x}}"
                                                       Stroke="{ThemeResource ScrollBarTrackStroke}"
                                                       StrokeThickness="{ThemeResource ScrollBarTrackBorderThemeThickness}" />
                                            <RepeatButton x:Name="VerticalSmallDecrease"
                                                          Grid.Row="0"
                                                          Height="{StaticResource ScrollBarSize}"
                                                          MinWidth="{StaticResource ScrollBarSize}"
                                                          Padding="{ThemeResource ScrollBarVerticalDecreaseMargin}"
                                                          HorizontalAlignment="Center"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource VerticalDecrementTemplate}" />
                                            <RepeatButton x:Name="VerticalLargeDecrease"
                                                          Grid.Row="1"
                                                          Height="0"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource RepeatButtonTemplate}" />
                                            <Thumb x:Name="VerticalThumb"
                                                   Grid.Row="2"
                                                   Width="{ThemeResource ScrollBarVerticalThumbMinWidth}"
                                                   MinHeight="{StaticResource ScrollBarVerticalThumbMinHeight}"
                                                   AutomationProperties.AccessibilityView="Raw"
                                                   Background="{ThemeResource ScrollBarPanningThumbBackground}"
                                                   BorderBrush="{ThemeResource ScrollBarThumbBorderBrush}"
                                                   CornerRadius="{TemplateBinding CornerRadius}"
                                                   Opacity="0"
                                                   RenderTransformOrigin="1,0.5"
                                                   Template="{StaticResource VerticalThumbTemplate}">
                                                <Thumb.RenderTransform>
                                                    <TranslateTransform x:Name="VerticalThumbTransform" X="{StaticResource ScrollBarThumbOffset}" />
                                                </Thumb.RenderTransform>
                                            </Thumb>
                                            <RepeatButton x:Name="VerticalLargeIncrease"
                                                          Grid.Row="3"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource RepeatButtonTemplate}" />
                                            <RepeatButton x:Name="VerticalSmallIncrease"
                                                          Grid.Row="4"
                                                          Height="{StaticResource ScrollBarSize}"
                                                          MinWidth="{StaticResource ScrollBarSize}"
                                                          Padding="{ThemeResource ScrollBarVerticalIncreaseMargin}"
                                                          HorizontalAlignment="Center"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource VerticalIncrementTemplate}" />

                                        </Grid>
                                        <Grid x:Name="VerticalPanningRoot"
                                              MinHeight="24"
                                              CornerRadius="{TemplateBinding CornerRadius}"
                                              Opacity="0"
                                              Visibility="Collapsed">
                                            <Border x:Name="VerticalPanningThumb"
                                                    Width="2"
                                                    MinHeight="32"
                                                    Margin="2,0,2,0"
                                                    HorizontalAlignment="Right"
                                                    VerticalAlignment="Top"
                                                    Background="{ThemeResource ScrollBarPanningThumbBackground}"
                                                    BorderThickness="0" />

                                        </Grid>

                                    </Grid>

                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <Style BasedOn="{StaticResource DefaultScrollBarStyle}"
                           TargetType="ScrollBar" />
                    <Style x:Key="MinScrollBarStyle"
                           TargetType="ScrollBar">
                        <Setter Property="MinWidth" Value="{StaticResource ScrollBarSize}" />
                        <Setter Property="MinHeight" Value="{StaticResource ScrollBarSize}" />
                        <Setter Property="Background" Value="{ThemeResource ScrollBarBackground}" />
                        <Setter Property="Foreground" Value="{ThemeResource ScrollBarForeground}" />
                        <Setter Property="BorderBrush" Value="{ThemeResource ScrollBarBorderBrush}" />
                        <Setter Property="IsTabStop" Value="False" />
                        <Setter Property="CornerRadius" Value="{ThemeResource ScrollBarCornerRadius}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ScrollBar">
                                    <Grid x:Name="Root">

                                        <Grid.Resources>
                                            <ControlTemplate x:Key="RepeatButtonTemplate"
                                                             TargetType="RepeatButton">
                                                <Grid x:Name="Root"
                                                      Background="Transparent">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>

                                                </Grid>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="HorizontalIncrementTemplate"
                                                             TargetType="RepeatButton">
                                                <Grid x:Name="Root"
                                                      Padding="{ThemeResource ScrollBarHorizontalIncreaseMargin}"
                                                      Background="{ThemeResource ScrollBarButtonBackground}"
                                                      BorderBrush="{ThemeResource ScrollBarButtonBorderBrush}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="PointerOver">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPointerOver}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Pressed">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPressed}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                    <FontIcon x:Name="Arrow"
                                                              FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                              FontSize="{StaticResource ScrollBarButtonArrowIconFontSize}"
                                                              Foreground="{ThemeResource ScrollBarButtonArrowForeground}"
                                                              Glyph="&#xEDDA;"
                                                              MirroredWhenRightToLeft="True"
                                                              RenderTransformOrigin="0.5, 0.5">
                                                        <FontIcon.RenderTransform>
                                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                                        </FontIcon.RenderTransform>
                                                    </FontIcon>

                                                </Grid>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="HorizontalDecrementTemplate"
                                                             TargetType="RepeatButton">
                                                <Grid x:Name="Root"
                                                      Padding="{ThemeResource ScrollBarHorizontalDecreaseMargin}"
                                                      Background="{ThemeResource ScrollBarButtonBackground}"
                                                      BorderBrush="{ThemeResource ScrollBarButtonBorderBrush}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="PointerOver">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPointerOver}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Pressed">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPressed}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                    <FontIcon x:Name="Arrow"
                                                              FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                              FontSize="{StaticResource ScrollBarButtonArrowIconFontSize}"
                                                              Foreground="{ThemeResource ScrollBarButtonArrowForeground}"
                                                              Glyph="&#xEDD9;"
                                                              MirroredWhenRightToLeft="True"
                                                              RenderTransformOrigin="0.5, 0.5">
                                                        <FontIcon.RenderTransform>
                                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                                        </FontIcon.RenderTransform>
                                                    </FontIcon>

                                                </Grid>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="VerticalIncrementTemplate"
                                                             TargetType="RepeatButton">
                                                <Grid x:Name="Root"
                                                      Padding="{ThemeResource ScrollBarVerticalIncreaseMargin}"
                                                      Background="{ThemeResource ScrollBarButtonBackground}"
                                                      BorderBrush="{ThemeResource ScrollBarButtonBorderBrush}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="PointerOver">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPointerOver}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Pressed">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPressed}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                    <FontIcon x:Name="Arrow"
                                                              FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                              FontSize="{StaticResource ScrollBarButtonArrowIconFontSize}"
                                                              Foreground="{ThemeResource ScrollBarButtonArrowForeground}"
                                                              Glyph="&#xEDDC;"
                                                              RenderTransformOrigin="0.5, 0.5">
                                                        <FontIcon.RenderTransform>
                                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                                        </FontIcon.RenderTransform>
                                                    </FontIcon>

                                                </Grid>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="VerticalDecrementTemplate"
                                                             TargetType="RepeatButton">
                                                <Grid x:Name="Root"
                                                      Padding="{ThemeResource ScrollBarVerticalDecreaseMargin}"
                                                      Background="{ThemeResource ScrollBarButtonBackground}"
                                                      BorderBrush="{ThemeResource ScrollBarButtonBorderBrush}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="PointerOver">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPointerOver}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Pressed">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundPressed}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                    <DoubleAnimationUsingKeyFrames RepeatBehavior="Forever"
                                                                                                   Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:0.016"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                        <DiscreteDoubleKeyFrame KeyTime="0:0:30"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowScalePressed}" />
                                                                    </DoubleAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow"
                                                                                                   Storyboard.TargetProperty="Foreground">
                                                                        <DiscreteObjectKeyFrame KeyTime="0"
                                                                                                Value="{ThemeResource ScrollBarButtonArrowForegroundDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                    <FontIcon x:Name="Arrow"
                                                              FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                              FontSize="{StaticResource ScrollBarButtonArrowIconFontSize}"
                                                              Foreground="{ThemeResource ScrollBarButtonArrowForeground}"
                                                              Glyph="&#xEDDB;"
                                                              RenderTransformOrigin="0.5, 0.5">
                                                        <FontIcon.RenderTransform>
                                                            <ScaleTransform x:Name="ScaleTransform" ScaleX="1" ScaleY="1" />
                                                        </FontIcon.RenderTransform>
                                                    </FontIcon>

                                                </Grid>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="VerticalThumbTemplate"
                                                             TargetType="Thumb">
                                                <Rectangle x:Name="ThumbVisual"
                                                           Fill="{TemplateBinding Background}"
                                                           RadiusX="{Binding Width, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource CapsuleCornerRadiusConverter}, ConverterParameter={ThemeResource ScrollBarThumbStrokeThickness}}"
                                                           RadiusY="{Binding Width, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource CapsuleCornerRadiusConverter}, ConverterParameter={ThemeResource ScrollBarThumbStrokeThickness}}"
                                                           Stroke="{TemplateBinding BorderBrush}"
                                                           StrokeThickness="{ThemeResource ScrollBarThumbStrokeThickness}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ThumbVisual"
                                                                                                   Storyboard.TargetProperty="Fill">
                                                                        <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarColorChangeDuration}"
                                                                                                Value="{ThemeResource ScrollBarThumbFillDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimation Storyboard.TargetName="ThumbVisual"
                                                                                     Storyboard.TargetProperty="Opacity"
                                                                                     To="0"
                                                                                     Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                </Rectangle>
                                            </ControlTemplate>
                                            <ControlTemplate x:Key="HorizontalThumbTemplate"
                                                             TargetType="Thumb">
                                                <Rectangle x:Name="ThumbVisual"
                                                           Fill="{TemplateBinding Background}"
                                                           RadiusX="{Binding Height, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource CapsuleCornerRadiusConverter}, ConverterParameter={ThemeResource ScrollBarThumbStrokeThickness}}"
                                                           RadiusY="{Binding Height, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource CapsuleCornerRadiusConverter}, ConverterParameter={ThemeResource ScrollBarThumbStrokeThickness}}"
                                                           Stroke="{TemplateBinding BorderBrush}"
                                                           StrokeThickness="{ThemeResource ScrollBarThumbStrokeThickness}">
                                                    <VisualStateManager.VisualStateGroups>
                                                        <VisualStateGroup x:Name="CommonStates">
                                                            <VisualState x:Name="Normal" />
                                                            <VisualState x:Name="Disabled">
                                                                <Storyboard>
                                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ThumbVisual"
                                                                                                   Storyboard.TargetProperty="Fill">
                                                                        <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarColorChangeDuration}"
                                                                                                Value="{ThemeResource ScrollBarThumbFillDisabled}" />
                                                                    </ObjectAnimationUsingKeyFrames>
                                                                    <DoubleAnimation Storyboard.TargetName="ThumbVisual"
                                                                                     Storyboard.TargetProperty="Opacity"
                                                                                     To="0"
                                                                                     Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                                </Storyboard>
                                                            </VisualState>
                                                        </VisualStateGroup>
                                                    </VisualStateManager.VisualStateGroups>
                                                </Rectangle>
                                            </ControlTemplate>
                                        </Grid.Resources>

                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />

                                                <VisualState x:Name="Disabled">
                                                    <VisualState.Setters>
                                                        <Setter Target="Root.Background" Value="{ThemeResource ScrollBarBackgroundDisabled}" />
                                                        <Setter Target="Root.BorderBrush" Value="{ThemeResource ScrollBarBorderBrushDisabled}" />
                                                        <Setter Target="Root.Opacity" Value="0.5" />
                                                        <Setter Target="HorizontalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokeDisabled}" />
                                                        <Setter Target="VerticalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokeDisabled}" />
                                                        <Setter Target="HorizontalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillDisabled}" />
                                                        <Setter Target="VerticalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillDisabled}" />
                                                        <Setter Target="HorizontalPanningThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackgroundDisabled}" />
                                                        <Setter Target="VerticalPanningThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackgroundDisabled}" />

                                                    </VisualState.Setters>
                                                </VisualState>

                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="ScrollingIndicatorStates">
                                                <VisualState x:Name="TouchIndicator">
                                                    <VisualState.Setters>
                                                        <Setter Target="HorizontalRoot.Visibility" Value="Collapsed" />
                                                        <Setter Target="VerticalRoot.Visibility" Value="Collapsed" />
                                                        <Setter Target="HorizontalPanningRoot.Opacity" Value="1" />
                                                        <Setter Target="VerticalPanningRoot.Opacity" Value="1" />

                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="MouseIndicator">
                                                    <VisualState.Setters>
                                                        <Setter Target="HorizontalPanningRoot.Visibility" Value="Collapsed" />
                                                        <Setter Target="VerticalPanningRoot.Visibility" Value="Collapsed" />
                                                        <Setter Target="HorizontalRoot.IsHitTestVisible" Value="True" />
                                                        <Setter Target="VerticalRoot.IsHitTestVisible" Value="True" />
                                                        <Setter Target="HorizontalThumb.Opacity" Value="1" />
                                                        <Setter Target="VerticalThumb.Opacity" Value="1" />

                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="NoIndicator">
                                                    <VisualState.Setters>
                                                        <Setter Target="HorizontalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />
                                                        <Setter Target="VerticalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />

                                                    </VisualState.Setters>

                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="VerticalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalThumb"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalThumb"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="VerticalThumb"
                                                                                       Storyboard.TargetProperty="Width">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                  Value="{StaticResource ScrollBarVerticalThumbMinWidth}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="VerticalThumbTransform"
                                                                                       Storyboard.TargetProperty="X">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                  Value="{StaticResource ScrollBarThumbOffset}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="HorizontalThumb"
                                                                                       Storyboard.TargetProperty="Height">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                  Value="{StaticResource ScrollBarHorizontalThumbMinHeight}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="HorizontalThumbTransform"
                                                                                       Storyboard.TargetProperty="Y">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                  Value="{StaticResource ScrollBarThumbOffset}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalRoot"
                                                                                       Storyboard.TargetProperty="Visibility">
                                                            <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarOpacityChangeDuration}">
                                                                <DiscreteObjectKeyFrame.Value>
                                                                    <Visibility>Collapsed</Visibility>
                                                                </DiscreteObjectKeyFrame.Value>
                                                            </DiscreteObjectKeyFrame>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalRoot"
                                                                                       Storyboard.TargetProperty="Visibility">
                                                            <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarOpacityChangeDuration}">
                                                                <DiscreteObjectKeyFrame.Value>
                                                                    <Visibility>Collapsed</Visibility>
                                                                </DiscreteObjectKeyFrame.Value>
                                                            </DiscreteObjectKeyFrame>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <DoubleAnimation Storyboard.TargetName="HorizontalPanningRoot"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation Storyboard.TargetName="VerticalPanningRoot"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalPanningRoot"
                                                                                       Storyboard.TargetProperty="Visibility">
                                                            <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarContractDuration}">
                                                                <DiscreteObjectKeyFrame.Value>
                                                                    <Visibility>Collapsed</Visibility>
                                                                </DiscreteObjectKeyFrame.Value>
                                                            </DiscreteObjectKeyFrame>
                                                        </ObjectAnimationUsingKeyFrames>
                                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalPanningRoot"
                                                                                       Storyboard.TargetProperty="Visibility">
                                                            <DiscreteObjectKeyFrame KeyTime="{StaticResource ScrollBarContractDuration}">
                                                                <DiscreteObjectKeyFrame.Value>
                                                                    <Visibility>Collapsed</Visibility>
                                                                </DiscreteObjectKeyFrame.Value>
                                                            </DiscreteObjectKeyFrame>
                                                        </ObjectAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>

                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="ConsciousStates">

                                                <VisualStateGroup.Transitions>
                                                    <VisualTransition From="Expanded"
                                                                      To="Collapsed">

                                                        <Storyboard>
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="VerticalSmallIncrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="VerticalLargeIncrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="VerticalLargeDecrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="VerticalSmallDecrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="VerticalTrackRect"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="HorizontalSmallIncrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="HorizontalLargeIncrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="HorizontalLargeDecrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="HorizontalSmallDecrease"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                             Storyboard.TargetName="HorizontalTrackRect"
                                                                             Storyboard.TargetProperty="Opacity"
                                                                             To="0"
                                                                             Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                            <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                           EnableDependentAnimation="True"
                                                                                           Storyboard.TargetName="VerticalThumb"
                                                                                           Storyboard.TargetProperty="Width">
                                                                <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                      KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                      Value="{StaticResource ScrollBarVerticalThumbMinWidth}" />
                                                            </DoubleAnimationUsingKeyFrames>
                                                            <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                           Storyboard.TargetName="VerticalThumbTransform"
                                                                                           Storyboard.TargetProperty="X">
                                                                <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                      KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                      Value="{StaticResource ScrollBarThumbOffset}" />
                                                            </DoubleAnimationUsingKeyFrames>
                                                            <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                           EnableDependentAnimation="True"
                                                                                           Storyboard.TargetName="HorizontalThumb"
                                                                                           Storyboard.TargetProperty="Height">
                                                                <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                      KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                      Value="{StaticResource ScrollBarHorizontalThumbMinHeight}" />
                                                            </DoubleAnimationUsingKeyFrames>
                                                            <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                           Storyboard.TargetName="HorizontalThumbTransform"
                                                                                           Storyboard.TargetProperty="Y">
                                                                <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                      KeyTime="{StaticResource ScrollBarContractDuration}"
                                                                                      Value="{StaticResource ScrollBarThumbOffset}" />
                                                            </DoubleAnimationUsingKeyFrames>
                                                        </Storyboard>
                                                    </VisualTransition>
                                                </VisualStateGroup.Transitions>
                                                <VisualState x:Name="Collapsed">
                                                    <VisualState.Setters>
                                                        <Setter Target="HorizontalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />
                                                        <Setter Target="VerticalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />

                                                    </VisualState.Setters>
                                                </VisualState>

                                                <VisualState x:Name="Expanded">
                                                    <VisualState.Setters>
                                                        <Setter Target="Root.Background" Value="{ThemeResource ScrollBarBackgroundPointerOver}" />
                                                        <Setter Target="Root.BorderBrush" Value="{ThemeResource ScrollBarBorderBrushPointerOver}" />
                                                        <Setter Target="HorizontalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokePointerOver}" />
                                                        <Setter Target="VerticalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokePointerOver}" />
                                                        <Setter Target="HorizontalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillPointerOver}" />
                                                        <Setter Target="VerticalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillPointerOver}" />
                                                        <Setter Target="HorizontalThumb.Background" Value="{ThemeResource ScrollBarThumbBackground}" />
                                                        <Setter Target="VerticalThumb.Background" Value="{ThemeResource ScrollBarThumbBackground}" />

                                                    </VisualState.Setters>

                                                    <Storyboard>
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="{StaticResource ScrollBarOpacityChangeDuration}" />
                                                        <!--  Because of the blurriness caused by SCALE animation performed on the object with rounded corners, we have to use dependent animation on width to rerasterize the mask on every tick of the animation.  -->
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="VerticalThumb"
                                                                                       Storyboard.TargetProperty="Width">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarExpandDuration}"
                                                                                  Value="{StaticResource ScrollBarSize}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       Storyboard.TargetName="VerticalThumbTransform"
                                                                                       Storyboard.TargetProperty="X">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarExpandDuration}"
                                                                                  Value="0" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="HorizontalThumb"
                                                                                       Storyboard.TargetProperty="Height">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarExpandDuration}"
                                                                                  Value="{StaticResource ScrollBarSize}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       Storyboard.TargetName="HorizontalThumbTransform"
                                                                                       Storyboard.TargetProperty="Y">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="{StaticResource ScrollBarExpandDuration}"
                                                                                  Value="0" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="ExpandedWithoutAnimation">
                                                    <VisualState.Setters>
                                                        <Setter Target="Root.Background" Value="{ThemeResource ScrollBarBackgroundPointerOver}" />
                                                        <Setter Target="Root.BorderBrush" Value="{ThemeResource ScrollBarBorderBrushPointerOver}" />
                                                        <Setter Target="HorizontalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokePointerOver}" />
                                                        <Setter Target="VerticalTrackRect.Stroke" Value="{ThemeResource ScrollBarTrackStrokePointerOver}" />
                                                        <Setter Target="HorizontalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillPointerOver}" />
                                                        <Setter Target="VerticalTrackRect.Fill" Value="{ThemeResource ScrollBarTrackFillPointerOver}" />
                                                        <Setter Target="HorizontalThumb.Background" Value="{ThemeResource ScrollBarThumbBackground}" />
                                                        <Setter Target="VerticalThumb.Background" Value="{ThemeResource ScrollBarThumbBackground}" />

                                                    </VisualState.Setters>
                                                    <!--
                                                        The storyboard below cannot be moved to a transition since transitions
                                                        will not be run by the framework when animations are disabled in the system
                                                    -->

                                                    <Storyboard>
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="VerticalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                         Storyboard.TargetName="HorizontalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="1"
                                                                         Duration="0" />
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="VerticalThumb"
                                                                                       Storyboard.TargetProperty="Width">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarSize}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       Storyboard.TargetName="VerticalThumbTransform"
                                                                                       Storyboard.TargetProperty="X">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="0" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="HorizontalThumb"
                                                                                       Storyboard.TargetProperty="Height">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarSize}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarExpandBeginTime}"
                                                                                       Storyboard.TargetName="HorizontalThumbTransform"
                                                                                       Storyboard.TargetProperty="Y">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="0" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="CollapsedWithoutAnimation">
                                                    <VisualState.Setters>
                                                        <Setter Target="HorizontalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />
                                                        <Setter Target="VerticalThumb.Background" Value="{ThemeResource ScrollBarPanningThumbBackground}" />

                                                    </VisualState.Setters>
                                                    <!--
                                                        The storyboard below cannot be moved to a transition since transitions
                                                        will not be run by the framework when animations are disabled in the system
                                                    -->

                                                    <Storyboard>
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="VerticalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="VerticalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="VerticalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeIncrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="HorizontalLargeDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="HorizontalSmallDecrease"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimation BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                         Storyboard.TargetName="HorizontalTrackRect"
                                                                         Storyboard.TargetProperty="Opacity"
                                                                         To="0"
                                                                         Duration="0" />
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="VerticalThumb"
                                                                                       Storyboard.TargetProperty="Width">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarVerticalThumbMinWidth}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                       Storyboard.TargetName="VerticalThumbTransform"
                                                                                       Storyboard.TargetProperty="X">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarThumbOffset}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                       EnableDependentAnimation="True"
                                                                                       Storyboard.TargetName="HorizontalThumb"
                                                                                       Storyboard.TargetProperty="Height">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarHorizontalThumbMinHeight}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                        <DoubleAnimationUsingKeyFrames BeginTime="{StaticResource ScrollBarContractBeginTime}"
                                                                                       Storyboard.TargetName="HorizontalThumbTransform"
                                                                                       Storyboard.TargetProperty="Y">
                                                            <SplineDoubleKeyFrame KeySpline="0,0,0,1"
                                                                                  KeyTime="0"
                                                                                  Value="{StaticResource ScrollBarThumbOffset}" />
                                                        </DoubleAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </VisualState>

                                            </VisualStateGroup>

                                        </VisualStateManager.VisualStateGroups>
                                        <Grid x:Name="HorizontalRoot"
                                              Background="{TemplateBinding Background}"
                                              BorderBrush="{TemplateBinding BorderBrush}"
                                              CornerRadius="{TemplateBinding CornerRadius}"
                                              IsHitTestVisible="False">

                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <Rectangle x:Name="HorizontalTrackRect"
                                                       Grid.ColumnSpan="5"
                                                       Margin="0"
                                                       Fill="{ThemeResource ScrollBarTrackFill}"
                                                       Opacity="0"
                                                       RadiusX="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TopLeftCornerRadiusDoubleValueConverter2x}}"
                                                       RadiusY="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BottomRightCornerRadiusDoubleValueConverter2x}}"
                                                       Stroke="{ThemeResource ScrollBarTrackStroke}"
                                                       StrokeThickness="{ThemeResource ScrollBarTrackBorderThemeThickness}" />
                                            <RepeatButton x:Name="HorizontalSmallDecrease"
                                                          Grid.Column="0"
                                                          Width="{StaticResource ScrollBarSize}"
                                                          MinHeight="{StaticResource ScrollBarSize}"
                                                          Padding="{ThemeResource ScrollBarHorizontalDecreaseMargin}"
                                                          VerticalAlignment="Center"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource HorizontalDecrementTemplate}" />
                                            <RepeatButton x:Name="HorizontalLargeDecrease"
                                                          Grid.Column="1"
                                                          Width="0"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource RepeatButtonTemplate}" />
                                            <Thumb x:Name="HorizontalThumb"
                                                   Grid.Column="2"
                                                   Height="{ThemeResource ScrollBarHorizontalThumbMinHeight}"
                                                   MinWidth="14"
                                                   AutomationProperties.AccessibilityView="Raw"
                                                   Background="{ThemeResource ScrollBarPanningThumbBackground}"
                                                   BorderBrush="{ThemeResource ScrollBarThumbBorderBrush}"
                                                   CornerRadius="{TemplateBinding CornerRadius}"
                                                   Opacity="0"
                                                   RenderTransformOrigin="0.5,1"
                                                   Template="{StaticResource HorizontalThumbTemplate}">
                                                <Thumb.RenderTransform>
                                                    <TranslateTransform x:Name="HorizontalThumbTransform" Y="{StaticResource ScrollBarThumbOffset}" />
                                                </Thumb.RenderTransform>
                                            </Thumb>
                                            <RepeatButton x:Name="HorizontalLargeIncrease"
                                                          Grid.Column="3"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource RepeatButtonTemplate}" />
                                            <RepeatButton x:Name="HorizontalSmallIncrease"
                                                          Grid.Column="4"
                                                          Width="{StaticResource ScrollBarSize}"
                                                          MinHeight="{StaticResource ScrollBarSize}"
                                                          Padding="{ThemeResource ScrollBarHorizontalIncreaseMargin}"
                                                          VerticalAlignment="Center"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource HorizontalIncrementTemplate}" />

                                        </Grid>
                                        <Grid x:Name="HorizontalPanningRoot"
                                              MinWidth="24"
                                              CornerRadius="{TemplateBinding CornerRadius}"
                                              Opacity="0"
                                              Visibility="Collapsed">
                                            <Border x:Name="HorizontalPanningThumb"
                                                    Height="2"
                                                    MinWidth="32"
                                                    Margin="0,2,0,2"
                                                    HorizontalAlignment="Left"
                                                    VerticalAlignment="Bottom"
                                                    Background="{ThemeResource ScrollBarPanningThumbBackground}"
                                                    BorderThickness="0" />

                                        </Grid>
                                        <Grid x:Name="VerticalRoot"
                                              Background="{TemplateBinding Background}"
                                              BorderBrush="{TemplateBinding BorderBrush}"
                                              IsHitTestVisible="False">

                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>
                                            <Rectangle x:Name="VerticalTrackRect"
                                                       Grid.RowSpan="5"
                                                       Margin="0"
                                                       Fill="{ThemeResource ScrollBarTrackFill}"
                                                       Opacity="0"
                                                       RadiusX="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TopLeftCornerRadiusDoubleValueConverter2x}}"
                                                       RadiusY="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BottomRightCornerRadiusDoubleValueConverter2x}}"
                                                       Stroke="{ThemeResource ScrollBarTrackStroke}"
                                                       StrokeThickness="{ThemeResource ScrollBarTrackBorderThemeThickness}" />
                                            <RepeatButton x:Name="VerticalSmallDecrease"
                                                          Grid.Row="0"
                                                          Height="{StaticResource ScrollBarSize}"
                                                          MinWidth="{StaticResource ScrollBarSize}"
                                                          Padding="{ThemeResource ScrollBarVerticalDecreaseMargin}"
                                                          HorizontalAlignment="Center"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource VerticalDecrementTemplate}" />
                                            <RepeatButton x:Name="VerticalLargeDecrease"
                                                          Grid.Row="1"
                                                          Height="0"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource RepeatButtonTemplate}" />
                                            <Thumb x:Name="VerticalThumb"
                                                   Grid.Row="2"
                                                   Width="{ThemeResource ScrollBarVerticalThumbMinWidth}"
                                                   MinHeight="14"
                                                   AutomationProperties.AccessibilityView="Raw"
                                                   Background="{ThemeResource ScrollBarPanningThumbBackground}"
                                                   BorderBrush="{ThemeResource ScrollBarThumbBorderBrush}"
                                                   CornerRadius="{TemplateBinding CornerRadius}"
                                                   Opacity="0"
                                                   RenderTransformOrigin="1,0.5"
                                                   Template="{StaticResource VerticalThumbTemplate}">
                                                <Thumb.RenderTransform>
                                                    <TranslateTransform x:Name="VerticalThumbTransform" X="{StaticResource ScrollBarThumbOffset}" />
                                                </Thumb.RenderTransform>
                                            </Thumb>
                                            <RepeatButton x:Name="VerticalLargeIncrease"
                                                          Grid.Row="3"
                                                          HorizontalAlignment="Stretch"
                                                          VerticalAlignment="Stretch"
                                                          AllowFocusOnInteraction="False"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource RepeatButtonTemplate}" />
                                            <RepeatButton x:Name="VerticalSmallIncrease"
                                                          Grid.Row="4"
                                                          Height="{StaticResource ScrollBarSize}"
                                                          MinWidth="{StaticResource ScrollBarSize}"
                                                          Padding="{ThemeResource ScrollBarVerticalIncreaseMargin}"
                                                          HorizontalAlignment="Center"
                                                          Interval="50"
                                                          IsTabStop="False"
                                                          Opacity="0"
                                                          Template="{StaticResource VerticalIncrementTemplate}" />

                                        </Grid>
                                        <Grid x:Name="VerticalPanningRoot"
                                              MinHeight="24"
                                              CornerRadius="{TemplateBinding CornerRadius}"
                                              Opacity="0"
                                              Visibility="Collapsed">
                                            <Border x:Name="VerticalPanningThumb"
                                                    Width="2"
                                                    MinHeight="32"
                                                    Margin="2,0,2,0"
                                                    HorizontalAlignment="Right"
                                                    VerticalAlignment="Top"
                                                    Background="{ThemeResource ScrollBarPanningThumbBackground}"
                                                    BorderThickness="0" />

                                        </Grid>

                                    </Grid>

                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>

                    <!--  PipsPager styles  -->
                    <Style x:Key="DefaultPipsPagerStyle"
                           TargetType="controls:PipsPager">
                        <Setter Property="Background" Value="Transparent" />
                        <Setter Property="HorizontalAlignment" Value="Left" />
                        <Setter Property="VerticalAlignment" Value="Top" />
                        <Setter Property="IsTabStop" Value="False" />
                        <Setter Property="PreviousButtonStyle" Value="{StaticResource PipsPagerPreviousPageButtonStyle}" />
                        <Setter Property="NextButtonStyle" Value="{StaticResource PipsPagerNextPageButtonStyle}" />
                        <Setter Property="SelectedPipStyle" Value="{StaticResource PipsPagerSelectedPipButtonStyle}" />
                        <Setter Property="NormalPipStyle" Value="{StaticResource PipsPagerNormalPipButtonStyle}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="controls:PipsPager">
                                    <StackPanel x:Name="RootPanel"
                                                Background="{TemplateBinding Background}"
                                                Orientation="{TemplateBinding Orientation}">

                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="PreviousPageButtonVisibilityStates">
                                                <VisualState x:Name="PreviousPageButtonVisible" />
                                                <VisualState x:Name="PreviousPageButtonHidden">
                                                    <VisualState.Setters>
                                                        <Setter Target="PreviousPageButton.Opacity" Value="0" />

                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="PreviousPageButtonCollapsed">
                                                    <VisualState.Setters>
                                                        <Setter Target="PreviousPageButton.Visibility" Value="Collapsed" />

                                                    </VisualState.Setters>
                                                </VisualState>

                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="PreviousPageButtonIsEnabledStates">
                                                <VisualState x:Name="PreviousPageButtonEnabled" />
                                                <VisualState x:Name="PreviousPageButtonDisabled">
                                                    <VisualState.Setters>
                                                        <Setter Target="PreviousPageButton.IsEnabled" Value="False" />

                                                    </VisualState.Setters>
                                                </VisualState>

                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="NextPageButtonVisibilityStates">
                                                <VisualState x:Name="NextPageButtonVisible" />
                                                <VisualState x:Name="NextPageButtonHidden">
                                                    <VisualState.Setters>
                                                        <Setter Target="NextPageButton.Opacity" Value="0" />

                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="NextPageButtonCollapsed">
                                                    <VisualState.Setters>
                                                        <Setter Target="NextPageButton.Visibility" Value="Collapsed" />

                                                    </VisualState.Setters>
                                                </VisualState>

                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="NextPageButtonIsEnabledStates">
                                                <VisualState x:Name="NextPageButtonEnabled" />
                                                <VisualState x:Name="NextPageButtonDisabled">
                                                    <VisualState.Setters>
                                                        <Setter Target="NextPageButton.IsEnabled" Value="False" />

                                                    </VisualState.Setters>
                                                </VisualState>

                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="RootPanelOrientationStates">
                                                <VisualState x:Name="VerticalOrientationView" />
                                                <VisualState x:Name="HorizontalOrientationView">
                                                    <VisualState.Setters>
                                                        <Setter Target="RootPanel.Orientation" Value="Horizontal" />
                                                        <Setter Target="PreviousPageButton.(ToolTipService.Placement)" Value="Left" />
                                                        <Setter Target="PreviousPageButton.RenderTransformOrigin" Value="0.5, 0.5" />
                                                        <Setter Target="PreviousPageButton.RenderTransform">
                                                            <Setter.Value>
                                                                <RotateTransform Angle="-90" />
                                                            </Setter.Value>
                                                        </Setter>
                                                        <Setter Target="NextPageButton.(ToolTipService.Placement)" Value="Right" />
                                                        <Setter Target="NextPageButton.RenderTransformOrigin" Value="0.5, 0.5" />
                                                        <Setter Target="NextPageButton.RenderTransform">
                                                            <Setter.Value>
                                                                <RotateTransform Angle="-90" />
                                                            </Setter.Value>
                                                        </Setter>

                                                    </VisualState.Setters>
                                                </VisualState>

                                            </VisualStateGroup>

                                        </VisualStateManager.VisualStateGroups>
                                        <Button x:Name="PreviousPageButton"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Style="{TemplateBinding PreviousButtonStyle}"
                                                ToolTipService.Placement="Top"
                                                ToolTipService.ToolTip="{Binding ElementName=PreviousPageButton, Path=(AutomationProperties.Name)}" />
                                        <ScrollViewer x:Name="PipsPagerScrollViewer"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      HorizontalScrollBarVisibility="Hidden"
                                                      HorizontalScrollMode="Disabled"
                                                      IsHorizontalScrollChainingEnabled="False"
                                                      IsVerticalScrollChainingEnabled="False"
                                                      VerticalScrollBarVisibility="Hidden"
                                                      VerticalScrollMode="Disabled">
                                            <controls:ItemsRepeater x:Name="PipsPagerItemsRepeater"
                                                                    ItemsSource="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.PipsPagerItems}">
                                                <controls:ItemsRepeater.Layout>
                                                    <controls:StackLayout Orientation="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Orientation}" />
                                                </controls:ItemsRepeater.Layout>
                                                <controls:ItemsRepeater.ItemTemplate>
                                                    <DataTemplate>
                                                        <Button IsTabStop="False" />
                                                    </DataTemplate>
                                                </controls:ItemsRepeater.ItemTemplate>
                                            </controls:ItemsRepeater>
                                        </ScrollViewer>
                                        <Button x:Name="NextPageButton"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Style="{TemplateBinding NextButtonStyle}"
                                                ToolTipService.Placement="Bottom"
                                                ToolTipService.ToolTip="{Binding ElementName=NextPageButton, Path=(AutomationProperties.Name)}" />
                                    </StackPanel>

                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    <Style BasedOn="{StaticResource DefaultPipsPagerStyle}"
                           TargetType="controls:PipsPager" />

                    <!--  Pivot styles  -->
                    <Style x:Key="ThinPivotHeaderItemStyle"
                           BasedOn="{StaticResource DefaultPivotHeaderItemStyle}"
                           TargetType="PivotHeaderItem">
                        <Setter Property="Height" Value="30" />
                    </Style>
                    <Style BasedOn="{StaticResource ThinPivotHeaderItemStyle}"
                           TargetType="PivotHeaderItem" />
                    <Thickness x:Key="PivotNavButtonMargin">0,-6,0,0</Thickness>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
            <!--  Font families  -->
            <FontFamily x:Key="FontAwesomeBrand">ms-appx:///Assets/Fonts/FontAwesomeBrand6.otf#Font Awesome 6 Brands</FontFamily>
            <FontFamily x:Key="FontAwesome">ms-appx:///Assets/Fonts/FontAwesomeRegular6.otf#Font Awesome 6 Free</FontFamily>
            <FontFamily x:Key="FontAwesomeSolid">ms-appx:///Assets/Fonts/FontAwesomeSolid6.otf#Font Awesome 6 Free</FontFamily>
        </ResourceDictionary>
    </Application.Resources>
</Application>
