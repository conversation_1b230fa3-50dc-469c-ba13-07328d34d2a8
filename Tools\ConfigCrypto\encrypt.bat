@echo off
REM CollapseLauncher Configuration Encryption Tool (Windows)
REM Quick encryption script for configuration files

setlocal enabledelayedexpansion

echo ========================================
echo CollapseLauncher Config Encryption Tool
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Check if config file is provided as argument
if "%~1"=="" (
    set /p CONFIG_FILE="Enter configuration file path (plaintext): "
) else (
    set CONFIG_FILE=%~1
)

REM Check if config file exists
if not exist "%CONFIG_FILE%" (
    echo Error: Configuration file not found: %CONFIG_FILE%
    pause
    exit /b 1
)

REM Check if master key file exists
set MASTER_KEY_FILE=..\MasterKeyGenerator\output\config_master.json
if not exist "%MASTER_KEY_FILE%" (
    set /p MASTER_KEY_FILE="Enter master key file path: "
    if not exist "!MASTER_KEY_FILE!" (
        echo Error: Master key file not found: !MASTER_KEY_FILE!
        pause
        exit /b 1
    )
)

REM Generate output filename
for %%F in ("%CONFIG_FILE%") do (
    set OUTPUT_FILE=%%~dpnF_encrypted%%~xF
)

echo.
echo Configuration: %CONFIG_FILE%
echo Master Key: %MASTER_KEY_FILE%
echo Output: %OUTPUT_FILE%
echo.

REM Check dependencies
echo Checking dependencies...
python -c "import cryptography, json" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Dependencies OK
echo.

REM Analyze the configuration first
echo Analyzing configuration file...
python field_analyzer.py "%CONFIG_FILE%" --brief

echo.
echo Starting encryption...
python crypto_tool.py encrypt-config --input "%CONFIG_FILE%" --output "%OUTPUT_FILE%" --master-key "%MASTER_KEY_FILE%"

if errorlevel 1 (
    echo.
    echo Error: Encryption failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Encryption completed successfully!
echo ========================================
echo Output file: %OUTPUT_FILE%
echo.

REM Ask if user wants to open the encrypted file
set /p OPEN_FILE="Open encrypted file? (y/n): "
if /i "%OPEN_FILE%"=="y" (
    start notepad "%OUTPUT_FILE%"
)

pause
