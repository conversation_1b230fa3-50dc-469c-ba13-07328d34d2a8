#!/usr/bin/env python3
"""
Test script to verify the fixed encryption implementation:
1. Uses ServeV3 format for encryption
2. Updates Hash field after encryption
"""

import json
import tempfile
import base64
from pathlib import Path

from crypto_tool import CollapseDecryptor
import sys
import os

# Add MasterKeyGenerator to path
sys.path.append(str(Path(__file__).parent.parent / "MasterKeyGenerator"))
from generate_master_key import Master<PERSON>eyGenerator


def test_serve_v3_encryption():
    """Test that encryption produces ServeV3 format data."""
    
    print("=" * 60)
    print("Testing ServeV3 Format Encryption")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Generate master key
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        master_config = generator.create_master_key_config()
        
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            
            # Test encryption
            test_plaintext = "https://example.com/test/api"
            encrypted_data = decryptor.encrypt_field(test_plaintext)
            
            print(f"Original: {test_plaintext}")
            print(f"Encrypted: {encrypted_data[:50]}...")
            
            # Verify it's base64
            try:
                decoded_data = base64.b64decode(encrypted_data)
                print(f"✓ Valid base64 format")
                print(f"  Decoded length: {len(decoded_data)} bytes")
            except Exception as e:
                print(f"❌ Invalid base64: {e}")
                return False
            
            # Check for ServeV3 signature
            if len(decoded_data) >= 8:
                signature = int.from_bytes(decoded_data[:8], byteorder='little')
                collapse_signature = 7310310183885631299
                
                if signature == collapse_signature:
                    print(f"✓ ServeV3 signature detected: {signature}")
                else:
                    print(f"❌ Wrong signature: {signature} (expected: {collapse_signature})")
                    return False
            else:
                print(f"❌ Data too short for ServeV3 format")
                return False
            
            # Test decryption
            try:
                decrypted_data = decryptor.decrypt_field(encrypted_data)
                if decrypted_data == test_plaintext:
                    print(f"✓ Encryption/Decryption cycle successful")
                    print(f"  Decrypted: {decrypted_data}")
                    return True
                else:
                    print(f"❌ Decryption mismatch:")
                    print(f"  Expected: {test_plaintext}")
                    print(f"  Got: {decrypted_data}")
                    return False
            except Exception as e:
                print(f"❌ Decryption failed: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Encryption test failed: {e}")
            return False


def test_hash_update():
    """Test that Hash field is updated after encryption."""
    
    print("\n" + "=" * 60)
    print("Testing Hash Field Update")
    print("=" * 60)
    
    # Create test configuration with Hash field
    test_config = {
        "ProfileName": "TestProfile",
        "LauncherResourceURL": "https://example.com/resources",
        "LauncherNewsURL": "https://news.example.com/api",
        "Hash": 1234567890,  # Original hash
        "PlaintextField": "This should not be encrypted"
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Generate master key
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        master_config = generator.create_master_key_config()
        
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        # Save test config
        input_config_path = temp_path / "test_config.json"
        with open(input_config_path, 'w') as f:
            json.dump(test_config, f, indent=2)
        
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            
            print(f"Original Hash: {test_config['Hash']}")
            
            # Encrypt configuration
            output_config_path = temp_path / "encrypted_config.json"
            encrypted_config = decryptor.encrypt_config_file(
                str(input_config_path),
                str(output_config_path)
            )
            
            # Check if Hash was updated
            new_hash = encrypted_config.get('Hash')
            original_hash = test_config['Hash']
            
            if new_hash != original_hash:
                print(f"✓ Hash updated successfully")
                print(f"  Original: {original_hash}")
                print(f"  New: {new_hash}")
                
                # Verify the encrypted file also has the new hash
                with open(output_config_path, 'r') as f:
                    saved_config = json.load(f)
                
                if saved_config.get('Hash') == new_hash:
                    print(f"✓ Hash correctly saved to file")
                    return True
                else:
                    print(f"❌ Hash not saved correctly to file")
                    return False
            else:
                print(f"❌ Hash was not updated")
                print(f"  Both old and new: {original_hash}")
                return False
                
        except Exception as e:
            print(f"❌ Hash update test failed: {e}")
            return False


def test_config_without_hash():
    """Test encryption of configuration without Hash field."""
    
    print("\n" + "=" * 60)
    print("Testing Config Without Hash Field")
    print("=" * 60)
    
    # Create test configuration without Hash field
    test_config = {
        "ProfileName": "TestProfile",
        "LauncherResourceURL": "https://example.com/resources",
        "PlaintextField": "This should not be encrypted"
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Generate master key
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        master_config = generator.create_master_key_config()
        
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        # Save test config
        input_config_path = temp_path / "test_config.json"
        with open(input_config_path, 'w') as f:
            json.dump(test_config, f, indent=2)
        
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            
            print("Config has no Hash field initially")
            
            # Encrypt configuration
            output_config_path = temp_path / "encrypted_config.json"
            encrypted_config = decryptor.encrypt_config_file(
                str(input_config_path),
                str(output_config_path)
            )
            
            # Check that no Hash field was added
            if 'Hash' not in encrypted_config:
                print(f"✓ No Hash field added (correct behavior)")
                return True
            else:
                print(f"❌ Hash field was unexpectedly added: {encrypted_config['Hash']}")
                return False
                
        except Exception as e:
            print(f"❌ Config without hash test failed: {e}")
            return False


def test_serve_v3_attributes():
    """Test ServeV3 format attributes (encryption flag, compression type)."""
    
    print("\n" + "=" * 60)
    print("Testing ServeV3 Attributes")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Generate master key
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        master_config = generator.create_master_key_config()
        
        master_key_path = temp_path / "config_master.json"
        with open(master_key_path, 'w') as f:
            json.dump(master_config, f, indent=2)
        
        try:
            decryptor = CollapseDecryptor(str(master_key_path))
            
            # Test encryption
            test_plaintext = "https://example.com/test/api"
            encrypted_data = decryptor.encrypt_field(test_plaintext)
            
            # Decode and analyze ServeV3 header
            decoded_data = base64.b64decode(encrypted_data)
            
            if len(decoded_data) >= 32:
                # Parse header
                signature = int.from_bytes(decoded_data[0:8], byteorder='little')
                attributes = int.from_bytes(decoded_data[8:16], byteorder='little')
                compressed_size = int.from_bytes(decoded_data[16:24], byteorder='little')
                decompressed_size = int.from_bytes(decoded_data[24:32], byteorder='little')
                
                compression_type = attributes & 0xFF
                is_encrypted = (attributes >> 8) & 0xFF
                
                print(f"ServeV3 Header Analysis:")
                print(f"  Signature: {signature} (✓ correct)")
                print(f"  Compression Type: {compression_type} (0=None)")
                print(f"  Is Encrypted: {is_encrypted} (1=Yes)")
                print(f"  Compressed Size: {compressed_size} bytes")
                print(f"  Decompressed Size: {decompressed_size} bytes")
                
                # Verify encryption flag is set
                if is_encrypted == 1:
                    print(f"✓ Encryption flag correctly set")
                    return True
                else:
                    print(f"❌ Encryption flag not set")
                    return False
            else:
                print(f"❌ ServeV3 header too short")
                return False
                
        except Exception as e:
            print(f"❌ ServeV3 attributes test failed: {e}")
            return False


def main():
    """Run all tests."""
    print("Testing Fixed Encryption Implementation")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: ServeV3 format encryption
    if test_serve_v3_encryption():
        success_count += 1
    
    # Test 2: Hash field update
    if test_hash_update():
        success_count += 1
    
    # Test 3: Config without hash
    if test_config_without_hash():
        success_count += 1
    
    # Test 4: ServeV3 attributes
    if test_serve_v3_attributes():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 All tests passed! Fixed encryption implementation is working correctly.")
        print("\nKey improvements:")
        print("  ✓ Uses ServeV3 format for encryption")
        print("  ✓ Updates Hash field after encryption")
        print("  ✓ Proper RSA chunk encryption")
        print("  ✓ Correct ServeV3 header attributes")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
