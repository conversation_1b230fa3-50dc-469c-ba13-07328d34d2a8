﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:labs="using:CommunityToolkit.Labs.WinUI.Labs.MarkdownTextBlock">
    <Style BasedOn="{StaticResource DefaultMarkdownTextBlockStyle}"
           TargetType="labs:MarkdownTextBlock" />
    <Style x:Key="DefaultMarkdownTextBlockStyle"
           TargetType="labs:MarkdownTextBlock">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="labs:MarkdownTextBlock">
                    <Grid x:Name="MarkdownContainer"
                          Padding="{TemplateBinding Padding}"
                          HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                          VerticalAlignment="{TemplateBinding VerticalAlignment}"
                          Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          CornerRadius="{TemplateBinding CornerRadius}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
