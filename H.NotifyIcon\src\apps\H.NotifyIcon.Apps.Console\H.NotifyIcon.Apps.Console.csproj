<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net4.8;net8.0-windows</TargetFrameworks>
    <OutputType>Exe</OutputType>
    <LangVersion>preview</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
  </PropertyGroup>

  <ItemGroup>
    <Using Remove="System.Net.Http" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
    <PackageReference Include="H.Resources.Generator" Version="1.6.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\libs\H.NotifyIcon\H.NotifyIcon.csproj" />
  </ItemGroup>

</Project>