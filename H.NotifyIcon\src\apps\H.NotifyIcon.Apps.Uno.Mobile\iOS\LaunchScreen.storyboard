<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="11762" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" colorMatched="YES" initialViewController="5" launchScreen="YES">
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="11757"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <scene sceneID="4">
            <objects>
                <viewController id="5" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="2"/>
                        <viewControllerLayoutGuide type="bottom" id="3"/>
                    </layoutGuides>
                    <view contentMode="scaleToFill" id="55" key="view">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <color key="backgroundColor" colorSpace="custom" customColorSpace="sRGB" red="1" green="1" blue="1" alpha="1"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" id="56" translatesAutoresizingMaskIntoConstraints="NO" image="SplashScreen.png">
                                <rect key="frame" x="58" y="120" width="204" height="242"/>
                                <constraints>
                                    <constraint id="91" firstItem="56" firstAttribute="width" constant="204"/>
                                    <constraint id="92" firstItem="56" firstAttribute="height" constant="242"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <constraints>
                            <constraint id="80" firstItem="55" firstAttribute="centerY" secondItem="56" secondAttribute="centerY" constant="0"/>
                            <constraint id="81" firstItem="55" firstAttribute="centerX" secondItem="56" secondAttribute="centerX" constant="0"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="7" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-300" y="-555"/>
        </scene>
    </scenes>
    <resources>
        <image name="<EMAIL>" width="40" height="40"/>
        <image name="<EMAIL>" width="60" height="60"/>
        <image name="<EMAIL>" width="76" height="76"/>
        <image name="<EMAIL>" width="40" height="40"/>
        <image name="Icon-Small.png" width="29" height="29"/>
        <image name="SplashScreen.png" width="620" height="300"/>
    </resources>
    <simulatedMetricsContainer key="defaultSimulatedMetrics">
        <simulatedStatusBarMetrics key="statusBar"/>
        <simulatedOrientationMetrics key="orientation"/>
        <simulatedScreenMetrics key="destination"/>
    </simulatedMetricsContainer>
</document>
