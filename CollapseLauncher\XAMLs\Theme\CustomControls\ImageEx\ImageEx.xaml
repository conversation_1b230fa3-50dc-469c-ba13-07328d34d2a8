﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="using:ImageEx"
                    xmlns:lottie="using:CollapseLauncher.AnimatedVisuals.Lottie">
    <Style TargetType="controls:ImageEx">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{ThemeResource ApplicationForegroundThemeBrush}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="LazyLoadingThreshold" Value="300" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="controls:ImageEx">
                    <Grid Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          CornerRadius="{TemplateBinding CornerRadius}">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Failed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Image"
                                                                       Storyboard.TargetProperty="Opacity">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="0" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderImage"
                                                                       Storyboard.TargetProperty="Opacity">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="1" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Loading">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Image"
                                                                       Storyboard.TargetProperty="Opacity">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="0" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PlaceholderImage"
                                                                       Storyboard.TargetProperty="Opacity">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="1" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Loaded">
                                    <Storyboard>
                                        <DoubleAnimation AutoReverse="False"
                                                         BeginTime="0"
                                                         Storyboard.TargetName="Image"
                                                         Storyboard.TargetProperty="Opacity"
                                                         From="0"
                                                         To="1"
                                                         Duration="0:0:0.25" />
                                        <DoubleAnimation AutoReverse="False"
                                                         BeginTime="0"
                                                         Storyboard.TargetName="PlaceholderImage"
                                                         Storyboard.TargetProperty="Opacity"
                                                         From="1"
                                                         To="0"
                                                         Duration="0:0:0.25" />
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unloaded" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <AnimatedVisualPlayer Name="PlaceholderImage"
                                              MaxWidth="512"
                                              MaxHeight="512"
                                              Opacity="1.0">
                            <lottie:LoadingSprite />
                        </AnimatedVisualPlayer>
                        <Image Name="Image"
                               HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                               VerticalAlignment="{TemplateBinding VerticalAlignment}"
                               NineGrid="{TemplateBinding NineGrid}"
                               Opacity="0.0"
                               Stretch="{TemplateBinding Stretch}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>