﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable IdentifierTypo  -->
<!--  <PERSON><PERSON><PERSON>per disable UnusedMember.Local  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<Page x:Class="CollapseLauncher.Pages.OOBE.OOBESelectGame"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:animatedvisuals="using:Microsoft.UI.Xaml.Controls.AnimatedVisuals"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:helper="using:Hi3Helper"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      Background="Transparent"
      mc:Ignorable="d">
    <Grid>
        <Frame x:Name="BackgroundFrame"
               HorizontalAlignment="Stretch"
               VerticalAlignment="Stretch" />
        <Button x:Name="NextPage"
                Margin="32"
                HorizontalAlignment="Center"
                VerticalAlignment="Bottom"
                Click="NextPage_Click"
                IsEnabled="False"
                Opacity="0"
                Style="{ThemeResource TransparentDefaultButtonStyle}">
            <Button.OpacityTransition>
                <ScalarTransition />
            </Button.OpacityTransition>
            <Button.Content>
                <StackPanel>
                    <TextBlock FontSize="16"
                               FontStretch="UltraExpanded"
                               FontWeight="SemiBold"
                               Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2NextBtn}" />
                    <AnimatedIcon Width="24"
                                  MinHeight="{ThemeResource ComboBoxMinHeight}"
                                  Margin="0,-4,0,-8"
                                  HorizontalAlignment="Center">
                        <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                        <AnimatedIcon.FallbackIconSource>
                            <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                            FontSize="12"
                                            Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                            Glyph="&#xE70D;" />
                        </AnimatedIcon.FallbackIconSource>
                    </AnimatedIcon>
                </StackPanel>
            </Button.Content>
        </Button>
        <Button x:Name="PrevPage"
                Margin="32"
                HorizontalAlignment="Center"
                VerticalAlignment="Top"
                Click="PrevPage_Click"
                IsEnabled="True"
                Style="{ThemeResource TransparentDefaultButtonStyle}">
            <Button.OpacityTransition>
                <ScalarTransition />
            </Button.OpacityTransition>
            <Button.Content>
                <StackPanel>
                    <Grid HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          RenderTransformOrigin="0.5, 0.5">
                        <Grid.RenderTransform>
                            <RotateTransform Angle="180" />
                        </Grid.RenderTransform>
                        <AnimatedIcon Width="24"
                                      MinHeight="{ThemeResource ComboBoxMinHeight}"
                                      Margin="0,-8,0,-4"
                                      HorizontalAlignment="Center">
                            <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                            <AnimatedIcon.FallbackIconSource>
                                <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                FontSize="12"
                                                Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                Glyph="&#xE70D;" />
                            </AnimatedIcon.FallbackIconSource>
                        </AnimatedIcon>
                    </Grid>
                    <TextBlock FontSize="16"
                               FontStretch="UltraExpanded"
                               FontWeight="SemiBold"
                               Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2PrevBtnNew}" />
                </StackPanel>
            </Button.Content>
        </Button>
        <Grid x:Name="Bg"
              Margin="32">
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <StackPanel x:Name="RightPanel"
                        Grid.Column="0"
                        Margin="32,0,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center">
                <!--<StackPanel.Transitions>
                    <EntranceThemeTransition/>
                </StackPanel.Transitions>-->
                <TextBlock Margin="0,0,0,32"
                           Foreground="{ThemeResource SystemAccentColor}"
                           Style="{ThemeResource TitleLargeTextBlockStyle}"
                           Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2Title}" />
                <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}">
                    <Run FontWeight="Normal"
                         Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2Subtitle1_1}" />
                    <Run FontWeight="Bold"
                         Foreground="{ThemeResource SystemAccentColor}"
                         Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2Subtitle1_2}" />
                    <Run FontWeight="Normal"
                         Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2Subtitle1_3}" />
                </TextBlock>
                <TextBlock Margin="0,16,0,24"
                           Style="{ThemeResource SubtitleTextBlockStyle}">
                    <Run FontWeight="Normal"
                         Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2Subtitle2_1}" />
                    <Run Foreground="{ThemeResource SystemAccentColor}"
                         Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2Subtitle2_2}" />
                    <Run FontWeight="Normal"
                         Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2Subtitle2_3}" />
                    <Run FontWeight="Bold"
                         Foreground="{ThemeResource SystemAccentColor}"
                         Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2Subtitle2_4}" />
                    <Run FontWeight="Normal"
                         Text="{x:Bind helper:Locale.Lang._StartupPage.Pg2Subtitle2_5}" />
                </TextBlock>
                <StackPanel Orientation="Horizontal">
                    <ComboBox x:Name="GameCategorySelect"
                              Width="192"
                              CornerRadius="16"
                              PlaceholderText="{x:Bind helper:Locale.Lang._StartupPage.Pg2ComboBox}"
                              SelectionChanged="GameCategorySelect_SelectionChanged" />
                    <ComboBox x:Name="GameRegionSelect"
                              Width="192"
                              Margin="16,0,0,0"
                              CornerRadius="16"
                              IsEnabled="False"
                              PlaceholderText="{x:Bind helper:Locale.Lang._StartupPage.Pg2ComboBoxRegion}"
                              SelectionChanged="GameSelect_SelectionChanged" />
                </StackPanel>
            </StackPanel>
        </Grid>
        <Grid x:Name="Overlay"
              Visibility="Collapsed">
            <StackPanel Margin="0,200,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Vertical">
                <ProgressRing x:Name="Ring"
                              Width="48"
                              Height="48"
                              Margin="32"
                              IsActive="True"
                              IsIndeterminate="false"
                              Maximum="100"
                              Value="100" />
                <TextBlock x:Name="OverlayTitle"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Style="{ThemeResource SubtitleTextBlockStyle}"
                           Text="Title" />
                <TextBlock x:Name="OverlaySubtitle"
                           Margin="0,8,0,192"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                           Text="Subtitle" />
            </StackPanel>
        </Grid>
        <ProgressBar x:Name="BarBGLoading"
                     VerticalAlignment="Bottom"
                     IsIndeterminate="true"
                     Visibility="Collapsed" />
    </Grid>
</Page>
