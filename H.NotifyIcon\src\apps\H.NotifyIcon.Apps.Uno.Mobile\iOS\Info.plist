<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
	<key>CFBundleDisplayName</key>
	<string>MyUnoApp</string>
	<key>CFBundleIdentifier</key>
	<string>com.companyname.MyUnoApp</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MinimumOSVersion</key>
	<string>10.0</string>
	<key>UIDeviceFamily</key>
	<array>
	  <integer>1</integer>
	  <integer>2</integer>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
	  <string>armv7</string>
	  <string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
	  <string>UIInterfaceOrientationPortrait</string>
	  <string>UIInterfaceOrientationLandscapeLeft</string>
	  <string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
	  <string>UIInterfaceOrientationPortrait</string>
	  <string>UIInterfaceOrientationPortraitUpsideDown</string>
	  <string>UIInterfaceOrientationLandscapeLeft</string>
	  <string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIAppFonts</key>
	<array>
	  <string>Fonts/uno-fluentui-assets.ttf</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UILaunchImageMinimumOSVersion</key>
	<string>9.0</string>
	<key>UILaunchImageOrientation</key>
	<string>Portrait</string>
	<key>UILaunchImageSize</key>
	<string>{320, 568}</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
  </dict>
</plist>
