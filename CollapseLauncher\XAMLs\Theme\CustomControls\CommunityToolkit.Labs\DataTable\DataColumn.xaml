﻿<!--  Licensed to the .NET Foundation under one or more agreements. The .NET Foundation licenses this file to you under the MIT license. See the LICENSE file in the project root for more information.  -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="using:CommunityToolkit.WinUI.Controls"
                    xmlns:converters="using:CommunityToolkit.WinUI.Converters"
                    xmlns:datatable="using:CommunityToolkit.WinUI.Controls.Labs.DataTable">
    <converters:BoolToVisibilityConverter x:Key="DataColumnBoolToVisibilityConverter" />
    <!--  Implicitly applied default style  -->
    <Style BasedOn="{StaticResource DefaultDataColumnStyle}"
           TargetType="datatable:DataColumn" />
    <Style x:Key="DefaultDataColumnStyle"
           TargetType="datatable:DataColumn">
        <Style.Setters>
            <!--  TODO: This is going to be different between ListView and HeaderedItemsControl, how do we want to handle?  -->
            <Setter Property="Padding" Value="4,0" />
            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="Foreground" Value="{ThemeResource TextFillColorSecondaryBrush}" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="datatable:DataColumn">
                        <Grid Background="{TemplateBinding Background}"
                              BorderBrush="{TemplateBinding BorderBrush}"
                              BorderThickness="{TemplateBinding BorderThickness}"
                              CornerRadius="{TemplateBinding CornerRadius}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Foreground="{TemplateBinding Foreground}" />
                            <controls:ContentSizer x:Name="PART_ColumnSizer"
                                                   Grid.Column="1"
                                                   Width="8"
                                                   MinWidth="4"
                                                   Margin="0"
                                                   Padding="0"
                                                   Background="Transparent"
                                                   Foreground="{ThemeResource ControlStrokeColorSecondaryBrush}"
                                                   Visibility="{Binding CanResize, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource DataColumnBoolToVisibilityConverter}}">
                                <controls:ContentSizer.Resources>
                                    <x:Double x:Key="SizerBaseThumbWidth">2</x:Double>
                                    <x:Double x:Key="SizerBaseThumbHeight">16</x:Double>
                                </controls:ContentSizer.Resources>
                            </controls:ContentSizer>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style.Setters>
    </Style>
</ResourceDictionary>
