﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable IdentifierTypo  -->
<!--  <PERSON><PERSON><PERSON>per disable UnusedMember.Local  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<!--  ReSharper disable GrammarMistakeInMarkupAttribute  -->
<Page x:Class="CollapseLauncher.Pages.SettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:animatedvisuals="using:Microsoft.UI.Xaml.Controls.AnimatedVisuals"
      xmlns:conv="using:CollapseLauncher.Pages"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:ext="using:CollapseLauncher.Extension"
      xmlns:helper="using:Hi3Helper"
      xmlns:innerConfig="using:Hi3Helper.Shared.Region"
      xmlns:localWindowSize="using:CollapseLauncher.WindowSize"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      CacheMode="BitmapCache"
      Loaded="Page_Loaded"
      NavigationCacheMode="Enabled"
      Unloaded="Page_Unloaded"
      mc:Ignorable="d">
    <Page.Resources>
        <conv:InverseBooleanVisibilityConverter x:Key="InverseBooleanVisibilityConverter" />
        <conv:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        <conv:DownloadSpeedLimitToStringConverter x:Key="DownloadSpeedLimitToStringConverter" />
        <conv:DownloadChunkSizeToStringConverter x:Key="DownloadChunkSizeToStringConverter" />
        <conv:DoubleFormatter x:Key="DoubleFormatter" />
        <conv:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />
    </Page.Resources>
    <Grid>
        <ScrollViewer>
            <Grid Margin="32,40,32,32">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="360" />
                </Grid.ColumnDefinitions>
                <StackPanel x:Name="MainSettingsPanel"
                            Grid.Column="0"
                            HorizontalAlignment="Left">
                    <TextBlock Margin="0,0,0,16"
                               Style="{ThemeResource TitleLargeTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._SettingsPage.PageTitle}" />
                    <StackPanel x:Name="AppSettings"
                                Margin="0,8"
                                HorizontalAlignment="Left">
                        <StackPanel>
                            <StackPanel Margin="0,0,0,16">
                                <TextBlock Margin="0,0,0,16"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.Language}" />
                                <ComboBox x:Name="LanguageSelector"
                                          MinWidth="256"
                                          CornerRadius="14"
                                          ItemsSource="{x:Bind LanguageList, Mode=OneWay}"
                                          MaxDropDownHeight="400"
                                          SelectedIndex="{x:Bind LanguageSelectedIndex, Mode=OneWay}"
                                          SelectionChangedTrigger="Committed"
                                          SelectionChanged="LanguageSelector_SelectionChanged">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate x:DataType="helper:LangMetadata">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="48"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition/>
                                                </Grid.ColumnDefinitions>
                                                <Grid Background="{ThemeResource NewAccentButtonBackground}"
                                                      Padding="6, 0"
                                                      VerticalAlignment="Center"
                                                      HorizontalAlignment="Stretch"
                                                      Margin="-2,0,6,0"
                                                      CornerRadius="7">
                                                    <TextBlock Text="{x:Bind LangID}"
                                                               Margin="0,0,0,2"
                                                               FontSize="10"
                                                               HorizontalAlignment="Center"
                                                               Foreground="{ThemeResource NewAccentButtonForeground}"/>
                                                </Grid>
                                                <TextBlock Grid.Column="1"
                                                           x:Name="LanguageName"
                                                           VerticalAlignment="Center"
                                                           Text="{x:Bind LangName}" />
                                                <TextBlock Grid.Column="2"
                                                           x:Name="LanguageCode"
                                                           Margin="6,0,0,0"
                                                           FontSize="11"
                                                           Opacity="0.50"
                                                           VerticalAlignment="Center"
                                                           HorizontalAlignment="Right"
                                                           Text="{x:Bind LangAuthor}" />
                                            </Grid>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                                <TextBlock x:Name="AppLangSelectionWarning"
                                           Margin="0,12,0,0"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource AccentColor}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppLang_ApplyNeedRestart}"
                                           TextWrapping="Wrap"
                                           Visibility="Collapsed" />
                            </StackPanel>
                            <StackPanel Margin="0,10,0,16">
                                <TextBlock Margin="0,0,0,8"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppBehavior_Title}" />
                                <TextBlock Margin="0,0,0,10"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppBehavior_PostGameLaunch}" />
                                <ComboBox x:Name="GameLaunchedBehaviorSelector"
                                          MinWidth="256"
                                          Margin="0,0,0,14"
                                          CornerRadius="14"
                                          MaxDropDownHeight="200"
                                          SelectedIndex="{x:Bind AppGameLaunchedBehaviorIndex, Mode=TwoWay}">
                                    <ComboBoxItem Content="{x:Bind helper:Locale.Lang._SettingsPage.AppBehavior_PostGameLaunch_Minimize}" />
                                    <ComboBoxItem Content="{x:Bind helper:Locale.Lang._SettingsPage.AppBehavior_PostGameLaunch_ToTray}" />
                                    <ComboBoxItem Content="{x:Bind helper:Locale.Lang._SettingsPage.AppBehavior_PostGameLaunch_Nothing}" />
                                </ComboBox>
                                <ToggleSwitch Margin="0,4"
                                              Header="{x:Bind helper:Locale.Lang._SettingsPage.AppBehavior_MinimizeToTray}"
                                              IsOn="{x:Bind IsMinimizeToTaskbar, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <ToggleSwitch x:Name="StartupToggle"
                                              Margin="0,4"
                                              Header="{x:Bind helper:Locale.Lang._SettingsPage.AppBehavior_LaunchOnStartup}"
                                              IsOn="{x:Bind IsLaunchOnStartup, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <ToggleSwitch x:Name="StartupToTrayToggle"
                                              Margin="0,4"
                                              Header="{x:Bind helper:Locale.Lang._SettingsPage.AppBehavior_StartupToTray}"
                                              IsOn="{x:Bind IsStartupToTray, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}"
                                              Visibility="Collapsed" />
                            </StackPanel>
                            <TextBlock Margin="0,0,0,16"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThemes}" />
                            <StackPanel Margin="0,-8,0,8">
                                <RadioButtons x:Name="AppThemeSelection"
                                              Margin="0,0,0,8"
                                              SelectedIndex="{x:Bind CurrentThemeSelection, Mode=TwoWay}">
                                    <RadioButton Content="{x:Bind helper:Locale.Lang._SettingsPage.AppThemes_Default}" />
                                    <RadioButton Content="{x:Bind helper:Locale.Lang._SettingsPage.AppThemes_Light}" />
                                    <RadioButton Content="{x:Bind helper:Locale.Lang._SettingsPage.AppThemes_Dark}" />
                                </RadioButtons>
                                <TextBlock x:Name="AppThemeSelectionWarning"
                                           Margin="0,4"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource AccentColor}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThemes_ApplyNeedRestart}"
                                           TextWrapping="Wrap"
                                           Visibility="Collapsed" />
                            </StackPanel>
                            <ToggleSwitch Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.IntroSequenceToggle}"
                                          IsOn="{x:Bind IsIntroEnabled, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <ToggleSwitch x:Name="AppBackgroundUseAcrylic"
                                          Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.EnableAcrylicEffect}"
                                          IsOn="{x:Bind IsAcrylicEffectEnabled, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <TextBlock Margin="0,16,0,8"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.AppBG}" />
                            <TextBlock x:Name="RegionalBGNotice"
                                       MaxWidth="400"
                                       Margin="0,0,0,8"
                                       HorizontalAlignment="Left"
                                       FontWeight="SemiBold"
                                       Foreground="{ThemeResource AccentColor}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.AppBG_Note_Regional}"
                                       TextWrapping="WrapWholeWords"
                                       Visibility="Visible" />
                            <ToggleSwitch x:Name="AppBGMode"
                                          Margin="0,4,0,8"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.AppBG_Checkbox}"
                                          IsOn="{x:Bind IsBgCustom, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <StackPanel x:Name="AppBGCustomizer"
                                        Margin="0,4,0,8"
                                        Visibility="Collapsed">
                                <StackPanel Margin="-2,4,0,8"
                                            Orientation="Horizontal">
                                    <Button x:Name="BGSelector"
                                            Height="35"
                                            Margin="-40,0,0,0"
                                            Click="SelectBackgroundImg"
                                            CornerRadius="0,16,16,0"
                                            IsEnabled="False"
                                            Style="{ThemeResource AccentButtonStyle}">
                                        <StackPanel Margin="32,0,0,0"
                                                    Orientation="Horizontal">
                                            <FontIcon FontFamily="{ThemeResource FontAwesome}"
                                                      FontSize="22"
                                                      Glyph="&#xF03E;" />
                                            <TextBlock Margin="8,0,0,0"
                                                       VerticalAlignment="Center"
                                                       FontWeight="Medium"
                                                       Text="{x:Bind helper:Locale.Lang._Misc.Select}" />
                                        </StackPanel>
                                    </Button>
                                    <ScrollViewer x:Name="BGPathDisplayViewer"
                                                  Width="{x:Bind localWindowSize:WindowSize.CurrentWindowSize.SettingsPanelWidth}"
                                                  Margin="12,-4,0,-4"
                                                  HorizontalScrollBarVisibility="Visible"
                                                  HorizontalScrollMode="Enabled"
                                                  VerticalScrollMode="Disabled">
                                        <TextBlock x:Name="BGPathDisplay"
                                                   Margin="0,4"
                                                   VerticalAlignment="Center"
                                                   Text="" />
                                    </ScrollViewer>
                                </StackPanel>
                                <TextBlock x:Name="AppBGCustomizerNote"
                                           Width="368"
                                           Margin="0,12,0,0"
                                           HorizontalAlignment="Left"
                                           FontSize="12"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource AccentColor}"
                                           TextWrapping="Wrap"
                                           Visibility="Collapsed" />
                            </StackPanel>
                            <StackPanel x:Name="CustomBGImageSettings"
                                        Margin="0,4">
                                <TextBlock Margin="0,0,0,4"
                                           FontSize="16"
                                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.ImageBackground}" />
                                <Grid Margin="0,4">
                                    <Border Background="Transparent"
                                            ToolTipService.ToolTip="{x:Bind Waifu2XToolTip}"
                                            Visibility="{x:Bind IsWaifu2XUsable, Converter={StaticResource InverseBooleanVisibilityConverter}}" />
                                    <ToggleSwitch x:Name="Waifu2XToggleSwitch"
                                                  IsEnabled="{x:Bind IsWaifu2XUsable}"
                                                  IsOn="{x:Bind IsWaifu2XEnabled, Mode=TwoWay}"
                                                  OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                                  OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}"
                                                  ToolTipService.ToolTip="{x:Bind Waifu2XToolTip}">
                                        <ToggleSwitch.Header>
                                            <StackPanel Loaded="EnableHeaderMouseEvent"
                                                        Orientation="Horizontal">
                                                <TextBlock VerticalAlignment="Center"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.Waifu2X_Toggle}" />
                                                <TextBlock Margin="10,0,0,0"
                                                           VerticalAlignment="Center"
                                                           FontFamily="{ThemeResource FontAwesomeSolid}"
                                                           FontSize="18"
                                                           Opacity="0.25"
                                                           Text="{x:Bind Waifu2XToolTipIcon}" />
                                            </StackPanel>
                                        </ToggleSwitch.Header>
                                    </ToggleSwitch>
                                </Grid>
                            </StackPanel>
                            <StackPanel x:Name="CustomBGVideoSettings"
                                        Margin="0,4">
                                <TextBlock Margin="0,0,0,4"
                                           FontSize="16"
                                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.VideoBackground}" />
                                <ToggleSwitch Margin="0,4"
                                              Header="{x:Bind helper:Locale.Lang._SettingsPage.VideoBackground_IsEnableAcrylicBackground}"
                                              IsEnabled="{x:Bind AppBackgroundUseAcrylic.IsOn, Mode=OneWay}"
                                              IsOn="{x:Bind IsUseVideoBgDynamicColorUpdate, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <ToggleSwitch x:Name="VideoBackgroundIsAudioMute"
                                              Margin="0,4"
                                              Header="{x:Bind helper:Locale.Lang._SettingsPage.VideoBackground_IsEnableAudio}"
                                              IsOn="{x:Bind IsVideoBackgroundAudioMute, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <TextBlock Margin="0,4"
                                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.VideoBackground_AudioVolume}" />
                                <Slider x:Name="VideoBackgroundAudioVolumeSlider"
                                        Width="250"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Maximum="100"
                                        Minimum="0"
                                        Style="{ThemeResource FatSliderStyle}"
                                        Value="{x:Bind VideoBackgroundAudioVolume, Mode=TwoWay}" />
                            </StackPanel>
                            <TextBlock Margin="0,16,0,8"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.AppWindowSize}" />
                            <StackPanel Margin="0,0,0,8">
                                <RadioButtons x:Name="WindowSizeSelection"
                                              MaxColumns="2"
                                              SelectedIndex="{x:Bind SelectedWindowSizeProfile, Mode=TwoWay}">
                                    <StackPanel Margin="0,-1,0,0"
                                                Orientation="Horizontal">
                                        <TextBlock FontFamily="{ThemeResource FontAwesomeSolid}"
                                                   FontSize="22"
                                                   Text="&#xf065;" />
                                        <TextBlock Margin="10,0,0,0"
                                                   VerticalAlignment="Center"
                                                   FontWeight="Medium"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.AppWindowSize_Normal}" />
                                    </StackPanel>
                                    <StackPanel Margin="0,-1,0,0"
                                                Orientation="Horizontal">
                                        <TextBlock FontFamily="{ThemeResource FontAwesomeSolid}"
                                                   FontSize="22"
                                                   Text="&#xf066;" />
                                        <TextBlock Margin="10,0,0,0"
                                                   VerticalAlignment="Center"
                                                   FontWeight="Medium"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.AppWindowSize_Small}" />
                                    </StackPanel>
                                </RadioButtons>
                            </StackPanel>
                            <TextBlock Margin="0,16,0,8"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.KbShortcuts_Title}" />
                            <ToggleSwitch Margin="0,4"
                                          IsOn="{x:Bind AreShortcutsEnabled, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <StackPanel x:Name="KbScBtns"
                                        Margin="0,8"
                                        Orientation="Horizontal"
                                        Visibility="Collapsed">
                                <Button Height="40"
                                        Margin="0,0,12,0"
                                        Click="ShowKbScList_Click"
                                        CornerRadius="20"
                                        Style="{ThemeResource AccentButtonStyle}">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock FontFamily="{ThemeResource FontAwesomeSolid}"
                                                   FontSize="18"
                                                   Text="&#xf101;" />
                                        <TextBlock Margin="8,-2,0,0"
                                                   VerticalAlignment="Center"
                                                   FontWeight="Medium"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.KbShortcuts_ShowBtn}" />
                                    </StackPanel>
                                </Button>
                                <Button Height="40"
                                        Click="ResetKeylist_Click"
                                        CornerRadius="20"
                                        Style="{ThemeResource AccentButtonStyle}">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock FontFamily="{ThemeResource FontAwesomeSolid}"
                                                   FontSize="18"
                                                   Text="" />
                                        <TextBlock Margin="8,-2,0,0"
                                                   VerticalAlignment="Center"
                                                   FontWeight="Medium"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.KbShortcuts_ResetBtn}" />
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                            <TextBlock Margin="0,16,0,8"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.DiscordRPC}" />
                            <ToggleSwitch x:Name="ToggleDiscordRPC"
                                          Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.DiscordRPC_Toggle}"
                                          IsOn="{x:Bind IsDiscordRpcEnabled, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <ToggleSwitch x:Name="ToggleDiscordGameStatus"
                                          Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.DiscordRPC_GameStatusToggle}"
                                          IsEnabled="False"
                                          IsOn="{x:Bind IsDiscordGameStatusEnabled, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}"
                                          Visibility="Collapsed" />
                            <ToggleSwitch x:Name="ToggleDiscordIdleStatus"
                                          Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.DiscordRPC_IdleStatusToggle}"
                                          IsOn="{x:Bind IsDiscordIdleStatusEnabled, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}"
                                          Visibility="Collapsed" />
                            <TextBlock Margin="0,16,0,8"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Title}" />
                            <StackPanel Orientation="Horizontal">
                                <TextBlock FontSize="16"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_Title}" />
                                <!--  ReSharper disable once Xaml.RedundantStyledValue  -->
                                <Button Width="24"
                                        Height="24"
                                        Margin="8,0,0,0"
                                        Padding="0"
                                        HorizontalAlignment="Left"
                                        CornerRadius="4"
                                        Style="{ThemeResource AcrylicButtonStyle}">
                                    <Button.Content>
                                        <FontIcon FontFamily="{ThemeResource FontAwesome}"
                                                  FontSize="10"
                                                  Glyph="&#x3f;" />
                                    </Button.Content>
                                    <Button.Flyout>
                                        <Flyout Placement="Right">
                                            <StackPanel MaxWidth="340">
                                                <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ConnectionType}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Margin="0,8,0,16"
                                                           ext:TextBlockExtension.RemoveEmptyRuns="True"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ConnectionType_Tooltip1}" />
                                                    <LineBreak />
                                                    <LineBreak />
                                                    <Run Text="  • " />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ConnectionType_SelectionDoH}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text=" [" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._Misc.Default}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="]" />
                                                    <LineBreak />
                                                    <Run Text="  • " />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ConnectionType_SelectionDoT}" />
                                                    <LineBreak />
                                                    <Run Text="  • " />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ConnectionType_SelectionUdp}" />
                                                </TextBlock>
                                                <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ProviderSelection}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Margin="0,4,0,0"
                                                           ext:TextBlockExtension.RemoveEmptyRuns="True"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._Misc.Default}" />
                                                    <Run Text=":" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text=" [" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind _dnsSettingsContext.DefaultExternalDnsProvider}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="]" />
                                                </TextBlock>
                                                <TextBlock Margin="0,4,0,16"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ProviderSelection_Tooltip1}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_CustomProvider}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Margin="0,4,0,0"
                                                           ext:TextBlockExtension.RemoveEmptyRuns="True"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._Misc.Default}" />
                                                    <Run Text=":" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text=" [" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_CustomProvider_Tooltip1}" />
                                                    <Run ext:TextBlockExtension.PreserveSpace="True"
                                                         Text=" " />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ProviderSelection}" />
                                                    <Run ext:TextBlockExtension.PreserveSpace="True"
                                                         Text=" " />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_CustomProvider_Tooltip2}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="]" />
                                                </TextBlock>
                                                <TextBlock Margin="0,4,0,0"
                                                           ext:TextBlockExtension.RemoveEmptyRuns="True"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_CustomProvider_Tooltip3}" />
                                                    <Run ext:TextBlockExtension.PreserveSpace="True"
                                                         Text=" " />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ProviderSelection_SelectionCustom}" />
                                                    <Run ext:TextBlockExtension.PreserveSpace="True"
                                                         Text=" " />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_CustomProvider_Tooltip4}" />
                                                    <Run ext:TextBlockExtension.PreserveSpace="True"
                                                         Text=" " />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ProviderSelection}" />
                                                    <Run ext:TextBlockExtension.PreserveSpace="True"
                                                         Text=" " />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_CustomProvider_Tooltip5}" />
                                                    <LineBreak />
                                                    <LineBreak />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind _dnsSettingsSeparatorList}" />
                                                    <LineBreak />
                                                    <LineBreak />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_CustomProvider_Tooltip6}" />
                                                    <LineBreak />
                                                    <Run FontWeight="Bold"
                                                         Text="dns.google;$quad9;**************" />
                                                </TextBlock>
                                            </StackPanel>
                                        </Flyout>
                                    </Button.Flyout>
                                </Button>
                            </StackPanel>
                            <StackPanel Margin="0,0,0,24">
                                <ToggleSwitch x:Name="CustomDnsSettingsToggle"
                                              IsOn="{x:Bind _dnsSettingsContext.IsUseExternalDns, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <Grid Width="400"
                                      HorizontalAlignment="Left">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <ComboBox x:Name="CustomDnsConnectionTypeComboBox"
                                              Margin="0,0,0,8"
                                              HorizontalAlignment="Stretch"
                                              Header="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ConnectionType}"
                                              IsEnabled="{x:Bind CustomDnsSettingsToggle.IsOn, Mode=OneWay}"
                                              ItemsSource="{x:Bind _dnsSettingsContext.ExternalDnsConnectionTypeList}"
                                              SelectedIndex="{x:Bind _dnsSettingsContext.ExternalDnsConnectionType, Mode=TwoWay}" />
                                    <ComboBox x:Name="CustomDnsProviderListComboBox"
                                              Grid.Row="1"
                                              Margin="0,0,0,8"
                                              HorizontalAlignment="Stretch"
                                              Header="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ProviderSelection}"
                                              IsEnabled="{x:Bind CustomDnsSettingsToggle.IsOn, Mode=OneWay}"
                                              ItemsSource="{x:Bind _dnsSettingsContext.ExternalDnsProviderList}"
                                              SelectedIndex="{x:Bind _dnsSettingsContext.ExternalDnsProvider, Mode=TwoWay}" />
                                    <TextBox x:Name="CustomDnsHostTextbox"
                                             Grid.Row="2"
                                             Margin="0,0,0,8"
                                             Header="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_CustomProvider}"
                                             IsEnabled="{x:Bind CustomDnsSettingsToggle.IsOn, Mode=OneWay}"
                                             Text="{x:Bind _dnsSettingsContext.ExternalDnsAddresses, Mode=TwoWay}"
                                             Visibility="Collapsed" />
                                    <Button x:Name="CustomDnsSettingsApplyButton"
                                            Grid.Row="3"
                                            Margin="0,8,0,8"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Stretch"
                                            Click="ValidateAndApplyDnsSettings"
                                            IsEnabled="{x:Bind CustomDnsSettingsToggle.IsOn, Mode=OneWay}"
                                            Style="{ThemeResource AcrylicButtonStyle}">
                                        <Grid Margin="0,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <TextBlock HorizontalAlignment="Left"
                                                       VerticalAlignment="Center"
                                                       FontWeight="SemiBold"
                                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ValidateAndSaveSettingsButton}" />
                                            <FontIcon Grid.Column="1"
                                                      HorizontalAlignment="Right"
                                                      VerticalAlignment="Center"
                                                      FontSize="18"
                                                      Glyph="" />
                                        </Grid>
                                    </Button>
                                    <Grid x:Name="DnsSettingsTestTextChecking"
                                          Grid.Row="4"
                                          Margin="0,0,0,8"
                                          Padding="12,8"
                                          HorizontalAlignment="Stretch"
                                          Background="{ThemeResource AccentAcrylicInAppFillColorBaseBrush}"
                                          CornerRadius="4"
                                          Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Margin="0,-2,0,0"
                                                   HorizontalAlignment="Left"
                                                   VerticalAlignment="Center"
                                                   FontWeight="Medium"
                                                   HorizontalTextAlignment="Left"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ApplyingSettingsButton}" />
                                        <ProgressRing Grid.Column="1"
                                                      Width="18"
                                                      Height="18"
                                                      VerticalAlignment="Center"
                                                      IsIndeterminate="true" />
                                    </Grid>
                                    <Grid x:Name="DnsSettingsTestTextSuccess"
                                          Grid.Row="4"
                                          Margin="0,0,0,8"
                                          Padding="12,8"
                                          HorizontalAlignment="Stretch"
                                          Background="{ThemeResource SystemFillColorSuccessBackgroundBrush}"
                                          CornerRadius="4"
                                          Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Margin="0,-2,0,0"
                                                   HorizontalAlignment="Left"
                                                   VerticalAlignment="Center"
                                                   FontWeight="Medium"
                                                   HorizontalTextAlignment="Left"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_SettingsSavedButton}" />
                                        <FontIcon Grid.Column="1"
                                                  HorizontalAlignment="Right"
                                                  VerticalAlignment="Center"
                                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                                  FontSize="18"
                                                  Foreground="{ThemeResource SystemFillColorSuccess}"
                                                  Glyph="" />
                                    </Grid>
                                    <Grid x:Name="DnsSettingsTestTextFailed"
                                          Grid.Row="4"
                                          Margin="0,0,0,8"
                                          Padding="12,8"
                                          HorizontalAlignment="Stretch"
                                          Background="{ThemeResource SystemFillColorCriticalBackgroundBrush}"
                                          CornerRadius="4"
                                          Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Margin="0,-2,0,0"
                                                   HorizontalAlignment="Left"
                                                   VerticalAlignment="Center"
                                                   FontWeight="Medium"
                                                   HorizontalTextAlignment="Left"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_SettingsFailedButton}" />
                                        <FontIcon Grid.Column="1"
                                                  HorizontalAlignment="Right"
                                                  VerticalAlignment="Center"
                                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                                  FontSize="18"
                                                  Foreground="{ThemeResource SystemFillColorCritical}"
                                                  Glyph="" />
                                    </Grid>
                                </Grid>
                                <TextBlock x:Name="CustomDnsSettingsChangeWarning"
                                           Margin="0,0,0,0"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource AccentColor}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Dns_ChangesWarning}"
                                           TextWrapping="Wrap"
                                           Visibility="Collapsed" />
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock FontSize="16"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_Title}" />
                                <!--  ReSharper disable once Xaml.RedundantStyledValue  -->
                                <Button Width="24"
                                        Height="24"
                                        Margin="8,0,0,0"
                                        Padding="0"
                                        HorizontalAlignment="Left"
                                        CornerRadius="4"
                                        Style="{ThemeResource AcrylicButtonStyle}">
                                    <Button.Content>
                                        <FontIcon FontFamily="{ThemeResource FontAwesome}"
                                                  FontSize="10"
                                                  Glyph="&#x3f;" />
                                    </Button.Content>
                                    <Button.Flyout>
                                        <Flyout Placement="Right">
                                            <StackPanel MaxWidth="340">
                                                <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_Hostname}" />
                                                <TextBlock Margin="0,8,0,16"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_HostnameHelp1}" />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_HostnameHelp2}" />
                                                    <Run FontWeight="Bold"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_HostnameHelp3}" />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_HostnameHelp4}" />
                                                </TextBlock>
                                                <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_Username}" />
                                                <TextBlock Margin="0,4,0,0">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_UsernameHelp1}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_UsernameHelp2}" />
                                                </TextBlock>
                                                <TextBlock Margin="0,4,0,16"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_UsernameHelp3}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_Password}" />
                                                <TextBlock Margin="0,4,0,0">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_PasswordHelp1}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_PasswordHelp2}" />
                                                </TextBlock>
                                                <TextBlock Margin="0,4,0,0"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_PasswordHelp3}"
                                                           TextWrapping="Wrap" />
                                            </StackPanel>
                                        </Flyout>
                                    </Button.Flyout>
                                </Button>
                            </StackPanel>
                            <StackPanel Width="400"
                                        MaxWidth="640"
                                        Margin="0,0,0,8"
                                        HorizontalAlignment="Left">
                                <ToggleSwitch x:Name="NetworkSettingsProxyToggle"
                                              IsOn="{x:Bind IsUseProxy, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <TextBox x:Name="ProxyHostnameTextbox"
                                         Margin="0,8,0,8"
                                         Header="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_Hostname}"
                                         InputScope="Url"
                                         IsEnabled="{x:Bind NetworkSettingsProxyToggle.IsOn, Mode=OneWay}"
                                         IsSpellCheckEnabled="False"
                                         PlaceholderText="Example: https://127.0.0.1:8080"
                                         Text="{x:Bind HttpProxyUrl, Mode=TwoWay}" />
                                <TextBlock x:Name="ProxyHostnameTextboxError"
                                           FontWeight="Medium"
                                           Foreground="{ThemeResource SystemFillColorCriticalBrush}"
                                           Text="Error"
                                           TextWrapping="Wrap"
                                           Visibility="Collapsed" />
                                <Grid Margin="0,8,0,0"
                                      ColumnSpacing="8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition />
                                        <RowDefinition />
                                        <RowDefinition />
                                    </Grid.RowDefinitions>
                                    <TextBox Header="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_Username}"
                                             IsEnabled="{x:Bind NetworkSettingsProxyToggle.IsOn, Mode=OneWay}"
                                             IsSpellCheckEnabled="False"
                                             PlaceholderText="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_UsernamePlaceholder}"
                                             Text="{x:Bind HttpProxyUsername, Mode=TwoWay}" />
                                    <PasswordBox Grid.Row="0"
                                                 Grid.Column="1"
                                                 Header="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_Password}"
                                                 IsEnabled="{x:Bind NetworkSettingsProxyToggle.IsOn, Mode=OneWay}"
                                                 Password="{x:Bind HttpProxyPassword, Mode=TwoWay}"
                                                 PasswordRevealMode="Peek"
                                                 PlaceholderText="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Proxy_PasswordPlaceholder}" />
                                    <Button x:Name="ProxyConnectivityTestButton"
                                            Grid.Row="1"
                                            Grid.Column="0"
                                            Grid.ColumnSpan="2"
                                            Margin="0,16,0,8"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Stretch"
                                            Click="ProxyConnectivityTestButton_Click"
                                            IsEnabled="{x:Bind NetworkSettingsProxyToggle.IsOn, Mode=OneWay}"
                                            Style="{ThemeResource AcrylicButtonStyle}"
                                            Visibility="{x:Bind ProxyHostnameTextbox.Text, Mode=OneWay, Converter={StaticResource StringToVisibilityConverter}}">
                                        <Grid HorizontalAlignment="Stretch">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <Grid HorizontalAlignment="Left">
                                                <TextBlock HorizontalAlignment="Left"
                                                           VerticalAlignment="Center"
                                                           FontWeight="Medium"
                                                           HorizontalTextAlignment="Left"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_ProxyTest_Button}" />
                                            </Grid>
                                            <FontIcon Grid.Column="1"
                                                      HorizontalAlignment="Right"
                                                      VerticalAlignment="Center"
                                                      FontSize="18"
                                                      Glyph="" />
                                        </Grid>
                                    </Button>
                                    <Grid x:Name="ProxyConnectivityTestTextChecking"
                                          Grid.Row="2"
                                          Grid.Column="0"
                                          Grid.ColumnSpan="2"
                                          Padding="12,8"
                                          HorizontalAlignment="Stretch"
                                          Background="{ThemeResource AccentAcrylicInAppFillColorBaseBrush}"
                                          CornerRadius="4"
                                          Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Margin="0,-2,0,0"
                                                   HorizontalAlignment="Left"
                                                   VerticalAlignment="Center"
                                                   FontWeight="Medium"
                                                   HorizontalTextAlignment="Left"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_ProxyTest_ButtonChecking}" />
                                        <ProgressRing Grid.Column="1"
                                                      Width="18"
                                                      Height="18"
                                                      VerticalAlignment="Center"
                                                      IsIndeterminate="False" />
                                    </Grid>
                                    <Grid x:Name="ProxyConnectivityTestTextSuccess"
                                          Grid.Row="2"
                                          Grid.Column="0"
                                          Grid.ColumnSpan="2"
                                          Padding="12,8"
                                          HorizontalAlignment="Stretch"
                                          Background="{ThemeResource SystemFillColorSuccessBackgroundBrush}"
                                          CornerRadius="4"
                                          Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Margin="0,-2,0,0"
                                                   HorizontalAlignment="Left"
                                                   VerticalAlignment="Center"
                                                   FontWeight="Medium"
                                                   HorizontalTextAlignment="Left"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_ProxyTest_ButtonSuccess}" />
                                        <FontIcon Grid.Column="1"
                                                  HorizontalAlignment="Right"
                                                  VerticalAlignment="Center"
                                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                                  FontSize="18"
                                                  Foreground="{ThemeResource SystemFillColorSuccess}"
                                                  Glyph="" />
                                    </Grid>
                                    <Grid x:Name="ProxyConnectivityTestTextFailed"
                                          Grid.Row="2"
                                          Grid.Column="0"
                                          Grid.ColumnSpan="2"
                                          Padding="12,8"
                                          HorizontalAlignment="Stretch"
                                          Background="{ThemeResource SystemFillColorCriticalBackgroundBrush}"
                                          CornerRadius="4"
                                          Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Margin="0,-2,0,0"
                                                   HorizontalAlignment="Left"
                                                   VerticalAlignment="Center"
                                                   FontWeight="Medium"
                                                   HorizontalTextAlignment="Left"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_ProxyTest_ButtonFailed}" />
                                        <FontIcon Grid.Column="1"
                                                  HorizontalAlignment="Right"
                                                  VerticalAlignment="Center"
                                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                                  FontSize="18"
                                                  Foreground="{ThemeResource SystemFillColorCritical}"
                                                  Glyph="" />
                                    </Grid>
                                </Grid>
                            </StackPanel>
                            <StackPanel Margin="0,0,0,16"
                                        Spacing="8">
                                <TextBlock Margin="0,0,0,0"
                                           FontSize="16"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Http_Title}" />
                                <ToggleSwitch Header="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Http_Redirect}"
                                              IsOn="{x:Bind IsAllowHttpRedirections, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <ToggleSwitch Header="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Http_SimulateCookies}"
                                              IsOn="{x:Bind IsAllowHttpCookies, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <ToggleSwitch Header="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Http_UntrustedHttps}"
                                              IsOn="{x:Bind IsAllowUntrustedCert, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <NumberBox x:Name="HttpClientTimeoutNumberBox"
                                           HorizontalAlignment="Left"
                                           Header="{x:Bind helper:Locale.Lang._SettingsPage.NetworkSettings_Http_Timeout}"
                                           Maximum="3600"
                                           Minimum="10"
                                           SpinButtonPlacementMode="Inline"
                                           Value="{x:Bind HttpClientTimeout, Mode=TwoWay}" />
                                <StackPanel Margin="0,16,0,0"
                                            Orientation="Horizontal">
                                    <TextBlock FontSize="16"
                                               Style="{ThemeResource SubtitleTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_Title}" />
                                    <!--  ReSharper disable once Xaml.RedundantStyledValue  -->
                                    <Button Width="24"
                                            Height="24"
                                            Margin="8,0,0,0"
                                            Padding="0"
                                            HorizontalAlignment="Left"
                                            CornerRadius="4"
                                            Style="{ThemeResource AcrylicButtonStyle}">
                                        <Button.Content>
                                            <FontIcon FontFamily="{ThemeResource FontAwesome}"
                                                      FontSize="10"
                                                      Glyph="&#x3f;" />
                                        </Button.Content>
                                        <Button.Flyout>
                                            <Flyout Placement="Right">
                                                <StackPanel MaxWidth="340">
                                                    <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_SpeedLimit_Title}" />
                                                    <TextBlock Margin="0,4,0,0">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_SpeedLimitHelp1}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._Misc.Disabled}" />
                                                    </TextBlock>
                                                    <TextBlock Margin="0,0,0,0">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_SpeedLimitHelp2}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_SpeedLimitHelp3}" />
                                                    </TextBlock>
                                                    <TextBlock Margin="0,8,0,16"
                                                               TextWrapping="Wrap">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_SpeedLimitHelp4}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownload_Title}" />
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_SpeedLimitHelp5}" />
                                                    </TextBlock>
                                                    <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunk_Title}" />
                                                    <TextBlock Margin="0,4,0,0">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunkHelp1}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                                    </TextBlock>
                                                    <TextBlock Margin="0,0,0,0">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunkHelp2}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunkHelp3}" />
                                                    </TextBlock>
                                                    <TextBlock Margin="0,8,0,4"
                                                               TextWrapping="Wrap">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunkHelp4}" />
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunkHelp5}" />
                                                    </TextBlock>
                                                    <TextBlock Margin="0,0,0,8"
                                                               TextWrapping="Wrap">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunkHelp6}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_SpeedLimit_Title}" />
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunkHelp7}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownload_Title}" />
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunkHelp8}" />
                                                    </TextBlock>
                                                    <TextBlock Margin="0,0,0,0"
                                                               FontSize="16"
                                                               FontWeight="Bold"
                                                               Foreground="{ThemeResource AccentColor}"
                                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunkHelp9}"
                                                               TextWrapping="Wrap" />
                                                    <TextBlock Margin="0,0,0,16"
                                                               FontSize="12"
                                                               TextWrapping="Wrap">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunkHelp10}" />
                                                    </TextBlock>
                                                    <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownload_Title}" />
                                                    <TextBlock Margin="0,4,0,0">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownloadHelp1}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                                    </TextBlock>
                                                    <TextBlock Margin="0,8,0,0"
                                                               TextWrapping="Wrap">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownloadHelp2}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._GameRepairPage.PageTitle}" />
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownloadHelp3}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._CachesPage.PageTitle}" />
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownloadHelp4}" />
                                                    </TextBlock>
                                                    <TextBlock Margin="0,8,0,0"
                                                               TextWrapping="Wrap">
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownloadHelp5}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._GameRepairPage.PageTitle}" />
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownloadHelp6}" />
                                                        <Run FontWeight="Bold"
                                                             Foreground="{ThemeResource AccentColor}"
                                                             Text="{x:Bind helper:Locale.Lang._CachesPage.PageTitle}" />
                                                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownloadHelp7}" />
                                                    </TextBlock>
                                                </StackPanel>
                                            </Flyout>
                                        </Button.Flyout>
                                    </Button>
                                </StackPanel>
                                <ToggleSwitch x:Name="NetworkDownloadSpeedLimitToggle"
                                              Header="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_SpeedLimit_Title}"
                                              IsOn="{x:Bind IsUseDownloadSpeedLimiter, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <Grid Margin="0,0,0,8"
                                      ColumnSpacing="8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <NumberBox x:Name="NetworkDownloadSpeedLimitNumberBox"
                                               Width="Auto"
                                               MinWidth="120"
                                               MaxWidth="300"
                                               Header="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_SpeedLimit_NumBox}"
                                               IsEnabled="{x:Bind NetworkDownloadSpeedLimitToggle.IsOn, Mode=OneWay}"
                                               LargeChange="10"
                                               Maximum="1000"
                                               Minimum="0"
                                               NumberFormatter="{StaticResource DoubleFormatter}"
                                               SmallChange="0.125"
                                               SpinButtonPlacementMode="Inline"
                                               Text="100"
                                               Value="{x:Bind DownloadSpeedLimit, Mode=TwoWay}" />
                                    <Grid x:Name="NetworkDownloadSpeedLimitGrid"
                                          Grid.Column="1"
                                          Margin="0,0,0,4"
                                          Padding="10,4"
                                          HorizontalAlignment="Left"
                                          VerticalAlignment="Bottom"
                                          Background="{ThemeResource DefaultBGColorAccentBrush}"
                                          CornerRadius="4"
                                          Shadow="{ThemeResource FatSliderThumbShadow}"
                                          Translation="0,0,8">
                                        <TextBlock VerticalAlignment="Center"
                                                   FontSize="12"
                                                   FontStyle="Italic"
                                                   FontWeight="SemiBold"
                                                   Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                                                   Text="{x:Bind NetworkDownloadSpeedLimitNumberBox.Value, Converter={StaticResource DownloadSpeedLimitToStringConverter}, Mode=OneWay}" />
                                    </Grid>
                                </Grid>
                                <ToggleSwitch x:Name="NetworkNewPreallocDownloaderToggle"
                                              IsOn="{x:Bind IsUsePreallocatedDownloader, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition />
                                            <RowDefinition />
                                        </Grid.RowDefinitions>
                                        <TextBlock Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunk_Title}" />
                                        <TextBlock Grid.Row="1"
                                                   FontSize="10"
                                                   FontWeight="SemiBold"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunk_Subtitle}" />
                                    </Grid>
                                </ToggleSwitch>
                                <Grid Margin="0,0,0,8"
                                      ColumnSpacing="8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <NumberBox x:Name="NetworkDownloadChunkSizeNumberBox"
                                               Width="Auto"
                                               MinWidth="120"
                                               MaxWidth="300"
                                               Header="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunk_NumBox}"
                                               IsEnabled="{x:Bind NetworkNewPreallocDownloaderToggle.IsOn, Mode=OneWay}"
                                               LargeChange="10"
                                               Maximum="512"
                                               Minimum="32"
                                               NumberFormatter="{StaticResource DoubleFormatter}"
                                               SmallChange="0.125"
                                               SpinButtonPlacementMode="Inline"
                                               Text="100"
                                               Value="{x:Bind DownloadChunkSize, Mode=TwoWay}" />
                                    <Grid x:Name="NetworkDownloadChunkSizeGrid"
                                          Grid.Column="1"
                                          Margin="0,0,0,4"
                                          Padding="10,4"
                                          HorizontalAlignment="Left"
                                          VerticalAlignment="Bottom"
                                          Background="{ThemeResource DefaultBGColorAccentBrush}"
                                          CornerRadius="4"
                                          Shadow="{ThemeResource FatSliderThumbShadow}"
                                          Translation="0,0,8">
                                        <TextBlock VerticalAlignment="Center"
                                                   FontSize="12"
                                                   FontStyle="Italic"
                                                   FontWeight="SemiBold"
                                                   Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                                                   Text="{x:Bind NetworkDownloadChunkSizeNumberBox.Value, Converter={StaticResource DownloadChunkSizeToStringConverter}, Mode=OneWay}" />
                                    </Grid>
                                </Grid>
                                <ToggleSwitch x:Name="NetworkBurstDownloadModeToggle"
                                              IsOn="{x:Bind IsBurstDownloadModeEnabled, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition />
                                            <RowDefinition />
                                        </Grid.RowDefinitions>
                                        <TextBlock Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownload_Title}" />
                                        <TextBlock Grid.Row="1"
                                                   FontSize="10"
                                                   FontWeight="SemiBold"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_BurstDownload_Subtitle}" />
                                    </Grid>
                                </ToggleSwitch>
                            </StackPanel>
                            <TextBlock Margin="0,0,0,8"
                                       FontSize="16"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.AppCDNRepository}" />
                            <StackPanel Margin="0,0,0,16">
                                <RadioButtons x:Name="AppCDNSelection"
                                              ItemsSource="{x:Bind CDNList, Mode=OneWay}"
                                              MaxColumns="3"
                                              SelectedIndex="{x:Bind SelectedCDN, Mode=OneWay}"
                                              SelectionChanged="AppCDNSelection_SelectionChanged">
                                    <RadioButtons.ItemTemplate>
                                        <DataTemplate x:DataType="innerConfig:CDNURLProperty">
                                            <Grid HorizontalAlignment="Stretch"
                                                  ToolTipService.ToolTip="{x:Bind Description}"
                                                  MinWidth="110"
                                                  Margin="0,0,16,0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                <TextBlock VerticalAlignment="Center"
                                                               Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                               Text="{x:Bind Name}" />
                                                <TextBlock Grid.Column="1"
                                                           Margin="8,0,0,0"
                                                           VerticalAlignment="Center"
                                                           HorizontalAlignment="Right"
                                                           HorizontalTextAlignment="Right"
                                                           FontFamily="{ThemeResource FontAwesomeSolid}"
                                                           FontSize="16"
                                                           Opacity="0.5"
                                                           Text="&#xf05a;" />
                                            </Grid>
                                        </DataTemplate>
                                    </RadioButtons.ItemTemplate>
                                </RadioButtons>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Margin="0,0,0,16"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads}" />

                                <Button Width="24"
                                        Height="24"
                                        Margin="8,-12,0,0"
                                        Padding="0"
                                        HorizontalAlignment="Right"
                                        CornerRadius="4"
                                        Style="{ThemeResource AcrylicButtonStyle}">
                                    <Button.Content>
                                        <FontIcon FontFamily="{ThemeResource FontAwesome}"
                                                  FontSize="10"
                                                  Glyph="&#x3f;" />
                                    </Button.Content>
                                    <Button.Flyout>
                                        <Flyout>
                                            <StackPanel MaxWidth="318">
                                                <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Download}" />
                                                <TextBlock Margin="0,8,0,0"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help1}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Margin="0,8,0,0"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help2}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="4" />
                                                </TextBlock>
                                                <TextBlock Margin="0,0,0,0"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help3}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="2 - 16" />
                                                </TextBlock>
                                                <TextBlock Margin="0,16,0,0"
                                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Extract}" />
                                                <TextBlock Margin="0,8,0,0"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help5}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Margin="0,8,0,0"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help6}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Margin="0,8,0,0"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help2}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help4}" />
                                                </TextBlock>
                                                <TextBlock TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help3}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="0 - 128" />
                                                </TextBlock>
                                                <MenuFlyoutSeparator Margin="0,16" />
                                                <StackPanel Orientation="Horizontal">
                                                    <FontIcon Margin="0,0,8,0"
                                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                                              Foreground="{ThemeResource SystemFillColorCautionBrush}"
                                                              Glyph="&#xf071;" />
                                                    <TextBlock Foreground="{ThemeResource SystemFillColorCautionBrush}"
                                                               Style="{ThemeResource SubtitleTextBlockStyle}"
                                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Attention}" />
                                                </StackPanel>
                                                <TextBlock Margin="0,8,0,0"
                                                           FontStyle="Italic"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_AttentionTop1}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.FileDownloadSettings_NewPreallocChunk_Title}" />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_AttentionTop2}" />
                                                </TextBlock>
                                                <TextBlock Margin="0,8,0,0"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Attention1}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Download}" />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Attention2}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Attention3}" />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Attention4}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Attention5}" />
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Attention6}" />
                                                </TextBlock>
                                            </StackPanel>
                                        </Flyout>
                                    </Button.Flyout>
                                </Button>
                            </StackPanel>
                            <StackPanel Margin="0,0,0,8"
                                        HorizontalAlignment="Left"
                                        Orientation="Horizontal">
                                <NumberBox x:Name="DownloadThreadsNumBox"
                                           Width="160"
                                           VerticalAlignment="Bottom"
                                           Header="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Download}"
                                           Maximum="16"
                                           Minimum="2"
                                           SpinButtonPlacementMode="Inline"
                                           Value="{x:Bind CurrentAppThreadDownloadValue, Mode=TwoWay}" />
                                <NumberBox x:Name="ExtractionThreadsNumBox"
                                           Width="160"
                                           Margin="16,0,0,0"
                                           VerticalAlignment="Bottom"
                                           Header="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Extract}"
                                           Maximum="128"
                                           Minimum="0"
                                           SpinButtonPlacementMode="Inline"
                                           Value="{x:Bind CurrentAppThreadExtractValue, Mode=TwoWay}" />
                            </StackPanel>
                            <ToggleSwitch x:Name="OldDownloadChunksMergingToggle"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.EnableDownloadChunksMerging}"
                                          IsOn="{x:Bind IsUseDownloadChunksMerging, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <StackPanel Margin="0,16,0,8"
                                        HorizontalAlignment="Left"
                                        Orientation="Horizontal">
                                <TextBlock Margin="0,0,0,8"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonSettingsTitle}" />
                                <Button Width="24"
                                        Height="24"
                                        Margin="8,-12,0,0"
                                        Padding="0"
                                        HorizontalAlignment="Right"
                                        CornerRadius="4"
                                        Style="{ThemeResource AcrylicButtonStyle}">
                                    <Button.Content>
                                        <FontIcon FontFamily="{ThemeResource FontAwesome}"
                                                  FontSize="10"
                                                  Glyph="&#x3f;" />
                                    </Button.Content>
                                    <Button.Flyout>
                                        <Flyout>
                                            <StackPanel MaxWidth="318">
                                                <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_Title}" />
                                                <TextBlock Margin="0,8,0,0"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_1}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Margin="0,8,0,0"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_2}"
                                                           TextWrapping="Wrap" />
                                                <MenuFlyoutSeparator Margin="0,16" />
                                                <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Download}" />
                                                <TextBlock Margin="0,8,0,0"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_Thread}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Margin="0,8,0,0"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help2}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help4}" />
                                                </TextBlock>
                                                <TextBlock Margin="0,0,0,0"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help3}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="0 - 64" />
                                                </TextBlock>
                                                <TextBlock Margin="0,16,0,0"
                                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHttpNumberBox}" />
                                                <TextBlock Margin="0,8,0,0"
                                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.SophonHelp_Http}"
                                                           TextWrapping="Wrap" />
                                                <TextBlock Margin="0,8,0,0"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help2}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help4}" />
                                                </TextBlock>
                                                <TextBlock Margin="0,0,0,0"
                                                           TextWrapping="Wrap">
                                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Help3}" />
                                                    <Run FontWeight="Bold"
                                                         Foreground="{ThemeResource AccentColor}"
                                                         Text="0 - 128" />
                                                </TextBlock>
                                            </StackPanel>
                                        </Flyout>
                                    </Button.Flyout>
                                </Button>
                            </StackPanel>
                            <ToggleSwitch Header="{x:Bind helper:Locale.Lang._SettingsPage.SophonToggle}"
                                          IsOn="{x:Bind IsEnableSophon, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <StackPanel Margin="0,0,0,8"
                                        HorizontalAlignment="Left"
                                        Orientation="Horizontal">
                                <NumberBox x:Name="SophonCpuThreadsNumBox"
                                           Width="128"
                                           VerticalAlignment="Bottom"
                                           Header="{x:Bind helper:Locale.Lang._SettingsPage.AppThreads_Download}"
                                           Maximum="64"
                                           Minimum="0"
                                           SpinButtonPlacementMode="Inline"
                                           Value="{x:Bind SophonDownThread, Mode=TwoWay}" />
                                <NumberBox x:Name="SophonHttpConnNumBox"
                                           Width="128"
                                           Margin="16,0,0,0"
                                           VerticalAlignment="Bottom"
                                           Header="{x:Bind helper:Locale.Lang._SettingsPage.SophonHttpNumberBox}"
                                           Maximum="128"
                                           Minimum="0"
                                           SpinButtonPlacementMode="Inline"
                                           Value="{x:Bind SophonHttpConn, Mode=TwoWay}" />
                            </StackPanel>
                            <ToggleSwitch Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.SophonPredownPerfMode_Toggle}"
                                          IsOn="{x:Bind IsSophonPreloadPerfMode, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}"
                                          ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._SettingsPage.SophonPredownPerfMode_Tooltip}" />
                            <TextBlock Margin="0,16,0,8"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.Database_Title}" />
                            <ToggleSwitch x:Name="DbToggle"
                                          Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.Database_Toggle}"
                                          IsOn="{x:Bind IsDbEnabled, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <Grid x:Name="DbPropertyGrid"
                                  Width="400"
                                  MaxWidth="400"
                                  HorizontalAlignment="Left"
                                  ColumnSpacing="8">
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition />
                                    <RowDefinition />
                                    <RowDefinition />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <TextBox x:Name="DbUriTextBox"
                                         Grid.ColumnSpan="2"
                                         Margin="0,8,0,8"
                                         HorizontalAlignment="Stretch"
                                         Header="{x:Bind helper:Locale.Lang._SettingsPage.Database_Url}"
                                         InputScope="Url"
                                         IsEnabled="{x:Bind DbToggle.IsOn, Mode=OneWay}"
                                         IsSpellCheckEnabled="False"
                                         PlaceholderText="{x:Bind helper:Locale.Lang._SettingsPage.Database_Url_Example}"
                                         Text="{x:Bind DbUrl, Mode=TwoWay}"
                                         TextChanged="DbTextBox_OnTextChanged" />
                                <PasswordBox x:Name="DbTokenPasswordBox"
                                             Grid.Row="1"
                                             Grid.Column="0"
                                             Grid.ColumnSpan="2"
                                             Margin="0,8,0,8"
                                             HorizontalAlignment="Stretch"
                                             Header="{x:Bind helper:Locale.Lang._SettingsPage.Database_Token}"
                                             InputScope="Password"
                                             IsEnabled="{x:Bind DbToggle.IsOn, Mode=OneWay}"
                                             Password="{x:Bind DbToken, Mode=TwoWay}"
                                             PasswordChanged="DbTokenPasswordBox_OnPasswordChanged"
                                             PlaceholderText="{x:Bind helper:Locale.Lang._SettingsPage.Database_Placeholder_DbTokenPasswordBox}" />
                                <TextBox x:Name="DbUserIdTextBox"
                                         Grid.Row="2"
                                         Grid.Column="0"
                                         Grid.ColumnSpan="2"
                                         Margin="0,8,0,8"
                                         HorizontalAlignment="Stretch"
                                         GotFocus="DbUserIdTextBox_GotFocus"
                                         Header="{x:Bind helper:Locale.Lang._SettingsPage.Database_UserId}"
                                         IsEnabled="{x:Bind DbToggle.IsOn, Mode=OneWay}"
                                         IsSpellCheckEnabled="False"
                                         LostFocus="DbUserIdTextBox_LostFocus"
                                         PlaceholderText="{x:Bind helper:Locale.Lang._SettingsPage.Database_Placeholder_DbUserIdTextBox}"
                                         Text="{x:Bind DbUserId, Mode=TwoWay}"
                                         TextChanged="DbTextBox_OnTextChanged" />
                                <Button x:Name="GenerateGuidButton"
                                        Grid.Row="3"
                                        Grid.Column="0"
                                        Margin="0,8,0,0"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Stretch"
                                        Click="GenerateGuidButton_Click"
                                        CornerRadius="14"
                                        IsEnabled="{x:Bind DbToggle.IsOn, Mode=OneWay}"
                                        Style="{ThemeResource AccentButtonStyle}">
                                    <Grid ColumnSpacing="12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <FontIcon Grid.Column="1"
                                                  HorizontalAlignment="Right"
                                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                                  FontSize="16"
                                                  Glyph="" />
                                        <TextBlock Grid.Column="0"
                                                   HorizontalAlignment="Left"
                                                   FontWeight="SemiBold"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.Database_GenerateGuid}" />
                                    </Grid>
                                </Button>
                                <Button x:Name="ValidateDbButton"
                                        Grid.Row="3"
                                        Grid.Column="1"
                                        Margin="0,8,0,0"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Stretch"
                                        Click="ValidateAndSaveDbButton_Click"
                                        CornerRadius="14"
                                        IsEnabled="{x:Bind DbToggle.IsOn, Mode=OneWay}"
                                        Style="{ThemeResource AccentButtonStyle}">
                                    <Grid ColumnSpacing="12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <FontIcon Grid.Column="1"
                                                  HorizontalAlignment="Right"
                                                  FontFamily="{ThemeResource FontAwesomeSolid}"
                                                  FontSize="16"
                                                  Glyph="" />
                                        <TextBlock Grid.Column="0"
                                                   HorizontalAlignment="Left"
                                                   FontWeight="SemiBold"
                                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.Database_Validate}" />
                                    </Grid>
                                </Button>
                            </Grid>
                            <StackPanel Width="400"
                                        MaxWidth="400"
                                        Margin="0,16,0,0"
                                        HorizontalAlignment="Left"
                                        Spacing="8">
                                <Grid x:Name="DatabaseConnectivityTestTextChecking"
                                      Padding="12,8"
                                      HorizontalAlignment="Stretch"
                                      Background="{ThemeResource AccentAcrylicInAppFillColorBaseBrush}"
                                      CornerRadius="4"
                                      Visibility="Collapsed">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Margin="0,-2,0,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               FontWeight="Medium"
                                               HorizontalTextAlignment="Left"
                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.Database_ValidationChecking}" />
                                    <ProgressRing Grid.Column="1"
                                                  Width="18"
                                                  Height="18"
                                                  VerticalAlignment="Center"
                                                  IsIndeterminate="true" />
                                </Grid>
                                <Grid x:Name="DatabaseConnectivityTestTextSuccess"
                                      Padding="12,8"
                                      HorizontalAlignment="Stretch"
                                      Background="{ThemeResource SystemFillColorSuccessBackgroundBrush}"
                                      CornerRadius="4"
                                      Visibility="Collapsed">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Margin="0,-2,0,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               FontWeight="Medium"
                                               HorizontalTextAlignment="Left"
                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.Database_ConnectionOk}" />
                                    <FontIcon Grid.Column="1"
                                              HorizontalAlignment="Right"
                                              VerticalAlignment="Center"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              FontSize="18"
                                              Foreground="{ThemeResource SystemFillColorSuccess}"
                                              Glyph="" />
                                </Grid>
                                <Grid x:Name="DatabaseConnectivityTestTextFailed"
                                      Padding="12,8"
                                      HorizontalAlignment="Stretch"
                                      Background="{ThemeResource SystemFillColorCriticalBackgroundBrush}"
                                      CornerRadius="4"
                                      Visibility="Collapsed">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition />
                                    </Grid.RowDefinitions>
                                    <TextBlock Margin="0,-2,0,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               FontWeight="Medium"
                                               HorizontalTextAlignment="Left"
                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.Database_ConnectFail}" />
                                    <FontIcon Grid.Row="0"
                                              Grid.Column="1"
                                              HorizontalAlignment="Right"
                                              VerticalAlignment="Center"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              FontSize="18"
                                              Foreground="{ThemeResource SystemFillColorCritical}"
                                              Glyph="" />
                                    <TextBox Grid.Row="1"
                                             Grid.Column="0"
                                             Grid.ColumnSpan="2"
                                             MaxHeight="300"
                                             Margin="0,8,0,4"
                                             HorizontalAlignment="Stretch"
                                             AcceptsReturn="True"
                                             FontSize="12"
                                             IsReadOnly="True"
                                             ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                             ScrollViewer.VerticalScrollBarVisibility="Auto" />
                                </Grid>
                                <Grid x:Name="DatabaseWarningBox"
                                      Padding="12,8"
                                      HorizontalAlignment="Stretch"
                                      Background="{ThemeResource SystemFillColorCautionBackgroundBrush}"
                                      CornerRadius="4"
                                      Visibility="Collapsed">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Margin="0,-2,0,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               FontWeight="Medium"
                                               HorizontalTextAlignment="Left"
                                               Text="" />
                                    <FontIcon Grid.Column="1"
                                              HorizontalAlignment="Right"
                                              VerticalAlignment="Center"
                                              FontFamily="{ThemeResource FontAwesomeSolid}"
                                              FontSize="18"
                                              Foreground="{ThemeResource SystemFillColorCaution}"
                                              Glyph="" />
                                </Grid>
                            </StackPanel>
                            <TextBlock Margin="0,16,0,8"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.Debug}" />
                            <ToggleSwitch x:Name="ToggleSendRemoteCrashData"
                                          Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.Debug_SendRemoteCrashData}"
                                          IsOn="{x:Bind IsSendRemoteCrashData, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}"
                                          Visibility="Visible" />
                            <ToggleSwitch Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.Debug_Console}"
                                          IsOn="{x:Bind IsConsoleEnabled, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <ToggleSwitch x:Name="ToggleIncludeGameLogs"
                                          Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.Debug_IncludeGameLogs}"
                                          IsOn="{x:Bind IsIncludeGameLogs, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}"
                                          Visibility="Collapsed" />
                            <ToggleSwitch Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.Debug_MultipleInstance}"
                                          IsOn="{x:Bind IsMultipleInstanceEnabled, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <Grid>
                                <Border Background="Transparent"
                                        ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._SettingsPage.LowerCollapsePrioOnGameLaunch_Tooltip}" />
                                <ToggleSwitch Margin="0,4"
                                              Header="{x:Bind helper:Locale.Lang._SettingsPage.LowerCollapsePrioOnGameLaunch}"
                                              IsOn="{x:Bind IsLowerCollapsePriorityOnGameLaunch, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}"
                                              ToolTipService.ToolTip="" />
                            </Grid>
                            <ToggleSwitch Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.ChangeRegionWarning_Toggle}"
                                          IsOn="{x:Bind IsShowRegionChangeWarning, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <TextBlock x:Name="ChangeRegionToggleWarning"
                                       Margin="0,0,0,8"
                                       FontWeight="Bold"
                                       Foreground="{ThemeResource AccentColor}"
                                       Text="{x:Bind helper:Locale.Lang._SettingsPage.ChangeRegionWarning_Warning}"
                                       TextWrapping="Wrap"
                                       Visibility="Collapsed" />
                            <StackPanel x:Name="PanelChangeRegionInstant">
                                <ToggleSwitch x:Name="ToggleChangeRegionInstant"
                                              Margin="0,4"
                                              Header="{x:Bind helper:Locale.Lang._SettingsPage.ChangeRegionInstant_Toggle}"
                                              IsOn="{x:Bind IsInstantRegionChange, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                <TextBlock x:Name="InstantRegionToggleWarning"
                                           Margin="0,0,0,8"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource AccentColor}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.ChangeRegionWarning_Warning}"
                                           TextWrapping="Wrap"
                                           Visibility="Collapsed" />
                            </StackPanel>
                            <ToggleSwitch Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.UseExternalBrowser}"
                                          IsOn="{x:Bind IsAlwaysUseExternalBrowser, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            <ToggleSwitch Margin="0,4"
                                          Header="{x:Bind helper:Locale.Lang._SettingsPage.Enforce7ZipExtract}"
                                          IsOn="{x:Bind IsEnforceToUse7ZipOnExtract, Mode=TwoWay}"
                                          OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                          OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
                <StackPanel x:Name="AboutApp"
                            Grid.Column="1"
                            Margin="0,16,0,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Top">
                    <TextBlock Style="{ThemeResource SubtitleTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._SettingsPage.About}" />
                    <Grid Margin="0,16,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="64" />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Image Height="Auto"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Top"
                               Source="ms-appx:///Assets/CollapseLauncherLogo.png" />
                        <StackPanel Grid.Column="1"
                                    Margin="16,0,0,0">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Style="{ThemeResource BodyStrongTextBlockStyle}"
                                               Text="Collapse Launcher"
                                               TextWrapping="Wrap" />
                                    <TextBlock x:Name="AppVersionTextBlock"
                                               Style="{ThemeResource BodyTextBlockStyle}"
                                               Text=" 1.0.0"
                                               TextWrapping="Wrap" />
                                </StackPanel>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Style="{ThemeResource BodyTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.About_Copyright1}"
                                               TextWrapping="Wrap" />
                                    <TextBlock Style="{ThemeResource BodyStrongTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.About_Copyright2}"
                                               TextWrapping="WrapWholeWords" />
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                    <TextBlock Margin="0,16,0,0"
                               TextWrapping="Wrap">
                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.About_Copyright3}" />
                        <Hyperlink NavigateUri="https://github.com/CollapseLauncher/Collapse/blob/main/LICENSE"
                                   UnderlineStyle="None">
                            <Run FontWeight="Bold"
                                 Text="{x:Bind helper:Locale.Lang._SettingsPage.LicenseType}" />
                        </Hyperlink>
                        <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.About_Copyright4}" />
                    </TextBlock>
                    <TextBlock HorizontalAlignment="Left"
                               IsTextSelectionEnabled="True"
                               Style="{ThemeResource BodyTextBlockStyle}"
                               TextWrapping="Wrap">
                        <Hyperlink x:Name="GitVersionIndicatorHyperlink"
                                   UnderlineStyle="None">
                            <Run x:Name="GitVersionIndicator" />
                        </Hyperlink>
                    </TextBlock>
                    <StackPanel Margin="0,16,0,0">
                        <TextBlock Margin="0,8,0,8"
                                   HorizontalAlignment="Left"
                                   Style="{ThemeResource BodyStrongTextBlockStyle}">
                            <Hyperlink NavigateUri="https://github.com/CollapseLauncher/Collapse/issues"
                                       UnderlineStyle="None">
                                <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.ReportIssueBtn}" />
                            </Hyperlink>
                        </TextBlock>
                        <TextBlock Margin="0,8,0,8"
                                   HorizontalAlignment="Left"
                                   Style="{ThemeResource BodyStrongTextBlockStyle}">
                            <Hyperlink NavigateUri="https://explore.transifex.com/collapse-launcher/collapse-mainapp/"
                                       UnderlineStyle="None">
                                <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.HelpLocalizeBtn}" />
                            </Hyperlink>
                        </TextBlock>
                        <TextBlock Margin="0,8,0,8"
                                   HorizontalAlignment="Left"
                                   Style="{ThemeResource BodyStrongTextBlockStyle}">
                            <Hyperlink NavigateUri="https://github.com/CollapseLauncher/Collapse#contributors"
                                       UnderlineStyle="None">
                                <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.ContributorListBtn}" />
                            </Hyperlink>
                        </TextBlock>
                        <TextBlock Margin="0,8,0,8"
                                   HorizontalAlignment="Left"
                                   Style="{ThemeResource BodyStrongTextBlockStyle}">
                            <Hyperlink NavigateUri="https://github.com/CollapseLauncher/Collapse/pulls"
                                       UnderlineStyle="None">
                                <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.ContributePRBtn}" />
                            </Hyperlink>
                        </TextBlock>
                    </StackPanel>
                    <NavigationViewItemSeparator Margin="16,16" />
                    <StackPanel Margin="0,0,0,16">
                        <TextBlock Margin="0,0,0,8"
                                   Style="{ThemeResource SubtitleTextBlockStyle}"
                                   Text="{x:Bind helper:Locale.Lang._SettingsPage.Disclaimer}"
                                   TextWrapping="Wrap" />
                        <TextBlock Style="{ThemeResource BodyTextBlockStyle}"
                                   TextWrapping="Wrap">
                            <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.Disclaimer1}" />
                            <Hyperlink FontWeight="Bold"
                                       NavigateUri="https://www.mihoyo.com"
                                       UnderlineStyle="None">
                                <Run Text="miHoYo" />
                            </Hyperlink>
                            <Run Text="/" />
                            <Hyperlink FontWeight="Bold"
                                       NavigateUri="https://www.hoyoverse.com/en-us"
                                       UnderlineStyle="None">
                                <Run Text="HoYoverse" />
                            </Hyperlink>
                            <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.Disclaimer2}" />
                            <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.Disclaimer3}" />
                        </TextBlock>
                    </StackPanel>
                    <StackPanel x:Name="HerLegacy"
                                Margin="0,16"
                                Visibility="Collapsed">
                        <TextBlock Margin="0,0,0,8"
                                   Style="{ThemeResource SubtitleTextBlockStyle}"
                                   Text="Dedication" />
                        <TextBlock Style="{ThemeResource BodyTextBlockStyle}"
                                   Text="This app is also dedicated for my crush who passed away from fighting" />
                        <TextBlock Style="{ThemeResource BodyTextBlockStyle}"
                                   Text="against her's TB disease. This app has been Dedicated with Love for her." />
                        <TextBlock Style="{ThemeResource BodyTextBlockStyle}"
                                   Text="" />
                        <TextBlock Style="{ThemeResource BodyTextBlockStyle}"
                                   Text="Rest In Peace~ (Jan 31st 2022)" />
                        <TextBlock FontSize="24">
                            <Run FontWeight="Bold"
                                 Text="Yui" />
                            <Run Text="( a.k.a" />
                            <Run FontWeight="Bold"
                                 Text="DreadBurst" />
                            <Run Text=")" />
                        </TextBlock>
                        <TextBlock Width="360"
                                   Margin="0,8,0,0"
                                   HorizontalAlignment="Left"
                                   TextWrapping="Wrap">
                            <Run Text="I will cherish every of our single moment we've spent together. I will never forget and remember your care, kindness, intelligent, braveness and cuteness." />
                            <LineBreak />
                        </TextBlock>
                        <TextBlock Width="360"
                                   HorizontalAlignment="Left"
                                   TextWrapping="Wrap">
                            <Run FontWeight="Bold"
                                 Text="Yui," />
                        </TextBlock>
                        <TextBlock Width="360"
                                   HorizontalAlignment="Left"
                                   TextWrapping="Wrap">
                            <Run Text="Thank you that you've found me and being a reason for me to change as a better person. I'm really glad about it." />
                        </TextBlock>
                        <TextBlock Width="360"
                                   Margin="0,0,0,16"
                                   HorizontalAlignment="Left"
                                   TextWrapping="Wrap">
                            <Run Text="Thank you... for everything." />
                        </TextBlock>
                        <TextBlock HorizontalAlignment="Left">
                            <Run FontWeight="Bold"
                                 Foreground="{ThemeResource AccentColor}"
                                 Text="I love you " />
                            <Run FontWeight="Bold"
                                 Text=",^__ ,^)" />
                            <Hyperlink NavigateUri="https://www.youtube.com/watch?v=4cugGAKsSNY&amp;list=PLLH-gVOGJkTLB_ixlYZ2fo51yPxn3m1LB"
                                       UnderlineStyle="None">
                                <Run Text="❤️" />
                            </Hyperlink>
                        </TextBlock>
                        <TextBlock Width="360"
                                   Margin="0,16,0,0"
                                   HorizontalAlignment="Left"
                                   FontWeight="Bold">
                            <Run Text="~neon-nyan" />
                        </TextBlock>
                    </StackPanel>
                    <Button x:Name="ButtonLinkWebsiteCollapse"
                            Margin="0,8,0,8"
                            Padding="16,12"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="ClickButtonLinkFromTag"
                            CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ButtonLinkWebsiteCollapse)}"
                            Style="{ThemeResource AccentButtonStyle}"
                            Tag="https://collapselauncher.com">
                        <Grid Margin="0,-2,0,0"
                              HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.WebsiteBtn}" />
                            </Grid>
                            <FontIcon Grid.Column="1"
                                      Margin="8,0,3,0"
                                      HorizontalAlignment="Left"
                                      VerticalAlignment="Center"
                                      FontFamily="{ThemeResource FontAwesomeSolid}"
                                      FontSize="20"
                                      Glyph="&#xF0AC;" />
                        </Grid>
                    </Button>
                    <Button x:Name="ButtonLinkDiscordCollapse"
                            Margin="0,8,0,8"
                            Padding="16,12"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="ClickButtonLinkFromTag"
                            CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ButtonLinkDiscordCollapse)}"
                            Style="{ThemeResource AccentButtonStyle}"
                            Tag="https://discord.gg/vJd2exaS7j">
                        <Grid Margin="0,-2,0,0"
                              HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.DiscordBtn3}" />
                            </Grid>
                            <FontIcon Grid.Column="1"
                                      Margin="8,0,0,0"
                                      HorizontalAlignment="Left"
                                      VerticalAlignment="Center"
                                      FontFamily="{ThemeResource FontAwesomeBrand}"
                                      FontSize="20"
                                      Glyph="&#xF392;" />
                        </Grid>
                    </Button>
                    <Button x:Name="ButtonLinkDiscordArmada"
                            Margin="0,8,0,8"
                            Padding="16,12"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="ClickButtonLinkFromTag"
                            CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ButtonLinkDiscordArmada)}"
                            Style="{ThemeResource AccentButtonStyle}"
                            Tag="https://discord.gg/NAYQ8TT2Ge">
                        <Grid Margin="0,-2,0,0"
                              HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.DiscordBtn1}" />
                            </Grid>
                            <FontIcon Grid.Column="1"
                                      Margin="8,0,0,0"
                                      HorizontalAlignment="Left"
                                      VerticalAlignment="Center"
                                      FontFamily="{ThemeResource FontAwesomeBrand}"
                                      FontSize="20"
                                      Glyph="&#xF392;" />
                        </Grid>
                    </Button>
                    <Button x:Name="ButtonLinkDiscordHi3"
                            Margin="0,8,0,8"
                            Padding="16,12"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="ClickButtonLinkFromTag"
                            CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ButtonLinkDiscordHi3)}"
                            Style="{ThemeResource AccentButtonStyle}"
                            Tag="https://discord.gg/hi3">
                        <Grid Margin="0,-2,0,0"
                              HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.DiscordBtn2}" />
                            </Grid>
                            <FontIcon Grid.Column="1"
                                      Margin="8,0,0,0"
                                      HorizontalAlignment="Left"
                                      VerticalAlignment="Center"
                                      FontFamily="{ThemeResource FontAwesomeBrand}"
                                      FontSize="20"
                                      Glyph="&#xF392;" />
                        </Grid>
                    </Button>
                    <NavigationViewItemSeparator Margin="16,16" />
                    <TextBlock Margin="0,0,0,8"
                               Style="{ThemeResource SubtitleTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._SettingsPage.Update}" />
                    <StackPanel Margin="0,0,0,8"
                                Orientation="Vertical">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Margin="0,0,0,16"
                                       VerticalAlignment="Center">
                                <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.Update_CurVer}" />
                                <Run x:Name="CurrentVersion"
                                     FontWeight="Bold"
                                     Foreground="{ThemeResource AccentColor}"
                                     Text="0.0.0.0" />
                            </TextBlock>
                        </StackPanel>
                        <Button x:Name="CheckUpdateBtn"
                                Padding="0,8"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Stretch"
                                Click="CheckUpdate"
                                CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(CheckUpdateBtn)}"
                                Style="{ThemeResource AccentButtonStyle}">
                            <Grid Margin="16,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Grid HorizontalAlignment="Left">
                                    <TextBlock Margin="0,-2,0,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               FontWeight="Medium"
                                               HorizontalTextAlignment="Left"
                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.Update_CheckBtn}" />
                                </Grid>
                                <FontIcon Grid.Column="1"
                                          HorizontalAlignment="Right"
                                          FontFamily="{ThemeResource FontAwesomeSolid}"
                                          FontSize="14"
                                          Glyph="&#xf105;" />
                            </Grid>
                        </Button>
                        <Grid x:Name="UpdateLoadingStatus"
                              Margin="0,14,0,0"
                              Padding="8"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Background="{ThemeResource AcrylicInAppFillColorDefaultBrush}"
                              CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(UpdateLoadingStatus)}"
                              Visibility="Collapsed">
                            <ProgressRing Width="20"
                                          Height="20"
                                          HorizontalAlignment="Center"
                                          IsIndeterminate="True" />
                        </Grid>
                        <Grid x:Name="UpdateAvailableStatus"
                              Margin="0,12,0,0"
                              Padding="16,4"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Background="{ThemeResource AcrylicInAppFillColorDefaultBrush}"
                              CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(UpdateAvailableStatus)}"
                              Visibility="Collapsed">
                            <StackPanel Margin="0,0,0,2"
                                        HorizontalAlignment="Center"
                                        Orientation="Horizontal">
                                <TextBlock FontSize="16"
                                           Foreground="{ThemeResource AccentColor}"
                                           Text="😆" />
                                <TextBlock Margin="8,2,0,0"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           Foreground="{ThemeResource AccentColor}">
                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.Update_NewVer1}" />
                                    <Run x:Name="UpdateAvailableLabel"
                                         FontWeight="Bold"
                                         Foreground="{ThemeResource AccentColor}"
                                         Text="0.0.0.0" />
                                    <Run Text="{x:Bind helper:Locale.Lang._SettingsPage.Update_NewVer2}" />
                                </TextBlock>
                            </StackPanel>
                        </Grid>
                        <Grid x:Name="UpToDateStatus"
                              Margin="0,12,0,0"
                              Padding="16,4"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Background="{ThemeResource SystemFillColorSuccessBrush}"
                              CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(UpToDateStatus)}"
                              Visibility="Collapsed">
                            <StackPanel Margin="0,0,0,2"
                                        HorizontalAlignment="Center"
                                        Orientation="Horizontal">
                                <TextBlock VerticalAlignment="Center"
                                           FontFamily="{ThemeResource FontAwesomeSolid}"
                                           FontSize="16"
                                           Foreground="{ThemeResource TextFillColorInverse}"
                                           Text="" />
                                <TextBlock Margin="8,0,0,0"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           Foreground="{ThemeResource TextFillColorInverse}"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.Update_LatestVer}" />
                            </StackPanel>
                        </Grid>
                        <Button x:Name="ForceUpdateBtn"
                                Margin="0,16,0,0"
                                Padding="0,8"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Stretch"
                                Click="ForceUpdate"
                                CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ForceUpdateBtn)}"
                                Style="{ThemeResource AccentButtonStyle}">
                            <Grid Margin="16,0"
                                  HorizontalAlignment="Stretch">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Grid HorizontalAlignment="Left">
                                    <TextBlock Margin="0,-2,0,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               FontWeight="Medium"
                                               HorizontalTextAlignment="Left"
                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.Update_ForceBtn}" />
                                </Grid>
                                <FontIcon Grid.Column="1"
                                          HorizontalAlignment="Right"
                                          FontFamily="{ThemeResource FontAwesomeSolid}"
                                          FontSize="14"
                                          Glyph="&#xf101;" />
                            </Grid>
                        </Button>
                        <Button x:Name="ChangeReleaseBtn"
                                Margin="0,16,0,0"
                                Padding="0,8"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Stretch"
                                Click="ChangeRelease"
                                CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ChangeReleaseBtn)}"
                                Style="{ThemeResource AccentButtonStyle}">
                            <Grid Margin="16,0"
                                  HorizontalAlignment="Stretch">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Grid HorizontalAlignment="Left">
                                    <TextBlock x:Name="ChangeReleaseBtnText"
                                               Margin="0,-2,0,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               FontWeight="Medium"
                                               HorizontalTextAlignment="Left" />
                                </Grid>
                                <FontIcon Grid.Column="1"
                                          HorizontalAlignment="Right"
                                          FontFamily="{ThemeResource FontAwesomeSolid}"
                                          FontSize="14"
                                          Glyph="&#xf0ec;" />
                            </Grid>
                        </Button>
                        <Button x:Name="OpenChangelogBtn"
                                Margin="0,16,0,0"
                                Padding="0,8"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Stretch"
                                Click="OpenChangelog"
                                CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(OpenChangelogBtn)}"
                                Style="{ThemeResource AccentButtonStyle}">
                            <Grid Margin="16,0"
                                  HorizontalAlignment="Stretch">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Grid HorizontalAlignment="Left">
                                    <TextBlock x:Name="OpenChangelogBtnText"
                                               Margin="0,-2,0,0"
                                               HorizontalAlignment="Left"
                                               VerticalAlignment="Center"
                                               FontWeight="Medium"
                                               HorizontalTextAlignment="Left"
                                               Text="{x:Bind helper:Locale.Lang._SettingsPage.Update_SeeChangelog}" />
                                </Grid>
                                <FontIcon Grid.Column="1"
                                          HorizontalAlignment="Right"
                                          FontFamily="{ThemeResource FontAwesomeSolid}"
                                          FontSize="14"
                                          Glyph="" />
                            </Grid>
                        </Button>
                    </StackPanel>
                    <NavigationViewItemSeparator Margin="16,16" />
                    <TextBlock Margin="0,0,0,16"
                               Style="{ThemeResource SubtitleTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._SettingsPage.AppFiles}" />
                    <Button x:Name="ButtonAppActionRelocateFolder"
                            Margin="0,0,0,8"
                            Padding="12,8"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="RelocateFolder"
                            CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ButtonAppActionRelocateFolder)}"
                            Style="{ThemeResource AccentButtonStyle}">
                        <Grid Margin="0,-2,0,0"
                              HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppFiles_RelocateDataFolderBtn}" />
                            </Grid>
                            <Grid Grid.Column="1"
                                  Margin="4,2,0,0"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  RenderTransformOrigin="0.5, 0.5">
                                <Grid.RenderTransform>
                                    <RotateTransform Angle="-90" />
                                </Grid.RenderTransform>
                                <AnimatedIcon Width="16"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center">
                                    <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                    <AnimatedIcon.FallbackIconSource>
                                        <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                        Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                        Glyph="&#xE70D;" />
                                    </AnimatedIcon.FallbackIconSource>
                                </AnimatedIcon>
                            </Grid>
                        </Grid>
                    </Button>
                    <Button x:Name="ButtonAppActionOpenAppDataFolder"
                            Margin="0,0,0,8"
                            Padding="12,8"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="OpenAppDataFolder"
                            CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ButtonAppActionOpenAppDataFolder)}"
                            Style="{ThemeResource AccentButtonStyle}">
                        <Grid Margin="0,-2,0,0"
                              HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppFiles_OpenDataFolderBtn}" />
                            </Grid>
                            <Grid Grid.Column="1"
                                  Margin="4,2,0,0"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  RenderTransformOrigin="0.5, 0.5">
                                <Grid.RenderTransform>
                                    <RotateTransform Angle="-90" />
                                </Grid.RenderTransform>
                                <AnimatedIcon Width="16"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center">
                                    <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                    <AnimatedIcon.FallbackIconSource>
                                        <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                        Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                        Glyph="&#xE70D;" />
                                    </AnimatedIcon.FallbackIconSource>
                                </AnimatedIcon>
                            </Grid>
                        </Grid>
                    </Button>
                    <Button x:Name="ButtonAppActionClearImgFolder"
                            Margin="0,0,0,8"
                            Padding="12,8"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="ClearImgFolder"
                            CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ButtonAppActionClearImgFolder)}"
                            Style="{ThemeResource AccentButtonStyle}">
                        <Grid Margin="0,-2,0,0"
                              HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppFiles_ClearImgCachesBtn}" />
                            </Grid>
                            <Grid Grid.Column="1"
                                  Margin="4,2,0,0"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  RenderTransformOrigin="0.5, 0.5">
                                <Grid.RenderTransform>
                                    <RotateTransform Angle="-90" />
                                </Grid.RenderTransform>
                                <AnimatedIcon Width="16"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center">
                                    <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                    <AnimatedIcon.FallbackIconSource>
                                        <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                        Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                        Glyph="&#xE70D;" />
                                    </AnimatedIcon.FallbackIconSource>
                                </AnimatedIcon>
                            </Grid>
                        </Grid>
                    </Button>
                    <Button x:Name="ButtonAppActionClearMetadataFolder"
                            Margin="0,0,0,8"
                            Padding="12,8"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="ClearMetadataFolder"
                            CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ButtonAppActionClearMetadataFolder)}"
                            Style="{ThemeResource AccentButtonStyle}">
                        <Grid Margin="0,-2,0,0"
                              HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppFiles_ClearMetadataBtn}" />
                            </Grid>
                            <Grid Grid.Column="1"
                                  Margin="4,2,0,0"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  RenderTransformOrigin="0.5, 0.5">
                                <Grid.RenderTransform>
                                    <RotateTransform Angle="-90" />
                                </Grid.RenderTransform>
                                <AnimatedIcon Width="16"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center">
                                    <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                    <AnimatedIcon.FallbackIconSource>
                                        <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                        Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                        Glyph="&#xE70D;" />
                                    </AnimatedIcon.FallbackIconSource>
                                </AnimatedIcon>
                            </Grid>
                        </Grid>
                    </Button>
                    <Button x:Name="ButtonAppActionClearLogsFolder"
                            Margin="0,0,0,8"
                            Padding="12,8"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="ClearLogsFolder"
                            CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ButtonAppActionClearLogsFolder)}"
                            Style="{ThemeResource AccentButtonStyle}">
                        <Grid Margin="0,-2,0,0"
                              HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.AppFiles_ClearLogBtn}" />
                            </Grid>
                            <Grid Grid.Column="1"
                                  Margin="4,2,0,0"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  RenderTransformOrigin="0.5, 0.5">
                                <Grid.RenderTransform>
                                    <RotateTransform Angle="-90" />
                                </Grid.RenderTransform>
                                <AnimatedIcon Width="16"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center">
                                    <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                    <AnimatedIcon.FallbackIconSource>
                                        <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                        Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                        Glyph="&#xE70D;" />
                                    </AnimatedIcon.FallbackIconSource>
                                </AnimatedIcon>
                            </Grid>
                        </Grid>
                    </Button>
                    <NavigationViewItemSeparator Margin="16,16" />
                    <Button x:Name="ShareYourFeedbackButton"
                            Margin="0,0,0,8"
                            Padding="12,8"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Click="ShareYourFeedbackClick"
                            CornerRadius="{x:Bind ext:UIElementExtensions.AttachRoundedKindCornerRadius(ShareYourFeedbackButton)}"
                            IsEnabled="True"
                            Style="{ThemeResource AccentButtonStyle}">
                        <Grid Margin="0,-2,0,0"
                              HorizontalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid HorizontalAlignment="Left">
                                <TextBlock HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontWeight="Medium"
                                           HorizontalTextAlignment="Left"
                                           Text="{x:Bind helper:Locale.Lang._SettingsPage.ShareYourFeedbackBtn}" />
                            </Grid>
                            <Grid Grid.Column="1"
                                  Margin="4,2,0,0"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  RenderTransformOrigin="0.5, 0.5">
                                <Grid.RenderTransform>
                                    <RotateTransform Angle="-90" />
                                </Grid.RenderTransform>
                                <AnimatedIcon Width="16"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center">
                                    <animatedvisuals:AnimatedChevronDownSmallVisualSource />
                                    <AnimatedIcon.FallbackIconSource>
                                        <FontIconSource FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                                        Foreground="{ThemeResource ComboBoxDropDownGlyphForeground}"
                                                        Glyph="&#xE70D;" />
                                    </AnimatedIcon.FallbackIconSource>
                                </AnimatedIcon>
                            </Grid>
                        </Grid>
                    </Button>
                    <StackPanel HorizontalAlignment="Center"
                                Orientation="Horizontal">
                        <TextBlock Margin="0,0,6,0"
                                   FontFamily="{ThemeResource FontAwesomeBrand}"
                                   FontSize="24"
                                   Foreground="{ThemeResource AccentColor}"
                                   PointerPressed="ClickTextLinkFromTag"
                                   Tag="https://web.facebook.com/kemalsetya.adhisetya/"
                                   Text="&#xF082;" />
                        <TextBlock Margin="6,0,6,0"
                                   FontFamily="{ThemeResource FontAwesomeBrand}"
                                   FontSize="24"
                                   Foreground="{ThemeResource AccentColor}"
                                   PointerPressed="ClickTextLinkFromTag"
                                   Tag="https://github.com/neon-nyan/"
                                   Text="&#xF092;" />
                        <TextBlock Margin="6,0,6,0"
                                   FontFamily="{ThemeResource FontAwesomeBrand}"
                                   FontSize="24"
                                   Foreground="{ThemeResource AccentColor}"
                                   PointerPressed="ClickTextLinkFromTag"
                                   Tag="https://www.reddit.com/user/justanewbie1-ID/"
                                   Text="&#xF1A2;" />
                        <TextBlock Margin="6,0,6,0"
                                   FontFamily="{ThemeResource FontAwesomeBrand}"
                                   FontSize="24"
                                   Foreground="{ThemeResource AccentColor}"
                                   PointerPressed="ClickTextLinkFromTag"
                                   Tag="https://twitter.com/neonnyann"
                                   Text="&#xF081;" />
                        <TextBlock Margin="6,0,6,0"
                                   FontFamily="{ThemeResource FontAwesomeBrand}"
                                   FontSize="24"
                                   Foreground="{ThemeResource AccentColor}"
                                   PointerPressed="ClickTextLinkFromTag"
                                   Tag="https://instagram.com/neonnyann/"
                                   Text="&#xE055;" />
                        <TextBlock Margin="6,0,6,0"
                                   FontFamily="{ThemeResource FontAwesomeBrand}"
                                   FontSize="24"
                                   Foreground="{ThemeResource AccentColor}"
                                   PointerPressed="Egg"
                                   Text="&#xE499;" />
                    </StackPanel>
                </StackPanel>
            </Grid>
        </ScrollViewer>
        <Grid x:Name="SettingsSearchBoxGridShadow"
              Width="{x:Bind SettingsSearchBoxGrid.Width, Mode=OneWay}"
              Height="{x:Bind SettingsSearchBox.Height, Mode=OneWay}"
              Margin="{x:Bind SettingsSearchBoxGrid.Margin, Mode=OneWay}"
              HorizontalAlignment="{x:Bind SettingsSearchBoxGrid.HorizontalAlignment, Mode=OneWay}"
              VerticalAlignment="{x:Bind SettingsSearchBoxGrid.VerticalAlignment, Mode=OneWay}"
              CornerRadius="{x:Bind SettingsSearchBoxGrid.CornerRadius, Mode=OneWay}"
              Shadow="{StaticResource SharedShadow}"
              Translation="0,0,8"/>
        <Grid x:Name="SettingsSearchBoxGrid"
              Width="380"
              Margin="0,0,16,0"
              HorizontalAlignment="Right"
              VerticalAlignment="Top"
              GettingFocus="SettingsSearchBox_OnGettingFocus"
              LosingFocus="SettingsSearchBox_OnLosingFocus"
              Background="{ThemeResource DialogAcrylicBrush}"
              CornerRadius="{x:Bind SettingsSearchBox.CornerRadius}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <AutoSuggestBox x:Name="SettingsSearchBox"
                            Height="40"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            VerticalContentAlignment="Center"
                            BorderThickness="0"
                            CornerRadius="4"
                            PlaceholderText="{x:Bind helper:Locale.Lang._SettingsPage.SearchPlaceholder}"
                            QueryIcon="Find"
                            QuerySubmitted="SettingsSearchBox_QuerySubmitted"
                            TextChanged="SettingsSearchBox_TextChanged"/>
            <Grid Grid.Column="1"
                  VerticalAlignment="Center"
                  Margin="8,0,8,0"
                  Visibility="{x:Bind _highlightedControls.Count, Converter={StaticResource CountToVisibilityConverter}, Mode=OneWay}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition MinWidth="54"/>
                    <ColumnDefinition/>
                    <ColumnDefinition/>
                </Grid.ColumnDefinitions>
                <TextBlock x:Name="SettingsSearchHighlightPosText"
                           Text="4 / 20"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           HorizontalTextAlignment="Center"
                           Margin="0,0,8,0"/>
                <Grid Grid.Column="1"
                      Grid.ColumnSpan="2"
                      Shadow="{ThemeResource SharedShadow}"
                      Translation="0,0,16"
                      CornerRadius="4"/>
                <Button x:Name="SettingsSearchHighlightPreviousBtn"
                        Grid.Column="1"
                        Padding="8,4,8,4"
                        CornerRadius="4,0,0,4"
                        TabFocusNavigation="Cycle"
                        TabIndex="99999"
                        Click="SettingsSearchBoxFindSelectPrevious">
                    <FontIcon Glyph="&#xE96D;"
                              FontSize="14"/>
                </Button>
                <Button x:Name="SettingsSearchHighlightNextBtn"
                        Grid.Column="2"
                        Padding="8,4,8,4"
                        CornerRadius="0,4,4,0"
                        TabFocusNavigation="Cycle"
                        TabIndex="99999"
                        Click="SettingsSearchBoxFindSelectNext">
                    <FontIcon Glyph="&#xE96E;"
                              FontSize="14"/>
                </Button>
            </Grid>
        </Grid>
    </Grid>
</Page>
