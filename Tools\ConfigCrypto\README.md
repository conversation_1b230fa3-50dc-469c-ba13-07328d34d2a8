# CollapseLauncher Configuration Crypto Tool

这个工具用于加密和解密CollapseLauncher配置文件中的敏感字段。

## 🚀 功能特性

- 🔓 解密配置文件中的加密字段
- 🔐 加密明文数据为配置格式
- 📊 分析配置文件结构和加密状态
- 🏗️ 生成新的配置文件和stamp条目
- 📦 批量处理配置文件
- ✅ 验证加密数据完整性
- 🧪 完整的测试套件

## 📁 工具组件

```
Tools/ConfigCrypto/
├── 🔧 crypto_tool.py          # 主加密/解密工具
├── 📊 field_analyzer.py       # 配置字段分析器
├── 🏗️ config_generator.py     # 配置文件生成器
├── 🧪 test_crypto.py          # 测试套件
├── 🖥️ decrypt.bat             # Windows快速解密脚本
├── 🐧 decrypt.sh              # Linux/macOS快速解密脚本
├── 📄 requirements.txt        # Python依赖
└── 📂 templates/              # 配置模板
    └── template_basic.json
```

## 🔒 支持的加密字段

### 单个字符串字段
- `GameDispatchURL` - 游戏分发服务器URL
- `LauncherSpriteURL` - 启动器精灵图URL
- `LauncherResourceURL` - 启动器资源URL
- `LauncherPluginURL` - 启动器插件URL
- `LauncherNewsURL` - 启动器新闻URL
- `DispatcherKey` - 分发器密钥
- `LauncherId` - 启动器ID
- `LauncherGameId` - 游戏ID
- `LauncherBizName` - 业务名称
- `LauncherCPSType` - CPS类型

### 字符串数组字段
- `GameDispatchArrayURL` - 游戏分发URL数组
- `GameSupportedLanguages` - 支持的语言列表

### 复杂对象字段
- `LauncherResourceChunksURL` - 资源块URL配置
- `ApiResourceAdditionalHeaders` - API额外头部信息

## 🚀 快速开始

### 方法1: 使用快速脚本 (推荐)

```bash
# Windows用户
decrypt.bat config_Hi3CN.json

# Linux/macOS用户
chmod +x decrypt.sh
./decrypt.sh config_Hi3CN.json
```

### 方法2: 直接使用Python工具

```bash
# 分析配置文件
python field_analyzer.py config_Hi3CN.json

# 解密配置文件
python crypto_tool.py decrypt-config \
    --input config_Hi3CN.json \
    --output decrypted_config.json \
    --master-key ../MasterKeyGenerator/output/config_master.json
```

## 🔧 详细使用方法

### 1. 配置文件分析

```bash
# 分析配置文件结构
python field_analyzer.py config_Hi3CN.json

# 生成详细分析报告
python field_analyzer.py config_Hi3CN.json --output-report analysis.json

# 生成自动解密脚本
python field_analyzer.py config_Hi3CN.json --generate-script auto_decrypt.py
```

### 2. 加密/解密操作

```bash
# 解密单个字段
python crypto_tool.py decrypt \
    --field "GameDispatchURL" \
    --data "Q29sbGFwc2U..." \
    --master-key config_master.json

# 加密明文数据
python crypto_tool.py encrypt \
    --data "https://example.com/api" \
    --master-key config_master.json

# 批量解密多个文件
python crypto_tool.py batch-decrypt \
    --input-dir ./configs \
    --output-dir ./decrypted \
    --master-key config_master.json
```

### 3. 生成新配置

```bash
# 交互式生成配置
python config_generator.py \
    --master-key config_master.json \
    --interactive

# 基于模板生成配置
python config_generator.py \
    --master-key config_master.json \
    --template templates/template_basic.json \
    --config-name config_MyGame.json \
    --game-name "My Game" \
    --game-region "Global"
```

## 📊 输出示例

### 分析报告示例
```
======================================================================
Configuration File Analysis: config_Hi3CN.json
======================================================================
📊 Summary:
   Total Fields: 45
   Encrypted Fields: 12
   Plaintext Fields: 33
   Unknown Fields: 0
   Total Encrypted Strings: 15

🔒 Encrypted Fields:
   🔐 GameDispatchArrayURL
      Type: list
      Description: Array of game dispatch URLs
      Items: 2
      Encrypted Items: 2

   🔐 LauncherResourceURL
      Type: str
      Description: Launcher resource download URL
      Length: 324 chars
      Encrypted Size: 256 bytes
```

### 解密输出示例
```
Decrypting configuration file: config_Hi3CN.json
--------------------------------------------------
✓ GameDispatchArrayURL: 2 items decrypted
✓ LauncherResourceURL: https://example.com/launcher/resources
✓ LauncherNewsURL: https://news.example.com/api
✓ DispatcherKey: dispatcher_key_12345
--------------------------------------------------
✓ Decrypted configuration saved to: config_Hi3CN_decrypted.json
```

## 🧪 测试工具

运行完整的测试套件：

```bash
python test_crypto.py
```

测试包括：
- 加密/解密循环测试
- 配置文件处理测试
- 字段分析测试
- 配置生成测试

## 📋 依赖要求

- Python 3.7+
- cryptography >= 3.4.8
- pycryptodome >= 3.15.0

安装依赖：
```bash
pip install -r requirements.txt
```

## 🔐 安全注意事项

1. **Master Key保护**
   - 确保master key文件安全存储
   - 不要将master key提交到版本控制
   - 使用适当的文件权限保护

2. **明文数据处理**
   - 解密后的数据包含敏感信息
   - 及时删除不需要的明文文件
   - 避免在不安全的环境中处理

3. **备份策略**
   - 处理前备份原始配置文件
   - 保留加密版本作为主要存储
   - 定期验证备份完整性

4. **访问控制**
   - 限制工具的访问权限
   - 记录敏感操作日志
   - 定期审查访问记录
