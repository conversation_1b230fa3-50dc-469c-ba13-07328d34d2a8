﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <!-- General Properties -->
        <OutputType>WinExe</OutputType>
        <ApplicationIcon>icon.ico</ApplicationIcon>
        <StartupObject>CollapseLauncher.MainEntryPoint</StartupObject>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <Configurations>Debug;Release;Publish</Configurations>
        <!-- Assembly Info Properties -->
        <AssemblyName>CollapseLauncher</AssemblyName>
        <ProductName>Collapse</ProductName>
        <Product>Collapse</Product>
        <Description>Collapse</Description>
        <AssemblyTitle>Collapse</AssemblyTitle>
        <Company>Collapse Launcher Team</Company>
        <Authors>$(Company). neon-nyan, Cry0, bagusnl, shatyuka, gablm.</Authors>
        <Copyright>Copyright 2022-2025 $(Company)</Copyright>
        <!-- Versioning -->
        <Version>1.83.5</Version>
        <LangVersion>preview</LangVersion>
        <!-- Target Settings -->
        <Platforms>x64</Platforms>
        <TargetFramework>net9.0-windows10.0.26100.0</TargetFramework>
        <WindowsSdkPackageVersion>10.0.26100.57</WindowsSdkPackageVersion>
        <TargetPlatformMinVersion>10.0.17763.0</TargetPlatformMinVersion>
        <RuntimeIdentifier>win-x64</RuntimeIdentifier>
        <EnableWindowsTargeting>true</EnableWindowsTargeting>
        <IsAotCompatible>true</IsAotCompatible>
        <CsWinRTAotOptimizerEnabled>true</CsWinRTAotOptimizerEnabled>
        <!-- Debug Settings -->
        <DebugType>portable</DebugType>
        <GitVersion>false</GitVersion>
        <!-- AoT Configs -->
        <PublishAot>false</PublishAot>
        <!-- WinUI Properties -->
        <UseWinUI>true</UseWinUI>
        <WindowsAppSDKSelfContained>true</WindowsAppSDKSelfContained>
        <!-- Analyzers Settings -->
        <EnableAOTAnalyzer>true</EnableAOTAnalyzer>
        <EnableTrimAnalyzer>true</EnableTrimAnalyzer>
        <SuppressTrimAnalysisWarnings>false</SuppressTrimAnalysisWarnings>
        <TrimmerSingleWarn>false</TrimmerSingleWarn>
        <!-- Other Settings -->
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
        <EnablePreviewMsixTooling>true</EnablePreviewMsixTooling>
        <BuiltInComInteropSupport>false</BuiltInComInteropSupport>
        <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
        <InvariantGlobalization>false</InvariantGlobalization>
        <ShouldComputeInputPris>true</ShouldComputeInputPris>
        <DisableImplicitNuGetFallbackFolder>true</DisableImplicitNuGetFallbackFolder>
        <GenerateNeutralResourcesLanguageAttribute>false</GenerateNeutralResourcesLanguageAttribute>
        <JsonSerializerIsReflectionEnabledByDefault>false</JsonSerializerIsReflectionEnabledByDefault>
        <WebView2EnableCsWinRTProjection>true</WebView2EnableCsWinRTProjection>
    </PropertyGroup>

    <PropertyGroup>
        <NoWarn>$(NoWarn);TA101;TA100</NoWarn>
        <WarningLevel>5</WarningLevel>
        <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    </PropertyGroup>

<!--    <PropertyGroup Condition="'$(PublishAot)' == 'true' And '$(Configuration)' != 'Debug'">-->
    <PropertyGroup Condition="'$(PublishAot)' == 'true'">
        <RunAOTCompilation>true</RunAOTCompilation>
        <OptimizationPreference>Speed</OptimizationPreference>
        <StripSymbols>true</StripSymbols>
        <IlcTrimMetadata>true</IlcTrimMetadata>
        <EventSourceSupport>false</EventSourceSupport>
        <DebuggerSupport>false</DebuggerSupport>
        <DefineConstants>$(DefineConstants);AOT</DefineConstants>
        <IlcInstructionSet>sse2</IlcInstructionSet>
        <!-- Security thingymajig -->
        <ControlFlowGuard>Guard</ControlFlowGuard>
        <CetCompat>true</CetCompat>
    </PropertyGroup>

<!--
  Constants List:
        - PREVIEW : States the version is a preview branch 
        - DISABLEDISCORD : Disable Discord RPC module
        - SIMULATEPRELOAD : Simulate downloading preload
        - ENABLEHTTPREPAIR : Override HTTPS download scheme to HTTP while downloading repair files
        - SIMULATEAPPLYPRELOAD : Simulates downloading and applying preload update, regardless of current working game version (WILL BREAK GAME)
        - DUMPGIJSON : Dumps Genshin Impact GeneralData JSON registry into console (CPU INTENSIVE LOAD!)
        - SIMULATEGIHDR : Force enable Genshin Impact HDR settings panel for non-HDR panels (Note: this wont force GI to use HDR)
        - GSPBYPASSGAMERUNNING : Bypass checks for Game Settings Pages for currently running game
        - MHYPLUGINSUPPORT : Enable miHoYo's patching system through launcher (EXPERIMENTAL)
        - USEFFMPEGFORVIDEOBG : Use FFmpeg to decode video background content.
                                This decoder supports lots of newest format, including AV1, HEVC and MPEG-DASH Contained video.
        - USENEWZIPDECOMPRESS : Use sharpcompress for decompressing .zip game package files
        - USEVELOPACK : Use Velopack as the update manager
        - ENABLEUSERFEEDBACK : Enable user feedback feature
-->
    <PropertyGroup Condition="'$(Configuration)'=='Debug'">
        <!-- !! IMPORTANT !!-->
        <!-- DO NOT FORGET TO UPDATE THE DEFINECONSTANTS IN THE PUBLISH PROFILE(S) AS WELL   -->
        <DefineConstants>DISABLE_XAML_GENERATED_MAIN;ENABLEUSERFEEDBACK;USEVELOPACK;USENEWZIPDECOMPRESS;ENABLEHTTPREPAIR;DISABLE_XAML_GENERATED_BREAK_ON_UNHANDLED_EXCEPTION;PREVIEW;DUMPGIJSON;SIMULATEGIHDR;GSPBYPASSGAMERUNNING;MHYPLUGINSUPPORT</DefineConstants>
        <TrimMode>full</TrimMode>
        <CsWinRTAotOptimizerEnabled>false</CsWinRTAotOptimizerEnabled>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Configuration)'=='Release'">
        <!-- !! IMPORTANT !!-->
        <!-- DO NOT FORGET TO UPDATE THE DEFINECONSTANTS IN THE PUBLISH PROFILE(S) AS WELL   -->
        <DefineConstants>DISABLE_XAML_GENERATED_MAIN;ENABLEUSERFEEDBACK;USEVELOPACK;USENEWZIPDECOMPRESS;ENABLEHTTPREPAIR;DISABLE_XAML_GENERATED_BREAK_ON_UNHANDLED_EXCEPTION;PREVIEW;MHYPLUGINSUPPORT</DefineConstants>
        <Optimize>True</Optimize>
        <GitSkipCache>true</GitSkipCache>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Configuration)'=='Publish'">
        <!-- !! IMPORTANT !!-->
        <!-- DO NOT FORGET TO UPDATE THE DEFINECONSTANTS IN THE PUBLISH PROFILE(S) AS WELL   -->
        <DefineConstants>DISABLE_XAML_GENERATED_MAIN;ENABLEUSERFEEDBACK;USEVELOPACK;USENEWZIPDECOMPRESS;ENABLEHTTPREPAIR;DISABLE_XAML_GENERATED_BREAK_ON_UNHANDLED_EXCEPTION;MHYPLUGINSUPPORT</DefineConstants>
        <Optimize>true</Optimize>
        <GitSkipCache>true</GitSkipCache>
    </PropertyGroup>
    
    <ItemGroup Condition="'$(PublishAot)' == 'true'">
        <!-- Use additional linker arguments -->
        <LinkerArg Include="/DEBUG:NONE" Condition="'$(Configuration)'!='Debug'" />
        <LinkerArg Include="/NODEFAULTLIB:MSVCRT /LTCG" />
        
        <!-- Region: Libsql.Client -->
        <DirectPInvoke Include="csharp_bindings" />
        <NativeLibrary Include="StaticLib\csharp_bindings_msvc-ucrt.lib" />
        <!--
        These libraries are required by csharp_bindings to resolve calls to:
            - GetUserProfileDirectoryW function from Win32's userenv.dll
            - SHGetKnownFolderPath function from Win32's shell32.dll
        -->
        <NativeLibrary Include="StaticLib\WindowsKits\10.0.26100.0_um_x64\UserEnv.lib" />
        <NativeLibrary Include="StaticLib\WindowsKits\10.0.26100.0_um_x64\shell32.lib" />
        
        <!-- Region: waifu2x-ncnn-vulkan -->
        <DirectPInvoke Include="Lib\waifu2x-ncnn-vulkan.dll" />
        <NativeLibrary Include="StaticLib\waifu2x-ncnn-vulkan-static_msvc-ucrt.lib" />
        <!--
        These libraries are required by waifu2x-ncnn-vulkan to resolve external calls.
        -->
        <NativeLibrary Include="StaticLib\Waifu2x_msvc-ucrt\ncnn.lib" />
        <NativeLibrary Include="StaticLib\Waifu2x_msvc-ucrt\SPIRV.lib" />
        <NativeLibrary Include="StaticLib\Waifu2x_msvc-ucrt\glslang.lib" />
        <NativeLibrary Include="StaticLib\Waifu2x_msvc-ucrt\glslang-default-resource-limits.lib" />
        <NativeLibrary Include="StaticLib\Waifu2x_msvc-ucrt\OGLCompiler.lib" />
        <NativeLibrary Include="StaticLib\Waifu2x_msvc-ucrt\MachineIndependent.lib" />
        <NativeLibrary Include="StaticLib\Waifu2x_msvc-ucrt\GenericCodeGen.lib" />
        <NativeLibrary Include="StaticLib\Waifu2x_msvc-ucrt\OSDependent.lib" />
        <NativeLibrary Include="StaticLib\libomp_msvc-ucrt.lib" />
        
        <!-- Region: webp -->
        <DirectPInvoke Include="webp" />
        <NativeLibrary Include="StaticLib\libwebp_msvc-ucrt.lib" />
        <DirectPInvoke Include="webpdemux" />
        <NativeLibrary Include="StaticLib\libwebpdemux_msvc-ucrt.lib" />
        <DirectPInvoke Include="webpmux" />
        <NativeLibrary Include="StaticLib\libwebpmux_msvc-ucrt.lib" />

        <!-- Region: zstd (Decompressor only) -->
        <DirectPInvoke Include="libzstd" />
        <NativeLibrary Include="StaticLib\libzstd-static_msvc-ucrt.lib" />

        <!--
        These libraries are required by libwebp to resolve external calls.
        -->
        <NativeLibrary Include="StaticLib\libsharpyuv_msvc-ucrt.lib" />
        <NativeLibrary Include="StaticLib\libwebpdecoder_msvc-ucrt.lib" />
    </ItemGroup>

    <ItemGroup>
        <!--
        Use Velopack if "USEVELOPACK" constant is used. Otherwise, use Squirrel.
        -->
        <PackageReference Include="Clowd.Squirrel" Version="2.11.1" Condition="!$(DefineConstants.Contains('USEVELOPACK'))" />
        <PackageReference Include="Libsql.Client" Version="0.5.0" />
        <!-- Velopacks' outdated dependencies, remove this once they switched to .NET 9 -->
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
        <!-- Velopacks' outdated dependencies, remove this once they switched to .NET 9 -->
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
        <PackageReference Include="TurnerSoftware.DinoDNS" Version="0.3.0" />
        <PackageReference Include="Velopack" Version="0.0.1297" Condition="$(DefineConstants.Contains('USEVELOPACK'))" />

        <PackageReference Include="CommunityToolkit.Common" Version="8.4.0" />
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="CommunityToolkit.WinUI.Behaviors" Version="8.2.250402" />
        <PackageReference Include="CommunityToolkit.WinUI.Controls.Primitives" Version="8.2.250402" />
        <PackageReference Include="CommunityToolkit.WinUI.Media" Version="8.2.250402" />
        <PackageReference Include="CommunityToolkit.WinUI.Converters" Version="8.2.250402" />
        <PackageReference Include="CommunityToolkit.WinUI.Extensions" Version="8.2.250402" />
        <PackageReference Include="CommunityToolkit.WinUI.Controls.Sizers" Version="8.2.250402" />
        
        <!--
        Only include FFmpegInteropX NuGet if USEFFMPEGFORVIDEOBG is defined in constants.
        Also, Version="*-*" means grab the latest preview version.
        -->
        <PackageReference Include="FFmpegInteropX" Version="*-*" Condition="$(DefineConstants.Contains('USEFFMPEGFORVIDEOBG'))" />
        
        <PackageReference Include="GitInfo" Version="3.5.0" PrivateAssets="all" />
        <PackageReference Include="HtmlAgilityPack" Version="1.12.1" />
        <PackageReference Include="Markdig.Signed" Version="0.41.2" />
        <PackageReference Include="Microsoft.Graphics.Win2D" Version="1.3.2" />
        <PackageReference Include="Microsoft.NETCore.Targets" Version="6.0.0-preview.4.21253.7" />
        <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.3344-prerelease" />
        <PackageReference Include="Microsoft.Windows.CsWinRT" Version="2.2.0" />
        <PackageReference Include="Microsoft.Windows.SDK.BuildTools" Version="10.0.26100.4188" />
        <PackageReference Include="Microsoft.WindowsAppSDK" Version="1.8.250515001-experimental2" />
        <PackageReference Include="Microsoft.Xaml.Behaviors.WinUI.Managed" Version="3.0.0" />
        <PackageReference Include="PhotoSauce.MagicScaler" Version="0.15.0" />
        <PackageReference Include="PhotoSauce.NativeCodecs.Libwebp" Version="*-*" />
        <PackageReference Include="Roman-Numerals" Version="2.0.1" />
        <PackageReference Include="SharpCompress" Version="0.40.0" Condition="$(DefineConstants.Contains('USENEWZIPDECOMPRESS'))" />
        <PackageReference Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
        <PackageReference Include="System.CommandLine.NamingConventionBinder" Version="2.0.0-beta4.22272.1" />
        <PackageReference Include="System.Security.AccessControl" Version="6.0.1" />
        <PackageReference Include="System.Security.Cryptography.ProtectedData" Version="9.0.5" />
        <PackageReference Include="System.Text.Encoding.CodePages" Version="9.0.5" />
        <PackageReference Include="System.Text.Json" Version="9.0.5" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\ColorThief\ColorThief\ColorThief.csproj" />
        <ProjectReference Include="..\H.NotifyIcon\src\libs\H.NotifyIcon.WinUI\H.NotifyIcon.WinUI.csproj" />
        <ProjectReference Include="..\Hi3Helper.Win32\Hi3Helper.Win32.csproj" />
        <ProjectReference Include="..\Hi3Helper.Win32\WinRT\Hi3Helper.Win32.WinRT.csproj" />
        <ProjectReference Include="..\SevenZipExtractor\SevenZipExtractor\SevenZipExtractor.csproj" />
        <ProjectReference Include="..\Hi3Helper.CommunityToolkit\ImageCropper\Hi3Helper.CommunityToolkit.WinUI.Controls.ImageCropper.csproj" />
        <ProjectReference Include="..\Hi3Helper.CommunityToolkit\SettingsControls\Hi3Helper.CommunityToolkit.WinUI.Controls.SettingsControls.csproj" />
        <ProjectReference Include="..\Hi3Helper.Core\Hi3Helper.Core.csproj" />
        <ProjectReference Include="..\Hi3Helper.SharpDiscordRPC\DiscordRPC\DiscordRPC.csproj" />
        <ProjectReference Include="..\Hi3Helper.Http\Hi3Helper.Http.csproj" />
        <ProjectReference Include="..\Hi3Helper.Sophon\Hi3Helper.Sophon.csproj" />
        <ProjectReference Include="..\InnoSetupHelper\InnoSetupHelper.csproj" />
        <ProjectReference Include="..\ImageEx\ImageEx\ImageEx.csproj" />
    </ItemGroup>

    <ItemGroup>

        <Content Include="Misc\InstallMediaPack.cmd">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>

        <Content Update="Assets\**">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>

        <Content Include="icon.ico">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>

        <None Include="..\.editorconfig" Link=".editorconfig" />

        <Content Include="..\msi-installer-licensefile.rtf">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
            <Link>LICENSE.rtf</Link>
        </Content>

        <Content Include="..\PRIVACY.md">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>

        <Content Include="..\THIRD_PARTY_NOTICES.md">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>

        <Content Include="Lib\**">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    
    <Target Name="ConfigureTrimming" BeforeTargets="PrepareForILLink">
        <ItemGroup>
            <!-- Trimmable submodule assemblies -->
            <TrimmableAssembly Include="ColorThief" />
            <TrimmableAssembly Include="DiscordRPC" />
            <TrimmableAssembly Include="Hi3Helper.Core" />
            <TrimmableAssembly Include="Hi3Helper.EncTool" />
            <TrimmableAssembly Include="Hi3Helper.Http" />
            <TrimmableAssembly Include="Hi3Helper.Sophon" />
            <TrimmableAssembly Include="ImageEx" />
            <TrimmableAssembly Include="InnoSetupHelper" />
            <TrimmableAssembly Include="SevenZipExtractor" />
            <TrimmableAssembly Include="SharpHDiffPatch.Core" />

            <!-- Untrimmable submodule assemblies-->
      
            <!-- Additional assemblies -->
            <TrimmableAssembly Include="ColorCode.Core" />
            <TrimmableAssembly Include="ColorCode.WinUI" />
            <TrimmableAssembly Include="CommunityToolkit.Common" />
            <TrimmableAssembly Include="CommunityToolkit.Mvvm" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Animations" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Behaviors" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Controls.ImageCropper" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Controls.Primitives" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Controls.SettingsControls" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Controls.Sizers" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Converters" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Extensions" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Helpers" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Media" />
            <TrimmableAssembly Include="CommunityToolkit.WinUI.Triggers" />
            <TrimmableAssembly Include="Google.Protobuf" />
            <TrimmableAssembly Include="H.GeneratedIcons.System.Drawing" />
            <TrimmableAssembly Include="H.NotifyIcon" />
            <TrimmableAssembly Include="H.NotifyIcon.WinUI" />
            <TrimmableAssembly Include="Microsoft.DirectManipulation" />
            <TrimmableAssembly Include="Microsoft.Graphics.Canvas" />
            <TrimmableAssembly Include="Microsoft.Graphics.Canvas.Interop" />
            <TrimmableAssembly Include="Microsoft.Graphics.Display" />
            <TrimmableAssembly Include="Microsoft.InputStateManager" />
            <TrimmableAssembly Include="Microsoft.InteractiveExperiences.Projection" />
            <TrimmableAssembly Include="Microsoft.Internal.FrameworkUdk" />
            <TrimmableAssembly Include="Microsoft.UI.Composition.OSSupport" />
            <TrimmableAssembly Include="Microsoft.UI" />
            <TrimmableAssembly Include="Microsoft.ui.xaml" />
            <TrimmableAssembly Include="Microsoft.UI.Xaml.Controls" />
            <TrimmableAssembly Include="Microsoft.UI.Xaml.Internal" />
            <TrimmableAssembly Include="Microsoft.UI.Xaml.Phone" />
            <TrimmableAssembly Include="Microsoft.UI.Windowing.Core" />
            <TrimmableAssembly Include="Microsoft.UI.Windowing" />
            <TrimmableAssembly Include="Microsoft.Web.WebView2.Core" />
            <TrimmableAssembly Include="Microsoft.Windows.SDK.NET" />
            <TrimmableAssembly Include="Microsoft.Windows.Widgets" />
            <TrimmableAssembly Include="Microsoft.WindowsAppRuntime.Bootstrap" />
            <TrimmableAssembly Include="Microsoft.WindowsAppRuntime" />
            <TrimmableAssembly Include="PhotoSauce.MagicScaler" />
            <TrimmableAssembly Include="SquirrelLib" />
            <TrimmableAssembly Include="System.CommandLine" />
            <TrimmableAssembly Include="System.Drawing.Common" />
            <TrimmableAssembly Include="System.Linq.Expressions" />
            <TrimmableAssembly Include="System.Net.Http" />
            <TrimmableAssembly Include="System.Net.Security" />
            <TrimmableAssembly Include="System.Security.Cryptography" />
            <TrimmableAssembly Include="System.Text.Json" />
            <TrimmableAssembly Include="System.Text.RegularExpressions" />
            <TrimmableAssembly Include="WinRT.Runtime" />
            <TrimmableAssembly Include="WinUIEdit" />
            <TrimmableAssembly Include="ZstdSharp" />

            <!-- Descriptor for all classes that cannot be trimmed -->
            <TrimmerRootDescriptor Include="NonTrimmableRoots.xml" />

            <!--
            <TrimmerRootAssembly Include="CommunityToolkit.WinUI" />
            <TrimmerRootAssembly Include="CommunityToolkit.WinUI.Extensions" />
            <TrimmerRootAssembly Include="ImageEx" />
            <TrimmerRootAssembly Include="Microsoft.Windows.AppLifecycle.Projection" />
            <TrimmerRootAssembly Include="Microsoft.Windows.AppNotifications.Builder.Projection" />
            <TrimmerRootAssembly Include="Microsoft.Windows.AppNotifications.Projection" />
            <TrimmerRootAssembly Include="Microsoft.Windows.ApplicationModel.DynamicDependency.Projection" />
            <TrimmerRootAssembly Include="Microsoft.Windows.ApplicationModel.Resources.Projection" />
            <TrimmerRootAssembly Include="Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection" />
            <TrimmerRootAssembly Include="Microsoft.Windows.PushNotifications.Projection" />
            <TrimmerRootAssembly Include="Microsoft.Windows.SDK.NET" />
            <TrimmerRootAssembly Include="Microsoft.Windows.Security.AccessControl.Projection" />
            <TrimmerRootAssembly Include="Microsoft.Windows.System.Power.Projection" />
            <TrimmerRootAssembly Include="Microsoft.Windows.System.Projection" />
            <TrimmerRootAssembly Include="Microsoft.Windows.Widgets.Providers.Projection" />
            <TrimmerRootAssembly Include="SquirrelLib" />
            <TrimmerRootAssembly Include="System.CommandLine.NamingConventionBinder" />
            <TrimmerRootAssembly Include="WinRT.Runtime" />
            -->
        </ItemGroup>
    </Target>

    <Target Name="PreBuild-TaskSheduler" BeforeTargets="PreBuildEvent" Inputs="$(ProjectDir)..\Hi3Helper.TaskScheduler\Hi3Helper.TaskScheduler.csproj;@(ProjectReference->'%(FullPath)')" Outputs="$(ProjectDir)$(OutDir)Lib\win-x64\Hi3Helper.TaskScheduler.exe">
        <Exec Command="dotnet publish $(ProjectDir)..\Hi3Helper.TaskScheduler\Hi3Helper.TaskScheduler.csproj /p:PublishProfile=AsRelease&#xD;&#xA;mkdir $(ProjectDir)$(OutDir)Lib\win-x64&#xD;&#xA;copy /Y $(ProjectDir)..\Hi3Helper.TaskScheduler\bin\publish\Hi3Helper.TaskScheduler.exe $(ProjectDir)$(OutDir)Lib\win-x64 &amp; exit 0" />
    </Target>

    <Target Name="PostBuild" AfterTargets="Build">
        <Exec Command="del /s /q $(OutDir)av*-58.dll $(OutDir)av*-60.dll $(OutDir)avfilter-9.dll $(OutDir)av*.lib&#xD;&#xA;rd /s /q $(OutDir)Lib\win-arm64 &amp; exit 0" Condition="$(DefineConstants.Contains('USEFFMPEGFORVIDEOBG'))" />
        <RemoveDir Directories="$(OutDir)Lib\win-arm64" Condition="Exists('$(OutDir)Lib\win-arm64')" />
        <RemoveDir Directories="$(OutDir)Lib\win-x86" Condition="Exists('$(OutDir)Lib\win-x86')" />
    </Target>

    <Target Name="PreBuild-NativeAOTStaticLib" BeforeTargets="PreBuildEvent" Condition="'$(PublishAot)' == 'true'">
        <Exec Command="&quot;$(ProjectDir)StaticLib\7za.exe&quot; x &quot;$(ProjectDir)StaticLib\StaticLib.7z.001&quot; -o&quot;$(ProjectDir)StaticLib\&quot; -y" />
    </Target>
    
    <Target Name="PostBuild" AfterTargets="Publish">
        <Exec Command="del /s /q $(PublishDir)av*-58.dll $(PublishDir)av*-60.dll $(PublishDir)avfilter-9.dll $(PublishDir)av*.lib&#xD;&#xA;rd /s /q $(PublishDir)Lib\win-arm64 &amp; exit 0" Condition="$(DefineConstants.Contains('USEFFMPEGFORVIDEOBG'))" />
        <Exec Command="copy /Y $(ProjectDir)$(OutDir)Lib\win-x64\Hi3Helper.TaskScheduler.exe $(PublishDir)Lib\win-x64\" />
        <Exec Command="&quot;$(ProjectDir)aot-locale-cleanup.bat&quot; &quot;$(PublishDir)&quot;" Condition="'$(PublishAot)' == 'true'" />
        <RemoveDir Directories="$(PublishDir)Lib\win-arm64" Condition="Exists('$(PublishDir)Lib\win-arm64')" />
		<RemoveDir Directories="$(PublishDir)Lib\win-x86" Condition="Exists('$(PublishDir)Lib\win-x86')" />
		<RemoveDir Directories="$(PublishDir)NpuDetect" Condition="Exists('$(PublishDir)NpuDetect')" />
    </Target>

    <Target Name="PostPublish" AfterTargets="Publish">
        <!-- Region: Libsql.Client -->
        <Delete Files="$(PublishDir)csharp_bindings.dll" Condition="'$(PublishAot)' == 'true'"/>

        <!-- Region: waifu2x-ncnn-vulkan -->
        <Delete Files="$(PublishDir)Lib\waifu2x-ncnn-vulkan.dll" Condition="'$(PublishAot)' == 'true'"/>
        <Delete Files="$(PublishDir)Lib\libomp140.x86_64.dll" Condition="'$(PublishAot)' == 'true'"/>

        <!-- Region: libzstd -->
        <ItemGroup Condition="'$(PublishAot)' == 'true'">
            <FilesToDelete Include="$(PublishDir)Lib\win-x64\libzstd*" />
        </ItemGroup>
        
        <!-- Trim unused files and debug symbols -->
        <ItemGroup>
            <FilesToDelete Include="$(PublishDir)*.winmd;" />
			<FilesToDelete Include="$(PublishDir)*.xml;" />
			<FilesToDelete Include="$(PublishDir)Microsoft.Windows.Vision*.dll;" />
			<FilesToDelete Include="$(PublishDir)Microsoft.Windows.Workloads*.dll;" />
			<FilesToDelete Include="$(PublishDir)Microsoft.Windows.AI*.dll;" />
			<FilesToDelete Include="$(PublishDir)workloads*.json;" />
        </ItemGroup>
        <Delete Files="@(FilesToDelete)" />
		<Delete Files="$(PublishDir)aspnetcorev2_inprocess.dll" />
        <Delete Files="$(PublishDir)DwmSceneI.dll" />
        <Delete Files="$(PublishDir)DWriteCore.dll" />
		<Delete Files="$(PublishDir)Microsoft.Graphics.Display.dll" />
		<Delete Files="$(PublishDir)Microsoft.Graphics.Imaging.dll" />
        <Delete Files="$(PublishDir)Microsoft.Windows.Widgets.dll" />
        <Delete Files="$(PublishDir)Microsoft.WindowsAppRuntime.Bootstrap.dll" />
		<Delete Files="$(PublishDir)Microsoft.WindowsAppRuntime.Insights.Resource.dll" />
		<Delete Files="$(PublishDir)Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll" />
        <Delete Files="$(PublishDir)PushNotificationsLongRunningTask.ProxyStub.dll" />
		<Delete Files="$(PublishDir)SessionHandleIPCProxyStub.dll" />
		<Delete Files="$(PublishDir)WindowsAppRuntime.DeploymentExtensions.OneCore.dll" />
        <Delete Files="$(PublishDir)WindowsAppSdk.AppxDeploymentExtensions.Desktop.dll" />
        <Delete Files="$(PublishDir)WindowsAppSdk.AppxDeploymentExtensions.Desktop-EventLog-Instrumentation.dll" />
        <Delete Files="$(PublishDir)webp.dll" Condition="'$(PublishAot)' == 'true'" />
        <Delete Files="$(PublishDir)webpdemux.dll" Condition="'$(PublishAot)' == 'true'" />
        <Delete Files="$(PublishDir)webpmux.dll" Condition="'$(PublishAot)' == 'true'" />
        <Delete Files="$(PublishDir)Microsoft.UI.Xaml\Assets\map.html" />
    </Target>

    <ItemGroup>
      <Compile Update="Properties\Resources.Designer.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>Resources.resx</DependentUpon>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Update="Properties\Resources.resx">
        <Generator>PublicResXFileCodeGenerator</Generator>
        <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      </EmbeddedResource>
    </ItemGroup>
</Project>
