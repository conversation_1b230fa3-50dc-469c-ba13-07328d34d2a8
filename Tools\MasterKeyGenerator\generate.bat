@echo off
REM CollapseLauncher Master Key Generator Batch Script
REM This script provides an easy way to generate master key configurations

setlocal enabledelayedexpansion

echo ========================================
echo CollapseLauncher Master Key Generator
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Check if required packages are installed
echo Checking dependencies...
python -c "import cryptography" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Dependencies OK
echo.

REM Get user input for parameters
set /p KEY_SIZE="Enter RSA key size (default: 1024): "
if "%KEY_SIZE%"=="" set KEY_SIZE=1024

set /p BIT_SIZE="Enter BitSize value (default: 128): "
if "%BIT_SIZE%"=="" set BIT_SIZE=128

set /p OUTPUT_DIR="Enter output directory (default: ./output): "
if "%OUTPUT_DIR%"=="" set OUTPUT_DIR=./output

set /p CONFIG_VERSION="Enter config version (default: 3.1.0): "
if "%CONFIG_VERSION%"=="" set CONFIG_VERSION=3.1.0

echo.
echo Generating master key with the following parameters:
echo - RSA Key Size: %KEY_SIZE% bits
echo - Bit Size: %BIT_SIZE%
echo - Output Directory: %OUTPUT_DIR%
echo - Config Version: %CONFIG_VERSION%
echo.

REM Generate the master key
python generate_master_key.py --key-size %KEY_SIZE% --bit-size %BIT_SIZE% --output-dir "%OUTPUT_DIR%" --config-version "%CONFIG_VERSION%"

if errorlevel 1 (
    echo.
    echo Error: Failed to generate master key
    pause
    exit /b 1
)

echo.
echo Validating generated files...
python validate_config.py "%OUTPUT_DIR%"

if errorlevel 1 (
    echo.
    echo Warning: Validation failed. Please check the generated files.
) else (
    echo.
    echo Success! Master key configuration generated and validated.
)

echo.
echo Generated files are located in: %OUTPUT_DIR%
echo.
pause
