﻿<!--  <PERSON><PERSON><PERSON><PERSON> disable IdentifierTypo  -->
<!--  <PERSON><PERSON><PERSON><PERSON> disable UnusedMember.Local  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<Page x:Class="CollapseLauncher.Pages.HonkaiGameSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:conv="using:CollapseLauncher.Pages"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:helper="using:Hi3Helper"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      CacheMode="BitmapCache"
      Loaded="InitializeSettings"
      NavigationCacheMode="Disabled"
      Unloaded="OnUnload"
      mc:Ignorable="d">
    <Page.Resources>
        <ThemeShadow x:Name="SharedShadow" />
        <conv:InverseBooleanConverter x:Key="BooleanInverse" />
    </Page.Resources>
    <Grid>
        <Grid x:Name="PageContent">
            <ScrollViewer x:Name="SettingsScrollViewer"
                          VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="32,40,32,32"
                            Padding="0,0,0,74">
                    <TextBlock Margin="0,0,0,16"
                               Style="{ThemeResource TitleLargeTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_Title}" />
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0"
                                    Margin="0,0,32,0">
                            <StackPanel x:Name="GameResolutionPanel"
                                        Margin="0,16">
                                <StackPanel>
                                    <TextBlock Margin="0,0,0,16"
                                               Style="{ThemeResource SubtitleTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_ResolutionPanel}" />
                                    <StackPanel x:Name="GameResolutionWindow"
                                                Margin="0,0,0,8"
                                                Orientation="Vertical">
                                        <CheckBox x:Name="GameResolutionFullscreen"
                                                  HorizontalAlignment="Left"
                                                  VerticalAlignment="Center"
                                                  IsChecked="{x:Bind IsFullscreenEnabled, Mode=TwoWay}">
                                            <TextBlock Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_Fullscreen}"
                                                       TextWrapping="Wrap" />
                                        </CheckBox>
                                        <CheckBox x:Name="GameResolutionFullscreenExclusive"
                                                  HorizontalAlignment="Left"
                                                  VerticalAlignment="Center"
                                                  IsChecked="{x:Bind IsExclusiveFullscreenEnabled, Mode=TwoWay}"
                                                  IsEnabled="{x:Bind IsCanExclusiveFullscreen, Mode=OneWay}">
                                            <TextBlock Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_ExclusiveFullscreen}"
                                                       TextWrapping="Wrap" />
                                        </CheckBox>
                                        <CheckBox x:Name="GameResolutionBorderless"
                                                  HorizontalAlignment="Left"
                                                  VerticalAlignment="Center"
                                                  IsChecked="{x:Bind IsBorderlessEnabled, Mode=TwoWay}">
                                            <TextBlock Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_Borderless}"
                                                       TextWrapping="Wrap" />
                                        </CheckBox>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <CheckBox x:Name="GameWindowResizable"
                                                      HorizontalAlignment="Left"
                                                      VerticalAlignment="Center"
                                                      IsChecked="{x:Bind IsResizableWindow, Mode=TwoWay}"
                                                      IsEnabled="{x:Bind IsCanResizableWindow, Mode=OneWay}">
                                                <TextBlock Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_ResizableWindow}"
                                                           TextWrapping="Wrap" />
                                            </CheckBox>
                                            <Button Grid.Column="1"
                                                    Width="24"
                                                    Height="24"
                                                    Margin="16,0,8,0"
                                                    Padding="0"
                                                    CornerRadius="4"
                                                    Style="{ThemeResource AcrylicButtonStyle}">
                                                <Button.Content>
                                                    <FontIcon FontFamily="{ThemeResource FontAwesome}"
                                                              FontSize="10"
                                                              Glyph="&#x3f;" />
                                                </Button.Content>
                                                <Button.Flyout>
                                                    <Flyout>
                                                        <TextBlock MaxWidth="360"
                                                                   FontWeight="SemiBold"
                                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_ResizableWindowTooltip}"
                                                                   TextAlignment="Center"
                                                                   TextWrapping="Wrap" />
                                                    </Flyout>
                                                </Button.Flyout>
                                            </Button>
                                        </Grid>
                                    </StackPanel>
                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition />
                                        </Grid.ColumnDefinitions>
                                        <ComboBox x:Name="GameResolutionSelector"
                                                  MinWidth="128"
                                                  VerticalAlignment="Center"
                                                  CornerRadius="14"
                                                  IsEnabled="{x:Bind IsCustomResolutionEnabled, Mode=OneWay, Converter={StaticResource BooleanInverse}}"
                                                  PlaceholderText="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_ResSelectPlaceholder}"
                                                  SelectedItem="{x:Bind ResolutionSelected, Mode=TwoWay}" />
                                        <CheckBox x:Name="GameCustomResolutionCheckbox"
                                                  Grid.Column="1"
                                                  Margin="16,0,0,0"
                                                  VerticalAlignment="Center"
                                                  IsChecked="{x:Bind IsCustomResolutionEnabled, Mode=TwoWay}"
                                                  IsEnabled="{x:Bind IsExclusiveFullscreenEnabled, Mode=OneWay, Converter={StaticResource BooleanInverse}}">
                                            <TextBlock Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_ResCustom}"
                                                       TextWrapping="Wrap" />
                                        </CheckBox>
                                    </Grid>
                                </StackPanel>
                                <StackPanel x:Name="GameCustomResolutionPanel"
                                            Orientation="Horizontal">
                                    <TextBlock Margin="0,0,8,0"
                                               VerticalAlignment="Center"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_ResCustomW}" />
                                    <NumberBox x:Name="GameCustomResolutionWidth"
                                               Width="100"
                                               HorizontalAlignment="Left"
                                               CornerRadius="8,8,0,0"
                                               IsEnabled="{x:Bind IsCanResolutionWH, Mode=OneWay}"
                                               Value="{x:Bind ResolutionW, Mode=TwoWay}" />
                                    <TextBlock Margin="16,0,8,0"
                                               VerticalAlignment="Center"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_ResCustomH}" />
                                    <NumberBox x:Name="GameCustomResolutionHeight"
                                               Width="100"
                                               HorizontalAlignment="Left"
                                               CornerRadius="8,8,0,0"
                                               IsEnabled="{x:Bind IsCanResolutionWH, Mode=OneWay}"
                                               Value="{x:Bind ResolutionH, Mode=TwoWay}" />
                                </StackPanel>
                            </StackPanel>
                            <TextBlock Margin="0,8"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_FPSPanel}" />
                            <StackPanel x:Name="GameMaxFPSPanel"
                                        Margin="0,8"
                                        Orientation="Horizontal">
                                <StackPanel x:Name="GameMaxFPSInCombatPanel"
                                            Orientation="Vertical">
                                    <TextBlock Margin="0,0,0,8"
                                               VerticalAlignment="Center"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_FPSInCombat}" />
                                    <StackPanel Orientation="Horizontal">
                                        <NumberBox x:Name="GameMaxFPSInCombatValue"
                                                   Width="64"
                                                   HorizontalAlignment="Left"
                                                   CornerRadius="8,8,0,0"
                                                   IsEnabled="True"
                                                   Maximum="1024"
                                                   Value="{x:Bind FPSInCombat, Mode=TwoWay}" />
                                        <TextBlock Margin="8,0,8,0"
                                                   VerticalAlignment="Center"
                                                   Text="FPS" />
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel x:Name="GameMaxFPSInMainMenuPanel"
                                            Margin="16,0"
                                            Orientation="Vertical">
                                    <TextBlock Margin="0,0,0,8"
                                               VerticalAlignment="Center"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_FPSInMenu}" />
                                    <StackPanel Orientation="Horizontal">
                                        <NumberBox x:Name="GameMaxFPSInMainMenuValue"
                                                   Width="64"
                                                   HorizontalAlignment="Left"
                                                   CornerRadius="8,8,0,0"
                                                   IsEnabled="True"
                                                   Maximum="1024"
                                                   Value="{x:Bind FPSInMainMenu, Mode=TwoWay}" />
                                        <TextBlock Margin="8,0,8,0"
                                                   VerticalAlignment="Center"
                                                   Text="FPS" />
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                            <TextBlock Margin="0,16"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_APIPanel}" />
                            <StackPanel x:Name="GraphicsAPIPanel"
                                        Orientation="Horizontal">
                                <ComboBox x:Name="GraphicsAPISelector"
                                          Width="224"
                                          CornerRadius="14"
                                          PlaceholderText="Select"
                                          SelectedIndex="{x:Bind GraphicsAPI, Mode=TwoWay}">
                                    <ComboBoxItem Content="DirectX 11 (FL 10.1)" />
                                    <ComboBoxItem Content="DirectX 11 (FL 11.0) No ST" />
                                    <ComboBoxItem Content="DirectX 11 (FL 11.1)" />
                                    <ComboBoxItem Content="DirectX 11 (FL 11.1) No ST" />
                                    <ComboBoxItem Content="DirectX 12 [EXPERIMENTAL]" />
                                </ComboBox>
                                <Button Width="24"
                                        Height="24"
                                        Margin="16,0,8,0"
                                        Padding="0"
                                        CornerRadius="4"
                                        Style="{ThemeResource AcrylicButtonStyle}">
                                    <Button.Content>
                                        <FontIcon FontFamily="{ThemeResource FontAwesome}"
                                                  FontSize="10"
                                                  Glyph="&#x3f;" />
                                    </Button.Content>
                                    <Button.Flyout>
                                        <Flyout>
                                            <StackPanel Width="318">
                                                <TextBlock Style="{ThemeResource BaseTextBlockStyle}">
                                                    <Run Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_APIHelp1}" />
                                                    <LineBreak />
                                                    <LineBreak />
                                                    <Run Text="- DirectX 11 level 10.1" />
                                                    <LineBreak />
                                                    <Run Text="- DirectX 11 level 11.0 No Single-Thread (Default)" />
                                                    <LineBreak />
                                                    <Run Text="- DirectX 11 level 11.1" />
                                                    <LineBreak />
                                                    <Run Text="- DirectX 11 level 11.1 No Single-Thread" />
                                                    <LineBreak />
                                                    <Run Text="- DirectX 12 (Experimental)" />
                                                    <LineBreak />
                                                </TextBlock>
                                                <TextBlock Style="{ThemeResource BaseTextBlockStyle}">
                                                    <Run Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_APIHelp2}" />
                                                    <Run Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_APIHelp3}" />
                                                </TextBlock>
                                            </StackPanel>
                                        </Flyout>
                                    </Button.Flyout>
                                </Button>
                            </StackPanel>
                            <StackPanel x:Name="GameBoostPanel"
                                        Orientation="Horizontal">
                                <ToggleSwitch Margin="4,12,0,8"
                                              Header="{x:Bind helper:Locale.Lang._GameSettingsPage.GameBoost}"
                                              IsOn="{x:Bind IsGameBoost, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            </StackPanel>
                        </StackPanel>
                        <StackPanel x:Name="GameGraphicsPanel"
                                    Grid.Column="1"
                                    Margin="0,16">
                            <TextBlock Margin="0,0,0,16"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_SpecPanel}" />
                            <StackPanel>
                                <!-- Disable presets for now (send help)
                                <TextBlock
                                    Margin="0,0,0,8"
                                    Style="{ThemeResource BodyStrongTextBlockStyle}"
                                    Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_Preset}" />
                                <ComboBox
                                    x:Name="GameGraphicsPresetSelector"
                                    Margin="0,0,0,16"
                                    CornerRadius="14"
                                    ItemsSource="{x:Bind PresetRenderingNames}"
                                    SelectedIndex="{x:Bind PresetRenderingIndex, Mode=TwoWay}" />
                                -->
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_Render}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="RenderingAccuracySelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsRenderingAccuracy, Mode=TwoWay}">
                                            <ComboBoxItem Content="0.6" />
                                            <ComboBoxItem Content="0.8" />
                                            <ComboBoxItem Content="0.9" />
                                            <ComboBoxItem Content="1.0" />
                                            <ComboBoxItem Content="1.1" />
                                            <ComboBoxItem Content="1.2" />
                                            <ComboBoxItem Content="1.3" />
                                            <ComboBoxItem Content="1.4" />
                                            <ComboBoxItem Content="1.5" />
                                            <ComboBoxItem Content="1.6" />
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_Shadow}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="ShadowQualitySelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsShadowQuality, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecDisabled}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecMedium}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecVeryHigh}" />
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Grid.Column="2"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_Reflection}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="ReflectionQualitySelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsReflectionQuality, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecDisabled}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecMedium}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                        </ComboBox>
                                    </StackPanel>
                                </Grid>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_APHO2LOD}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="LevelOfDetailSelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsLevelOfDetail, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecMedium}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_ParticleQuality}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="ParticleQualitySelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsParticleQuality, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecMedium}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Grid.Column="2"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_LightingQuality}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="LightingQualitySelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsLightingQuality, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecDisabled}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecMedium}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecUltra}" />
                                        </ComboBox>
                                    </StackPanel>
                                </Grid>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_PostFXQuality}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="PostFXQualitySelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsPostFXQuality, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecDisabled}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecMedium}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_AAMode}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="AATypeSelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsAAType, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecDisabled}" />
                                            <ComboBoxItem Content="FXAA" />
                                            <ComboBoxItem Content="TAA" />
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Grid.Column="2"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_CharacterQuality}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="CharacterQualitySelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsCharacterQuality, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecMedium}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                        </ComboBox>
                                    </StackPanel>
                                </Grid>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_WeatherQuality}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="WeatherQualitySelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsWeatherQuality, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecMedium}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_FXPhysics}"
                                                   TextWrapping="Wrap" />
                                        <ToggleSwitch x:Name="GameFXPhysicsSwitch"
                                                      Margin="0,0,16,0"
                                                      HorizontalAlignment="Stretch"
                                                      IsOn="{x:Bind IsGraphicsPhysicsEnabled, Mode=TwoWay}"
                                                      OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                                      OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                                    </StackPanel>
                                </Grid>
                                <Grid />
                                <MenuFlyoutSeparator Margin="0,8,0,0" />
                                <TextBlock Margin="0,8,0,4"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_Legacy_Title}" />
                                <TextBlock Margin="0,0,0,8"
                                           Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_Legacy_Subtitle}"
                                           TextWrapping="WrapWholeWords" />
                                <TextBlock Margin="0,0,0,8"
                                           FontSize="16"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_FX}" />
                                <Expander x:Name="GameFXPostProcExpander"
                                          MaxWidth="514"
                                          Margin="0,0,0,8"
                                          HorizontalContentAlignment="Stretch"
                                          CornerRadius="8"
                                          IsExpanded="{x:Bind IsGraphicsPostFXEnabled, Mode=OneWay}">
                                    <Expander.Header>
                                        <StackPanel Orientation="Horizontal">
                                            <CheckBox x:Name="GameFXPostProcCheckBox"
                                                      IsChecked="{x:Bind IsGraphicsPostFXEnabled, Mode=TwoWay}">
                                                <CheckBox.Content>
                                                    <TextBlock Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_FXPost}"
                                                               TextWrapping="Wrap" />
                                                </CheckBox.Content>
                                            </CheckBox>
                                        </StackPanel>
                                    </Expander.Header>
                                    <Expander.Content>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition MinWidth="100" />
                                                <ColumnDefinition MinWidth="100" />
                                                <ColumnDefinition MinWidth="100" />
                                                <ColumnDefinition MinWidth="100" />
                                            </Grid.ColumnDefinitions>
                                            <CheckBox x:Name="GameFXHDRCheckBox"
                                                      Grid.Column="0"
                                                      MinWidth="0"
                                                      Margin="8,0,0,0"
                                                      IsChecked="{x:Bind IsGraphicsFXHDREnabled, Mode=TwoWay}"
                                                      IsEnabled="{x:Bind IsGraphicsPostFXEnabled, Mode=OneWay}">
                                                <TextBlock Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_FXHDR}"
                                                           TextWrapping="Wrap" />
                                            </CheckBox>
                                            <CheckBox x:Name="GameFXHighQualityCheckBox"
                                                      Grid.Column="1"
                                                      MinWidth="0"
                                                      Margin="8,0,0,0"
                                                      IsChecked="{x:Bind IsGraphicsFXHighQualityEnabled, Mode=TwoWay}"
                                                      IsEnabled="{x:Bind IsGraphicsPostFXEnabled, Mode=OneWay}">
                                                <TextBlock Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_FXHQ}"
                                                           TextWrapping="Wrap" />
                                            </CheckBox>
                                            <CheckBox x:Name="GameFXFXAACheckBox"
                                                      Grid.Column="2"
                                                      MinWidth="0"
                                                      Margin="8,0,0,0"
                                                      HorizontalAlignment="Center"
                                                      IsChecked="{x:Bind IsGraphicsFXFXAAEnabled, Mode=TwoWay}"
                                                      IsEnabled="{x:Bind IsGraphicsPostFXEnabled, Mode=OneWay}">
                                                <TextBlock Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_FXAA}"
                                                           TextWrapping="Wrap" />
                                            </CheckBox>
                                            <CheckBox x:Name="GameFXDistortionCheckBox"
                                                      Grid.Column="3"
                                                      MinWidth="0"
                                                      Margin="8,0,0,0"
                                                      HorizontalAlignment="Right"
                                                      IsChecked="{x:Bind IsGraphicsFXDistortionEnabled, Mode=TwoWay}"
                                                      IsEnabled="{x:Bind IsGraphicsPostFXEnabled, Mode=OneWay}">
                                                <TextBlock Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_FXDistort}"
                                                           TextWrapping="Wrap" />
                                            </CheckBox>
                                        </Grid>
                                    </Expander.Content>
                                </Expander>
                            </StackPanel>
                            <TextBlock Margin="0,8,0,16"
                                       FontSize="16"
                                       Style="{ThemeResource SubtitleTextBlockStyle}"
                                       Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_APHO2Panel}" />
                            <StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_APHO2GI}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="GlobalIlluminationSelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsGlobalIllumination, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_APHO2AO}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="AmbientOcclusionSelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsAmbientOcclusion, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecDisabled}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Grid.Column="2"
                                                VerticalAlignment="Bottom">
                                        <TextBlock MaxWidth="128"
                                                   Margin="0,0,0,8"
                                                   HorizontalAlignment="Left"
                                                   Style="{ThemeResource BodyStrongTextBlockStyle}"
                                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Graphics_APHO2VL}"
                                                   TextWrapping="Wrap" />
                                        <ComboBox x:Name="GameVolumetricLightSelector"
                                                  Margin="0,0,16,8"
                                                  HorizontalAlignment="Stretch"
                                                  CornerRadius="14"
                                                  SelectedIndex="{x:Bind GraphicsVolumetricLight, Mode=TwoWay}">
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecLow}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecMedium}" />
                                            <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.SpecHigh}" />
                                        </ComboBox>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                    <MenuFlyoutSeparator Margin="0,4,0,8" />
                    <TextBlock Margin="0,0,0,8"
                               Style="{ThemeResource TitleLargeTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_Title}" />
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <Grid Grid.Row="1"
                              Grid.Column="0"
                              Grid.ColumnSpan="2"
                              Margin="0,0,0,16"
                              HorizontalAlignment="Left">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <StackPanel Margin="0,0,16,0">
                                <TextBlock Margin="0,8,0,8"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_VOLang}" />
                                <ComboBox x:Name="AudioCVLanguageSelector"
                                          MinWidth="172"
                                          CornerRadius="14"
                                          SelectedIndex="{x:Bind AudioVoiceLanguage, Mode=TwoWay}">
                                    <ComboBoxItem Content="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_VOLang1}" />
                                    <ComboBoxItem>
                                        <TextBlock>
                                            <Run Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_VOLang2}" />
                                            <Run FontWeight="Bold"
                                                 Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_VODefault}" />
                                        </TextBlock>
                                    </ComboBoxItem>
                                </ComboBox>
                            </StackPanel>
                            <StackPanel Grid.Column="1"
                                        HorizontalAlignment="Center">
                                <TextBlock Margin="0,8,0,4"
                                           Style="{ThemeResource SubtitleTextBlockStyle}"
                                           Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_Mute}" />
                                <ToggleSwitch IsOn="{x:Bind AudioMute, Mode=TwoWay}"
                                              OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                              OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                            </StackPanel>
                        </Grid>
                        <StackPanel Grid.Row="0"
                                    Grid.Column="0">
                            <StackPanel x:Name="AudioSettingsPanelLeft"
                                        Margin="0,8,64,16">
                                <StackPanel>
                                    <TextBlock Margin="0,0,0,8"
                                               Style="{ThemeResource SubtitleTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_Master}" />
                                    <StackPanel Margin="0,0,32,8"
                                                Orientation="Vertical">
                                        <Slider x:Name="AudioMasterVolumeSlider"
                                                Maximum="100"
                                                Style="{ThemeResource FatSliderStyle}"
                                                TickFrequency="10"
                                                TickPlacement="Outside"
                                                Value="{x:Bind AudioMasterVolume, Mode=TwoWay}" />
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel>
                                    <TextBlock Margin="0,16,0,8"
                                               Style="{ThemeResource SubtitleTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_BGM}" />
                                    <StackPanel Margin="0,0,32,8"
                                                Orientation="Vertical">
                                        <Slider x:Name="AudioBGMVolumeSlider"
                                                Maximum="100"
                                                Style="{ThemeResource FatSliderStyle}"
                                                TickFrequency="10"
                                                TickPlacement="Outside"
                                                Value="{x:Bind AudioBGMVolume, Mode=TwoWay}" />
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel>
                                    <TextBlock Margin="0,16,0,8"
                                               Style="{ThemeResource SubtitleTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_SFX}" />
                                    <StackPanel Margin="0,0,32,8"
                                                Orientation="Vertical">
                                        <Slider x:Name="AudioSFXVolumeSlider"
                                                Maximum="100"
                                                Style="{ThemeResource FatSliderStyle}"
                                                TickFrequency="10"
                                                TickPlacement="Outside"
                                                Value="{x:Bind AudioSFXVolume, Mode=TwoWay}" />
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                        <StackPanel Grid.Row="0"
                                    Grid.Column="1">
                            <StackPanel x:Name="AudioSettingsPanelRight"
                                        Margin="0,8,64,16">
                                <StackPanel>
                                    <TextBlock Margin="0,0,0,8"
                                               Style="{ThemeResource SubtitleTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_VO}" />
                                    <StackPanel Margin="0,0,32,8"
                                                Orientation="Vertical">
                                        <Slider x:Name="AudioCVVolumeSlider"
                                                Maximum="100"
                                                Style="{ThemeResource FatSliderStyle}"
                                                TickFrequency="10"
                                                TickPlacement="Outside"
                                                Value="{x:Bind AudioVoiceVolume, Mode=TwoWay}" />
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel>
                                    <TextBlock Margin="0,16,0,8"
                                               Style="{ThemeResource SubtitleTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_Elf}" />
                                    <StackPanel Margin="0,0,32,8"
                                                Orientation="Vertical">
                                        <Slider x:Name="AudioElfCVVolumeSlider"
                                                Maximum="100"
                                                Style="{ThemeResource FatSliderStyle}"
                                                TickFrequency="10"
                                                TickPlacement="Outside"
                                                Value="{x:Bind AudioElfVolume, Mode=TwoWay}" />
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel>
                                    <TextBlock Margin="0,16,0,8"
                                               Style="{ThemeResource SubtitleTextBlockStyle}"
                                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Audio_Cutscenes}" />
                                    <StackPanel Margin="0,0,32,8"
                                                Orientation="Vertical">
                                        <Slider x:Name="AudioCutscenesVolumeSlider"
                                                Maximum="100"
                                                Style="{ThemeResource FatSliderStyle}"
                                                TickFrequency="10"
                                                TickPlacement="Outside"
                                                Value="{x:Bind AudioCutsceneVolume, Mode=TwoWay}" />
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                    <MenuFlyoutSeparator Margin="0,4,0,8" />
                    <TextBlock Margin="0,0,0,16"
                               Style="{ThemeResource TitleLargeTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.CustomArgs_Title}" />
                    <TextBlock Margin="0,0,0,0"
                               Style="{ThemeResource SubtitleTextBlockStyle}"
                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.CustomArgs_Subtitle}" />
                    <ToggleSwitch Margin="0,0,0,16"
                                  IsOn="{x:Bind IsUseCustomArgs, Mode=TwoWay}"
                                  OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                  OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                    <TextBox x:Name="CustomArgsTextBox"
                             Margin="0,0,0,16"
                             HorizontalAlignment="Stretch"
                             CornerRadius="8,8,0,0"
                             Text="{x:Bind CustomArgsValue, Mode=TwoWay}" />
                    <TextBlock Height="Auto"
                               TextWrapping="WrapWholeWords">
                        <Run Text="{x:Bind helper:Locale.Lang._StarRailGameSettingsPage.CustomArgs_Footer1}" />
                        <Hyperlink NavigateUri="https://docs.unity3d.com/Manual/PlayerCommandLineArguments.html"
                                   UnderlineStyle="None">
                            <Run FontWeight="Bold"
                                 Text="{x:Bind helper:Locale.Lang._GameSettingsPage.CustomArgs_Footer2}" />
                        </Hyperlink>
                        <Run Text="{x:Bind helper:Locale.Lang._GameSettingsPage.CustomArgs_Footer3}" />
                    </TextBlock>
                    <MenuFlyoutSeparator Margin="0,16,0,16" />
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0"
                                   Margin="0,0,8,0"
                                   VerticalAlignment="Stretch"
                                   Style="{ThemeResource TitleLargeTextBlockStyle}"
                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_Title}" />
                        <ToggleSwitch Grid.Column="1"
                                      Margin="8,12,0,8"
                                      VerticalAlignment="Stretch"
                                      VerticalContentAlignment="Stretch"
                                      FontSize="26"
                                      FontWeight="SemiBold"
                                      IsOn="{x:Bind IsUseAdvancedSettings, Mode=TwoWay}"
                                      OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                      OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                    </Grid>
                    <StackPanel x:Name="AdvancedSettingsPanel">
                        <TextBlock Margin="0,0,0,8"
                                   TextWrapping="WrapWholeWords">
                            <Run Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_Subtitle1}" />
                            <Run FontWeight="Bold"
                                 Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_Subtitle2}" />
                            <Run Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_Subtitle3}" />
                        </TextBlock>
                        <TextBlock Margin="0,0,8,8"
                                   Style="{ThemeResource SubtitleTextBlockStyle}"
                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_GLC_PreLaunch_Title}" />
                        <ToggleSwitch x:Name="PreLaunchToggle"
                                      Margin="0,0,0,0"
                                      Header="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_GLC_PreLaunch_Subtitle}"
                                      IsOn="{x:Bind IsUsePreLaunchCommand, Mode=TwoWay}"
                                      OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                      OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                        <ToggleSwitch x:Name="PreLaunchForceCloseToggle"
                                      Margin="0,0,0,0"
                                      Header="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_GLC_PreLaunch_Exit}"
                                      IsOn="{x:Bind IsPreLaunchCommandExitOnGameClose, Mode=TwoWay}"
                                      OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                      OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                        <NumberBox x:Name="GameLaunchDelay"
                                   Width="200"
                                   Margin="0,0,0,12"
                                   HorizontalAlignment="Left"
                                   Header="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_GLC_PreLaunch_Delay}"
                                   ValueChanged="GameLaunchDelay_OnValueChanged"
                                   Value="{x:Bind LaunchDelay, Mode=TwoWay}" />
                        <TextBox x:Name="PreLaunchCommandTextBox"
                                 Margin="0,0,0,4"
                                 HorizontalAlignment="Stretch"
                                 CornerRadius="8,8,0,0"
                                 IsSpellCheckEnabled="False"
                                 Text="{x:Bind PreLaunchCommand, Mode=TwoWay}" />
                        <TextBlock Margin="0,0,0,8"
                                   FontWeight="Semibold"
                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_GLC_WarningAdmin}" />
                        <TextBlock Margin="0,0,8,8"
                                   Style="{ThemeResource SubtitleTextBlockStyle}"
                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_GLC_PostExit_Title}" />
                        <ToggleSwitch x:Name="PostExitToggle"
                                      Margin="0,0,0,8"
                                      Header="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_GLC_PostExit_Subtitle}"
                                      IsOn="{x:Bind IsUsePostExitCommand, Mode=TwoWay}"
                                      OffContent="{x:Bind helper:Locale.Lang._Misc.Disabled}"
                                      OnContent="{x:Bind helper:Locale.Lang._Misc.Enabled}" />
                        <TextBox x:Name="PostExitCommandTextBox"
                                 Margin="0,0,0,4"
                                 HorizontalAlignment="Stretch"
                                 CornerRadius="8,8,0,0"
                                 IsSpellCheckEnabled="False"
                                 Text="{x:Bind PostExitCommand, Mode=TwoWay}" />
                        <TextBlock Margin="0,0,0,16"
                                   FontWeight="Semibold"
                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.Advanced_GLC_WarningAdmin}" />
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
            <Grid x:Name="GameSettingsApplyGrid"
                  Margin="16"
                  Padding="16,16"
                  HorizontalAlignment="Stretch"
                  VerticalAlignment="Bottom"
                  Background="{ThemeResource GameSettingsApplyGridBrush}"
                  CornerRadius="8"
                  Shadow="{ThemeResource SharedShadow}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Button x:Name="ApplyButton"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Click="ApplyButton_Click"
                        CornerRadius="16"
                        IsEnabled="True"
                        Shadow="{ThemeResource SharedShadow}"
                        Style="{ThemeResource AccentButtonStyle}">
                    <StackPanel Margin="8,0"
                                Orientation="Horizontal">
                        <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                  FontSize="14"
                                  Glyph="&#xf00c;" />
                        <TextBlock Margin="8,0,0,0"
                                   FontWeight="Medium"
                                   Text="{x:Bind helper:Locale.Lang._GameSettingsPage.ApplyBtn}" />
                    </StackPanel>
                </Button>
                <TextBlock x:Name="ApplyText"
                           Grid.Column="1"
                           Margin="16,-4,0,0"
                           HorizontalAlignment="Stretch"
                           VerticalAlignment="Center"
                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                           Text="{x:Bind helper:Locale.Lang._GameSettingsPage.SettingsApplied}"
                           TextWrapping="Wrap"
                           Visibility="Collapsed" />
                <StackPanel Grid.Column="2"
                            HorizontalAlignment="Right"
                            Orientation="Horizontal">
                    <TextBlock Margin="16,-4,16,0"
                               VerticalAlignment="Center"
                               FontWeight="Medium"
                               Text="{x:Bind helper:Locale.Lang._GameSettingsPage.RegImportExport}" />
                    <Button x:Name="RegistryExport"
                            Height="32"
                            Click="RegistryExportClick"
                            CornerRadius="16,0,0,16"
                            Shadow="{ThemeResource SharedShadow}"
                            Style="{ThemeResource AccentButtonStyle}"
                            ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._GameSettingsPage.RegExportTooltip}">
                        <StackPanel Margin="8,0"
                                    Orientation="Horizontal">
                            <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                      FontSize="14"
                                      Glyph="&#xf56e;" />
                            <TextBlock Margin="8,0,0,0"
                                       FontWeight="Medium"
                                       Text="{x:Bind helper:Locale.Lang._GameSettingsPage.RegExportTitle}" />
                        </StackPanel>
                    </Button>
                    <Button x:Name="RegistryImport"
                            Height="32"
                            Click="RegistryImportClick"
                            CornerRadius="0,16,16,0"
                            Shadow="{ThemeResource SharedShadow}"
                            Style="{ThemeResource AccentButtonStyle}"
                            ToolTipService.ToolTip="{x:Bind helper:Locale.Lang._GameSettingsPage.RegImportTooltip}">
                        <StackPanel Margin="8,0"
                                    Orientation="Horizontal">
                            <TextBlock Margin="0,0,8,0"
                                       FontWeight="Medium"
                                       Text="{x:Bind helper:Locale.Lang._GameSettingsPage.RegImportTitle}" />
                            <FontIcon FontFamily="{ThemeResource FontAwesomeSolid}"
                                      FontSize="14"
                                      Glyph="&#xf56f;" />
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Grid>
        <Grid x:Name="Overlay"
              Visibility="Collapsed">
            <StackPanel Margin="0,176,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Vertical">
                <ProgressRing x:Name="Ring"
                              Width="48"
                              Height="48"
                              Margin="32"
                              IsActive="True"
                              IsIndeterminate="false"
                              Maximum="100"
                              Value="100" />
                <TextBlock x:Name="OverlayTitle"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Style="{ThemeResource SubtitleTextBlockStyle}"
                           Text="Title" />
                <TextBlock x:Name="OverlaySubtitle"
                           Margin="0,8,0,192"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Style="{ThemeResource BodyStrongTextBlockStyle}"
                           Text="Subtitle" />
            </StackPanel>
        </Grid>
    </Grid>
</Page>
