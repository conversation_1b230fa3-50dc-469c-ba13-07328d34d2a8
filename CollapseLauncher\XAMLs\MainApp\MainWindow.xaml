﻿<Window x:Class="CollapseLauncher.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:customcontrol="using:CollapseLauncher.CustomControls"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:helper="using:Hi3Helper"
        xmlns:local="using:CollapseLauncher"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:uiextension="using:CollapseLauncher.Extension"
        SizeChanged="MainWindow_OnSizeChanged"
        mc:Ignorable="d">
    <Grid>
        <Grid.Resources>
            <ThemeShadow x:Name="SharedThemeShadow" />
        </Grid.Resources>
        <Grid.RowDefinitions>
            <RowDefinition Height="48" />
            <RowDefinition />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>
        <!--  ReSharper disable once InconsistentNaming  -->
        <local:TrayIcon x:Name="_TrayIcon"
                        Grid.Row="0"
                        x:FieldModifier="public" />
        <Grid x:Name="RootFrameGrid"
              Grid.Row="0"
              Grid.RowSpan="3"
              Opacity="1">
            <Frame x:Name="RootFrame"
                   x:FieldModifier="public"
                   Opacity="1">
                <Frame.ContentTransitions>
                    <TransitionCollection>
                        <NavigationThemeTransition />
                    </TransitionCollection>
                </Frame.ContentTransitions>
            </Frame>
            <Frame x:Name="OverlayFrame"
                   x:FieldModifier="public"
                   Opacity="1">
                <Frame.ContentTransitions>
                    <TransitionCollection>
                        <NavigationThemeTransition />
                    </TransitionCollection>
                </Frame.ContentTransitions>
            </Frame>
        </Grid>
        <Grid x:Name="IntroAnimationGrid"
              Grid.Row="0"
              Grid.RowSpan="3"
              HorizontalAlignment="Stretch"
              VerticalAlignment="Stretch">
            <AnimatedVisualPlayer x:Name="IntroAnimation"
                                  AutoPlay="False"
                                  Visibility="Collapsed" />
            <CheckBox x:Name="IntroSequenceToggle"
                      Margin="32,32,16,24"
                      HorizontalAlignment="Right"
                      VerticalAlignment="Bottom"
                      HorizontalContentAlignment="Right"
                      Content="{x:Bind helper:Locale.Lang._SettingsPage.IntroSequenceToggle}"
                      IsChecked="{x:Bind local:MainWindow.IsIntroEnabled, Mode=TwoWay}"
                      Opacity="0.25"
                      PointerEntered="IntroSequenceToggle_PointerEntered"
                      PointerExited="IntroSequenceToggle_PointerExited"
                      Scale="0.85,0.85,0.85"
                      Visibility="Collapsed" />
        </Grid>
        <Grid x:Name="LoadingStatusBackgroundGrid"
              Grid.Row="0"
              Grid.RowSpan="3"
              x:FieldModifier="internal"
              Background="{ThemeResource LoadingGradientBG}"
              Visibility="Collapsed" />
        <Grid Grid.Row="1" />
        <Grid x:Name="LoadingStatusGrid"
              Grid.Row="2"
              Margin="32"
              Padding="16"
              VerticalAlignment="Bottom"
              x:FieldModifier="internal"
              Background="{ThemeResource AccentAcrylicBackgroundFillColorBaseBrush}"
              CornerRadius="8"
              Visibility="Collapsed">
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <Grid x:Name="LoadingStatusUIContainer"
                  Margin="0,0,16,0"
                  VerticalAlignment="Stretch">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>
                <ProgressRing x:Name="LoadingStatusProgressRing"
                              Grid.Column="0"
                              Width="24"
                              Height="24"
                              Margin="0,0,16,0"
                              HorizontalAlignment="Left"
                              x:FieldModifier="internal"
                              IsIndeterminate="True" />
                <Grid Grid.Column="1"
                      Margin="0,-2,0,0"
                      HorizontalAlignment="Stretch"
                      VerticalAlignment="Center">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <!-- ReSharper disable GrammarMistakeInMarkupAttribute -->
                    <TextBlock x:Name="LoadingStatusTextTitle"
                               Grid.Column="0"
                               VerticalAlignment="Center"
                               x:FieldModifier="internal"
                               FontWeight="Bold"
                               Text="Never gonna give you up"
                               TextWrapping="WrapWholeWords" />
                    <TextBlock x:Name="LoadingStatusTextSeparator"
                               Grid.Column="1"
                               Margin="8,0"
                               VerticalAlignment="Center"
                               x:FieldModifier="internal"
                               FontWeight="Bold"
                               Text="●"
                               TextWrapping="WrapWholeWords" />
                    <TextBlock x:Name="LoadingStatusTextSubtitle"
                               Grid.Column="2"
                               HorizontalAlignment="Stretch"
                               VerticalAlignment="Stretch"
                               x:FieldModifier="internal"
                               Text="Never gonna let you down. Never gonna run around and desert you. Never gonna make you cry. Never gonna say goodbye. Never gonna tell a lie and hurt you."
                               TextWrapping="Wrap" />
                </Grid>
            </Grid>
            <Button x:Name="LoadingStatusActionButton"
                    Grid.Column="1"
                    HorizontalAlignment="Right"
                    x:FieldModifier="internal"
                    CornerRadius="{x:Bind uiextension:UIElementExtensions.AttachRoundedKindCornerRadius(LoadingStatusActionButton)}"
                    Shadow="{StaticResource SharedThemeShadow}"
                    Style="{ThemeResource AccentButtonStyle}"
                    Translation="0,0,16"
                    Visibility="Collapsed">
                <StackPanel Orientation="Horizontal">
                    <FontIcon x:Name="LoadingStatusActionButtonIcon"
                              Margin="0,0,8,0"
                              VerticalAlignment="Center"
                              x:FieldModifier="internal"
                              FontFamily="{ThemeResource FontAwesomeSolid}"
                              FontSize="16"
                              Glyph="&#xf00c;"
                              Visibility="Collapsed" />
                    <Grid x:Name="LoadingStatusActionButtonContentContainer"
                          x:FieldModifier="internal" />
                </StackPanel>
            </Button>
        </Grid>
        <customcontrol:ContentDialogCollapse x:Name="ContentDialog"
                                             Grid.Row="0"
                                             x:FieldModifier="internal" />
        <Grid x:Name="OverlayRootGrid"
              Grid.Row="0"
              Grid.RowSpan="3"
              HorizontalAlignment="Stretch"
              VerticalAlignment="Stretch" />
        <Grid x:Name="TitleBarFrameGrid"
              Grid.Row="0">
            <Grid x:Name="AppTitleBar"
                  x:FieldModifier="internal">
                <Grid.Resources>
                    <ThemeShadow x:Key="CaptionButtonShadow" />
                </Grid.Resources>
                <Grid Width="80"
                      Height="32"
                      Margin="8,8"
                      HorizontalAlignment="Right"
                      CornerRadius="8"
                      Shadow="{StaticResource CaptionButtonShadow}"
                      Translation="0,0,16" />
                <Grid Margin="8,8"
                      HorizontalAlignment="Right"
                      Background="{ThemeResource WindowCaptionBackgroundAcrylicLight}"
                      CornerRadius="8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <Button x:Name="MinimizeButton"
                            Grid.Column="0"
                            Width="40"
                            x:FieldModifier="internal"
                            Click="MinimizeButton_Click"
                            Content="M 0 0 H 10"
                            CornerRadius="8,0,0,8"
                            Loaded="SetWindowCaptionLoadedCursor"
                            Style="{ThemeResource WindowCaptionButton}" />
                    <Button x:Name="CloseButton"
                            Grid.Column="1"
                            Width="40"
                            HorizontalAlignment="Right"
                            x:FieldModifier="internal"
                            Background="{ThemeResource WindowCaptionBackgroundClose}"
                            Click="CloseButton_Click"
                            Content="M 0 0 L 10 10 M 10 0 L 0 10"
                            CornerRadius="0,8,8,0"
                            Loaded="SetWindowCaptionLoadedCursor"
                            Style="{ThemeResource WindowCaptionButton}">
                        <Button.Resources>
                            <ResourceDictionary>
                                <ResourceDictionary.ThemeDictionaries>
                                    <!--  ReSharper disable Xaml.StaticResourceNotResolved  -->
                                    <ResourceDictionary x:Key="Light">
                                        <StaticResource x:Key="WindowCaptionButtonBackgroundPointerOver"
                                                        ResourceKey="WindowCaptionBackgroundClosePointerOver" />
                                        <StaticResource x:Key="WindowCaptionButtonBackgroundPressed"
                                                        ResourceKey="WindowCaptionBackgroundClosePressed" />
                                        <StaticResource x:Key="WindowCaptionButtonStrokePointerOver"
                                                        ResourceKey="CloseButtonStrokePointerOver" />
                                        <StaticResource x:Key="WindowCaptionButtonStrokePressed"
                                                        ResourceKey="CloseButtonStrokePressed" />
                                    </ResourceDictionary>
                                    <ResourceDictionary x:Key="Default">
                                        <StaticResource x:Key="WindowCaptionButtonBackgroundPointerOver"
                                                        ResourceKey="WindowCaptionBackgroundClosePointerOver" />
                                        <StaticResource x:Key="WindowCaptionButtonBackgroundPressed"
                                                        ResourceKey="WindowCaptionBackgroundClosePressed" />
                                        <StaticResource x:Key="WindowCaptionButtonStrokePointerOver"
                                                        ResourceKey="CloseButtonStrokePointerOver" />
                                        <StaticResource x:Key="WindowCaptionButtonStrokePressed"
                                                        ResourceKey="CloseButtonStrokePressed" />
                                    </ResourceDictionary>
                                    <!--  ReSharper restore Xaml.StaticResourceNotResolved  -->
                                </ResourceDictionary.ThemeDictionaries>
                            </ResourceDictionary>
                        </Button.Resources>
                    </Button>
                </Grid>
            </Grid>
        </Grid>
    </Grid>
</Window>
