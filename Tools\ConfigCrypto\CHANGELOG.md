# 更新日志

## v2.1.0 - 2024-12-XX (重要修正版本)

### 🔧 重要修正

#### 问题1: 加密格式修正
- **问题**: 之前的加密方法使用了错误的格式，不是标准的ServeV3格式
- **修正**: 
  - 重写了`encrypt_field`方法，现在使用正确的ServeV3格式
  - 添加了`_create_serve_v3_data`方法来生成标准的ServeV3数据
  - 使用正确的CollapseSignature (7310310183885631299)
  - 正确设置加密标志和压缩类型

#### 问题2: Hash字段更新
- **问题**: 加密配置文件后，Hash字段没有更新
- **修正**:
  - 在`encrypt_config_file`方法中添加了Hash重新计算逻辑
  - 实现了`_calculate_config_hash`方法
  - 加密完成后自动更新Hash字段
  - 只有当配置中存在Hash字段时才会更新

### ✨ 新增功能

#### ServeV3格式支持
```python
# 现在加密产生的数据格式：
# [32字节头部] + [加密数据]
# 头部包含：
# - CollapseSignature (8字节)
# - 属性标志 (8字节): 压缩类型 + 加密标志
# - 压缩后大小 (8字节)
# - 原始大小 (8字节)
```

#### 自动Hash计算
```python
# Hash计算逻辑：
# 1. 移除现有Hash字段
# 2. 将配置转换为标准JSON字符串
# 3. 计算字符串的哈希值
# 4. 转换为64位有符号整数
```

### 🧪 测试增强

#### 新增测试文件
- `test_fixed_encryption.py` - 验证修正后的加密实现
  - ServeV3格式验证
  - Hash更新验证
  - 加密/解密循环测试
  - ServeV3属性验证

#### 测试覆盖
- ✅ ServeV3签名检测
- ✅ 加密标志验证
- ✅ Hash字段更新
- ✅ 配置完整性验证

### 📊 性能改进

#### 加密效率
- 移除了不必要的hex编码步骤
- 直接使用base64编码ServeV3数据
- 优化了RSA分块加密逻辑

#### 内存使用
- 减少了中间数据转换
- 优化了大文件处理

### 🔄 向后兼容性

#### 解密兼容性
- 保持对旧格式数据的解密支持
- 自动检测数据格式类型
- 平滑迁移到新格式

#### API兼容性
- 保持所有公共API不变
- 内部实现优化不影响使用方式

### 📝 使用示例

#### 验证修正效果
```bash
# 运行修正验证测试
python test_fixed_encryption.py

# 测试ServeV3格式加密
python crypto_tool.py encrypt --data "test" --master-key key.json

# 验证Hash更新
python crypto_tool.py encrypt-config --input config.json --output encrypted.json --master-key key.json
```

#### 输出示例
```
✓ ServeV3 signature detected: 7310310183885631299
✓ Encryption flag correctly set
✓ Hash updated: 1234567890 -> 9876543210
✓ Encryption/Decryption cycle successful
```

---

## v2.0.0 - 2024-12-XX (批量加密版本)

### ✨ 新增功能

#### 批量加密支持
- 添加了`encrypt-config`命令用于单文件加密
- 添加了`batch-encrypt`命令用于批量加密
- 支持嵌套对象字段的加密

#### 新增脚本
- `encrypt.bat` / `encrypt.sh` - 单文件加密脚本
- `batch_process.bat` / `batch_process.sh` - 批量处理脚本

#### 嵌套字段支持
- 完整支持`LauncherResourceChunksURL`对象
- 自动处理`BranchUrl`, `MainUrl`, `PreloadUrl`, `PatchUrl`字段

### 🔧 改进

#### 字段分析器增强
- 改进ServeV3格式检测
- 添加嵌套字段描述
- 更准确的加密状态检测

#### 用户体验
- 详细的加密进度显示
- 更好的错误处理和提示
- 完整的使用示例文档

---

## v1.0.0 - 2024-12-XX (初始版本)

### ✨ 核心功能

#### 基础加密/解密
- 实现了基础的字段解密功能
- 支持单个字段和配置文件解密
- Master Key生成和管理

#### 配置分析
- 配置文件结构分析
- 加密字段识别
- 详细的分析报告

#### 批量处理
- 批量解密多个配置文件
- 目录级别的处理支持

### 🧪 测试框架
- 基础测试套件
- 加密/解密验证
- 配置文件处理测试

---

## 升级指南

### 从v1.x升级到v2.1.0

1. **备份现有配置**
   ```bash
   cp -r configs/ configs_backup/
   ```

2. **更新工具**
   ```bash
   git pull origin main
   pip install -r requirements.txt
   ```

3. **验证新功能**
   ```bash
   python test_fixed_encryption.py
   ```

4. **重新加密配置** (如果需要)
   ```bash
   # 解密现有配置
   python crypto_tool.py decrypt-config --input old_config.json --output temp.json --master-key key.json
   
   # 使用新格式重新加密
   python crypto_tool.py encrypt-config --input temp.json --output new_config.json --master-key key.json
   
   # 清理临时文件
   rm temp.json
   ```

### 注意事项

1. **Hash字段变化**: 重新加密后Hash值会改变，这是正常现象
2. **格式兼容性**: 新版本仍可解密旧格式数据
3. **性能提升**: 新版本加密速度更快，内存使用更少
