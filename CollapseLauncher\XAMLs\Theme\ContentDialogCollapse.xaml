﻿<!--  Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. See LICENSE in the project root for license information.  -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:customcontrol="using:CollapseLauncher.CustomControls">
    <!--  ReSharper disable Xaml.InvalidResourceType  -->
    <!--  ReSharper disable Xaml.StaticResourceNotResolved  -->
    <ResourceDictionary.ThemeDictionaries>
        <ResourceDictionary x:Key="Default">
            <SolidColorBrush x:Key="SmokeFillColorCollapseBrush"
                             Color="#7F000000" />
            <StaticResource x:Key="ContentDialogForeground"
                            ResourceKey="TextFillColorPrimaryBrush" />
            <StaticResource x:Key="ContentDialogBackground"
                            ResourceKey="SolidBackgroundFillColorBaseBrush" />
            <StaticResource x:Key="ContentDialogSmokeFill"
                            ResourceKey="SmokeFillColorCollapseBrush" />
            <StaticResource x:Key="ContentDialogTopOverlay"
                            ResourceKey="LayerFillColorAltBrush" />
            <StaticResource x:Key="ContentDialogBorderBrush"
                            ResourceKey="SurfaceStrokeColorDefaultBrush" />
            <StaticResource x:Key="ContentDialogSeparatorBorderBrush"
                            ResourceKey="CardStrokeColorDefaultBrush" />
            <Thickness x:Key="ContentDialogBorderWidth">1</Thickness>
            <x:Double x:Key="ContentDialogMinWidth">320</x:Double>
            <x:Double x:Key="ContentDialogMaxWidth">640</x:Double>
            <x:Double x:Key="ContentDialogMinHeight">64</x:Double>
            <x:Double x:Key="ContentDialogMaxHeight">756</x:Double>
            <GridLength x:Key="ContentDialogButtonSpacing">8</GridLength>
            <Thickness x:Key="ContentDialogTitleMargin">0,0,0,12</Thickness>
            <Thickness x:Key="ContentDialogPadding">24</Thickness>
            <Thickness x:Key="ContentDialogSeparatorThickness">0,0,0,1</Thickness>
        </ResourceDictionary>
        <ResourceDictionary x:Key="HighContrast">
            <StaticResource x:Key="ContentDialogForeground"
                            ResourceKey="SystemColorWindowTextColorBrush" />
            <StaticResource x:Key="ContentDialogBackground"
                            ResourceKey="SystemColorWindowColorBrush" />
            <SolidColorBrush x:Key="ContentDialogSmokeFill"
                             Opacity="0.8"
                             Color="{ThemeResource SystemColorWindowColor}" />
            <StaticResource x:Key="ContentDialogTopOverlay"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="ContentDialogBorderBrush"
                            ResourceKey="SystemColorWindowTextColorBrush" />
            <StaticResource x:Key="ContentDialogSeparatorBorderBrush"
                            ResourceKey="SystemColorWindowTextColorBrush" />
            <Thickness x:Key="ContentDialogBorderWidth">2</Thickness>
            <x:Double x:Key="ContentDialogMinWidth">320</x:Double>
            <x:Double x:Key="ContentDialogMaxWidth">640</x:Double>
            <x:Double x:Key="ContentDialogMinHeight">64</x:Double>
            <x:Double x:Key="ContentDialogMaxHeight">756</x:Double>
            <GridLength x:Key="ContentDialogButtonSpacing">8</GridLength>
            <Thickness x:Key="ContentDialogTitleMargin">0,0,0,12</Thickness>
            <Thickness x:Key="ContentDialogPadding">24</Thickness>
            <Thickness x:Key="ContentDialogSeparatorThickness">0,0,0,1</Thickness>
        </ResourceDictionary>
        <ResourceDictionary x:Key="Light">
            <SolidColorBrush x:Key="SmokeFillColorCollapseBrush"
                             Color="#7FFFFFFF" />
            <StaticResource x:Key="ContentDialogForeground"
                            ResourceKey="TextFillColorPrimaryBrush" />
            <StaticResource x:Key="ContentDialogBackground"
                            ResourceKey="SolidBackgroundFillColorBaseBrush" />
            <StaticResource x:Key="ContentDialogSmokeFill"
                            ResourceKey="SmokeFillColorCollapseBrush" />
            <StaticResource x:Key="ContentDialogTopOverlay"
                            ResourceKey="LayerFillColorAltBrush" />
            <StaticResource x:Key="ContentDialogBorderBrush"
                            ResourceKey="SurfaceStrokeColorDefaultBrush" />
            <StaticResource x:Key="ContentDialogSeparatorBorderBrush"
                            ResourceKey="CardStrokeColorDefaultBrush" />
            <Thickness x:Key="ContentDialogBorderWidth">1</Thickness>
            <x:Double x:Key="ContentDialogMinWidth">320</x:Double>
            <x:Double x:Key="ContentDialogMaxWidth">640</x:Double>
            <x:Double x:Key="ContentDialogMinHeight">64</x:Double>
            <x:Double x:Key="ContentDialogMaxHeight">756</x:Double>
            <GridLength x:Key="ContentDialogButtonSpacing">8</GridLength>
            <Thickness x:Key="ContentDialogTitleMargin">0,0,0,12</Thickness>
            <Thickness x:Key="ContentDialogPadding">24</Thickness>
            <Thickness x:Key="ContentDialogSeparatorThickness">0,0,0,1</Thickness>
        </ResourceDictionary>
    </ResourceDictionary.ThemeDictionaries>
    <Style BasedOn="{StaticResource CollapseContentDialogStyle}"
           TargetType="customcontrol:ContentDialogCollapse" />
    <Style x:Key="CollapseContentDialogStyle"
           TargetType="customcontrol:ContentDialogCollapse">
        <Setter Property="Foreground" Value="{ThemeResource ContentDialogForeground}" />
        <Setter Property="Background" Value="{ThemeResource DialogAcrylicBrush}" />
        <Setter Property="BorderThickness" Value="{ThemeResource ContentDialogBorderWidth}" />
        <Setter Property="BorderBrush" Value="{ThemeResource ContentDialogBorderBrush}" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="PrimaryButtonStyle" Value="{ThemeResource DefaultButtonStyle}" />
        <Setter Property="SecondaryButtonStyle" Value="{ThemeResource DefaultButtonStyle}" />
        <Setter Property="CloseButtonStyle" Value="{ThemeResource DefaultButtonStyle}" />
        <Setter Property="CornerRadius" Value="{ThemeResource OverlayCornerRadius}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="customcontrol:ContentDialogCollapse">
                    <Border x:Name="Container">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="DialogShowingStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition To="DialogHidden">
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                           Storyboard.TargetProperty="Visibility">
                                                <DiscreteObjectKeyFrame KeyTime="0:0:0"
                                                                        Value="Visible" />
                                            </ObjectAnimationUsingKeyFrames>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                           Storyboard.TargetProperty="IsHitTestVisible">
                                                <DiscreteObjectKeyFrame KeyTime="0:0:0"
                                                                        Value="False" />
                                            </ObjectAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ScaleTransform"
                                                                           Storyboard.TargetProperty="ScaleX">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="1.0" />
                                                <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                      KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                      Value="1.05" />
                                            </DoubleAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ScaleTransform"
                                                                           Storyboard.TargetProperty="ScaleY">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="1.0" />
                                                <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                      KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                      Value="1.05" />
                                            </DoubleAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                           Storyboard.TargetProperty="Opacity">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="1.0" />
                                                <LinearDoubleKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                      Value="0.0" />
                                            </DoubleAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </VisualTransition>
                                    <VisualTransition To="DialogShowing">
                                        <Storyboard>
                                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                           Storyboard.TargetProperty="Visibility">
                                                <DiscreteObjectKeyFrame KeyTime="0:0:0"
                                                                        Value="Visible" />
                                            </ObjectAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ScaleTransform"
                                                                           Storyboard.TargetProperty="ScaleX">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="1.05" />
                                                <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                      KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                      Value="1.0" />
                                            </DoubleAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ScaleTransform"
                                                                           Storyboard.TargetProperty="ScaleY">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="1.05" />
                                                <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                      KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                      Value="1.0" />
                                            </DoubleAnimationUsingKeyFrames>
                                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="LayoutRoot"
                                                                           Storyboard.TargetProperty="Opacity">
                                                <DiscreteDoubleKeyFrame KeyTime="0:0:0"
                                                                        Value="0.0" />
                                                <LinearDoubleKeyFrame KeyTime="{StaticResource ControlFasterAnimationDuration}"
                                                                      Value="1.0" />
                                            </DoubleAnimationUsingKeyFrames>
                                        </Storyboard>
                                    </VisualTransition>
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="DialogHidden" />
                                <VisualState x:Name="DialogShowing">
                                    <VisualState.Setters>
                                        <Setter Target="PrimaryButton.IsTabStop" Value="True" />
                                        <Setter Target="SecondaryButton.IsTabStop" Value="True" />
                                        <Setter Target="CloseButton.IsTabStop" Value="True" />
                                        <Setter Target="LayoutRoot.Visibility" Value="Visible" />
                                        <Setter Target="BackgroundElement.TabFocusNavigation" Value="Cycle" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="DialogShowingWithoutSmokeLayer">
                                    <VisualState.Setters>
                                        <Setter Target="PrimaryButton.IsTabStop" Value="True" />
                                        <Setter Target="SecondaryButton.IsTabStop" Value="True" />
                                        <Setter Target="CloseButton.IsTabStop" Value="True" />
                                        <Setter Target="LayoutRoot.Visibility" Value="Visible" />
                                        <Setter Target="LayoutRoot.Background" Value="{x:Null}" />
                                    </VisualState.Setters>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="DialogSizingStates">
                                <VisualState x:Name="DefaultDialogSizing" />
                                <VisualState x:Name="FullDialogSizing">
                                    <VisualState.Setters>
                                        <Setter Target="BackgroundElement.VerticalAlignment" Value="Stretch" />
                                    </VisualState.Setters>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="ButtonsVisibilityStates">
                                <VisualState x:Name="AllVisible">
                                    <VisualState.Setters>
                                        <Setter Target="FirstSpacer.Width" Value="{ThemeResource ContentDialogButtonSpacing}" />
                                        <Setter Target="SecondaryColumn.Width" Value="*" />
                                        <Setter Target="SecondaryButton.(Grid.Column)" Value="2" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="NoneVisible">
                                    <VisualState.Setters>
                                        <Setter Target="CommandSpace.Visibility" Value="Collapsed" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="PrimaryVisible">
                                    <VisualState.Setters>
                                        <Setter Target="PrimaryButton.(Grid.Column)" Value="4" />
                                        <Setter Target="SecondaryButton.Visibility" Value="Collapsed" />
                                        <Setter Target="CloseButton.Visibility" Value="Collapsed" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="SecondaryVisible">
                                    <VisualState.Setters>
                                        <Setter Target="SecondaryButton.(Grid.Column)" Value="4" />
                                        <Setter Target="PrimaryButton.Visibility" Value="Collapsed" />
                                        <Setter Target="CloseButton.Visibility" Value="Collapsed" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="CloseVisible">
                                    <VisualState.Setters>
                                        <Setter Target="PrimaryButton.Visibility" Value="Collapsed" />
                                        <Setter Target="SecondaryButton.Visibility" Value="Collapsed" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="PrimaryAndSecondaryVisible">
                                    <VisualState.Setters>
                                        <Setter Target="SecondaryButton.(Grid.Column)" Value="4" />
                                        <Setter Target="CloseButton.Visibility" Value="Collapsed" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="PrimaryAndCloseVisible">
                                    <VisualState.Setters>
                                        <Setter Target="SecondaryButton.Visibility" Value="Collapsed" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="SecondaryAndCloseVisible">
                                    <VisualState.Setters>
                                        <Setter Target="PrimaryButton.Visibility" Value="Collapsed" />
                                    </VisualState.Setters>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="DefaultButtonStates">
                                <VisualState x:Name="NoDefaultButton" />
                                <VisualState x:Name="PrimaryAsDefaultButton">
                                    <VisualState.Setters>
                                        <Setter Target="PrimaryButton.Style" Value="{StaticResource AccentButtonStyle}" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="SecondaryAsDefaultButton">
                                    <VisualState.Setters>
                                        <Setter Target="SecondaryButton.Style" Value="{StaticResource AccentButtonStyle}" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="CloseAsDefaultButton">
                                    <VisualState.Setters>
                                        <Setter Target="CloseButton.Style" Value="{StaticResource AccentButtonStyle}" />
                                    </VisualState.Setters>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="DialogBorderStates">
                                <VisualState x:Name="NoBorder" />
                                <VisualState x:Name="AccentColorBorder">
                                    <VisualState.Setters>
                                        <Setter Target="BackgroundElement.BorderBrush" Value="{ThemeResource SystemControlForegroundAccentBrush}" />
                                    </VisualState.Setters>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid x:Name="LayoutRoot"
                              Visibility="Collapsed">
                            <Grid.Transitions>
                                <PopupThemeTransition />
                            </Grid.Transitions>
                            <Rectangle x:Name="SmokeLayerBackground"
                                       Fill="{ThemeResource ContentDialogSmokeFill}" />
                            <Border x:Name="BackgroundElement"
                                    MinWidth="{ThemeResource ContentDialogMinWidth}"
                                    MinHeight="{ThemeResource ContentDialogMinHeight}"
                                    MaxWidth="{ThemeResource ContentDialogMaxWidth}"
                                    MaxHeight="{ThemeResource ContentDialogMaxHeight}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Background="{ThemeResource DialogAcrylicBrush}"
                                    BackgroundSizing="InnerBorderEdge"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    FlowDirection="{TemplateBinding FlowDirection}"
                                    RenderTransformOrigin="0.5,0.5">
                                <Border.RenderTransform>
                                    <ScaleTransform x:Name="ScaleTransform" />
                                </Border.RenderTransform>
                                <Grid x:Name="DialogSpace"
                                      CornerRadius="{ThemeResource OverlayCornerRadius}">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="*" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <ScrollViewer x:Name="ContentScrollViewer"
                                                  HorizontalScrollBarVisibility="Disabled"
                                                  IsTabStop="False"
                                                  VerticalScrollBarVisibility="Auto"
                                                  ZoomMode="Disabled">
                                        <Grid Background="{ThemeResource ContentDialogTopOverlay}"
                                              BorderBrush="{ThemeResource ContentDialogSeparatorBorderBrush}"
                                              BorderThickness="{ThemeResource ContentDialogSeparatorThickness}">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                            </Grid.RowDefinitions>
                                            <Grid Padding="24,12"
                                                  Background="{ThemeResource DialogTitleBrush}">
                                                <StackPanel Orientation="Horizontal">
                                                    <ContentControl x:Name="Title"
                                                                    Margin="0,-2,0,0"
                                                                    HorizontalAlignment="Left"
                                                                    VerticalAlignment="Center"
                                                                    Content="{TemplateBinding Title}"
                                                                    ContentTemplate="{TemplateBinding TitleTemplate}"
                                                                    FontFamily="{StaticResource ContentControlThemeFontFamily}"
                                                                    FontSize="16"
                                                                    FontWeight="SemiBold"
                                                                    Foreground="{ThemeResource DefaultFGColorAccentBrush}"
                                                                    IsTabStop="False">
                                                        <ContentControl.Template>
                                                            <ControlTemplate TargetType="ContentControl">
                                                                <ContentPresenter Margin="{TemplateBinding Padding}"
                                                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                                  VerticalAlignment="Center"
                                                                                  Content="{TemplateBinding Content}"
                                                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                                                  ContentTransitions="{TemplateBinding ContentTransitions}"
                                                                                  MaxLines="2"
                                                                                  TextWrapping="Wrap" />
                                                            </ControlTemplate>
                                                        </ContentControl.Template>
                                                    </ContentControl>
                                                </StackPanel>
                                            </Grid>
                                            <ContentPresenter x:Name="Content"
                                                              Grid.Row="1"
                                                              Padding="24,20"
                                                              Content="{TemplateBinding Content}"
                                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                                              FontFamily="{StaticResource ContentControlThemeFontFamily}"
                                                              FontSize="{StaticResource ControlContentThemeFontSize}"
                                                              Foreground="{TemplateBinding Foreground}"
                                                              TextWrapping="Wrap" />
                                        </Grid>
                                    </ScrollViewer>
                                    <Grid x:Name="CommandSpace"
                                          Grid.Row="1"
                                          Padding="24,20"
                                          HorizontalAlignment="Stretch"
                                          VerticalAlignment="Bottom"
                                          Background="{ThemeResource DialogAcrylicBrush}"
                                          XYFocusKeyboardNavigation="Enabled">
                                        <Grid HorizontalAlignment="Center">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition x:Name="PrimaryColumn"
                                                                  Width="Auto" />
                                                <ColumnDefinition x:Name="FirstSpacer"
                                                                  Width="0" />
                                                <ColumnDefinition x:Name="SecondaryColumn"
                                                                  Width="Auto" />
                                                <ColumnDefinition x:Name="SecondSpacer"
                                                                  Width="{ThemeResource ContentDialogButtonSpacing}" />
                                                <ColumnDefinition x:Name="CloseColumn"
                                                                  Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <Button x:Name="PrimaryButton"
                                                    Grid.Column="0"
                                                    HorizontalAlignment="Center"
                                                    CornerRadius="14"
                                                    ElementSoundMode="FocusOnly"
                                                    IsEnabled="{TemplateBinding IsPrimaryButtonEnabled}"
                                                    IsTabStop="False"
                                                    Style="{TemplateBinding PrimaryButtonStyle}">
                                                <ContentPresenter Margin="8,0"
                                                                  Content="{TemplateBinding PrimaryButtonText}"
                                                                  FontWeight="SemiBold"
                                                                  MaxLines="2"
                                                                  TextWrapping="Wrap" />
                                            </Button>
                                            <Button x:Name="SecondaryButton"
                                                    Grid.Column="2"
                                                    HorizontalAlignment="Center"
                                                    CornerRadius="14"
                                                    ElementSoundMode="FocusOnly"
                                                    IsEnabled="{TemplateBinding IsSecondaryButtonEnabled}"
                                                    IsTabStop="False"
                                                    Style="{TemplateBinding SecondaryButtonStyle}">
                                                <ContentPresenter Margin="8,0"
                                                                  Content="{TemplateBinding SecondaryButtonText}"
                                                                  FontWeight="SemiBold"
                                                                  MaxLines="2"
                                                                  TextWrapping="Wrap" />
                                            </Button>
                                            <Button x:Name="CloseButton"
                                                    Grid.Column="4"
                                                    HorizontalAlignment="Center"
                                                    CornerRadius="14"
                                                    ElementSoundMode="FocusOnly"
                                                    IsTabStop="False"
                                                    Style="{TemplateBinding CloseButtonStyle}">
                                                <ContentPresenter Margin="8,0"
                                                                  Content="{TemplateBinding CloseButtonText}"
                                                                  FontWeight="SemiBold"
                                                                  MaxLines="2"
                                                                  TextWrapping="Wrap" />
                                            </Button>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
