﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//       LottieGen version:
//           8.0.280225.1+7cd366a738
//       
//       Command:
//           LottieGen -Language CSharp -Namespace CollapseLauncher.AnimatedVisuals.Lottie -Public -WinUIVersion 3.0 -InputFile DownloadIcon.lottie
//       
//       Input file:
//           DownloadIcon.lottie (1623 bytes created 23:20+07:00 Jun 1 2024)
//       
//       LottieGen source:
//           http://aka.ms/Lottie
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
// ____________________________________
// |       Object stats       | Count |
// |__________________________|_______|
// | All CompositionObjects   |   118 |
// |--------------------------+-------|
// | Expression animators     |     8 |
// | KeyFrame animators       |    15 |
// | Reference parameters     |     8 |
// | Expression operations    |     6 |
// |--------------------------+-------|
// | Animated brushes         |     - |
// | Animated gradient stops  |     - |
// | ExpressionAnimations     |     8 |
// | PathKeyFrameAnimations   |     1 |
// |--------------------------+-------|
// | ContainerVisuals         |     5 |
// | ShapeVisuals             |     3 |
// |--------------------------+-------|
// | ContainerShapes          |     3 |
// | CompositionSpriteShapes  |     4 |
// |--------------------------+-------|
// | Brushes                  |     5 |
// | Gradient stops           |     - |
// | CompositionVisualSurface |     2 |
// ------------------------------------
using Microsoft.Graphics;
using Microsoft.Graphics.Canvas;
using Microsoft.Graphics.Canvas.Effects;
using Microsoft.Graphics.Canvas.Geometry;
using Microsoft.UI.Composition;
using System;
using System.Collections.Generic;
using System.Numerics;
using Windows.UI;

namespace CollapseLauncher.AnimatedVisuals.Lottie
{
    // Name:        DownloadIconMasterComp
    // Frame rate:  60 fps
    // Frame count: 300
    // Duration:    5000.0 mS
    sealed partial class DownloadIcon
        : Microsoft.UI.Xaml.Controls.IAnimatedVisualSource
        , Microsoft.UI.Xaml.Controls.IAnimatedVisualSource2
    {
        // Animation duration: 5.000 seconds.
        internal const long c_durationTicks = 50000000;

        public Microsoft.UI.Xaml.Controls.IAnimatedVisual TryCreateAnimatedVisual(Compositor compositor)
        {
            object ignored = null;
            return TryCreateAnimatedVisual(compositor, out ignored);
        }

        public Microsoft.UI.Xaml.Controls.IAnimatedVisual TryCreateAnimatedVisual(Compositor compositor, out object diagnostics)
        {
            diagnostics = null;

            var res = 
                new DownloadIcon_AnimatedVisual(
                    compositor
                    );
                res.CreateAnimations();
                return res;
        }

        /// <summary>
        /// Gets the number of frames in the animation.
        /// </summary>
        public double FrameCount => 300d;

        /// <summary>
        /// Gets the frame rate of the animation.
        /// </summary>
        public double Framerate => 60d;

        /// <summary>
        /// Gets the duration of the animation.
        /// </summary>
        public TimeSpan Duration => TimeSpan.FromTicks(50000000);

        /// <summary>
        /// Converts a zero-based frame number to the corresponding progress value denoting the
        /// start of the frame.
        /// </summary>
        public double FrameToProgress(double frameNumber)
        {
            return frameNumber / 300d;
        }

        /// <summary>
        /// Returns a map from marker names to corresponding progress values.
        /// </summary>
        public IReadOnlyDictionary<string, double> Markers =>
            new Dictionary<string, double>
            {
            };

        /// <summary>
        /// Sets the color property with the given name, or does nothing if no such property
        /// exists.
        /// </summary>
        public void SetColorProperty(string propertyName, Color value)
        {
        }

        /// <summary>
        /// Sets the scalar property with the given name, or does nothing if no such property
        /// exists.
        /// </summary>
        public void SetScalarProperty(string propertyName, double value)
        {
        }

        sealed partial class DownloadIcon_AnimatedVisual
            : Microsoft.UI.Xaml.Controls.IAnimatedVisual
            , Microsoft.UI.Xaml.Controls.IAnimatedVisual2
        {
            const long c_durationTicks = 50000000;
            readonly Compositor _c;
            readonly ExpressionAnimation _reusableExpressionAnimation;
            AnimationController _animationController_0;
            CompositionColorBrush _colorBrush_White;
            CompositionContainerShape _containerShape_0;
            CompositionContainerShape _containerShape_1;
            CompositionContainerShape _containerShape_2;
            CompositionPath _path;
            CompositionPathGeometry _pathGeometry_0;
            CompositionPathGeometry _pathGeometry_1;
            CompositionPathGeometry _pathGeometry_2;
            CompositionPathGeometry _pathGeometry_3;
            CompositionSpriteShape _spriteShape_2;
            ContainerVisual _containerVisual_1;
            ContainerVisual _root;
            CubicBezierEasingFunction _cubicBezierEasingFunction_0;
            CubicBezierEasingFunction _cubicBezierEasingFunction_1;
            ScalarKeyFrameAnimation _opacityScalarAnimation_1_to_0;
            ScalarKeyFrameAnimation _positionYScalarAnimation_1025_to_2350;
            ScalarKeyFrameAnimation _rotationAngleInDegreesScalarAnimation_0_to_0_0;
            ShapeVisual _shapeVisual_2;
            StepEasingFunction _holdThenStepEasingFunction;
            StepEasingFunction _stepThenHoldEasingFunction;

            void BindProperty(
                CompositionObject target,
                string animatedPropertyName,
                string expression,
                string referenceParameterName,
                CompositionObject referencedObject)
            {
                _reusableExpressionAnimation.ClearAllParameters();
                _reusableExpressionAnimation.Expression = expression;
                _reusableExpressionAnimation.SetReferenceParameter(referenceParameterName, referencedObject);
                target.StartAnimation(animatedPropertyName, _reusableExpressionAnimation);
            }

            PathKeyFrameAnimation CreatePathKeyFrameAnimation(float initialProgress, CompositionPath initialValue, CompositionEasingFunction initialEasingFunction)
            {
                var result = _c.CreatePathKeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InsertKeyFrame(initialProgress, initialValue, initialEasingFunction);
                return result;
            }

            ScalarKeyFrameAnimation CreateScalarKeyFrameAnimation(float initialProgress, float initialValue, CompositionEasingFunction initialEasingFunction)
            {
                var result = _c.CreateScalarKeyFrameAnimation();
                result.Duration = TimeSpan.FromTicks(c_durationTicks);
                result.InsertKeyFrame(initialProgress, initialValue, initialEasingFunction);
                return result;
            }

            CompositionSpriteShape CreateSpriteShape(CompositionGeometry geometry, Matrix3x2 transformMatrix)
            {
                var result = _c.CreateSpriteShape(geometry);
                result.TransformMatrix = transformMatrix;
                return result;
            }

            AnimationController AnimationController_0()
            {
                if (_animationController_0 != null) { return _animationController_0; }
                var result = _animationController_0 = _c.CreateAnimationController();
                result.Pause();
                BindProperty(_animationController_0, "Progress", "_.Progress", "_", _root);
                return result;
            }

            CanvasGeometry Geometry_0()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(0F, 116F));
                    builder.AddLine(new Vector2(0F, -832F));
                    builder.AddCubicBezier(new Vector2(-459.501007F, -832F), new Vector2(-832F, -459.501007F), new Vector2(-832F, 0F));
                    builder.AddCubicBezier(new Vector2(-832F, 459.501007F), new Vector2(-459.501007F, 832F), new Vector2(0F, 832F));
                    builder.AddCubicBezier(new Vector2(459.501007F, 832F), new Vector2(832F, 459.501007F), new Vector2(832F, 0F));
                    builder.AddCubicBezier(new Vector2(832F, -459.501007F), new Vector2(515F, -688F), new Vector2(515F, -688F));
                    builder.AddLine(new Vector2(0F, 116F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - Transforms: Shape Layer 2 Offset:<50, 50>
            CanvasGeometry Geometry_1()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(360.516998F, -180.5F));
                    builder.AddLine(new Vector2(0F, 180.016998F));
                    builder.AddLine(new Vector2(-360.516998F, -180.5F));
                    builder.AddLine(new Vector2(360.516998F, -180.5F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_2()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(2316F, -196F));
                    builder.AddCubicBezier(new Vector2(2316F, -196F), new Vector2(-336F, -196F), new Vector2(-336F, -196F));
                    builder.AddCubicBezier(new Vector2(-336F, -196F), new Vector2(-336F, 2132F), new Vector2(-336F, 2132F));
                    builder.AddCubicBezier(new Vector2(-336F, 2132F), new Vector2(2316F, 2132F), new Vector2(2316F, 2132F));
                    builder.AddCubicBezier(new Vector2(2316F, 2132F), new Vector2(2316F, -196F), new Vector2(2316F, -196F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - PreComp layer: DownloadIcon
            // - - - - Masks
            // - Path
            CanvasGeometry Geometry_3()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(2316F, -196F));
                    builder.AddCubicBezier(new Vector2(2316F, -196F), new Vector2(-336F, -196F), new Vector2(-336F, -196F));
                    builder.AddCubicBezier(new Vector2(-336F, -196F), new Vector2(-336F, 1532F), new Vector2(-336F, 1532F));
                    builder.AddCubicBezier(new Vector2(-336F, 1532F), new Vector2(2316F, 1532F), new Vector2(2316F, 1532F));
                    builder.AddCubicBezier(new Vector2(2316F, 1532F), new Vector2(2316F, -196F), new Vector2(2316F, -196F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - - - - PreComp layer: DownloadIcon
            // - - - - Masks
            // - Path
            CanvasGeometry Geometry_4()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(2316F, -196F));
                    builder.AddCubicBezier(new Vector2(2316F, -196F), new Vector2(-336F, -196F), new Vector2(-336F, -196F));
                    builder.AddCubicBezier(new Vector2(-336F, -196F), new Vector2(-336F, 1432F), new Vector2(-336F, 1432F));
                    builder.AddCubicBezier(new Vector2(-336F, 1432F), new Vector2(2316F, 1432F), new Vector2(2316F, 1432F));
                    builder.AddCubicBezier(new Vector2(2316F, 1432F), new Vector2(2316F, -196F), new Vector2(2316F, -196F));
                    builder.EndFigure(CanvasFigureLoop.Closed);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            CanvasGeometry Geometry_5()
            {
                CanvasGeometry result;
                using (var builder = new CanvasPathBuilder(null))
                {
                    builder.BeginFigure(new Vector2(-460F, 344F));
                    builder.AddLine(new Vector2(468F, 344F));
                    builder.EndFigure(CanvasFigureLoop.Open);
                    result = CanvasGeometry.CreatePath(builder);
                }
                return result;
            }

            // - - - - - - PreComp layer: DownloadIcon
            // - Masks
            CompositionColorBrush ColorBrush_Black()
            {
                return _c.CreateColorBrush(Color.FromArgb(0xFF, 0x00, 0x00, 0x00));
            }

            CompositionColorBrush ColorBrush_White()
            {
                return (_colorBrush_White == null)
                    ? _colorBrush_White = _c.CreateColorBrush(InnerLauncherConfig.IsAppThemeLight ? Color.FromArgb(0xFF, 0xFF, 0xFF, 0xFF) : Color.FromArgb(0xFF, 0x00, 0x00, 0x00))
                    : _colorBrush_White;
            }

            CompositionContainerShape ContainerShape_0()
            {
                if (_containerShape_0 != null) { return _containerShape_0; }
                var result = _containerShape_0 = _c.CreateContainerShape();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(1024F, 1025F));
                // Transforms: Shape Layer 1 Offset:<50, 50>
                result.Shapes.Add(SpriteShape_0());
                BindProperty(_containerShape_0, "Offset", "Vector2(my.Position.X-50,my.Position.Y-50)", "my", _containerShape_0);
                return result;
            }

            CompositionContainerShape ContainerShape_1()
            {
                if (_containerShape_1 != null) { return _containerShape_1; }
                var result = _containerShape_1 = _c.CreateContainerShape();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(1024F, 1025F));
                // Transforms: Shape Layer 2 Offset:<50, 50>
                result.Shapes.Add(SpriteShape_1());
                BindProperty(_containerShape_1, "Offset", "Vector2(my.Position.X-50,my.Position.Y-50)", "my", _containerShape_1);
                return result;
            }

            CompositionContainerShape ContainerShape_2()
            {
                if (_containerShape_2 != null) { return _containerShape_2; }
                var result = _containerShape_2 = _c.CreateContainerShape();
                var propertySet = result.Properties;
                propertySet.InsertVector2("Position", new Vector2(1024F, 1264F));
                result.CenterPoint = new Vector2(4F, 344F);
                // ShapeGroup: Shape 1
                result.Shapes.Add(SpriteShape_3());
                BindProperty(_containerShape_2, "Offset", "Vector2(my.Position.X-4,my.Position.Y-344)", "my", _containerShape_2);
                return result;
            }

            // PreComp layer: DownloadIcon
            CompositionEffectBrush EffectBrush()
            {
                var effectFactory = EffectFactory();
                var result = effectFactory.CreateBrush();
                result.SetSourceParameter("destination", SurfaceBrush_0());
                result.SetSourceParameter("source", SurfaceBrush_1());
                return result;
            }

            // - PreComp layer: DownloadIcon
            CompositionEffectFactory EffectFactory()
            {
                var compositeEffect = new CompositeEffect();
                compositeEffect.Mode = CanvasComposite.DestinationIn;
                compositeEffect.Sources.Add(new CompositionEffectSourceParameter("destination"));
                compositeEffect.Sources.Add(new CompositionEffectSourceParameter("source"));
                var result = _c.CreateEffectFactory(compositeEffect);
                return result;
            }

            CompositionPath Path()
            {
                if (_path != null) { return _path; }
                var result = _path = new CompositionPath(Geometry_2());
                return result;
            }

            CompositionPathGeometry PathGeometry_0()
            {
                if (_pathGeometry_0 != null) { return _pathGeometry_0; }
                var result = _pathGeometry_0 = _c.CreatePathGeometry(new CompositionPath(Geometry_0()));
                var propertySet = result.Properties;
                propertySet.InsertScalar("TEnd", 0.855000019F);
                propertySet.InsertScalar("TStart", 0.855000019F);
                BindProperty(_pathGeometry_0, "TrimStart", "Min(my.TStart,my.TEnd)", "my", _pathGeometry_0);
                BindProperty(_pathGeometry_0, "TrimEnd", "Max(my.TStart,my.TEnd)", "my", _pathGeometry_0);
                return result;
            }

            // Transforms: Shape Layer 2 Offset:<50, 50>
            CompositionPathGeometry PathGeometry_1()
            {
                if (_pathGeometry_1 != null) { return _pathGeometry_1; }
                var result = _pathGeometry_1 = _c.CreatePathGeometry(new CompositionPath(Geometry_1()));
                result.TrimEnd = 0.584999979F;
                return result;
            }

            // - - - - - - PreComp layer: DownloadIcon
            // - Masks
            CompositionPathGeometry PathGeometry_2()
            {
                if (_pathGeometry_2 != null) { return _pathGeometry_2; }
                var result = _pathGeometry_2 = _c.CreatePathGeometry();
                return result;
            }

            CompositionPathGeometry PathGeometry_3()
            {
                if (_pathGeometry_3 != null) { return _pathGeometry_3; }
                var result = _pathGeometry_3 = _c.CreatePathGeometry(new CompositionPath(Geometry_5()));
                var propertySet = result.Properties;
                propertySet.InsertScalar("TEnd", 0F);
                propertySet.InsertScalar("TStart", 0F);
                BindProperty(_pathGeometry_3, "TrimStart", "Min(my.TStart,my.TEnd)", "my", _pathGeometry_3);
                BindProperty(_pathGeometry_3, "TrimEnd", "Max(my.TStart,my.TEnd)", "my", _pathGeometry_3);
                return result;
            }

            // Path 1
            CompositionSpriteShape SpriteShape_0()
            {
                // Offset:<50, 50>
                var result = CreateSpriteShape(PathGeometry_0(), new Matrix3x2(1F, 0F, 0F, 1F, 50F, 50F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeDashCap = CompositionStrokeCap.Round;
                result.StrokeStartCap = CompositionStrokeCap.Round;
                result.StrokeEndCap = CompositionStrokeCap.Round;
                result.StrokeLineJoin = CompositionStrokeLineJoin.Round;
                result.StrokeMiterLimit = 2F;
                result.StrokeThickness = 128F;
                return result;
            }

            // Path 1
            CompositionSpriteShape SpriteShape_1()
            {
                // Offset:<50, 50>
                var result = CreateSpriteShape(PathGeometry_1(), new Matrix3x2(1F, 0F, 0F, 1F, 50F, 50F));;
                result.StrokeBrush = ColorBrush_White();
                result.StrokeDashCap = CompositionStrokeCap.Round;
                result.StrokeStartCap = CompositionStrokeCap.Round;
                result.StrokeEndCap = CompositionStrokeCap.Round;
                result.StrokeLineJoin = CompositionStrokeLineJoin.Round;
                result.StrokeMiterLimit = 2F;
                result.StrokeThickness = 128F;
                return result;
            }

            // - - - - - PreComp layer: DownloadIcon
            // Masks
            CompositionSpriteShape SpriteShape_2()
            {
                if (_spriteShape_2 != null) { return _spriteShape_2; }
                var result = _spriteShape_2 = _c.CreateSpriteShape(PathGeometry_2());
                result.CenterPoint = new Vector2(1024F, 1024F);
                result.FillBrush = ColorBrush_Black();
                return result;
            }

            // Path 1
            CompositionSpriteShape SpriteShape_3()
            {
                var result = _c.CreateSpriteShape(PathGeometry_3());
                result.StrokeBrush = ColorBrush_White();
                result.StrokeDashCap = CompositionStrokeCap.Round;
                result.StrokeStartCap = CompositionStrokeCap.Round;
                result.StrokeEndCap = CompositionStrokeCap.Round;
                result.StrokeLineJoin = CompositionStrokeLineJoin.Round;
                result.StrokeMiterLimit = 2F;
                result.StrokeThickness = 128F;
                return result;
            }

            // - PreComp layer: DownloadIcon
            CompositionSurfaceBrush SurfaceBrush_0()
            {
                return _c.CreateSurfaceBrush(VisualSurface_0());
            }

            // - PreComp layer: DownloadIcon
            CompositionSurfaceBrush SurfaceBrush_1()
            {
                return _c.CreateSurfaceBrush(VisualSurface_1());
            }

            // - - PreComp layer: DownloadIcon
            CompositionVisualSurface VisualSurface_0()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_0();
                result.SourceSize = new Vector2(2048F, 2048F);
                return result;
            }

            // - - PreComp layer: DownloadIcon
            CompositionVisualSurface VisualSurface_1()
            {
                var result = _c.CreateVisualSurface();
                result.SourceVisual = ContainerVisual_3();
                result.SourceSize = new Vector2(2048F, 2048F);
                return result;
            }

            // - - - PreComp layer: DownloadIcon
            ContainerVisual ContainerVisual_0()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Opacity for layer: DownloadIcon
                result.Children.InsertAtTop(ContainerVisual_1());
                return result;
            }

            // - - - - PreComp layer: DownloadIcon
            // Opacity for layer: DownloadIcon
            ContainerVisual ContainerVisual_1()
            {
                if (_containerVisual_1 != null) { return _containerVisual_1; }
                var result = _containerVisual_1 = _c.CreateContainerVisual();
                result.CenterPoint = new Vector3(1024F, 1024F, 0F);
                result.Scale = new Vector3(1F, 1F, 0F);
                // Transforms for DownloadIcon
                result.Children.InsertAtTop(ContainerVisual_2());
                return result;
            }

            // - - - - - PreComp layer: DownloadIcon
            // Opacity for layer: DownloadIcon
            // Transforms for DownloadIcon
            ContainerVisual ContainerVisual_2()
            {
                var result = _c.CreateContainerVisual();
                result.Clip = InsetClip_0();
                result.Size = new Vector2(2048F, 2048F);
                // Layer aggregator
                result.Children.InsertAtTop(ShapeVisual_0());
                return result;
            }

            // - - - PreComp layer: DownloadIcon
            ContainerVisual ContainerVisual_3()
            {
                var result = _c.CreateContainerVisual();
                result.BorderMode = CompositionBorderMode.Soft;
                // Masks
                result.Children.InsertAtTop(ShapeVisual_1());
                return result;
            }

            // The root of the composition.
            ContainerVisual Root()
            {
                if (_root != null) { return _root; }
                var result = _root = _c.CreateContainerVisual();
                var propertySet = result.Properties;
                propertySet.InsertScalar("Progress", 0F);
                var children = result.Children;
                // PreComp layer: DownloadIcon
                children.InsertAtTop(SpriteVisual_0());
                // Opacity for layer: Shape Layer 3
                children.InsertAtTop(ShapeVisual_2());
                return result;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_0()
            {
                return (_cubicBezierEasingFunction_0 == null)
                    ? _cubicBezierEasingFunction_0 = _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0F, 1F))
                    : _cubicBezierEasingFunction_0;
            }

            CubicBezierEasingFunction CubicBezierEasingFunction_1()
            {
                return (_cubicBezierEasingFunction_1 == null)
                    ? _cubicBezierEasingFunction_1 = _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0.166999996F), new Vector2(0.833000004F, 0.833000004F))
                    : _cubicBezierEasingFunction_1;
            }

            // - - - - - - PreComp layer: DownloadIcon
            // - Opacity for layer: DownloadIcon
            // Transforms for DownloadIcon
            InsetClip InsetClip_0()
            {
                var result = _c.CreateInsetClip();
                return result;
            }

            // - - - - - - - PreComp layer: DownloadIcon
            // - - Masks
            // Path
            PathKeyFrameAnimation PathKeyFrameAnimation_0()
            {
                // Frame 0.
                var result = CreatePathKeyFrameAnimation(0F, Path(), StepThenHoldEasingFunction());
                // Frame 150.
                result.InsertKeyFrame(0.5F, Path(), HoldThenStepEasingFunction());
                // Frame 189.
                result.InsertKeyFrame(0.629999995F, new CompositionPath(Geometry_3()), CubicBezierEasingFunction_1());
                // Frame 230.
                result.InsertKeyFrame(0.766666651F, new CompositionPath(Geometry_4()), CubicBezierEasingFunction_1());
                return result;
            }

            // Layer opacity animation
            ScalarKeyFrameAnimation OpacityScalarAnimation_1_to_0()
            {
                // Frame 0.
                if (_opacityScalarAnimation_1_to_0 != null) { return _opacityScalarAnimation_1_to_0; }
                var result = _opacityScalarAnimation_1_to_0 = CreateScalarKeyFrameAnimation(0F, 1F, StepThenHoldEasingFunction());
                // Frame 260.
                result.InsertKeyFrame(0.866666675F, 1F, HoldThenStepEasingFunction());
                // Frame 280.
                result.InsertKeyFrame(0.933333337F, 0F, CubicBezierEasingFunction_1());
                return result;
            }

            // Position.Y
            ScalarKeyFrameAnimation PositionYScalarAnimation_1025_to_2350()
            {
                // Frame 0.
                if (_positionYScalarAnimation_1025_to_2350 != null) { return _positionYScalarAnimation_1025_to_2350; }
                var result = _positionYScalarAnimation_1025_to_2350 = CreateScalarKeyFrameAnimation(0F, 1025F, StepThenHoldEasingFunction());
                // Frame 50.
                result.InsertKeyFrame(0.166666672F, 1025F, HoldThenStepEasingFunction());
                // Frame 90.
                result.InsertKeyFrame(0.300000012F, 1009F, CubicBezierEasingFunction_0());
                // Frame 165.
                result.InsertKeyFrame(0.550000012F, 1009F, CubicBezierEasingFunction_0());
                // Frame 200.
                result.InsertKeyFrame(0.666666687F, 876F, CubicBezierEasingFunction_0());
                // Frame 210.
                result.InsertKeyFrame(0.699999988F, 876F, _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0.167999998F, 1F)));
                // Frame 255.
                result.InsertKeyFrame(0.850000024F, 2350F, CubicBezierEasingFunction_0());
                return result;
            }

            // Position.Y
            ScalarKeyFrameAnimation PositionYScalarAnimation_1264_to_1433()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 1264F, StepThenHoldEasingFunction());
                // Frame 30.
                result.InsertKeyFrame(0.100000001F, 1264F, HoldThenStepEasingFunction());
                // Frame 70.
                result.InsertKeyFrame(0.233333334F, 1433F, CubicBezierEasingFunction_0());
                // Frame 167.
                result.InsertKeyFrame(0.556666672F, 1433F, _c.CreateCubicBezierEasingFunction(new Vector2(0.157000005F, 0F), new Vector2(0.0299999993F, 1F)));
                // Frame 188.
                result.InsertKeyFrame(0.626666665F, 1577F, CubicBezierEasingFunction_0());
                // Frame 210.
                result.InsertKeyFrame(0.699999988F, 1577F, CubicBezierEasingFunction_0());
                // Frame 240.
                result.InsertKeyFrame(0.800000012F, 1433F, CubicBezierEasingFunction_0());
                return result;
            }

            // Rotation
            ScalarKeyFrameAnimation RotationAngleInDegreesScalarAnimation_0_to_0_0()
            {
                // Frame 0.
                if (_rotationAngleInDegreesScalarAnimation_0_to_0_0 != null) { return _rotationAngleInDegreesScalarAnimation_0_to_0_0; }
                var result = _rotationAngleInDegreesScalarAnimation_0_to_0_0 = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 140.
                result.InsertKeyFrame(0.466666669F, 0F, HoldThenStepEasingFunction());
                // Frame 150.
                result.InsertKeyFrame(0.5F, -9F, CubicBezierEasingFunction_0());
                // Frame 160.
                result.InsertKeyFrame(0.533333361F, 11F, CubicBezierEasingFunction_0());
                // Frame 179.
                result.InsertKeyFrame(0.596666694F, 0F, CubicBezierEasingFunction_0());
                return result;
            }

            // Rotation
            ScalarKeyFrameAnimation RotationAngleInDegreesScalarAnimation_0_to_0_1()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 140.
                result.InsertKeyFrame(0.466666669F, 0F, HoldThenStepEasingFunction());
                // Frame 150.
                result.InsertKeyFrame(0.5F, 8F, CubicBezierEasingFunction_0());
                // Frame 167.
                result.InsertKeyFrame(0.556666672F, -8F, CubicBezierEasingFunction_0());
                // Frame 184.
                result.InsertKeyFrame(0.613333344F, 0F, CubicBezierEasingFunction_0());
                return result;
            }

            // TEnd
            ScalarKeyFrameAnimation TEndScalarAnimation_0_to_1()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 30.
                result.InsertKeyFrame(0.100000001F, 0F, HoldThenStepEasingFunction());
                // Frame 70.
                result.InsertKeyFrame(0.233333334F, 1F, CubicBezierEasingFunction_0());
                // Frame 260.
                result.InsertKeyFrame(0.866666675F, 1F, _c.CreateCubicBezierEasingFunction(new Vector2(0.333000004F, 0F), new Vector2(0.666999996F, 1F)));
                // Frame 280.
                result.InsertKeyFrame(0.933333337F, 1F, CubicBezierEasingFunction_0());
                return result;
            }

            // TEnd
            ScalarKeyFrameAnimation TEndScalarAnimation_0p855_to_0p1()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0.855000019F, HoldThenStepEasingFunction());
                // Frame 80.
                result.InsertKeyFrame(0.266666681F, 0.100000001F, _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0.166999996F), new Vector2(0F, 1F)));
                return result;
            }

            // - Transforms: Shape Layer 2 Offset:<50, 50>
            // TrimOffset
            ScalarKeyFrameAnimation TrimOffsetScalarAnimation_m0p282_to_0()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, -0.282222211F, StepThenHoldEasingFunction());
                // Frame 53.
                result.InsertKeyFrame(0.176666662F, -0.282222211F, HoldThenStepEasingFunction());
                // Frame 83.
                result.InsertKeyFrame(0.276666671F, 0F, CubicBezierEasingFunction_0());
                return result;
            }

            // - Transforms: Shape Layer 2 Offset:<50, 50>
            // TrimStart
            ScalarKeyFrameAnimation TrimStartScalarAnimation_0p585_to_0()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0.584999979F, StepThenHoldEasingFunction());
                // Frame 53.
                result.InsertKeyFrame(0.176666662F, 0.584999979F, HoldThenStepEasingFunction());
                // Frame 83.
                result.InsertKeyFrame(0.276666671F, 0F, CubicBezierEasingFunction_0());
                return result;
            }

            // TStart
            ScalarKeyFrameAnimation TStartScalarAnimation_0_to_1()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0F, StepThenHoldEasingFunction());
                // Frame 30.
                result.InsertKeyFrame(0.100000001F, 0F, HoldThenStepEasingFunction());
                // Frame 70.
                result.InsertKeyFrame(0.233333334F, 0F, CubicBezierEasingFunction_0());
                // Frame 260.
                result.InsertKeyFrame(0.866666675F, 0F, _c.CreateCubicBezierEasingFunction(new Vector2(0.166999996F, 0F), new Vector2(0.586000025F, 1F)));
                // Frame 280.
                result.InsertKeyFrame(0.933333337F, 1F, CubicBezierEasingFunction_0());
                return result;
            }

            // TStart
            ScalarKeyFrameAnimation TStartScalarAnimation_0p855_to_0()
            {
                // Frame 0.
                var result = CreateScalarKeyFrameAnimation(0F, 0.855000019F, HoldThenStepEasingFunction());
                // Frame 60.
                result.InsertKeyFrame(0.200000003F, 0F, CubicBezierEasingFunction_0());
                return result;
            }

            // - - - - - - PreComp layer: DownloadIcon
            // - Opacity for layer: DownloadIcon
            // Transforms for DownloadIcon
            // Layer aggregator
            ShapeVisual ShapeVisual_0()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(2048F, 2048F);
                var shapes = result.Shapes;
                shapes.Add(ContainerShape_0());
                shapes.Add(ContainerShape_1());
                return result;
            }

            // - - - - PreComp layer: DownloadIcon
            // Masks
            ShapeVisual ShapeVisual_1()
            {
                var result = _c.CreateShapeVisual();
                result.Size = new Vector2(2048F, 2048F);
                result.Shapes.Add(SpriteShape_2());
                return result;
            }

            // Shape tree root for layer: Shape Layer 3
            ShapeVisual ShapeVisual_2()
            {
                if (_shapeVisual_2 != null) { return _shapeVisual_2; }
                var result = _shapeVisual_2 = _c.CreateShapeVisual();
                result.Size = new Vector2(2048F, 2048F);
                result.Shapes.Add(ContainerShape_2());
                return result;
            }

            // PreComp layer: DownloadIcon
            SpriteVisual SpriteVisual_0()
            {
                var result = _c.CreateSpriteVisual();
                result.Size = new Vector2(2048F, 2048F);
                result.Brush = EffectBrush();
                return result;
            }

            StepEasingFunction HoldThenStepEasingFunction()
            {
                if (_holdThenStepEasingFunction != null) { return _holdThenStepEasingFunction; }
                var result = _holdThenStepEasingFunction = _c.CreateStepEasingFunction();
                result.IsFinalStepSingleFrame = true;
                return result;
            }

            StepEasingFunction StepThenHoldEasingFunction()
            {
                if (_stepThenHoldEasingFunction != null) { return _stepThenHoldEasingFunction; }
                var result = _stepThenHoldEasingFunction = _c.CreateStepEasingFunction();
                result.IsInitialStepSingleFrame = true;
                return result;
            }

            internal DownloadIcon_AnimatedVisual(
                Compositor compositor
                )
            {
                _c = compositor;
                _reusableExpressionAnimation = compositor.CreateExpressionAnimation();
                Root();
            }

            public Visual RootVisual => _root;
            public TimeSpan Duration => TimeSpan.FromTicks(c_durationTicks);
            public Vector2 Size => new Vector2(2048F, 2048F);
            void IDisposable.Dispose() => _root?.Dispose();

            public void CreateAnimations()
            {
                _containerShape_0.Properties.StartAnimation("Position.Y", PositionYScalarAnimation_1025_to_2350(), AnimationController_0());
                _containerShape_1.Properties.StartAnimation("Position.Y", PositionYScalarAnimation_1025_to_2350(), AnimationController_0());
                _containerShape_2.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_0_to_0_1(), AnimationController_0());
                _containerShape_2.Properties.StartAnimation("Position.Y", PositionYScalarAnimation_1264_to_1433(), AnimationController_0());
                _pathGeometry_0.StartAnimation("TStart", TStartScalarAnimation_0p855_to_0(), AnimationController_0());
                _pathGeometry_0.StartAnimation("TEnd", TEndScalarAnimation_0p855_to_0p1(), AnimationController_0());
                _pathGeometry_1.StartAnimation("TrimStart", TrimStartScalarAnimation_0p585_to_0(), AnimationController_0());
                _pathGeometry_1.StartAnimation("TrimOffset", TrimOffsetScalarAnimation_m0p282_to_0(), AnimationController_0());
                _pathGeometry_2.StartAnimation("Path", PathKeyFrameAnimation_0(), AnimationController_0());
                _pathGeometry_3.StartAnimation("TStart", TStartScalarAnimation_0_to_1(), AnimationController_0());
                _pathGeometry_3.StartAnimation("TEnd", TEndScalarAnimation_0_to_1(), AnimationController_0());
                _spriteShape_2.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_0_to_0_0(), AnimationController_0());
                _containerVisual_1.StartAnimation("Opacity", OpacityScalarAnimation_1_to_0(), AnimationController_0());
                _containerVisual_1.StartAnimation("RotationAngleInDegrees", RotationAngleInDegreesScalarAnimation_0_to_0_0(), AnimationController_0());
                _shapeVisual_2.StartAnimation("Opacity", OpacityScalarAnimation_1_to_0(), AnimationController_0());
            }

            public void DestroyAnimations()
            {
                _containerShape_0.Properties.StopAnimation("Position.Y");
                _containerShape_1.Properties.StopAnimation("Position.Y");
                _containerShape_2.StopAnimation("RotationAngleInDegrees");
                _containerShape_2.Properties.StopAnimation("Position.Y");
                _pathGeometry_0.StopAnimation("TStart");
                _pathGeometry_0.StopAnimation("TEnd");
                _pathGeometry_1.StopAnimation("TrimStart");
                _pathGeometry_1.StopAnimation("TrimOffset");
                _pathGeometry_2.StopAnimation("Path");
                _pathGeometry_3.StopAnimation("TStart");
                _pathGeometry_3.StopAnimation("TEnd");
                _spriteShape_2.StopAnimation("RotationAngleInDegrees");
                _containerVisual_1.StopAnimation("Opacity");
                _containerVisual_1.StopAnimation("RotationAngleInDegrees");
                _shapeVisual_2.StopAnimation("Opacity");
            }

        }
    }
}
