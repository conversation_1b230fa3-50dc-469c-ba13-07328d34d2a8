﻿<!--  Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. See LICENSE in the project root for license information.  -->
<!--  <PERSON>S<PERSON>per disable Xaml.InvalidResourceType  -->
<!--  ReSharper disable Xaml.ConstructorWarning  -->
<!--  ReSharper disable Xaml.StaticResourceNotResolved  -->
<!--  <PERSON>S<PERSON>per disable Xaml.BindingWithContextNotResolved  -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:conv="using:CollapseLauncher.Pages"
                    xmlns:text="using:Windows.UI.Text">
    <ResourceDictionary.ThemeDictionaries>
        <ResourceDictionary x:Key="Default">
            <!--  ColorAnimation requires TargetProperty to be Color thus some resources point to the colour instead of brush  -->
            <StaticResource x:Key="FatSliderContainerBackground"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="FatSliderContainerBackgroundPointerOver"
                            ResourceKey="ControlFillColorTransparent" />
            <StaticResource x:Key="FatSliderContainerBackgroundPressed"
                            ResourceKey="ControlFillColorTransparent" />
            <StaticResource x:Key="FatSliderContainerBackgroundDisabled"
                            ResourceKey="ControlFillColorTransparent" />
            <StaticResource x:Key="FatSliderThumbBackground"
                            ResourceKey="AccentFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderThumbBackgroundPointerOver"
                            ResourceKey="AccentFillColorSecondaryBrush" />
            <StaticResource x:Key="FatSliderThumbBackgroundPressed"
                            ResourceKey="AccentFillColorTertiaryBrush" />
            <StaticResource x:Key="FatSliderThumbBackgroundDisabled"
                            ResourceKey="AccentFillColorDisabledBrush" />
            <StaticResource x:Key="FatSliderThumbBorderBrush"
                            ResourceKey="ControlElevationBorderBrush" />
            <StaticResource x:Key="FatSliderOuterThumbBackground"
                            ResourceKey="ControlSolidFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTrackFill"
                            ResourceKey="ControlStrongFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTrackFillPointerOver"
                            ResourceKey="ControlStrongFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTrackFillPressed"
                            ResourceKey="ControlStrongFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTrackFillDisabled"
                            ResourceKey="ControlStrongFillColorDisabledBrush" />
            <StaticResource x:Key="FatSliderTrackValueFill"
                            ResourceKey="AccentFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTrackValueFillPointerOver"
                            ResourceKey="AccentFillColorSecondaryBrush" />
            <StaticResource x:Key="FatSliderTrackValueFillPressed"
                            ResourceKey="AccentFillColorTertiaryBrush" />
            <StaticResource x:Key="FatSliderTrackValueFillDisabled"
                            ResourceKey="AccentFillColorDisabledBrush" />
            <StaticResource x:Key="FatSliderHeaderForeground"
                            ResourceKey="TextFillColorPrimaryBrush" />
            <StaticResource x:Key="FatSliderHeaderForegroundDisabled"
                            ResourceKey="TextFillColorDisabled" />
            <StaticResource x:Key="FatSliderTickBarFill"
                            ResourceKey="ControlStrongFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTickBarFillDisabled"
                            ResourceKey="ControlStrongFillColorDisabled" />
            <StaticResource x:Key="FatSliderInlineTickBarFill"
                            ResourceKey="ControlFillColorInputActiveBrush" />

            <!--  Legacy Brushes  -->
            <SolidColorBrush x:Key="FatSliderBorderThemeBrush"
                             Color="Transparent" />
            <SolidColorBrush x:Key="FatSliderDisabledBorderThemeBrush"
                             Color="Transparent" />
            <SolidColorBrush x:Key="FatSliderThumbBackgroundThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderThumbBorderThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderThumbDisabledBackgroundThemeBrush"
                             Color="#FF7E7E7E" />
            <SolidColorBrush x:Key="FatSliderThumbPointerOverBackgroundThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderThumbPointerOverBorderThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderThumbPressedBackgroundThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderThumbPressedBorderThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderTickMarkInlineBackgroundThemeBrush"
                             Color="Black" />
            <SolidColorBrush x:Key="FatSliderTickMarkInlineDisabledForegroundThemeBrush"
                             Color="Black" />
            <SolidColorBrush x:Key="FatSliderTickmarkOutsideBackgroundThemeBrush"
                             Color="#80FFFFFF" />
            <SolidColorBrush x:Key="FatSliderTickMarkOutsideDisabledForegroundThemeBrush"
                             Color="#80FFFFFF" />
            <SolidColorBrush x:Key="FatSliderTrackBackgroundThemeBrush"
                             Color="#29FFFFFF" />
            <SolidColorBrush x:Key="FatSliderTrackDecreaseBackgroundThemeBrush"
                             Color="#FF5B2EC5" />
            <SolidColorBrush x:Key="FatSliderTrackDecreaseDisabledBackgroundThemeBrush"
                             Color="#1FFFFFFF" />
            <SolidColorBrush x:Key="FatSliderTrackDecreasePointerOverBackgroundThemeBrush"
                             Color="#FF724BCD" />
            <SolidColorBrush x:Key="FatSliderTrackDecreasePressedBackgroundThemeBrush"
                             Color="#FF8152EF" />
            <SolidColorBrush x:Key="FatSliderTrackDisabledBackgroundThemeBrush"
                             Color="#29FFFFFF" />
            <SolidColorBrush x:Key="FatSliderTrackPointerOverBackgroundThemeBrush"
                             Color="#46FFFFFF" />
            <SolidColorBrush x:Key="FatSliderTrackPressedBackgroundThemeBrush"
                             Color="#59FFFFFF" />
            <SolidColorBrush x:Key="FatSliderHeaderForegroundThemeBrush"
                             Color="#FFFFFFFF" />
        </ResourceDictionary>
        <ResourceDictionary x:Key="HighContrast">
            <StaticResource x:Key="FatSliderContainerBackground"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="FatSliderContainerBackgroundPointerOver"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="FatSliderContainerBackgroundPressed"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="FatSliderContainerBackgroundDisabled"
                            ResourceKey="SystemControlTransparentBrush" />
            <StaticResource x:Key="FatSliderThumbBackground"
                            ResourceKey="SystemControlForegroundAccentBrush" />
            <StaticResource x:Key="FatSliderThumbBackgroundPointerOver"
                            ResourceKey="SystemAccentColorLight1" />
            <StaticResource x:Key="FatSliderThumbBackgroundPressed"
                            ResourceKey="SystemAccentColorDark1" />
            <StaticResource x:Key="FatSliderThumbBackgroundDisabled"
                            ResourceKey="SystemControlDisabledChromeDisabledHighBrush" />
            <StaticResource x:Key="FatSliderThumbBorderBrush"
                            ResourceKey="SystemControlForegroundAccentBrush" />
            <StaticResource x:Key="FatSliderOuterThumbBackground"
                            ResourceKey="SystemControlForegroundAccentBrush" />
            <StaticResource x:Key="FatSliderTrackFill"
                            ResourceKey="SystemControlForegroundBaseMediumLowBrush" />
            <StaticResource x:Key="FatSliderTrackFillPointerOver"
                            ResourceKey="SystemControlForegroundBaseMediumBrush" />
            <StaticResource x:Key="FatSliderTrackFillPressed"
                            ResourceKey="SystemControlForegroundBaseMediumLowBrush" />
            <StaticResource x:Key="FatSliderTrackFillDisabled"
                            ResourceKey="SystemControlDisabledChromeDisabledHighBrush" />
            <StaticResource x:Key="FatSliderTrackValueFill"
                            ResourceKey="SystemControlHighlightAccentBrush" />
            <StaticResource x:Key="FatSliderTrackValueFillPointerOver"
                            ResourceKey="SystemControlHighlightAccentBrush" />
            <StaticResource x:Key="FatSliderTrackValueFillPressed"
                            ResourceKey="SystemControlHighlightAccentBrush" />
            <StaticResource x:Key="FatSliderTrackValueFillDisabled"
                            ResourceKey="SystemControlDisabledChromeDisabledHighBrush" />
            <StaticResource x:Key="FatSliderHeaderForeground"
                            ResourceKey="SystemControlForegroundBaseHighBrush" />
            <StaticResource x:Key="FatSliderHeaderForegroundDisabled"
                            ResourceKey="SystemControlDisabledBaseMediumLowBrush" />
            <StaticResource x:Key="FatSliderTickBarFill"
                            ResourceKey="SystemControlForegroundBaseMediumLowBrush" />
            <StaticResource x:Key="FatSliderTickBarFillDisabled"
                            ResourceKey="SystemControlDisabledBaseMediumLowBrush" />
            <StaticResource x:Key="FatSliderInlineTickBarFill"
                            ResourceKey="SystemControlBackgroundAltHighBrush" />

            <SolidColorBrush x:Key="FatSliderBorderThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="FatSliderDisabledBorderThemeBrush"
                             Color="{ThemeResource SystemColorGrayTextColor}" />
            <SolidColorBrush x:Key="FatSliderThumbBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="FatSliderThumbBorderThemeBrush"
                             Color="{ThemeResource SystemColorHighlightTextColor}" />
            <SolidColorBrush x:Key="FatSliderThumbDisabledBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorGrayTextColor}" />
            <SolidColorBrush x:Key="FatSliderThumbPointerOverBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightColor}" />
            <SolidColorBrush x:Key="FatSliderThumbPointerOverBorderThemeBrush"
                             Color="{ThemeResource SystemColorHighlightTextColor}" />
            <SolidColorBrush x:Key="FatSliderThumbPressedBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightTextColor}" />
            <SolidColorBrush x:Key="FatSliderThumbPressedBorderThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="FatSliderTickMarkInlineBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="FatSliderTickMarkInlineDisabledForegroundThemeBrush"
                             Color="{ThemeResource SystemColorGrayTextColor}" />
            <SolidColorBrush x:Key="FatSliderTickmarkOutsideBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
            <SolidColorBrush x:Key="FatSliderTickMarkOutsideDisabledForegroundThemeBrush"
                             Color="{ThemeResource SystemColorGrayTextColor}" />
            <SolidColorBrush x:Key="FatSliderTrackBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonFaceColor}" />
            <SolidColorBrush x:Key="FatSliderTrackDecreaseBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightColor}" />
            <SolidColorBrush x:Key="FatSliderTrackDecreaseDisabledBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonFaceColor}" />
            <SolidColorBrush x:Key="FatSliderTrackDecreasePointerOverBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightColor}" />
            <SolidColorBrush x:Key="FatSliderTrackDecreasePressedBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorHighlightColor}" />
            <SolidColorBrush x:Key="FatSliderTrackDisabledBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonFaceColor}" />
            <SolidColorBrush x:Key="FatSliderTrackPointerOverBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonFaceColor}" />
            <SolidColorBrush x:Key="FatSliderTrackPressedBackgroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonFaceColor}" />
            <SolidColorBrush x:Key="FatSliderHeaderForegroundThemeBrush"
                             Color="{ThemeResource SystemColorButtonTextColor}" />
        </ResourceDictionary>
        <ResourceDictionary x:Key="Light">
            <!--  ColorAnimation requires TargetProperty to be Color thus some resources point to the colour instead of brush  -->
            <StaticResource x:Key="FatSliderContainerBackground"
                            ResourceKey="ControlFillColorTransparentBrush" />
            <StaticResource x:Key="FatSliderContainerBackgroundPointerOver"
                            ResourceKey="ControlFillColorTransparent" />
            <StaticResource x:Key="FatSliderContainerBackgroundPressed"
                            ResourceKey="ControlFillColorTransparent" />
            <StaticResource x:Key="FatSliderContainerBackgroundDisabled"
                            ResourceKey="ControlFillColorTransparent" />
            <StaticResource x:Key="FatSliderThumbBackground"
                            ResourceKey="AccentFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderThumbBackgroundPointerOver"
                            ResourceKey="AccentFillColorSecondaryBrush" />
            <StaticResource x:Key="FatSliderThumbBackgroundPressed"
                            ResourceKey="AccentFillColorTertiaryBrush" />
            <StaticResource x:Key="FatSliderThumbBackgroundDisabled"
                            ResourceKey="AccentFillColorDisabledBrush" />
            <StaticResource x:Key="FatSliderThumbBorderBrush"
                            ResourceKey="ControlElevationBorderBrush" />
            <StaticResource x:Key="FatSliderOuterThumbBackground"
                            ResourceKey="ControlSolidFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTrackFill"
                            ResourceKey="ControlStrongFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTrackFillPointerOver"
                            ResourceKey="ControlStrongFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTrackFillPressed"
                            ResourceKey="ControlStrongFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTrackFillDisabled"
                            ResourceKey="ControlStrongFillColorDisabledBrush" />
            <StaticResource x:Key="FatSliderTrackValueFill"
                            ResourceKey="AccentFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTrackValueFillPointerOver"
                            ResourceKey="AccentFillColorSecondaryBrush" />
            <StaticResource x:Key="FatSliderTrackValueFillPressed"
                            ResourceKey="AccentFillColorTertiaryBrush" />
            <StaticResource x:Key="FatSliderTrackValueFillDisabled"
                            ResourceKey="AccentFillColorDisabledBrush" />
            <StaticResource x:Key="FatSliderHeaderForeground"
                            ResourceKey="TextFillColorPrimaryBrush" />
            <StaticResource x:Key="FatSliderHeaderForegroundDisabled"
                            ResourceKey="TextFillColorDisabled" />
            <StaticResource x:Key="FatSliderTickBarFill"
                            ResourceKey="ControlStrongFillColorDefaultBrush" />
            <StaticResource x:Key="FatSliderTickBarFillDisabled"
                            ResourceKey="ControlStrongFillColorDisabled" />
            <StaticResource x:Key="FatSliderInlineTickBarFill"
                            ResourceKey="ControlFillColorInputActiveBrush" />

            <!--  Legacy Brushes  -->
            <SolidColorBrush x:Key="FatSliderBorderThemeBrush"
                             Color="Transparent" />
            <SolidColorBrush x:Key="FatSliderDisabledBorderThemeBrush"
                             Color="Transparent" />
            <SolidColorBrush x:Key="FatSliderThumbBackgroundThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderThumbBorderThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderThumbDisabledBackgroundThemeBrush"
                             Color="#FF7E7E7E" />
            <SolidColorBrush x:Key="FatSliderThumbPointerOverBackgroundThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderThumbPointerOverBorderThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderThumbPressedBackgroundThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderThumbPressedBorderThemeBrush"
                             Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="FatSliderTickMarkInlineBackgroundThemeBrush"
                             Color="Black" />
            <SolidColorBrush x:Key="FatSliderTickMarkInlineDisabledForegroundThemeBrush"
                             Color="Black" />
            <SolidColorBrush x:Key="FatSliderTickmarkOutsideBackgroundThemeBrush"
                             Color="#80FFFFFF" />
            <SolidColorBrush x:Key="FatSliderTickMarkOutsideDisabledForegroundThemeBrush"
                             Color="#80FFFFFF" />
            <SolidColorBrush x:Key="FatSliderTrackBackgroundThemeBrush"
                             Color="#29FFFFFF" />
            <SolidColorBrush x:Key="FatSliderTrackDecreaseBackgroundThemeBrush"
                             Color="#FF5B2EC5" />
            <SolidColorBrush x:Key="FatSliderTrackDecreaseDisabledBackgroundThemeBrush"
                             Color="#1FFFFFFF" />
            <SolidColorBrush x:Key="FatSliderTrackDecreasePointerOverBackgroundThemeBrush"
                             Color="#FF724BCD" />
            <SolidColorBrush x:Key="FatSliderTrackDecreasePressedBackgroundThemeBrush"
                             Color="#FF8152EF" />
            <SolidColorBrush x:Key="FatSliderTrackDisabledBackgroundThemeBrush"
                             Color="#29FFFFFF" />
            <SolidColorBrush x:Key="FatSliderTrackPointerOverBackgroundThemeBrush"
                             Color="#46FFFFFF" />
            <SolidColorBrush x:Key="FatSliderTrackPressedBackgroundThemeBrush"
                             Color="#59FFFFFF" />
            <SolidColorBrush x:Key="FatSliderHeaderForegroundThemeBrush"
                             Color="#FFFFFFFF" />
        </ResourceDictionary>
    </ResourceDictionary.ThemeDictionaries>

    <conv:DoubleRound2Converter x:Key="DoubleRound2Converter" />
    <conv:DoubleRound3Converter x:Key="DoubleRound3Converter" />

    <Thickness x:Key="FatSliderTopHeaderMargin">0,0,0,4</Thickness>
    <CornerRadius x:Key="FatSliderTrackCornerRadius">4</CornerRadius>
    <x:Double x:Key="FatSliderTrackBackgroundCornerRadiusX">4</x:Double>
    <x:Double x:Key="FatSliderTrackBackgroundCornerRadiusY">4</x:Double>
    <x:Double x:Key="FatSliderTrackForegroundCornerRadiusX">6</x:Double>
    <x:Double x:Key="FatSliderTrackForegroundCornerRadiusY">6</x:Double>
    <CornerRadius x:Key="FatSliderThumbCornerRadius">8</CornerRadius>
    <ThemeShadow x:Key="FatSliderThumbShadow" />
    <x:Double x:Key="FatSliderPreContentMargin">14</x:Double>
    <x:Double x:Key="FatSliderPostContentMargin">14</x:Double>
    <x:Double x:Key="FatSliderHorizontalHeight">32</x:Double>
    <x:Double x:Key="FatSliderVerticalWidth">32</x:Double>
    <x:Double x:Key="FatSliderHorizontalThumbWidth">18</x:Double>
    <x:Double x:Key="FatSliderHorizontalThumbHeight">18</x:Double>
    <x:Double x:Key="FatSliderVerticalThumbWidth">18</x:Double>
    <x:Double x:Key="FatSliderVerticalThumbHeight">18</x:Double>
    <x:Double x:Key="FatSliderInnerThumbWidth">48</x:Double>
    <x:Double x:Key="FatSliderInnerThumbHeight">32</x:Double>

    <x:Double x:Key="FatSliderHorizontalOutlineTickBarHeight">3</x:Double>
    <x:Double x:Key="FatSliderHorizontalInlineTickBarHeight">4</x:Double>

    <x:Double x:Key="FatSliderOutsideTickBarThemeHeight">10</x:Double>
    <x:Double x:Key="FatSliderTrackBackgroundThemeHeight">14</x:Double>
    <x:Double x:Key="FatSliderTrackThemeHeight">20</x:Double>
    <Thickness x:Key="FatSliderBorderThemeThickness">0</Thickness>
    <Thickness x:Key="FatSliderHeaderThemeMargin">0,0,0,4</Thickness>
    <text:FontWeight x:Key="FatSliderHeaderThemeFontWeight"><![CDATA[Normal]]></text:FontWeight>

    <Style x:Key="FatSliderStyle"
           TargetType="Slider">
        <Setter Property="Background" Value="{ThemeResource FatSliderTrackFill}" />
        <Setter Property="BorderThickness" Value="{ThemeResource FatSliderBorderThemeThickness}" />
        <Setter Property="BorderBrush" Value="{ThemeResource FatSliderThumbBorderBrush}" />
        <Setter Property="Foreground" Value="{ThemeResource FatSliderTrackValueFill}" />
        <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
        <Setter Property="FontSize" Value="{ThemeResource ControlContentThemeFontSize}" />
        <Setter Property="ManipulationMode" Value="None" />
        <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
        <Setter Property="FocusVisualMargin" Value="-7,0,-7,0" />
        <Setter Property="IsFocusEngagementEnabled" Value="True" />
        <Setter Property="CornerRadius" Value="{ThemeResource FatSliderTrackCornerRadius}" />
        <Setter Property="IsThumbToolTipEnabled" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Slider">
                    <Grid Margin="{TemplateBinding Padding}">
                        <Grid.Resources>
                            <Style x:Key="FatSliderThumbStyle"
                                   TargetType="Thumb">
                                <Setter Property="BorderThickness" Value="1" />
                                <Setter Property="Background" Value="{ThemeResource FatSliderThumbBackground}" />
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Thumb">
                                            <Border Margin="-20">
                                                <VisualStateManager.VisualStateGroups>
                                                    <VisualStateGroup x:Name="CommonStates">
                                                        <VisualState x:Name="Normal">
                                                            <Storyboard>
                                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetName="FatSliderInnerThumb"
                                                                                               Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleX)">
                                                                    <!--  0.86 is relative scale from 14px to 12px  -->
                                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                                          KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                                          Value="0.86" />
                                                                </DoubleAnimationUsingKeyFrames>
                                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetName="FatSliderInnerThumb"
                                                                                               Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleY)">
                                                                    <!--  0.86 is relative scale from 14px to 12px  -->
                                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                                          KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                                          Value="0.86" />
                                                                </DoubleAnimationUsingKeyFrames>
                                                            </Storyboard>
                                                        </VisualState>
                                                        <VisualState x:Name="PointerOver">
                                                            <Storyboard>
                                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetName="FatSliderInnerThumb"
                                                                                               Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleX)">
                                                                    <!--  1.167 is relative scale from 12px to 14px  -->
                                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                                          KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                                          Value="1" />
                                                                </DoubleAnimationUsingKeyFrames>
                                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetName="FatSliderInnerThumb"
                                                                                               Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleY)">
                                                                    <!--  1.167 is relative scale from 12px to 14px  -->
                                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                                          KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                                          Value="1" />
                                                                </DoubleAnimationUsingKeyFrames>
                                                            </Storyboard>
                                                        </VisualState>
                                                        <VisualState x:Name="Pressed">
                                                            <Storyboard>
                                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetName="FatSliderInnerThumb"
                                                                                               Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleX)">
                                                                    <!--  0.71 is relative scale from 14px to 10px  -->
                                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                                          KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                                          Value="0.70" />
                                                                </DoubleAnimationUsingKeyFrames>
                                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetName="FatSliderInnerThumb"
                                                                                               Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleY)">
                                                                    <!--  0.71 is relative scale from 14px to 10px  -->
                                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                                          KeyTime="{StaticResource ControlNormalAnimationDuration}"
                                                                                          Value="0.70" />
                                                                </DoubleAnimationUsingKeyFrames>
                                                            </Storyboard>
                                                        </VisualState>
                                                        <VisualState x:Name="Disabled">
                                                            <Storyboard>
                                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetName="FatSliderInnerThumb"
                                                                                               Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleX)">
                                                                    <!--  1.167 is relative scale from 12px to 14px  -->
                                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                                          KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                                          Value="1.167" />
                                                                </DoubleAnimationUsingKeyFrames>
                                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetName="FatSliderInnerThumb"
                                                                                               Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleY)">
                                                                    <!--  1.167 is relative scale from 12px to 14px  -->
                                                                    <SplineDoubleKeyFrame KeySpline="{StaticResource ControlFastOutSlowInKeySpline}"
                                                                                          KeyTime="{StaticResource ControlFastAnimationDuration}"
                                                                                          Value="1.167" />
                                                                </DoubleAnimationUsingKeyFrames>
                                                            </Storyboard>
                                                        </VisualState>
                                                    </VisualStateGroup>
                                                </VisualStateManager.VisualStateGroups>
                                                <Grid x:Name="FatSliderInnerThumb"
                                                      Width="Auto"
                                                      Height="{ThemeResource FatSliderInnerThumbHeight}"
                                                      MinWidth="{ThemeResource FatSliderInnerThumbWidth}"
                                                      Background="White"
                                                      CornerRadius="{ThemeResource FatSliderThumbCornerRadius}"
                                                      RenderTransformOrigin="0.5, 0.5"
                                                      Shadow="{ThemeResource FatSliderThumbShadow}"
                                                      Translation="0,0,16">
                                                    <Grid.RenderTransform>
                                                        <CompositeTransform />
                                                    </Grid.RenderTransform>
                                                    <TextBlock x:Name="FatSliderInnerThumbValue"
                                                               Margin="8,0"
                                                               VerticalAlignment="Center"
                                                               FontSize="16"
                                                               FontWeight="SemiBold"
                                                               Foreground="DimGray"
                                                               Text="{Binding DataContext, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource DoubleRound3Converter}}"
                                                               TextAlignment="Center" />
                                                </Grid>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Grid.Resources>

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalThumb"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderThumbBackground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalThumb"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderThumbBackground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FatSliderContainer"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderContainerBackground}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="PointerOver">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalTrackRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackFillPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalTrackRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackFillPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalThumb"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderThumbBackgroundPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalThumb"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderThumbBackgroundPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="FatSliderContainer"
                                                                      Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="0:0:0.083"
                                                                 Value="{ThemeResource FatSliderContainerBackgroundPointerOver}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalDecreaseRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackValueFillPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalDecreaseRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackValueFillPointerOver}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalTrackRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackFillPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalTrackRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackFillPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalThumb"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderThumbBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalThumb"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderThumbBackgroundPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="FatSliderContainer"
                                                                      Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="0:0:0.083"
                                                                 Value="{ThemeResource FatSliderContainerBackgroundPressed}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalDecreaseRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackValueFillPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalDecreaseRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackValueFillPressed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="HeaderContentPresenter"
                                                                      Storyboard.TargetProperty="(Panel.Foreground).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="0:0:0.083"
                                                                 Value="{ThemeResource FatSliderHeaderForegroundDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalDecreaseRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackValueFillDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalTrackRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackFillDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalDecreaseRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackValueFillDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalTrackRect"
                                                                       Storyboard.TargetProperty="Fill">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderTrackFillDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalThumb"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderThumbBackgroundDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalThumb"
                                                                       Storyboard.TargetProperty="Background">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="{ThemeResource FatSliderThumbBackgroundDisabled}" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="TopTickBar"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="0:0:0.083"
                                                                 Value="{ThemeResource FatSliderTickBarFillDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="BottomTickBar"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="0:0:0.083"
                                                                 Value="{ThemeResource FatSliderTickBarFillDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="LeftTickBar"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="0:0:0.083"
                                                                 Value="{ThemeResource FatSliderTickBarFillDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="RightTickBar"
                                                                      Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="0:0:0.083"
                                                                 Value="{ThemeResource FatSliderTickBarFillDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="FatSliderContainer"
                                                                      Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)">
                                            <LinearColorKeyFrame KeyTime="0:0:0.083"
                                                                 Value="{ThemeResource FatSliderContainerBackgroundDisabled}" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="FocusEngagementStates">
                                <VisualState x:Name="FocusDisengaged" />
                                <VisualState x:Name="FocusEngagedHorizontal">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FocusBorder"
                                                                       Storyboard.TargetProperty="(Control.IsTemplateFocusTarget)">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="False" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="HorizontalThumb"
                                                                       Storyboard.TargetProperty="(Control.IsTemplateFocusTarget)">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="True" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="FocusEngagedVertical">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FocusBorder"
                                                                       Storyboard.TargetProperty="(Control.IsTemplateFocusTarget)">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="False" />
                                        </ObjectAnimationUsingKeyFrames>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="VerticalThumb"
                                                                       Storyboard.TargetProperty="(Control.IsTemplateFocusTarget)">
                                            <DiscreteObjectKeyFrame KeyTime="0"
                                                                    Value="True" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>

                        <ContentPresenter x:Name="HeaderContentPresenter"
                                          Grid.Row="0"
                                          Margin="{ThemeResource FatSliderTopHeaderMargin}"
                                          x:DeferLoadStrategy="Lazy"
                                          Content="{TemplateBinding Header}"
                                          ContentTemplate="{TemplateBinding HeaderTemplate}"
                                          FontWeight="{ThemeResource FatSliderHeaderThemeFontWeight}"
                                          Foreground="{ThemeResource FatSliderHeaderForeground}"
                                          TextWrapping="Wrap"
                                          Visibility="Collapsed" />

                        <!--  This border exists only to draw the correct focus rect with rounded corners when element is focused.  -->
                        <Border x:Name="FocusBorder"
                                Grid.Row="1"
                                Control.IsTemplateFocusTarget="True"
                                CornerRadius="{ThemeResource ControlCornerRadius}" />

                        <Grid x:Name="FatSliderContainer"
                              Grid.Row="1"
                              Background="{ThemeResource FatSliderContainerBackground}">
                            <Grid x:Name="HorizontalTemplate"
                                  MinHeight="{ThemeResource FatSliderHorizontalHeight}">

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="{ThemeResource FatSliderPreContentMargin}" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="{ThemeResource FatSliderPostContentMargin}" />
                                </Grid.RowDefinitions>

                                <Rectangle x:Name="HorizontalTrackRect"
                                           Grid.Row="1"
                                           Grid.ColumnSpan="3"
                                           Height="{ThemeResource FatSliderTrackBackgroundThemeHeight}"
                                           Fill="{TemplateBinding Background}"
                                           RadiusX="{ThemeResource FatSliderTrackBackgroundCornerRadiusX}"
                                           RadiusY="{ThemeResource FatSliderTrackBackgroundCornerRadiusY}" />
                                <Rectangle x:Name="HorizontalDecreaseRect"
                                           Grid.Row="1"
                                           Grid.Column="0"
                                           Height="{ThemeResource FatSliderTrackThemeHeight}"
                                           Fill="{TemplateBinding Foreground}"
                                           RadiusX="{ThemeResource FatSliderTrackForegroundCornerRadiusX}"
                                           RadiusY="{ThemeResource FatSliderTrackForegroundCornerRadiusY}"
                                           Shadow="{ThemeResource FatSliderThumbShadow}"
                                           Translation="0,0,8" />
                                <TickBar x:Name="TopTickBar"
                                         Grid.Row="0"
                                         Grid.Column="0"
                                         Grid.ColumnSpan="3"
                                         Height="{ThemeResource FatSliderHorizontalOutlineTickBarHeight}"
                                         Margin="0,0,0,4"
                                         VerticalAlignment="Bottom"
                                         Fill="{ThemeResource FatSliderTickBarFill}"
                                         Visibility="Collapsed" />
                                <TickBar x:Name="FatSliderHorizontalInlineTickBar"
                                         Grid.Row="1"
                                         Grid.Column="0"
                                         Grid.ColumnSpan="3"
                                         Height="{ThemeResource FatSliderHorizontalInlineTickBarHeight}"
                                         Fill="{ThemeResource FatSliderInlineTickBarFill}"
                                         Visibility="Collapsed" />
                                <TickBar x:Name="BottomTickBar"
                                         Grid.Row="2"
                                         Grid.Column="0"
                                         Grid.ColumnSpan="3"
                                         Height="{ThemeResource FatSliderHorizontalOutlineTickBarHeight}"
                                         Margin="0,4,0,0"
                                         VerticalAlignment="Top"
                                         Fill="{ThemeResource FatSliderTickBarFill}"
                                         Visibility="Collapsed" />
                                <Thumb x:Name="HorizontalThumb"
                                       Grid.Row="0"
                                       Grid.RowSpan="3"
                                       Grid.Column="1"
                                       Width="{ThemeResource FatSliderHorizontalThumbWidth}"
                                       Height="{ThemeResource FatSliderHorizontalThumbHeight}"
                                       AutomationProperties.AccessibilityView="Raw"
                                       CornerRadius="{StaticResource ControlCornerRadius}"
                                       DataContext="{TemplateBinding Value}"
                                       FocusVisualMargin="-14,-6,-14,-6"
                                       Style="{StaticResource FatSliderThumbStyle}" />
                            </Grid>
                            <Grid x:Name="VerticalTemplate"
                                  MinWidth="{ThemeResource FatSliderVerticalWidth}"
                                  Visibility="Collapsed">

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="{ThemeResource FatSliderPreContentMargin}" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="{ThemeResource FatSliderPostContentMargin}" />
                                </Grid.ColumnDefinitions>

                                <Rectangle x:Name="VerticalTrackRect"
                                           Grid.RowSpan="3"
                                           Grid.Column="1"
                                           Width="{ThemeResource FatSliderTrackThemeHeight}"
                                           Fill="{TemplateBinding Background}"
                                           RadiusX="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TopLeftCornerRadiusDoubleValueConverter}}"
                                           RadiusY="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BottomRightCornerRadiusDoubleValueConverter}}" />
                                <Rectangle x:Name="VerticalDecreaseRect"
                                           Grid.Row="2"
                                           Grid.Column="1"
                                           Fill="{TemplateBinding Foreground}"
                                           RadiusX="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource TopLeftCornerRadiusDoubleValueConverter}}"
                                           RadiusY="{Binding CornerRadius, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BottomRightCornerRadiusDoubleValueConverter}}" />
                                <TickBar x:Name="LeftTickBar"
                                         Grid.Row="0"
                                         Grid.RowSpan="3"
                                         Grid.Column="0"
                                         Width="{ThemeResource FatSliderOutsideTickBarThemeHeight}"
                                         Margin="0,0,4,0"
                                         HorizontalAlignment="Right"
                                         Fill="{ThemeResource FatSliderTickBarFill}"
                                         Visibility="Collapsed" />
                                <TickBar x:Name="VerticalInlineTickBar"
                                         Grid.Row="0"
                                         Grid.RowSpan="3"
                                         Grid.Column="1"
                                         Width="{ThemeResource FatSliderTrackThemeHeight}"
                                         Fill="{ThemeResource FatSliderInlineTickBarFill}"
                                         Visibility="Collapsed" />
                                <TickBar x:Name="RightTickBar"
                                         Grid.Row="0"
                                         Grid.RowSpan="3"
                                         Grid.Column="2"
                                         Width="{ThemeResource FatSliderOutsideTickBarThemeHeight}"
                                         Margin="4,0,0,0"
                                         HorizontalAlignment="Left"
                                         Fill="{ThemeResource FatSliderTickBarFill}"
                                         Visibility="Collapsed" />
                                <Thumb x:Name="VerticalThumb"
                                       Grid.Row="1"
                                       Grid.Column="0"
                                       Grid.ColumnSpan="3"
                                       Width="{ThemeResource FatSliderVerticalThumbWidth}"
                                       Height="{ThemeResource FatSliderVerticalThumbHeight}"
                                       AutomationProperties.AccessibilityView="Raw"
                                       CornerRadius="{StaticResource ControlCornerRadius}"
                                       DataContext="{TemplateBinding Value}"
                                       FocusVisualMargin="-6,-14,-6,-14"
                                       Style="{StaticResource FatSliderThumbStyle}" />
                            </Grid>

                        </Grid>

                    </Grid>

                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
